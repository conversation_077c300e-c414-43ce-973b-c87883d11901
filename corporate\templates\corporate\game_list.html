{% extends 'corporate/base.html' %}

{% block title %}
    {% if company %}
        Available Games - {{ company.name }}
    {% else %}
        Available Games - Corporate Prompt Master
    {% endif %}
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-3">Available Games</h2>

        {% if is_public_view %}
        <div class="alert alert-info mb-4">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Welcome, Guest!</strong> You can play all games without logging in, but your progress won't be saved between sessions and you won't appear on leaderboards.
            <div class="mt-2">
                <a href="{% url 'corporate:corporate_login' %}" class="btn btn-sm btn-primary me-2">
                    <i class="fas fa-sign-in-alt me-1"></i>Log In
                </a>
                <a href="{% url 'corporate:corporate_register' %}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-user-plus me-1"></i>Register
                </a>
            </div>
        </div>
        {% endif %}

        {% if user_sessions %}
        <div class="row mb-4">
            <div class="col-md-12">
                {% include 'corporate/user_game_progress.html' %}
            </div>
        </div>
        {% endif %}

        {% if available_games %}
        <div class="row">
            {% for game in available_games %}
            <div class="col-md-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">{{ game.game_name }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="game-icon mb-3 text-center">
                            <i class="fas fa-gamepad fa-5x text-primary"></i>
                        </div>

                        <h5 class="card-title">Prompt Engineering Training</h5>
                        <p class="card-text">Master the art of prompt engineering by progressing through a corporate hierarchy. Complete challenges to advance your career from applicant to CEO!</p>

                        <div class="game-details mt-4">
                            <div class="row">
                                <div class="col-6">
                                    <p><strong>Access Granted:</strong> {{ game.access_granted_at|date:"M d, Y" }}</p>
                                </div>
                                <div class="col-6">
                                    <p><strong>Status:</strong>
                                        {% if game.is_active %}
                                        <span class="badge bg-success">Active</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>

                            {% if game.access_expires_at %}
                            <div class="alert alert-warning mt-2">
                                <i class="fas fa-clock me-2"></i>Access expires on {{ game.access_expires_at|date:"M d, Y" }}
                            </div>
                            {% endif %}

                            {% if is_public_view and game.is_public %}
                            <div class="alert alert-info mt-2">
                                <i class="fas fa-info-circle me-2"></i>You're playing as a guest. <a href="{% url 'corporate:corporate_login' %}" class="alert-link">Log in</a> to save your progress and appear on leaderboards.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-grid">
                            {% if user_sessions %}
                            <a href="{% url 'game:index' %}{% if company %}?company={{ company.slug }}{% endif %}" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>Continue Game
                            </a>
                            {% else %}
                            <a href="{% url 'game:index' %}{% if company %}?company={{ company.slug }}{% endif %}" class="btn btn-primary">
                                <i class="fas fa-play me-2"></i>Start Game
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
            {% if is_public_view %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>No public games are currently available.
            </div>

            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-gamepad fa-5x text-muted mb-3"></i>
                    <h3>No Public Games Available</h3>
                    <p class="lead">Please check back later for public games.</p>
                </div>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>No games are currently available for your company.
            </div>

            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-gamepad fa-5x text-muted mb-3"></i>
                    <h3>No Games Available</h3>
                    <p class="lead">Your company administrator needs to activate games for your account.</p>

                    {% if user.corporate_profile.is_company_admin %}
                    <div class="mt-4">
                        <p>As an administrator, you can contact support to add games to your company account.</p>
                        <a href="mailto:<EMAIL>" class="btn btn-primary">
                            <i class="fas fa-envelope me-2"></i>Contact Support
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        {% endif %}
    </div>
</div>

{% if available_games %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>About Prompt Engineering Training</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4>What is Prompt Engineering?</h4>
                        <p>Prompt engineering is the art and science of crafting effective prompts to get the best results from AI language models. It's a critical skill for working with AI systems in a corporate environment.</p>

                        <h4>Game Overview</h4>
                        <p>In this game, you'll progress through a corporate hierarchy by completing prompt engineering challenges. Starting as an applicant, you'll work your way up through various departments, eventually reaching executive positions.</p>

                        <h4>Key Features</h4>
                        <ul>
                            <li><strong>Career Progression:</strong> Advance through 20+ corporate roles</li>
                            <li><strong>Real-world Challenges:</strong> Solve practical prompt engineering tasks</li>
                            <li><strong>Performance Scoring:</strong> Earn points based on the quality of your prompts</li>
                            <li><strong>Detailed Feedback:</strong> Receive personalized feedback from AI managers</li>
                            <li><strong>Certificate:</strong> Earn a certificate upon completion</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Benefits</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Improve AI communication skills
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Learn effective prompt patterns
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Understand AI capabilities and limitations
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Develop problem-solving skills
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Earn a verifiable certificate
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Restart Game Confirmation Modal -->
<div class="modal fade" id="restartGameModal" tabindex="-1" aria-labelledby="restartGameModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title" id="restartGameModalLabel"><i class="fas fa-exclamation-triangle me-2"></i>Restart Game?</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="lead">Are you sure you want to restart the game?</p>
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-circle me-2"></i>
          <strong>Warning:</strong> This will clear all your messages and reset your progress.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmRestartButton">
          <i class="fas fa-redo-alt me-2"></i>Yes, Restart Game
        </button>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for Restart Game functionality -->
{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Handle restart game button click
    const confirmRestartButton = document.getElementById('confirmRestartButton');
    let currentSessionId = null;

    // Store the session ID when a restart button is clicked
    const restartButtons = document.querySelectorAll('.restart-game-btn');
    restartButtons.forEach(button => {
      button.addEventListener('click', function() {
        currentSessionId = this.getAttribute('data-session-id');
      });
    });

    // Handle confirmation button click
    if (confirmRestartButton) {
      confirmRestartButton.addEventListener('click', async function() {
        try {
          // Show loading state
          this.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Restarting...';
          this.disabled = true;

          // Call the start_game API endpoint with a cache-busting parameter
          const timestamp = new Date().getTime();
          const response = await fetch(`/game/api/start_game/?t=${timestamp}`, {
            method: 'GET',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          });

          if (response.ok) {
            // Get the response data to ensure the API call is complete
            await response.json();

            // Redirect to the game page with a cache-busting parameter
            window.location.href = `/game/?restart=true&t=${timestamp}`;
          } else {
            throw new Error('Failed to restart game');
          }
        } catch (error) {
          console.error('Error restarting game:', error);
          alert('An error occurred while trying to restart the game. Please try again.');

          // Reset button state
          this.innerHTML = '<i class="fas fa-redo-alt me-2"></i>Yes, Restart Game';
          this.disabled = false;
        }
      });
    }
  });
</script>
{% endblock %}
{% endblock %}