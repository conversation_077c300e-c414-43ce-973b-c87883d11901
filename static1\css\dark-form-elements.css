/* Dark theme styling for form elements */

/* File input styling */
input[type="file"].form-control {
    background-color: #1a1a1a !important;
    border: 1px solid #333 !important;
    color: #e0e0e0 !important;
    box-shadow: none !important;
    transition: all 0.3s ease !important;
    padding: 10px 15px !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    position: relative !important;
}

input[type="file"].form-control:hover {
    border-color: #4d81d6 !important;
    box-shadow: 0 0 10px rgba(77, 129, 214, 0.2) !important;
}

input[type="file"].form-control:focus {
    border-color: #4d81d6 !important;
    box-shadow: 0 0 15px rgba(77, 129, 214, 0.3) !important;
    outline: none !important;
}

/* Add a glow effect when a file is selected */
input[type="file"].form-control.file-selected {
    border-color: #4d81d6 !important;
    box-shadow: 0 0 15px rgba(77, 129, 214, 0.4) !important;
}

/* Custom file input button */
input[type="file"].form-control::file-selector-button {
    background: linear-gradient(135deg, #2d4b7a 0%, #3a5d94 100%) !important;
    color: #ffffff !important;
    border: none !important;
    padding: 8px 16px !important;
    margin-right: 15px !important;
    border-radius: 6px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

input[type="file"].form-control::file-selector-button:hover {
    background: linear-gradient(135deg, #3a5d94 0%, #4d81d6 100%) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    transform: translateY(-1px) !important;
}

/* Text inputs */
input[type="text"].form-control,
input[type="email"].form-control,
input[type="password"].form-control,
input[type="number"].form-control,
input[type="url"].form-control,
input[type="tel"].form-control,
input[type="search"].form-control,
textarea.form-control,
select.form-select {
    background-color: #1a1a1a !important;
    border: 1px solid #333 !important;
    color: #e0e0e0 !important;
    box-shadow: none !important;
    transition: all 0.3s ease !important;
    border-radius: 8px !important;
}

input[type="text"].form-control:hover,
input[type="email"].form-control:hover,
input[type="password"].form-control:hover,
input[type="number"].form-control:hover,
input[type="url"].form-control:hover,
input[type="tel"].form-control:hover,
input[type="search"].form-control:hover,
textarea.form-control:hover,
select.form-select:hover {
    border-color: #4d81d6 !important;
}

input[type="text"].form-control:focus,
input[type="email"].form-control:focus,
input[type="password"].form-control:focus,
input[type="number"].form-control:focus,
input[type="url"].form-control:focus,
input[type="tel"].form-control:focus,
input[type="search"].form-control:focus,
textarea.form-control:focus,
select.form-select:focus {
    border-color: #4d81d6 !important;
    box-shadow: 0 0 0 3px rgba(77, 129, 214, 0.25) !important;
    outline: none !important;
}

/* Form labels */
.form-label {
    color: #e0e0e0 !important;
    font-weight: 500 !important;
    margin-bottom: 8px !important;
}

/* Form help text */
.form-text {
    color: #a0a0a0 !important;
    font-size: 0.85rem !important;
}

/* Placeholder text */
::placeholder {
    color: #666 !important;
    opacity: 1 !important;
}

/* Checkboxes and radio buttons */
.form-check-input {
    background-color: #1a1a1a !important;
    border: 1px solid #333 !important;
}

.form-check-input:checked {
    background-color: #4d81d6 !important;
    border-color: #4d81d6 !important;
}

.form-check-label {
    color: #e0e0e0 !important;
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, #2d4b7a 0%, #3a5d94 100%) !important;
    border: none !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.3s ease !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #3a5d94 0%, #4d81d6 100%) !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    transform: translateY(-1px) !important;
}

.btn-secondary {
    background-color: #333 !important;
    border: none !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.3s ease !important;
}

.btn-secondary:hover {
    background-color: #444 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
    transform: translateY(-1px) !important;
}

/* Form validation */
.is-valid {
    border-color: #28a745 !important;
}

.is-invalid {
    border-color: #dc3545 !important;
}

.invalid-feedback {
    color: #dc3545 !important;
}

.valid-feedback {
    color: #28a745 !important;
}
