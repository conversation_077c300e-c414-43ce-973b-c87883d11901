"""
Gunicorn configuration file for the prompt_game project.
This replaces the Passenger deployment method.

For more information on Gunicorn configuration, see:
https://docs.gunicorn.org/en/stable/configure.html
"""

import multiprocessing
import os
from pathlib import Path

# Get the base directory of the project
BASE_DIR = Path(__file__).resolve().parent

# Bind to this socket
bind = "0.0.0.0:8000"

# Number of worker processes
# A good rule of thumb is 2-4 x number of CPU cores
workers = multiprocessing.cpu_count() * 2 + 1

# Worker class to use
worker_class = "sync"

# The maximum number of requests a worker will process before restarting
max_requests = 1000
max_requests_jitter = 50

# Timeout for worker processes in seconds
timeout = 120

# Restart workers when code changes (development only)
reload = False

# Logging
accesslog = "-"  # Log to stdout
errorlog = "-"   # Log to stderr
loglevel = "info"

# Process name
proc_name = "prompt_game"

# Set environment variables
raw_env = [
    "DJANGO_SETTINGS_MODULE=prompt_game.settings",
]

# Preload the application
preload_app = True

# User and group to run as
# Uncomment and set these in production
# user = "www-data"
# group = "www-data"

# Directory to chdir to before loading apps
chdir = str(BASE_DIR)

# Daemon mode
daemon = False

# SSL configuration (if needed)
# keyfile = "/path/to/keyfile"
# certfile = "/path/to/certfile"

def post_fork(server, worker):
    """
    Called just after a worker has been forked.
    """
    server.log.info("Worker spawned (pid: %s)", worker.pid)

def pre_fork(server, worker):
    """
    Called just prior to forking the worker subprocess.
    """
    pass

def pre_exec(server):
    """
    Called just prior to forking off a secondary
    master process during things like config reloading.
    """
    server.log.info("Forked child, re-executing.")

def when_ready(server):
    """
    Called just after the server is started.
    """
    server.log.info("Server is ready. Spawning workers")
