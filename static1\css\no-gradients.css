/**
 * No Gradients CSS
 * This file specifically targets the HTML structure shown in the screenshot
 * to ensure all gradients are removed
 */

/* Target the exact structure shown in the screenshot */
div.message.assistant-message.mb-3 span.message-content,
div.message.assistant-message span.message-content,
.message.assistant-message .message-content,
.assistant-message .message-content,
span.message-content {
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #333333 !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    border-radius: 1rem !important;
}

/* Target user messages */
div.message.user-message.mb-3 span.message-content,
div.message.user-message span.message-content,
.message.user-message .message-content,
.user-message .message-content {
    background: #3b7dd8 !important;
    background-color: #3b7dd8 !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
    border-radius: 1rem !important;
}

/* Target the message container */
div.message.assistant-message.mb-3,
div.message.assistant-message,
.message.assistant-message,
.assistant-message {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}

div.message.user-message.mb-3,
div.message.user-message,
.message.user-message,
.user-message {
    background: transparent !important;
    background-color: transparent !important;
    background-image: none !important;
}

/* Remove any pseudo-elements that might add gradients */
span.message-content::before,
span.message-content::after,
.message-content::before,
.message-content::after {
    display: none !important;
    content: none !important;
    background: none !important;
    background-image: none !important;
    opacity: 0 !important;
    visibility: hidden !important;
}

/* Target the specific span.message-content class */
span.message-content {
    position: relative !important;
    z-index: 10 !important;
}

/* Override any inline styles */
[style*="background"] span.message-content,
span.message-content[style*="background"],
[style*="linear-gradient"] span.message-content,
span.message-content[style*="linear-gradient"],
[style*="radial-gradient"] span.message-content,
span.message-content[style*="radial-gradient"] {
    background: inherit !important;
    background-color: inherit !important;
    background-image: none !important;
}

/* Ensure these styles are applied with the highest priority */
html body div.message.assistant-message.mb-3 span.message-content,
html body div.message.assistant-message span.message-content,
html body .message.assistant-message .message-content,
html body .assistant-message .message-content {
    background: #ffffff !important;
    background-color: #ffffff !important;
    background-image: none !important;
}

html body div.message.user-message.mb-3 span.message-content,
html body div.message.user-message span.message-content,
html body .message.user-message .message-content,
html body .user-message .message-content {
    background: #3b7dd8 !important;
    background-color: #3b7dd8 !important;
    background-image: none !important;
}
