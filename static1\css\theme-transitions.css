/**
 * Theme Transitions CSS
 * Smooth transitions between light and dark mode
 */

/* Apply transitions to all elements */
* {
  transition-property: color, background-color, border-color, box-shadow, filter, background;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}

/* Elements that should not have transitions */
.no-transition,
.no-transition * {
  transition: none !important;
}

/* Disable transitions for performance-sensitive elements */
img, video, canvas, iframe, svg {
  transition: none !important;
}

/* Disable transitions for TinyMCE editor content to prevent flicker */
.tox-edit-area__iframe {
  transition: none !important;
}

/* Ensure smooth scrolling is maintained */
html {
  scroll-behavior: smooth;
}

/* Ensure transitions don't affect layout shifts */
body {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Ensure fixed elements transition smoothly */
.fixed-top, 
.fixed-bottom, 
.sticky-top, 
.theme-toggle-btn {
  transition: background-color 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease;
}

/* Ensure form controls transition smoothly */
.form-control, 
.form-select, 
.btn {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure cards transition smoothly */
.card {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease;
}

/* Ensure modals transition smoothly */
.modal-content {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Ensure dropdowns transition smoothly */
.dropdown-menu {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure alerts transition smoothly */
.alert {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Ensure badges transition smoothly */
.badge {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Ensure chat messages transition smoothly */
.message-content {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure sidebar transitions smoothly */
.sidebar {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Ensure navbar transitions smoothly */
.navbar {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure list group items transition smoothly */
.list-group-item {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Ensure tables transition smoothly */
.table, .table th, .table td {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Ensure progress bars transition smoothly */
.progress, .progress-bar {
  transition: background-color 0.3s ease, width 0.6s ease;
}

/* Ensure tooltips and popovers transition smoothly */
.tooltip, .popover {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Ensure toasts transition smoothly */
.toast {
  transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure scrollbars transition smoothly */
::-webkit-scrollbar-track,
::-webkit-scrollbar-thumb {
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Ensure links transition smoothly */
a {
  transition: color 0.3s ease, text-decoration 0.3s ease;
}

/* Ensure buttons transition smoothly */
button {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease, transform 0.3s ease;
}

/* Ensure inputs transition smoothly */
input, select, textarea {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
}

/* Ensure icons transition smoothly */
.bi, .icon, svg {
  transition: color 0.3s ease, fill 0.3s ease;
}

/* Ensure borders transition smoothly */
.border, [class*="border-"] {
  transition: border-color 0.3s ease;
}

/* Ensure backgrounds transition smoothly */
[class*="bg-"] {
  transition: background-color 0.3s ease;
}

/* Ensure text colors transition smoothly */
[class*="text-"] {
  transition: color 0.3s ease;
}

/* Ensure shadows transition smoothly */
[class*="shadow"] {
  transition: box-shadow 0.3s ease;
}

/* Ensure opacity transitions smoothly */
[class*="opacity"] {
  transition: opacity 0.3s ease;
}

/* Ensure transforms transition smoothly */
[style*="transform"] {
  transition: transform 0.3s ease;
}

/* Ensure filters transition smoothly */
[style*="filter"] {
  transition: filter 0.3s ease;
}
