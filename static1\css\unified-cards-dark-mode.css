/**
 * Unified Cards Dark Mode CSS
 * Applies consistent dark mode styling to all directory cards (assistants, companies)
 * with proper mobile and tablet responsiveness
 */

/* Dark mode styling for all directory cards */
[data-theme="dark"] .directory-card {
  background: linear-gradient(145deg, #1e1e1e, #252525) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .directory-card:hover {
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  transform: translateY(-5px) !important;
}

/* Dark mode styling for logo containers */
[data-theme="dark"] .logo-container {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

[data-theme="dark"] .logo-placeholder {
  color: #0d6efd !important;
  background-color: rgba(13, 110, 253, 0.1) !important;
}

/* Dark mode styling for text elements */
[data-theme="dark"] .assistant-name a,
[data-theme="dark"] .directory-card h5 a,
[data-theme="dark"] .directory-card h6 a {
  color: #ffffff !important;
  text-decoration: none !important;
}

[data-theme="dark"] .assistant-name a:hover,
[data-theme="dark"] .directory-card h5 a:hover,
[data-theme="dark"] .directory-card h6 a:hover {
  color: #0088ff !important;
}

[data-theme="dark"] .assistant-meta,
[data-theme="dark"] .directory-card .text-muted {
  color: #cccccc !important;
}

/* Dark mode styling for filter forms */
[data-theme="dark"] .filter-form {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
  color: #ffffff !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .filter-form .input-group {
  background-color: transparent !important;
  border: none !important;
}

[data-theme="dark"] .filter-form .input-group-text {
  background-color: #252525 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .filter-form .form-control {
  background-color: #252525 !important;
  border-color: #444444 !important;
  color: #ffffff !important;
}

/* Dark mode styling for buttons */
[data-theme="dark"] .btn-outline-secondary {
  color: #ffffff !important;
  border-color: #444444 !important;
}

[data-theme="dark"] .btn-outline-secondary:hover {
  background-color: #333333 !important;
  color: #ffffff !important;
}

/* Dark mode styling for tier sections */
[data-theme="dark"] .tier-section {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

/* Dark mode styling for featured sections */
[data-theme="dark"] .featured-section {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

/* Dark mode styling for list group items */
[data-theme="dark"] .list-group-item {
  background-color: #252525 !important;
  border-color: #333333 !important;
  color: #ffffff !important;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .list-group-item:hover {
  background-color: #2a2a2a !important;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3) !important;
}

/* Dark mode styling for featured carousel */
[data-theme="dark"] .featured-carousel-container {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
}

[data-theme="dark"] .featured-item-wrapper {
  background-color: #252525 !important;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .featured-item-wrapper:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4) !important;
}

[data-theme="dark"] .featured-carousel-item .logo-container {
  background-color: #333333 !important;
  border-color: #444444 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .featured-carousel-item .item-info h5 {
  color: #3b7dd8 !important;
}

[data-theme="dark"] .featured-carousel-item .item-info p {
  color: #cccccc !important;
}

/* Dark mode styling for like buttons */
[data-theme="dark"] .like-button {
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

/* Heart icon styling for like buttons - white for unliked, pink for liked */
[data-theme="dark"] .like-button i,
[data-theme="dark"] .btn-like i,
[data-theme="dark"] .btn-favorite i,
[data-theme="dark"] .favorite-button i {
  color: #ffffff !important;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5)) !important;
}

/* ONLY liked hearts should be pink/red in dark mode */
[data-theme="dark"] .like-button.text-danger i,
[data-theme="dark"] .btn-like.text-danger i,
[data-theme="dark"] .btn-favorite.text-danger i,
[data-theme="dark"] .favorite-button.text-danger i {
  color: #ff3366 !important;
  filter: drop-shadow(0 0 3px rgba(255, 51, 102, 0.5)) !important;
}

/* Mobile-specific dark mode optimizations */
@media (max-width: 768px) {
  [data-theme="dark"] .filter-form {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
    padding: 1rem !important;
  }

  [data-theme="dark"] .tier-section {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
    padding: 1rem !important;
  }

  [data-theme="dark"] .directory-card {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    padding: 1rem !important;
  }
}

/* Tablet-specific dark mode optimizations */
@media (min-width: 769px) and (max-width: 991.98px) {
  [data-theme="dark"] .filter-form {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
    padding: 1.25rem !important;
  }

  [data-theme="dark"] .tier-section {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
    padding: 1.25rem !important;
  }

  [data-theme="dark"] .directory-card {
    background: linear-gradient(145deg, #1e1e1e, #252525) !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
    color: #ffffff !important;
    padding: 1.25rem !important;
  }

  /* Improve text contrast in dark mode for tablet */
  [data-theme="dark"] .directory-card h6,
  [data-theme="dark"] .directory-card .assistant-name a,
  [data-theme="dark"] .directory-card h5 a {
    color: #ffffff !important;
    font-weight: 500 !important;
  }

  [data-theme="dark"] .directory-card .text-muted,
  [data-theme="dark"] .directory-card .item-description {
    color: #cccccc !important;
  }

  /* Improve logo container in dark mode for tablet */
  [data-theme="dark"] .logo-container {
    background-color: #1a1a1a !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important;
  }
}

/* Very small screens dark mode optimizations */
@media (max-width: 576px) {
  [data-theme="dark"] .filter-form {
    padding: 0.75rem !important;
  }

  [data-theme="dark"] .tier-section {
    padding: 0.75rem !important;
  }

  [data-theme="dark"] .directory-card {
    padding: 0.75rem !important;
  }
}
