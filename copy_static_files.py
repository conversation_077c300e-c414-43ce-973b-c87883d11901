"""
<PERSON><PERSON>t to copy static files from the Flask app to the Django app.
"""

import os
import shutil
import glob
import re

# Define paths
FLASK_ROOT = os.path.abspath('.')
DJANGO_STATIC_ROOT = os.path.join(FLASK_ROOT, 'game', 'static', 'game')

# Create directories if they don't exist
os.makedirs(os.path.join(DJANGO_STATIC_ROOT, 'css'), exist_ok=True)
os.makedirs(os.path.join(DJANGO_STATIC_ROOT, 'js'), exist_ok=True)
os.makedirs(os.path.join(DJANGO_STATIC_ROOT, 'images'), exist_ok=True)

# Find all CSS files in the project
css_files = []
for root, dirs, files in os.walk(FLASK_ROOT):
    # Skip Django static directories and migrations
    if 'static' in root or 'migrations' in root or '__pycache__' in root:
        continue
    for file in files:
        if file.endswith('.css'):
            css_files.append(os.path.join(root, file))

# Copy CSS files
for css_file in css_files:
    filename = os.path.basename(css_file)
    dest_path = os.path.join(DJANGO_STATIC_ROOT, 'css', filename)
    print(f"Copying {css_file} to {dest_path}")
    shutil.copy2(css_file, dest_path)

# Find all JS files in the project
js_files = []
for root, dirs, files in os.walk(FLASK_ROOT):
    # Skip Django static directories and migrations
    if 'static' in root or 'migrations' in root or '__pycache__' in root:
        continue
    for file in files:
        if file.endswith('.js') and not file.endswith('.min.js'):
            js_files.append(os.path.join(root, file))

# Copy JS files
for js_file in js_files:
    filename = os.path.basename(js_file)
    dest_path = os.path.join(DJANGO_STATIC_ROOT, 'js', filename)
    print(f"Copying {js_file} to {dest_path}")
    shutil.copy2(js_file, dest_path)

# Find all image files in the project
image_files = []
image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico']
for root, dirs, files in os.walk(FLASK_ROOT):
    # Skip Django static directories and migrations
    if 'static' in root or 'migrations' in root or '__pycache__' in root:
        continue
    for file in files:
        if any(file.lower().endswith(ext) for ext in image_extensions):
            image_files.append(os.path.join(root, file))

# Copy image files
for image_file in image_files:
    filename = os.path.basename(image_file)
    dest_path = os.path.join(DJANGO_STATIC_ROOT, 'images', filename)
    print(f"Copying {image_file} to {dest_path}")
    shutil.copy2(image_file, dest_path)

# Update static file references in HTML templates
template_file = os.path.join(FLASK_ROOT, 'game', 'templates', 'game', 'index.html')
if os.path.exists(template_file):
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()

    # Already using Django static tags, no need to update
    print("Template file already using Django static tags")

print("Static files copied successfully!")
