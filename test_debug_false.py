"""
Test script to check if the site is accessible with DEBUG = False.
"""

import os
import sys
import requests
import time

def test_site_accessibility():
    """Test if the site is accessible with DEBUG = False."""
    print("Testing site accessibility with DEBUG = False...")

    # URLs to test
    urls = [
        "http://localhost:8000/",
        "http://localhost:8000/corporate/login/",
        "http://localhost:8000/game/",
        "http://localhost:8000/admin/",
        "http://localhost:8000/static/img/favicons/favicon.ico",
        "http://localhost:8000/static/corporate/css/corporate_styles.css",
        "http://localhost:8000/static/js/button-fix.js",
    ]

    # Wait for the server to start
    print("Waiting for server to start...")
    time.sleep(5)

    # Test each URL
    for url in urls:
        try:
            print(f"Testing {url}...")
            response = requests.get(url)
            print(f"  Status code: {response.status_code}")
            print(f"  Content type: {response.headers.get('Content-Type', 'unknown')}")
            print(f"  Content length: {len(response.content)} bytes")
            if response.status_code == 200:
                print("  SUCCESS: URL is accessible")
            else:
                print("  ERROR: URL returned non-200 status code")
        except Exception as e:
            print(f"  ERROR: {str(e)}")

    print("\nTest completed.")

if __name__ == "__main__":
    test_site_accessibility()
