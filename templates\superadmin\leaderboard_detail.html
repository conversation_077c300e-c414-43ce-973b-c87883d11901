{% extends 'superadmin/base_superadmin.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block superadmin_content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{% url 'superadmin:leaderboard_list' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="bi bi-arrow-left"></i> {% trans "Back to Leaderboards" %}
        </a>
    </div>

    <!-- Leaderboard Info Card -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "Leaderboard Information" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">{% trans "ID" %}</th>
                                <td>{{ leaderboard.id }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Name" %}</th>
                                <td>{{ leaderboard.name }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Company" %}</th>
                                <td>
                                    {% if leaderboard.company %}
                                    <a href="{% url 'superadmin:company_detail' pk=leaderboard.company.id %}">
                                        {{ leaderboard.company.name }}
                                    </a>
                                    {% if company_user_count %}
                                    <span class="badge bg-info ms-2">{{ company_user_count }} {% trans "users" %}</span>
                                    {% endif %}
                                    {% else %}
                                    {% trans "Global Leaderboard" %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% trans "Time Period" %}</th>
                                <td>
                                    <span class="badge bg-primary fs-6">
                                    {% if leaderboard.time_period == 'all_time' %}
                                    {% trans "All Time" %}
                                    {% elif leaderboard.time_period == 'monthly' %}
                                    {% trans "Monthly" %}
                                    {% elif leaderboard.time_period == 'weekly' %}
                                    {% trans "Weekly" %}
                                    {% elif leaderboard.time_period == 'daily' %}
                                    {% trans "Daily" %}
                                    {% else %}
                                    {{ leaderboard.time_period }}
                                    {% endif %}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <th>{% trans "Created" %}</th>
                                <td>{{ leaderboard.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Last Updated" %}</th>
                                <td>{{ leaderboard.updated_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "Leaderboard Statistics" %}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card border-left-primary h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                {% trans "Total Entries" %}</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ entry_count }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-list-ol fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-left-success h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                {% trans "Average Score" %}</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {% if avg_score %}
                                                {{ avg_score|floatformat:1 }}
                                                {% else %}
                                                0
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-calculator fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-left-info h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                {% trans "Highest Score" %}</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                {% if max_score %}
                                                {{ max_score }}
                                                {% else %}
                                                0
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-trophy fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if top_roles %}
    <!-- Top Roles Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Top Roles" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>{% trans "Role" %}</th>
                            <th>{% trans "Count" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for role in top_roles %}
                        <tr>
                            <td>{{ role.highest_role|default:"Not specified"|title }}</td>
                            <td>{{ role.count }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="2" class="text-center">{% trans "No roles found." %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}

    {% if company and other_time_periods %}
    <!-- Other Time Periods Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Other Time Periods for" %} {{ company.name }}</h6>
        </div>
        <div class="card-body">
            <div class="d-flex flex-wrap gap-3 mb-3">
                {% for period in other_time_periods %}
                <a href="{% url 'superadmin:leaderboard_detail' period.id %}" class="btn btn-outline-primary">
                    {{ period.display_name }}
                    <span class="badge bg-secondary ms-1">{{ period.entry_count }}</span>
                </a>
                {% endfor %}
            </div>
            <div class="alert alert-info mb-0">
                <i class="bi bi-info-circle me-2"></i>
                {% trans "Click on a time period to view its leaderboard." %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Leaderboard Entries Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Leaderboard Entries" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th style="width: 5%">{% trans "Rank" %}</th>
                            <th style="width: 20%">{% trans "User" %}</th>
                            <th style="width: 15%">{% trans "Score" %}</th>
                            <th style="width: 20%">{% trans "Highest Role" %}</th>
                            <th style="width: 15%">{% trans "Date" %}</th>
                            <th style="width: 15%">{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entry in entries %}
                        <tr>
                            <td>{{ entry.rank }}</td>
                            <td>{{ entry.user.username }}</td>
                            <td>{{ entry.score }}</td>
                            <td>{{ entry.highest_role|default:"Not specified"|title }}</td>
                            <td>{{ entry.recorded_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                <a href="{% url 'superadmin:user_detail' entry.user.id %}" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> {% trans "View User" %}
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "No entries found." %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if entries.paginator.num_pages > 1 %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    {% if entries.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ entries.previous_page_number }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in entries.paginator.page_range %}
                    {% if entries.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > entries.number|add:'-3' and num < entries.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if entries.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ entries.next_page_number }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ entries.paginator.num_pages }}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock superadmin_content %}