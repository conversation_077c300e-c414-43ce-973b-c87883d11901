{% extends 'superadmin/base_superadmin.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block superadmin_content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0" style="color: var(--text-color-bright); font-weight: 600; letter-spacing: 0.5px;">{{ title }}</h1>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="stat-card" style="border-left: 4px solid var(--accent-color-blue);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-label">{% trans "Total Sessions" %}</div>
                        <div class="stat-value">{{ total_sessions }}</div>
                    </div>
                    <div class="stat-icon" style="color: var(--accent-color-blue);">
                        <i class="bi bi-controller"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="stat-card" style="border-left: 4px solid var(--accent-color-green);">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-label">{% trans "Active Sessions" %}</div>
                        <div class="stat-value">{{ active_sessions }}</div>
                    </div>
                    <div class="stat-icon" style="color: var(--accent-color-green);">
                        <i class="bi bi-play-circle"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="stat-card" style="border-left: 4px solid #06b6d4;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <div class="stat-label">{% trans "Completed Sessions" %}</div>
                        <div class="stat-value">{{ completed_sessions }}</div>
                    </div>
                    <div class="stat-icon" style="color: #06b6d4;">
                        <i class="bi bi-check-circle"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold" style="color: var(--accent-color-blue);">{% trans "Filter Game Sessions" %}</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="company" class="form-label">{% trans "Company" %}</label>
                    <select name="company" id="company" class="form-select">
                        <option value="">{% trans "All Companies" %}</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}" {% if filter_company == company.id|stringformat:"i" %}selected{% endif %}>
                            {{ company.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="status" class="form-label">{% trans "Status" %}</label>
                    <select name="status" id="status" class="form-select">
                        <option value="">{% trans "All Statuses" %}</option>
                        <option value="active" {% if filter_status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                        <option value="completed" {% if filter_status == 'completed' %}selected{% endif %}>{% trans "Completed" %}</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="q" class="form-label">{% trans "Search" %}</label>
                    <input type="text" class="form-control" id="q" name="q" placeholder="{% trans 'Search by user or company' %}" value="{{ filter_q }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">{% trans "Filter" %}</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Game Sessions Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold" style="color: var(--accent-color-blue);">{% trans "Game Sessions" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "ID" %}</th>
                            <th>{% trans "User" %}</th>
                            <th>{% trans "Company" %}</th>
                            <th>{% trans "Status" %}</th>
                            <th>{% trans "Created" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for session in game_sessions %}
                        <tr>
                            <td>{{ session.id }}</td>
                            <td>{{ session.user.username }}</td>
                            <td>{{ session.company.name }}</td>
                            <td>
                                {% if session.game_completed %}
                                <span class="badge bg-info">{% trans "Completed" %}</span>
                                {% else %}
                                <span class="badge bg-success">{% trans "Active" %}</span>
                                {% endif %}
                            </td>
                            <td>{{ session.created_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                <a href="{% url 'superadmin:game_session_detail' session.id %}" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> {% trans "View" %}
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">{% trans "No game sessions found." %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock superadmin_content %}