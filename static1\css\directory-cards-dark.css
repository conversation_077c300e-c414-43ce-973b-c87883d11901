/**
 * Directory Cards Dark Mode CSS
 * Specific dark mode styling for directory cards
 */

/* Directory cards container */
[data-theme="dark"] .row {
  background-color: transparent !important;
}

/* Directory cards */
[data-theme="dark"] .card {
  background: linear-gradient(145deg, #1e1e1e, #252525) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
  transition: all 0.3s ease !important;
  border-radius: 10px !important;
  overflow: hidden !important;
}

[data-theme="dark"] .card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* Card body */
[data-theme="dark"] .card-body {
  background-color: transparent !important;
  padding: 1.25rem !important;
}

/* Card title */
[data-theme="dark"] .card-title {
  color: #ffffff !important;
  font-weight: 600 !important;
  margin-bottom: 0.75rem !important;
  font-size: 1.25rem !important;
}

/* Card text */
[data-theme="dark"] .card-text {
  color: #cccccc !important;
  margin-bottom: 1rem !important;
}

/* Card image */
[data-theme="dark"] .card-img-top {
  background-color: #1a1a1a !important;
  border-bottom: 1px solid #333333 !important;
  padding: 1rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 150px !important;
}

[data-theme="dark"] .card-img-top img {
  max-height: 100% !important;
  max-width: 100% !important;
  object-fit: contain !important;
}

/* Card footer */
[data-theme="dark"] .card-footer {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border-top: 1px solid #333333 !important;
  padding: 0.75rem 1.25rem !important;
}

/* Card badges */
[data-theme="dark"] .card .badge {
  background-color: #333333 !important;
  color: #ffffff !important;
  padding: 0.5em 0.75em !important;
  font-weight: 500 !important;
  border-radius: 20px !important;
  margin-right: 0.5rem !important;
}

[data-theme="dark"] .card .badge.bg-primary {
  background-color: #0077ff !important;
  color: #ffffff !important;
}

/* Card links */
[data-theme="dark"] .card a:not(.btn) {
  color: #0088ff !important;
  text-decoration: none !important;
  transition: color 0.2s ease !important;
}

[data-theme="dark"] .card a:not(.btn):hover {
  color: #00aaff !important;
  text-decoration: underline !important;
}

/* Card buttons */
[data-theme="dark"] .card .btn-primary {
  background: linear-gradient(to bottom, #0077ff, #0055cc) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 10px rgba(0, 102, 255, 0.3) !important;
  border-radius: 6px !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .card .btn-primary:hover {
  background: linear-gradient(to bottom, #0088ff, #0066dd) !important;
  box-shadow: 0 6px 15px rgba(0, 102, 255, 0.4) !important;
  transform: translateY(-2px) !important;
}

/* Card icons */
[data-theme="dark"] .card .bi {
  color: inherit !important;
  margin-right: 0.25rem !important;
}

/* Empty state card */
[data-theme="dark"] .card.empty-state {
  background: linear-gradient(145deg, #1a1a1a, #151515) !important;
  border: 1px dashed #333333 !important;
  color: #999999 !important;
  text-align: center !important;
  padding: 2rem !important;
}

/* Card with accent */
[data-theme="dark"] .card.card-accent {
  border-top: 4px solid #0077ff !important;
}

/* Featured card */
[data-theme="dark"] .card.featured {
  border: 1px solid #0077ff !important;
  box-shadow: 0 8px 20px rgba(0, 119, 255, 0.2), 0 0 0 1px rgba(0, 119, 255, 0.1) !important;
}

/* Featured badge */
[data-theme="dark"] .featured-badge {
  position: absolute !important;
  top: 10px !important;
  right: 10px !important;
  background: linear-gradient(to bottom, #0077ff, #0055cc) !important;
  color: white !important;
  padding: 0.25rem 0.75rem !important;
  border-radius: 20px !important;
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
  z-index: 1 !important;
}
