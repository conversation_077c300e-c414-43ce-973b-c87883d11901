<?xml version="1.0" encoding="UTF-8"?>
<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
    <rect width="800" height="600" fill="#f8f9fa"/>
    <pattern id="grid" x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
        <rect width="40" height="40" fill="none" stroke="#e9ecef" stroke-width="1"/>
    </pattern>
    <rect width="800" height="600" fill="url(#grid)"/>
    <circle cx="200" cy="150" r="80" fill="#007bff" fill-opacity="0.1"/>
    <circle cx="600" cy="450" r="100" fill="#007bff" fill-opacity="0.1"/>
    <circle cx="400" cy="300" r="60" fill="#007bff" fill-opacity="0.1"/>
    <g fill="#007bff">
        <circle cx="160" cy="120" r="3"/>
        <circle cx="200" cy="120" r="3"/>
        <circle cx="240" cy="120" r="3"/>
        <circle cx="160" cy="160" r="3"/>
        <circle cx="200" cy="160" r="3"/>
        <circle cx="240" cy="160" r="3"/>
        <circle cx="160" cy="200" r="3"/>
        <circle cx="200" cy="200" r="3"/>
        <circle cx="240" cy="200" r="3"/>
        <circle cx="560" cy="420" r="3"/>
        <circle cx="600" cy="420" r="3"/>
        <circle cx="640" cy="420" r="3"/>
        <circle cx="560" cy="460" r="3"/>
        <circle cx="600" cy="460" r="3"/>
        <circle cx="640" cy="460" r="3"/>
        <circle cx="560" cy="500" r="3"/>
        <circle cx="600" cy="500" r="3"/>
        <circle cx="640" cy="500" r="3"/>
        <circle cx="360" cy="280" r="3"/>
        <circle cx="400" cy="280" r="3"/>
        <circle cx="440" cy="280" r="3"/>
        <circle cx="360" cy="320" r="3"/>
        <circle cx="400" cy="320" r="3"/>
        <circle cx="440" cy="320" r="3"/>
        <circle cx="360" cy="360" r="3"/>
        <circle cx="400" cy="360" r="3"/>
        <circle cx="440" cy="360" r="3"/>
    </g>
</svg>
