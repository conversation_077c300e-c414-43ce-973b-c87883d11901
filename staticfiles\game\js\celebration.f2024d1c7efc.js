// Celebration effects for promotions and task completions

// Create confetti effect
function createConfetti(amount = 100, isFront = false) {
    // Create back confetti (behind overlay)
    const confettiContainer = document.createElement('div');
    confettiContainer.className = 'confetti-container';
    document.body.appendChild(confettiContainer);

    // Create front confetti (in front of overlay) if requested
    let frontConfettiContainer = null;
    if (isFront) {
        frontConfettiContainer = document.createElement('div');
        frontConfettiContainer.className = 'confetti-container-front';
        document.body.appendChild(frontConfettiContainer);
    }

    // Create confetti pieces
    const colors = ['#f44336', '#e91e63', '#9c27b0', '#673ab7', '#3f51b5', '#2196f3', '#03a9f4', '#00bcd4', '#009688', '#4CAF50', '#8BC34A', '#CDDC39', '#FFEB3B', '#FFC107', '#FF9800', '#FF5722'];

    // Function to create a single confetti piece
    function createConfettiPiece(container, isFront = false) {
        const confetti = document.createElement('div');
        confetti.className = 'confetti';

        // Random color with slight transparency for front confetti
        const colorIndex = Math.floor(Math.random() * colors.length);
        const color = colors[colorIndex];

        // Add slight transparency to some confetti for a more layered effect
        if (isFront && Math.random() > 0.7) {
            // Convert hex to rgba with transparency for some pieces
            const r = parseInt(color.slice(1, 3), 16);
            const g = parseInt(color.slice(3, 5), 16);
            const b = parseInt(color.slice(5, 7), 16);
            const alpha = Math.random() * 0.3 + 0.7; // 0.7-1.0 transparency
            confetti.style.backgroundColor = `rgba(${r}, ${g}, ${b}, ${alpha})`;
        } else {
            confetti.style.backgroundColor = color;
        }

        // Random position
        confetti.style.left = Math.random() * 100 + 'vw';

        // Random animation timing
        confetti.style.animationDelay = Math.random() * 5 + 's';
        confetti.style.animationDuration = Math.random() * 3 + 2 + 's';

        // Randomize size for more variety
        const size = Math.random() * 8 + 6; // 6-14px
        confetti.style.width = `${size}px`;
        confetti.style.height = `${size}px`;

        // Add some rotation for more dynamic effect
        confetti.style.transform = `rotate(${Math.random() * 360}deg)`;

        // Add different shapes occasionally
        const shapeRandom = Math.random();
        if (shapeRandom > 0.8) {
            confetti.style.borderRadius = '50%'; // Circle
        } else if (shapeRandom > 0.6) {
            confetti.style.clipPath = 'polygon(50% 0%, 0% 100%, 100% 100%)'; // Triangle
        } else if (shapeRandom > 0.4) {
            confetti.style.width = `${size * 0.8}px`;
            confetti.style.height = `${size * 1.5}px`; // Rectangle
        }

        container.appendChild(confetti);
    }

    // Create back confetti
    for (let i = 0; i < amount; i++) {
        createConfettiPiece(confettiContainer);
    }

    // Create front confetti if requested (fewer pieces to not obstruct view)
    if (frontConfettiContainer) {
        // Create fewer confetti pieces in front to not obstruct the view too much
        const frontAmount = Math.floor(amount * 0.4); // 40% of the total amount
        for (let i = 0; i < frontAmount; i++) {
            createConfettiPiece(frontConfettiContainer, true); // Pass true for isFront parameter
        }
    }

    // Remove confetti after animation
    setTimeout(() => {
        confettiContainer.remove();
        if (frontConfettiContainer) {
            frontConfettiContainer.remove();
        }
    }, 8000);
}

// Create layered confetti effect for celebrations
function createLayeredConfetti(amount = 100) {
    // Create both back and front confetti
    createConfetti(amount, true);
}

// Show task completion celebration
function showTaskCompletionCelebration(taskName) {
    console.log('Showing task completion celebration for:', taskName);

    // Create celebration overlay
    const celebrationOverlay = document.createElement('div');
    celebrationOverlay.className = 'celebration-overlay task-completion';

    // Create celebration content
    const celebrationContent = document.createElement('div');
    celebrationContent.className = 'celebration-content task-completion';

    // Add completion message
    const completionMessage = document.createElement('h2');
    completionMessage.textContent = '✅ TASK COMPLETED! ✅';
    completionMessage.style.fontSize = '32px';
    completionMessage.style.color = '#2e7d32';
    completionMessage.style.textTransform = 'uppercase';
    completionMessage.style.fontWeight = 'bold';
    completionMessage.style.textShadow = '1px 1px 3px rgba(0,0,0,0.1)';
    completionMessage.style.marginBottom = '15px';

    const completionDetails = document.createElement('p');
    const formattedTaskName = taskName ? taskName.replace(/_/g, ' ') : 'this task';
    completionDetails.textContent = `You've successfully completed ${formattedTaskName}!`;
    completionDetails.style.fontSize = '20px';
    completionDetails.style.fontWeight = 'bold';
    completionDetails.style.color = '#388e3c';
    completionDetails.style.marginBottom = '15px';

    // Add task continuation message
    const continuationMessage = document.createElement('p');
    continuationMessage.className = 'continuation-message';
    continuationMessage.textContent = 'Continue working on your current role to advance in your career.';
    continuationMessage.style.fontSize = '18px';
    continuationMessage.style.padding = '12px';
    continuationMessage.style.marginBottom = '20px';

    // Next task button removed - tasks now progress automatically

    // Add close button as secondary option
    const closeButton = document.createElement('button');
    closeButton.className = 'celebration-close-button';
    closeButton.textContent = 'Close';
    closeButton.style.padding = '8px 16px';
    closeButton.style.fontSize = '14px';
    closeButton.style.backgroundColor = '#888';
    closeButton.style.color = 'white';
    closeButton.style.border = 'none';
    closeButton.style.borderRadius = '4px';
    closeButton.style.cursor = 'pointer';

    // Add elements to DOM
    celebrationContent.appendChild(completionMessage);
    celebrationContent.appendChild(completionDetails);
    celebrationContent.appendChild(continuationMessage);
    // nextTaskButton removed - tasks now progress automatically
    celebrationContent.appendChild(closeButton);
    celebrationOverlay.appendChild(celebrationContent);
    document.body.appendChild(celebrationOverlay);

    // Create a smaller amount of layered confetti for task completion
    createLayeredConfetti(50);

    // Next task button event removed - tasks now progress automatically
    // Instead, automatically fetch the next task after a short delay
    setTimeout(() => {
        console.log('Automatically fetching next task after celebration');
        if (typeof fetchNextTask === 'function') {
            try {
                fetchNextTask();
            } catch (error) {
                console.error('Error auto-fetching next task:', error);
            }
        }
    }, 1000); // 1 second delay

    // Add close event
    closeButton.addEventListener('click', () => {
        celebrationOverlay.remove();
    });

    // Auto-close after 8 seconds
    setTimeout(() => {
        if (document.body.contains(celebrationOverlay)) {
            celebrationOverlay.remove();
        }
    }, 8000);
}

// Show promotion celebration
function showPromotionCelebration(newRole) {
    console.log('Showing PROMOTION celebration for new role:', newRole);

    // Create celebration overlay
    const celebrationOverlay = document.createElement('div');
    celebrationOverlay.className = 'celebration-overlay promotion';

    // Create celebration content
    const celebrationContent = document.createElement('div');
    celebrationContent.className = 'celebration-content promotion';

    // Add promotion message
    const promotionMessage = document.createElement('h2');
    promotionMessage.textContent = '🎉 PROMOTION ACHIEVED! 🎉';
    promotionMessage.style.fontSize = '36px';
    promotionMessage.style.color = '#2a56b8';
    promotionMessage.style.textTransform = 'uppercase';
    promotionMessage.style.fontWeight = 'bold';
    promotionMessage.style.textShadow = '2px 2px 4px rgba(0,0,0,0.2)';
    promotionMessage.style.marginBottom = '20px';

    const promotionDetails = document.createElement('p');
    const formattedRole = newRole.replace(/_/g, ' ').toUpperCase();
    promotionDetails.textContent = `You've been promoted to ${formattedRole}!`;
    promotionDetails.style.fontSize = '24px';
    promotionDetails.style.fontWeight = 'bold';
    promotionDetails.style.color = '#1a237e';
    promotionDetails.style.marginBottom = '20px';

    // Add additional promotion text
    const promotionExplanation = document.createElement('p');
    promotionExplanation.className = 'promotion-explanation';
    promotionExplanation.textContent = 'You have completed all required tasks for your previous role and have advanced in your career!';
    promotionExplanation.style.fontSize = '18px';
    promotionExplanation.style.padding = '15px';

    // Add career advancement text
    const careerAdvancement = document.createElement('p');
    careerAdvancement.className = 'career-advancement';
    careerAdvancement.textContent = 'This is a significant milestone in your journey up the corporate ladder.';
    careerAdvancement.style.fontSize = '18px';
    careerAdvancement.style.padding = '15px';

    // Add close button
    const closeButton = document.createElement('button');
    closeButton.className = 'celebration-close-button promotion-button';
    closeButton.textContent = 'Continue to New Role';

    // Add elements to DOM
    celebrationContent.appendChild(promotionMessage);
    celebrationContent.appendChild(promotionDetails);
    celebrationContent.appendChild(promotionExplanation);
    celebrationContent.appendChild(careerAdvancement);
    celebrationContent.appendChild(closeButton);
    celebrationOverlay.appendChild(celebrationContent);
    document.body.appendChild(celebrationOverlay);

    // Create layered confetti - more for a promotion!
    createLayeredConfetti(200);

    // Play celebration sound
    const celebrationSound = new Audio('https://freesound.org/data/previews/270/270402_5123827-lq.mp3');
    try {
        celebrationSound.volume = 0.5;
        celebrationSound.play().catch(e => console.log('Could not play celebration sound'));
    } catch (e) {
        console.log('Could not play celebration sound');
    }

    // Add close event
    closeButton.addEventListener('click', () => {
        celebrationOverlay.remove();
    });

    // Auto-close after 6 seconds
    setTimeout(() => {
        if (document.body.contains(celebrationOverlay)) {
            celebrationOverlay.remove();
        }
    }, 6000);
}
