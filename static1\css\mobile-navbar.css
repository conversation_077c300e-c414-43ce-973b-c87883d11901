/**
 * Mobile Navbar Improvements
 * Enhanced mobile navigation experience
 */

/* Base navbar improvements */
.navbar {
  padding: 0.5rem 1rem !important;
}

/* Improve mobile navbar */
@media (max-width: 991.98px) {
  /* Enhance navbar collapse */
  .navbar-collapse {
    position: fixed !important;
    top: 56px !important; /* Height of navbar */
    left: 0 !important;
    width: 100% !important;
    height: calc(100vh - 56px) !important;
    background-color: #ffffff !important;
    z-index: 1050 !important;
    overflow-y: auto !important;
    transition: transform 0.3s ease-in-out !important;
    transform: translateX(-100%) !important;
    padding: 1rem !important;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15) !important;
  }

  /* Show navbar when expanded */
  .navbar-collapse.show {
    transform: translateX(0) !important;
  }

  /* Improve navbar toggler */
  .navbar-toggler {
    border: none !important;
    padding: 0.5rem !important;
    margin-right: 0.5rem !important;
    border-radius: 0.25rem !important;
    transition: background-color 0.2s ease !important;
  }

  .navbar-toggler:focus {
    box-shadow: none !important;
    outline: none !important;
  }

  /* Enhance nav links for better touch targets */
  .navbar .nav-link {
    padding: 0.75rem 1rem !important;
    border-radius: 0.375rem !important;
    margin-bottom: 0.25rem !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
  }

  .navbar .nav-link:hover {
    background-color: rgba(13, 110, 253, 0.05) !important;
  }

  /* Improve dropdown menus - Modified to work with navbar-dropdown-fix.css */
  .navbar-collapse .dropdown-menu {
    border: none !important;
    background-color: rgba(0, 0, 0, 0.02) !important;
    padding: 0 !important;
    margin: 0 0 0.5rem 0 !important;
    box-shadow: none !important;
    position: static !important;
    float: none !important;
    width: 100% !important;
    border-radius: 0.375rem !important;
    transform: none !important;
    opacity: 1 !important;
    visibility: visible !important;
    display: none !important;
    transition: none !important;
  }

  .navbar-collapse .dropdown-menu.show {
    display: block !important;
  }

  .navbar .dropdown-item {
    padding: 0.75rem 1.5rem !important;
    border-radius: 0.375rem !important;
    font-size: 0.95rem !important;
  }

  .navbar .dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.05) !important;
  }

  /* Improve navbar brand */
  .navbar-brand {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
  }

  /* Add dividers between nav items */
  .navbar .nav-item:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
  }
}

/* Dark mode navbar improvements */
@media (max-width: 991.98px) {
  [data-theme="dark"] .navbar-collapse {
    background-color: #1a1a1a !important;
    border-right: 1px solid #333333 !important;
  }

  [data-theme="dark"] .navbar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  [data-theme="dark"] .navbar-collapse .dropdown-menu {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }

  [data-theme="dark"] .navbar .dropdown-item:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
  }

  [data-theme="dark"] .navbar .nav-item:not(:last-child) {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
  }
}

/* Improve navbar on very small screens */
@media (max-width: 576px) {
  .navbar {
    padding: 0.5rem !important;
  }

  .navbar-brand {
    font-size: 1.1rem !important;
    max-width: 200px !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
  }

  .navbar-toggler {
    padding: 0.4rem !important;
  }
}
