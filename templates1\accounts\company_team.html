{% extends 'base/layout.html' %}
{% load static %}
{% load permission_tags %} {# Load the new permission tags #}
{% load account_tags %} {# Re-enabled account_tags #}

{% block title %}Team Management - {{ company.name }} - 24seven{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Team Management</h1>
            <p class="text-muted mb-0">
                Manage team members and invitations for {{ company.name }}
            </p>
        </div>
        {% if can_manage_links_invites %} {# Use permission variable from view #}
            <div class="col-auto">
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#inviteModal">
                    <i class="bi bi-person-plus me-2"></i>
                    Invite Members
                </button>
            </div>
        {% endif %}
    </div>

    <!-- Tab Navigation -->
    <ul class="nav nav-tabs mb-4" id="teamTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="members-tab" data-bs-toggle="tab" data-bs-target="#members-tab-pane" type="button" role="tab" aria-controls="members-tab-pane" aria-selected="true">
                <i class="bi bi-people me-1"></i> Team Members
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="invitations-tab" data-bs-toggle="tab" data-bs-target="#invitations-tab-pane" type="button" role="tab" aria-controls="invitations-tab-pane" aria-selected="false">
                <i class="bi bi-envelope-paper me-1"></i> Pending Invitations
                {% if pending_invitations.count > 0 %}
                    <span class="badge rounded-pill bg-warning text-dark ms-1">{{ pending_invitations.count }}</span>
                {% endif %}
            </button>
        </li>
        {% if can_manage_links_invites %} {# Only show tab if user can manage links #}
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="links-tab" data-bs-toggle="tab" data-bs-target="#links-tab-pane" type="button" role="tab" aria-controls="links-tab-pane" aria-selected="false">
                <i class="bi bi-link-45deg me-1"></i> Registration Links
            </button>
        </li>
        {% endif %}
    </ul>

    <!-- Tab Content -->
    <div class="tab-content" id="teamTabsContent">

        <!-- Team Members Tab Pane -->
        <div class="tab-pane fade show active" id="members-tab-pane" role="tabpanel" aria-labelledby="members-tab" tabindex="0">
            <!-- Filter Form -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <form method="get" action="{% url 'accounts:company_team' company.id %}">
                        <div class="row g-2 align-items-end">
                            <div class="col-md">
                                <label for="id_q" class="form-label small">Search Name/Email</label>
                                <input type="text" name="q" id="id_q" class="form-control form-control-sm" value="{{ search_query|default:'' }}" placeholder="Search...">
                            </div>
                            <div class="col-md">
                                <label for="id_group" class="form-label small">Filter by Group</label>
                                <select name="group" id="id_group" class="form-select form-select-sm">
                                    <option value="">All Groups</option>
                                    {% for group in available_groups %}
                                        <option value="{{ group.name }}" {% if group_filter == group.name %}selected{% endif %}>{{ group.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-auto">
                                <button type="submit" class="btn btn-secondary btn-sm">
                                    <i class="bi bi-search me-1"></i> Filter
                                </button>
                                {# Add Reset button linking to the same page without query params #}
                                <a href="{% url 'accounts:company_team' company.id %}" class="btn btn-outline-secondary btn-sm">
                                    <i class="bi bi-arrow-clockwise me-1"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Active Members Card -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent d-flex justify-content-between align-items-center"> {# Added flex for alignment #}
                    <h5 class="mb-0">Active Members</h5>
                    {# Display count of filtered members #}
                    <span class="badge bg-light text-dark">{{ memberships.count }} Member{{ memberships.count|pluralize }} Found</span>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {# Display Owner First (if exists and no filters applied) #}
                        {% if owner and not search_query and not group_filter %}
                        <div class="list-group-item">
                            <div class="d-flex align-items-center">
                                <!-- Avatar -->
                                <div class="flex-shrink-0">
                                    {% if owner.email %}
                                        <img src="{{ owner.email|gravatar_url:48 }}"
                                             class="rounded-circle"
                                             width="48" height="48"
                                             alt="{{ owner.get_full_name|default:owner.username }}">
                                    {% else %}
                                        <div class="bg-light rounded-circle d-flex align-items-center justify-content-center"
                                             style="width: 48px; height: 48px;">
                                            <i class="bi bi-person text-muted"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                <!-- Owner Info -->
                                <div class="flex-grow-1 ms-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-0">{{ owner.get_full_name|default:owner.username }}</h6>
                                            <p class="text-muted small mb-0">
                                                {{ owner.email }}
                                                <span class="badge bg-dark ms-2">Owner</span>
                                            </p>
                                        </div>
                                        {# No actions dropdown for the owner #}
                                    </div>
                                </div>
                            </div>
                             <!-- Last Active -->
                             <div class="mt-2 small text-muted">
                                <i class="bi bi-clock me-1"></i>
                                {% if owner.last_login %}
                                    Last active: {{ owner.last_login|timesince }} ago
                                {% else %}
                                    Last active: Never
                                {% endif %}
                            </div>
                        </div>
                        {% endif %} {# End owner display #}

                        {# Display Other Members (using filtered memberships) #}
                        {% for membership in memberships %} {# Iterate over filtered memberships #}
                            <div class="list-group-item">
                                <div class="d-flex align-items-center">
                                    <!-- Avatar -->
                                    <div class="flex-shrink-0">
                                        {% if membership.user.email %}
                                            <img src="{{ membership.user.email|gravatar_url:48 }}"
                                                 class="rounded-circle"
                                                 width="48" height="48"
                                                 alt="{{ membership.user.get_full_name|default:membership.user.username }}">
                                        {% else %}
                                            <div class="bg-light rounded-circle d-flex align-items-center justify-content-center"
                                                 style="width: 48px; height: 48px;">
                                                <i class="bi bi-person text-muted"></i>
                                            </div>
                                        {% endif %}
                                    </div>

                                    <!-- Member Info -->
                                    <div class="flex-grow-1 ms-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <h6 class="mb-0">{{ membership.user.get_full_name|default:membership.user.username }}</h6>
                                                <p class="text-muted small mb-0">
                                                    {{ membership.user.email }}
                                                    {# Use the company_role and role_badge filters - Moved here #}
                                                    {% with user_role=membership.user|company_role:company %}
                                                        {% if user_role and user_role != 'owner' %} {# Don't show badge for owner here #}
                                                            <span class="ms-2"> {# Add margin start for spacing #}
                                                                {{ user_role|role_badge }}
                                                            </span>
                                                        {% endif %}
                                                    {% endwith %}
                                                </p>
                                            </div>
                                            {# Badge rendering moved inside the <p> tag above #}
                                        </div>
                                    </div>

                                    <!-- Actions -->
                                    {% if can_manage_team and membership.user != company.owner and membership.user != request.user %}
                                        <div class="flex-shrink-0 ms-3">
                                            <div class="dropdown">
                                                <button class="btn btn-light btn-sm" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="bi bi-three-dots-vertical"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li><a class="dropdown-item" href="#">Change Role</a></li> {# Placeholder #}
                                                    <li><a class="dropdown-item" href="#">Manage Folder Access</a></li> {# Placeholder #}
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <form method="post"
                                                              action="{% url 'accounts:remove_member' company.id membership.user.id %}" {# Corrected to use user.id #}
                                                              onsubmit="return confirm('Are you sure you want to remove {{ membership.user.username }}?');">
                                                            {% csrf_token %}
                                                            <button type="submit" class="dropdown-item text-danger">
                                                                <i class="bi bi-person-x me-2"></i>
                                                                Remove Member
                                                            </button>
                                                        </form>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    {% endif %}
                                </div>

                                <!-- Last Active -->
                                <div class="mt-2 small text-muted">
                                    <i class="bi bi-clock me-1"></i>
                                    {% if membership.user.last_login %}
                                        Last active: {{ membership.user.last_login|timesince }} ago
                                    {% else %}
                                        Last active: Never
                                    {% endif %}
                                </div>
                            </div>
                        {% empty %} {# Use {% empty %} tag for the loop #}
                            {# Display appropriate message based on whether filters are active #}
                            <div class="list-group-item text-center py-4">
                                <div class="text-muted">
                                    {% if search_query or group_filter %}
                                        <i class="bi bi-search h3 mb-2"></i>
                                        <p>No members found matching your criteria.</p>
                                    {% elif not owner %} {# Only show "No team members yet" if owner is also missing AND no filters active #}
                                        <i class="bi bi-people h3 mb-2"></i>
                                        <p>No team members yet.</p>
                                    {% endif %}
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div><!-- End Team Members Tab Pane -->

        <!-- Pending Invitations Tab Pane -->
        <div class="tab-pane fade" id="invitations-tab-pane" role="tabpanel" aria-labelledby="invitations-tab" tabindex="0">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent">
                    <h5 class="mb-0">Pending Invitations</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for invite in pending_invitations %}
                            <div class="list-group-item">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div>
                                        <h6 class="mb-1">{{ invite.email }}</h6> {# Show full email for admin #}
                                        <p class="small text-muted mb-0">
                                            Invited {{ invite.invited_at|timesince }} ago
                                            {% if invite.role %} as {{ invite.role.name }} {% endif %}
                                        </p>
                                    </div>
                                    {% if can_manage_links_invites %} {# Use permission variable #}
                                        <div class="btn-group btn-group-sm">
                                            <button type="button"
                                                    class="btn btn-outline-secondary" {# Use outline buttons #}
                                                    onclick="resendInvitation('{% url 'accounts:resend_invitation' company.id invite.id %}')">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-outline-danger" {# Use outline buttons #}
                                                    onclick="cancelInvitation('{% url 'accounts:cancel_invitation' company.id invite.id %}')">
                                                <i class="bi bi-trash"></i> {# Use trash icon #}
                                            </button>
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="progress mt-2" style="height: 4px;">
                                    {% with expires_in=invite.expires_at|timeuntil %}
                                        <div class="progress-bar bg-warning" style="width: 50%;"
                                             title="Expires in {{ expires_in }}">
                                        </div>
                                    {% endwith %}
                                </div>
                            </div>
                        {% empty %}
                            <div class="list-group-item text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-envelope h3 mb-2"></i>
                                    <p>No pending invitations</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div><!-- End Pending Invitations Tab Pane -->

        <!-- Registration Links Tab Pane -->
        {% if can_manage_links_invites %} {# Check permission for the whole tab content #}
        <div class="tab-pane fade" id="links-tab-pane" role="tabpanel" aria-labelledby="links-tab" tabindex="0">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Registration Links</h5>
                    {# Changed button to toggle collapse #}
                    <button type="button" class="btn btn-success btn-sm" data-bs-toggle="collapse" data-bs-target="#createRegLinkFormCollapse" aria-expanded="false" aria-controls="createRegLinkFormCollapse">
                        <i class="bi bi-link-45deg me-2"></i> Create New Link
                    </button>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for link in registration_links %}
                            {# Add a CSS class based on the role name (slugified) #}
                            <div class="list-group-item reg-link-item {% if link.intended_group %}role-{{ link.intended_group.name|slugify }}{% else %}role-none{% endif %}">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <code class="d-block mb-1">{{ link.get_absolute_url }}</code>
                                        <p class="small text-muted mb-0">
                                            {# Display intended group name if it exists #}
                                            Role: {% if link.intended_group %}<span class="fw-bold">{{ link.intended_group.name }}</span>{% else %}<span class="text-warning">None Assigned</span>{% endif %} |
                                            Created by: {{ link.created_by.username|default:'System' }} {{ link.created_at|timesince }} ago |
                                            Uses: {{ link.uses_count }}{% if link.max_uses %}/{{ link.max_uses }}{% endif %} |
                                            Status: {% if link.is_valid %}<span class="text-success">Active</span>{% else %}<span class="text-danger">Inactive/Expired</span>{% endif %}
                                            {% if link.expires_at %}| Expires: {{ link.expires_at|date:"SHORT_DATETIME_FORMAT" }}{% endif %}
                                            {% if link.notes %}| Notes: {{ link.notes }}{% endif %}
                                        </p>
                                    </div>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard('{{ request.scheme }}://{{ request.get_host }}{{ link.get_absolute_url }}')">
                                            <i class="bi bi-clipboard"></i> Copy
                                        </button>
                                        {# QR Code Collapse Button - Ensure it exists #}
                                        {% if link.qr_code %}
                                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                                data-bs-toggle="collapse"
                                                data-bs-target="#collapse-qr-{{ link.token }}"
                                                aria-expanded="false"
                                                aria-controls="collapse-qr-{{ link.token }}">
                                            <i class="bi bi-qr-code"></i> QR
                                        </button>
                                        {% endif %}
                                        {# Deactivate Button Form #}
                                        <form method="post" action="{% url 'accounts:deactivate_reg_link' link.token %}" class="d-inline" onsubmit="return confirm('Are you sure you want to deactivate this link?');">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-outline-warning btn-sm" {% if not link.is_active %}disabled{% endif %}>
                                                Deactivate
                                            </button>
                                        </form>
                                        {# Delete Button Form #}
                                        <form method="post" action="{% url 'accounts:delete_reg_link' link.token %}" class="d-inline" onsubmit="return confirm('Are you sure you want to permanently delete this link?');">
                                            {% csrf_token %}
                                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                                Delete
                                            </button>
                                        </form>
                                    </div>
                                </div>
                                {# Collapsible QR Code Display - Ensure it exists #}
                                {% if link.qr_code %}
                                <div class="collapse mt-2" id="collapse-qr-{{ link.token }}">
                                    <div class="p-2 border rounded d-inline-block bg-light">
                                        <img src="{{ link.qr_code.url }}" alt="QR Code for link {{ link.token }}" class="img-fluid" style="max-width: 120px;">
                                        <p class="small text-muted mt-1 mb-0">Scan to join</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        {% empty %}
                            <div class="list-group-item text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-link-45deg h3 mb-2"></i>
                                    <p>No registration links created yet.</p>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                    {# Added Collapse Section for the form #}
                    <div class="collapse mt-3 p-3" id="createRegLinkFormCollapse">
                        <div class="card card-body">
                            <h6 class="mb-3">Create New Registration Link</h6>
                            {# Moved form here from the modal #}
                            <form method="post" action="{% url 'accounts:company_team' company.id %}">
                                {% csrf_token %}
                                <input type="hidden" name="submit_reg_link_form" value="1">
                                {% if reg_link_form.errors %}
                                    <div class="alert alert-danger">
                                        Please correct the errors below.
                                    </div>
                                {% endif %}
                                {# Improved Form Layout - Render intended_group field #}
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ reg_link_form.intended_group.id_for_label }}" class="form-label">{{ reg_link_form.intended_group.label }}</label>
                                        {{ reg_link_form.intended_group }}
                                        {% if reg_link_form.intended_group.help_text %}<div class="form-text">{{ reg_link_form.intended_group.help_text }}</div>{% endif %}
                                        {% for error in reg_link_form.intended_group.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ reg_link_form.notes.id_for_label }}" class="form-label">{{ reg_link_form.notes.label }}</label>
                                        {{ reg_link_form.notes }}
                                        {% if reg_link_form.notes.help_text %}<div class="form-text">{{ reg_link_form.notes.help_text }}</div>{% endif %}
                                        {% for error in reg_link_form.notes.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                </div>

                                {# --- Accessible Folders Field (Visible by default) --- #}
                                <div class="mb-3" id="existing-folders-wrapper">
                                    <label class="form-label">{{ reg_link_form.accessible_folders.label }}</label>
                                    <div id="id_accessible_folders_container" class="form-control" style="max-height: 150px; overflow-y: auto;"> {# Added ID for JS targeting #}
                                        {{ reg_link_form.accessible_folders }}
                                    </div>
                                    {% if reg_link_form.accessible_folders.help_text %}<div class="form-text">{{ reg_link_form.accessible_folders.help_text }}</div>{% endif %}
                                    {% for error in reg_link_form.accessible_folders.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    {# Link to show New Folder input #}
                                    <button type="button" class="btn btn-link btn-sm p-0 mt-1" id="show-new-folder-btn">Or Create New Folder?</button>
                                </div>

                                {# --- New Folder Name Field (Hidden by default) --- #}
                                <div class="mb-3" id="new-folder-input-wrapper" style="display: none;">
                                    <label for="{{ reg_link_form.new_folder_name.id_for_label }}" class="form-label">{{ reg_link_form.new_folder_name.label }}</label>
                                    {{ reg_link_form.new_folder_name }}
                                    {% if reg_link_form.new_folder_name.help_text %}<div class="form-text">{{ reg_link_form.new_folder_name.help_text }}</div>{% endif %}
                                    {% for error in reg_link_form.new_folder_name.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    {# Link to show Existing Folders input #}
                                    <button type="button" class="btn btn-link btn-sm p-0 mt-1" id="show-existing-folders-btn">Select Existing Folders Instead?</button>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ reg_link_form.expires_at.id_for_label }}" class="form-label">{{ reg_link_form.expires_at.label }}</label>
                                        {{ reg_link_form.expires_at }}
                                        {% if reg_link_form.expires_at.help_text %}<div class="form-text">{{ reg_link_form.expires_at.help_text }}</div>{% endif %}
                                        {% for error in reg_link_form.expires_at.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ reg_link_form.max_uses.id_for_label }}" class="form-label">{{ reg_link_form.max_uses.label }}</label>
                                        {{ reg_link_form.max_uses }}
                                        {% if reg_link_form.max_uses.help_text %}<div class="form-text">{{ reg_link_form.max_uses.help_text }}</div>{% endif %}
                                        {% for error in reg_link_form.max_uses.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                    </div>
                                </div>
                                <div class="mb-3 form-check">
                                    {{ reg_link_form.is_active }}
                                    <label class="form-check-label" for="{{ reg_link_form.is_active.id_for_label }}">{{ reg_link_form.is_active.label }}</label>
                                    {% if reg_link_form.is_active.help_text %}<div class="form-text">{{ reg_link_form.is_active.help_text }}</div>{% endif %}
                                    {% for error in reg_link_form.is_active.errors %}<div class="invalid-feedback d-block">{{ error }}</div>{% endfor %}
                                </div>
                                <div class="text-end mt-3 pt-3 border-top"> {# Added spacing and border #}
                                    <button type="button" class="btn btn-secondary me-2" data-bs-toggle="collapse" data-bs-target="#createRegLinkFormCollapse">Cancel</button>
                                    <button type="submit" class="btn btn-success" name="submit_reg_link_form">
                                        <i class="bi bi-link-45deg me-2"></i> Create Link
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div><!-- End Registration Links Tab Pane -->
        {% endif %} {# End of Registration Links Section if #}

    </div><!-- End Tab Content -->

    <!-- Modals should ideally be outside the main layout flow, but within the block -->
    <!-- Invite Modal -->
    {% if can_manage_links_invites %} {# Use permission variable #}
        <div class="modal fade" id="inviteModal" tabindex="-1" aria-labelledby="inviteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                {# Use the invite_form passed from the view context #}
                <form method="post" action="{% url 'accounts:company_team' company.id %}">
                    {% csrf_token %}
                    {# Add hidden input to identify form submission #}
                    <input type="hidden" name="submit_invite_form" value="1">
                    <div class="modal-header">
                        <h5 class="modal-title" id="inviteModalLabel">Invite Team Members</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        {% if invite_form.errors %}
                            <div class="alert alert-danger">
                                Please correct the errors below.
                            </div>
                        {% endif %}
                        {# Render invite_form fields using Django template rendering #}
                        <div class="mb-3">
                            <label for="{{ invite_form.emails.id_for_label }}" class="form-label">{{ invite_form.emails.label }}</label>
                            {{ invite_form.emails }}
                            {% if invite_form.emails.help_text %}
                                <div class="form-text">{{ invite_form.emails.help_text }}</div>
                            {% endif %}
                            {% for error in invite_form.emails.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                         <div class="mb-3">
                            <label for="{{ invite_form.role.id_for_label }}" class="form-label">{{ invite_form.role.label }}</label>
                            {{ invite_form.role }}
                            {% if invite_form.role.help_text %}
                                <div class="form-text">{{ invite_form.role.help_text }}</div>
                            {% endif %}
                            {% for error in invite_form.role.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                         <div class="mb-3">
                            <label for="{{ invite_form.message.id_for_label }}" class="form-label">{{ invite_form.message.label }}</label>
                            {{ invite_form.message }}
                            {% if invite_form.message.help_text %}
                                <div class="form-text">{{ invite_form.message.help_text }}</div>
                            {% endif %}
                            {% for error in invite_form.message.errors %}
                                <div class="invalid-feedback d-block">{{ error }}</div>
                            {% endfor %}
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary" name="submit_invite_form"> {# Add name to button #}
                            <i class="bi bi-send me-2"></i>
                            Send Invitations
                        </button>
                    </div>
                </form>
            </div>
        </div>
    {% endif %}

    {# QR Code Modal Removed #}

</div> {# End of container py-4 #}
{% endblock %} {# End of block content #}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[title]');
    tooltips.forEach(el => {
        new bootstrap.Tooltip(el);
    });

    // QR Code Modal Logic Removed

    // --- Registration Link Form Enhancements ---
    const regFormCollapse = document.getElementById('createRegLinkFormCollapse');
    const accessibleFoldersContainer = document.getElementById('id_accessible_folders_container');
    const newFolderInputWrapper = document.getElementById('new-folder-input-wrapper');
    const showNewFolderBtn = document.getElementById('show-new-folder-btn');
    const showExistingFoldersBtn = document.getElementById('show-existing-folders-btn');
    const existingFoldersWrapper = document.getElementById('existing-folders-wrapper'); // Get the wrapper for existing folders

    // 1. Select all folders by default when the form appears (only if inside the links tab)
    if (regFormCollapse && accessibleFoldersContainer) {
        // Check if the parent tab pane is active initially or becomes active
        const linksTabPane = document.getElementById('links-tab-pane');
        const selectAllCheckboxes = () => {
            const checkboxes = accessibleFoldersContainer.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = true;
            });
        };

        if (linksTabPane && linksTabPane.classList.contains('active')) {
             regFormCollapse.addEventListener('shown.bs.collapse', selectAllCheckboxes);
        } else if (linksTabPane) {
            // Also handle the case where the tab becomes active later
            const linksTabButton = document.getElementById('links-tab');
            if (linksTabButton) {
                linksTabButton.addEventListener('shown.bs.tab', function() {
                    regFormCollapse.addEventListener('shown.bs.collapse', selectAllCheckboxes);
                    // If collapse is already shown when tab becomes active, trigger manually
                    if (regFormCollapse.classList.contains('show')) {
                        selectAllCheckboxes();
                    }
                });
                 // Cleanup listener if tab becomes inactive
                linksTabButton.addEventListener('hidden.bs.tab', function() {
                     regFormCollapse.removeEventListener('shown.bs.collapse', selectAllCheckboxes);
                });
            }
        }
    }


    // 2. Toggle between existing folders and new folder input
    if (showNewFolderBtn && newFolderInputWrapper && existingFoldersWrapper && accessibleFoldersContainer) {
        showNewFolderBtn.addEventListener('click', function() {
            existingFoldersWrapper.style.display = 'none';
            newFolderInputWrapper.style.display = 'block';
            // Clear existing folder selections if switching to new folder
            const checkboxes = accessibleFoldersContainer.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => { checkbox.checked = false });
        });
    }
    if (showExistingFoldersBtn && newFolderInputWrapper && existingFoldersWrapper && accessibleFoldersContainer) {
        showExistingFoldersBtn.addEventListener('click', function() {
            newFolderInputWrapper.style.display = 'none';
            existingFoldersWrapper.style.display = 'block';
            // Clear new folder input if switching back
            const newFolderInput = newFolderInputWrapper.querySelector('input');
            if (newFolderInput) { newFolderInput.value = ''; }
            // Re-select all existing folders when switching back
            const checkboxes = accessibleFoldersContainer.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => { checkbox.checked = true });
        });
    }
    // --- End Registration Link Form Enhancements ---

 });

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Use a more subtle notification if possible, e.g., a small temporary message
        alert('Link copied to clipboard!');
    }, function(err) {
        console.error('Could not copy link: ', err);
        alert('Error copying link. Please copy manually.');
    });
}


// Invitation management functions
function resendInvitation(url) {
    if (!confirm('Resend this invitation?')) return;

    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Accept': 'application/json' // Ensure server knows we expect JSON
        }
    })
    .then(response => {
        if (!response.ok) {
            // Try to parse error from JSON response if possible
            return response.json().then(errData => {
                throw new Error(errData.message || `HTTP error! status: ${response.status}`);
            }).catch(() => {
                // Fallback if response is not JSON
                throw new Error(`HTTP error! status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            // Maybe show a success message before reloading?
            alert(data.message || 'Invitation resent successfully!');
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Could not resend invitation.'));
        }
    })
    .catch(error => {
        console.error('Error resending invitation:', error);
        alert('An error occurred while resending the invitation: ' + error.message);
    });
}

function cancelInvitation(url) {
    if (!confirm('Cancel this invitation?')) return;

    fetch(url, {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
            'Accept': 'application/json' // Ensure server knows we expect JSON
        }
    })
    .then(response => {
         if (!response.ok) {
            // Try to parse error from JSON response if possible
            return response.json().then(errData => {
                throw new Error(errData.message || `HTTP error! status: ${response.status}`);
            }).catch(() => {
                // Fallback if response is not JSON
                throw new Error(`HTTP error! status: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
             // Maybe show a success message before reloading?
            alert(data.message || 'Invitation cancelled successfully!');
            location.reload();
        } else {
            alert('Error: ' + (data.message || 'Could not cancel invitation.'));
        }
    })
    .catch(error => {
        console.error('Error cancelling invitation:', error);
        alert('An error occurred while cancelling the invitation: ' + error.message);
    });
}
</script>
{% endblock %}
