// Navigation Content Handler
document.addEventListener('DOMContentLoaded', function() {
    // Get all navigation items
    const navItems = document.querySelectorAll('.list-group-item[data-section-id]');

    // Add click event listener to each navigation item
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            // Get section ID from data attribute
            const sectionId = this.getAttribute('data-section-id');
            if (!sectionId) return;

            // Get company and assistant IDs from meta tags
            const companyId = document.querySelector('meta[name="company-id"]').content;
            const assistantId = document.querySelector('meta[name="assistant-id"]').content;

            // Show loading message in chat box
            const chatBox = document.getElementById('chat-box');
            if (chatBox) {
                const loadingDiv = document.createElement('div');
                loadingDiv.className = 'message assistant-message loading';
                loadingDiv.innerHTML = '<div class="message-content"><div class="spinner-border spinner-border-sm" role="status"></div> Loading content...</div>';
                chatBox.appendChild(loadingDiv);
                chatBox.scrollTop = chatBox.scrollHeight;

                // Hide initial display area if it exists
                const initialDisplay = document.getElementById('initial-display-area');
                if (initialDisplay) {
                    initialDisplay.style.display = 'none';
                }
            }

            // Prepare request data
            const data = {
                navigation_click: true,
                section_id: sectionId
            };

            // Debug log for troubleshooting
            const interactUrl = `/assistant/company/${companyId}/assistants/${assistantId}/interact/`;
            console.log('Navigation content - sending request to:', interactUrl);
            console.log('Navigation content - request data:', data);

            // Send request to server
            fetch(interactUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                console.log('Navigation content - response status:', response.status);
                if (!response.ok) {
                    console.error('Navigation content - HTTP error:', response.status);
                }
                return response.json();
            })
            .then(data => {
                console.log('Navigation content - response data:', data);
                // Remove loading indicator
                if (chatBox) {
                    const loadingIndicator = chatBox.querySelector('.loading');
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                    }
                }

                if (data.status === 'error') {
                    throw new Error(data.error || 'Error loading content');
                }

                // Display the content in a chat bubble with enhanced formatting
                if (chatBox && data.content) {
                    // Create the message container
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message assistant-message nav-content-bubble';

                    // Get the section label - try multiple sources
                    let sectionLabel = '';

                    // 1. Try from the response data (most reliable)
                    if (data.section_label) {
                        sectionLabel = data.section_label;
                        console.log('Using section label from response:', sectionLabel);
                    }
                    // 2. Try from the clicked element's data attribute
                    else if (this.getAttribute('data-nav-label')) {
                        sectionLabel = this.getAttribute('data-nav-label');
                        console.log('Using section label from element attribute:', sectionLabel);
                    }
                    // 3. Try from the element's text content
                    else if (this.textContent && this.textContent.trim()) {
                        sectionLabel = this.textContent.trim();
                        console.log('Using section label from element text:', sectionLabel);
                    }
                    // 4. Fallback
                    else {
                        sectionLabel = 'Information';
                        console.log('Using default section label');
                    }

                    // Check if the content already has styling (to avoid duplicate headers)
                    let contentToDisplay = data.content;

                    // If content already has styling (contains <style> tag), use it directly
                    if (contentToDisplay.includes('<style>')) {
                        console.log('Content already has styling, using as-is');
                        messageDiv.innerHTML = `<div class="message-content">${contentToDisplay}</div>`;
                    } else {
                        // Format the content with a header
                        const formattedContent = `
                            <div class="message-content">
                                <h4 class="nav-content-header">${sectionLabel}</h4>
                                <div class="nav-content-body">${contentToDisplay}</div>
                            </div>
                        `;
                        messageDiv.innerHTML = formattedContent;
                    }

                    chatBox.appendChild(messageDiv);
                    chatBox.scrollTop = chatBox.scrollHeight;

                    // Add a style tag for the nav content if it doesn't exist
                    if (!document.getElementById('nav-content-styles')) {
                        const styleTag = document.createElement('style');
                        styleTag.id = 'nav-content-styles';
                        styleTag.textContent = `
                            .nav-content-bubble .message-content {
                                background: #f0f7ff !important;
                                border-left: 3px solid #0d6efd !important;
                                border-radius: 0.8rem !important;
                                padding: 1rem !important;
                                color: #333333 !important;
                            }

                            .nav-content-header {
                                color: #0d6efd;
                                margin-bottom: 0.75rem;
                                padding-bottom: 0.5rem;
                                border-bottom: 1px solid rgba(13, 110, 253, 0.2);
                                font-weight: 600;
                            }

                            .nav-content-body {
                                line-height: 1.6;
                            }

                            .nav-content-body img {
                                max-width: 100%;
                                height: auto;
                                border-radius: 0.5rem;
                                margin: 0.5rem 0;
                            }

                            .nav-content-body a {
                                color: #0d6efd;
                                text-decoration: none;
                            }

                            .nav-content-body a:hover {
                                text-decoration: underline;
                            }
                        `;
                        document.head.appendChild(styleTag);
                    }

                    // On mobile, close the sidebar after clicking a navigation item
                    if (window.innerWidth < 768) {
                        const sidebar = document.getElementById('chat-sidebar');
                        if (sidebar && sidebar.classList.contains('active')) {
                            // Use the existing toggleSidebarDirect function if available
                            if (typeof toggleSidebarDirect === 'function') {
                                toggleSidebarDirect(document.getElementById('sidebar-toggle-btn-mobile') || {});
                            } else {
                                sidebar.classList.remove('active');
                                sidebar.style.left = '-350px';

                                const sidebarOverlay = document.getElementById('sidebar-overlay');
                                if (sidebarOverlay) {
                                    sidebarOverlay.style.opacity = '0';
                                    sidebarOverlay.style.pointerEvents = 'none';
                                    setTimeout(() => {
                                        sidebarOverlay.style.display = 'none';
                                    }, 300);
                                }
                            }
                        }
                    }
                }
            })
            .catch(error => {
                // Remove loading indicator
                if (chatBox) {
                    const loadingIndicator = chatBox.querySelector('.loading');
                    if (loadingIndicator) {
                        loadingIndicator.remove();
                    }

                    // Provide more detailed error message for debugging
                    const errorDetails = error.message || 'Unknown error';
                    console.error('Navigation content - detailed error:', {
                        message: errorDetails,
                        url: interactUrl,
                        requestData: data
                    });

                    // Display error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'message assistant-message';
                    errorDiv.innerHTML = `<div class="message-content text-danger"><i class="bi bi-exclamation-triangle-fill me-2"></i>${errorDetails}. Please check the console for more details.</div>`;
                    chatBox.appendChild(errorDiv);
                    chatBox.scrollTop = chatBox.scrollHeight;
                }
                console.error('Error loading navigation content:', error);
            });
        });
    });
});
