{% if flagged_questions %}
    <div class="list-group list-group-flush">
        {% for flagged in flagged_questions %}
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between mb-1">
                    <h6 class="mb-1">{{ flagged.question|truncatechars:100 }}</h6>
                    <small class="text-muted">{{ flagged.created_at|timesince }} ago</small>
                </div>
                <p class="mb-1 text-muted small">{{ flagged.original_answer|truncatechars:150 }}</p>
                {% if flagged.reason %}
                    <p class="mb-2 fst-italic small">
                        <i class="bi bi-quote me-1"></i>{{ flagged.reason|truncatechars:100 }}
                    </p>
                {% endif %}
                <div class="d-flex justify-content-between align-items-center">
                    <small class="text-muted">Flagged by {{ flagged.user.username }}</small>
                    <div>
                        <a href="{% url 'assistants:flagged_question_detail' assistant.company.id assistant.id flagged.id %}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye me-1"></i> View
                        </a>
                        <a href="{% url 'assistants:add_context_to_flagged' assistant.company.id assistant.id flagged.id %}" class="btn btn-sm btn-outline-success ms-1">
                            <i class="bi bi-plus-circle me-1"></i> Add Context
                        </a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <div class="card-footer bg-transparent text-center">
        <a href="{% url 'assistants:flagged_questions' assistant.company.id assistant.id %}" class="btn btn-outline-warning">
            <i class="bi bi-flag me-1"></i> View All Flagged Questions
        </a>
    </div>
{% else %}
    <div class="card-body">
        <div class="alert alert-success mb-0">
            <i class="bi bi-check-circle-fill me-2"></i>
            No questions currently need assistance. Great job!
        </div>
    </div>
{% endif %}
