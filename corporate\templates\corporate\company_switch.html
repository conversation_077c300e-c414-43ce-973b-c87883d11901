{% extends 'corporate/base.html' %}
{% load static %}

{% block title %}Switch Company - Corporate Prompt Master{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="h3 mb-3">Switch Company</h1>
                <p class="text-muted">
                    Select the company you want to work with
                </p>
            </div>

            <!-- Company List -->
            <div class="card shadow">
                <div class="list-group list-group-flush">
                    {% for company in user_companies %}
                        <div class="list-group-item list-group-item-action p-4">
                            <form method="post" class="d-flex align-items-center">
                                {% csrf_token %}
                                <input type="hidden" name="company_id" value="{{ company.id }}">

                                <!-- Company Info -->
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center">
                                        <!-- Logo/Icon -->
                                        <div class="flex-shrink-0 me-3">
                                            {% if company.logo %}
                                                <img src="{{ company.logo.url }}"
                                                     alt="{{ company.name }}"
                                                     class="rounded"
                                                     width="48" height="48">
                                            {% else %}
                                                <div class="bg-light rounded d-flex align-items-center justify-content-center"
                                                     style="width: 48px; height: 48px;">
                                                    <i class="fas fa-building text-muted"></i>
                                                </div>
                                            {% endif %}
                                        </div>

                                        <!-- Details -->
                                        <div class="flex-grow-1">
                                            <h6 class="mb-1">
                                                {{ company.name }}
                                                {% if company == active_company %}
                                                    <span class="badge bg-success ms-2">Active</span>
                                                {% endif %}
                                            </h6>
                                            <div class="text-muted small d-flex align-items-center">
                                                <span class="me-3">
                                                    <i class="fas fa-users me-1"></i>
                                                    {{ company.employees.count }} member{{ company.employees.count|pluralize }}
                                                </span>
                                                {% if user == company.owner %}
                                                    <span class="badge bg-primary">Owner</span>
                                                {% elif user.corporate_profile.company == company and user.corporate_profile.is_company_admin %}
                                                    <span class="badge bg-danger">Admin</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">Member</span>
                                                {% endif %}
                                            </div>
                                        </div>

                                        <!-- Action -->
                                        <div class="flex-shrink-0 ms-3">
                                            {% if company == active_company %}
                                                <button type="submit" class="btn btn-light" disabled>
                                                    <i class="fas fa-check-circle"></i>
                                                </button>
                                            {% else %}
                                                <button type="submit" class="btn btn-primary">
                                                    Switch
                                                </button>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    {% empty %}
                        <div class="list-group-item text-center py-5">
                            <div class="text-muted">
                                <i class="fas fa-building h3 mb-2"></i>
                                <p>You don't belong to any companies yet</p>
                            </div>
                        </div>
                    {% endfor %}
                </div>

                <!-- Actions -->
                <div class="card-footer bg-transparent p-4">
                    <div class="d-grid gap-2">
                        <a href="{% url 'corporate:create_company' %}" class="btn btn-primary">
                            <i class="fas fa-plus-circle me-2"></i>
                            Create New Company
                        </a>
                        <a href="{% url 'corporate:corporate_dashboard' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Help Text -->
            <div class="text-center mt-4">
                <p class="text-muted small mb-0">
                    Need to join another company? Ask for an invitation from a company admin.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.list-group-item-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 .125rem .25rem rgba(0,0,0,.075);
    z-index: 1;
}

.company-card {
    transition: all 0.2s;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form submissions
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const button = form.querySelector('button[type="submit"]:not([disabled])');
            if (button) {
                button.disabled = true;
                button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Switching...';
            }
        });
    });
});
</script>
{% endblock %}
