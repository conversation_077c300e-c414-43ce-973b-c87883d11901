from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.db import transaction
from django.conf import settings
from accounts.models import Company, CompanyInvitation
from assistants.models import Assistant

class Command(BaseCommand):
    help = 'Set up test data for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Remove all test data instead of creating it',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation of test data even if it already exists',
        )
    
    def cleanup_test_data(self):
        """Remove all test data"""
        # Delete test users and their related data
        User.objects.filter(username='test_user').delete()
        Company.objects.filter(name='Test Company').delete()
        self.stdout.write(self.style.SUCCESS('Test data cleaned up successfully'))
    
    @transaction.atomic
    def create_test_data(self, force=False):
        """Create test data for development"""
        # Check if test user already exists
        if User.objects.filter(username='test_user').exists() and not force:
            self.stderr.write('Test user already exists. Use --force to override.')
            return
        
        # Create test user
        test_user = User.objects.create_user(
            username='test_user',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.stdout.write(
            self.style.SUCCESS(f'Created test user: {test_user.username}')
        )
        
        # Create test company
        company = Company.objects.create(
            name='Test Company',
            owner=test_user,
            info={
                'mission': 'To test everything thoroughly',
                'founded': 2025,
                'website': 'https://test.example.com'
            }
        )
        company.members.add(test_user)
        self.stdout.write(
            self.style.SUCCESS(f'Created test company: {company.name}')
        )
        
        # Create test assistant
        assistant = Assistant.objects.create(
            name='Test Assistant',
            company=company,
            type='simple',
            is_public=True,
            info={
                'description': 'A test assistant for development',
                'capabilities': [
                    'basic_chat',
                    'company_info',
                    'product_info'
                ],
                'personality': 'helpful and friendly'
            }
        )
        self.stdout.write(
            self.style.SUCCESS(f'Created test assistant: {assistant.name}')
        )
        
        # Create test invitation
        invitation = CompanyInvitation.objects.create(
            company=company,
            email='<EMAIL>'
        )
        self.stdout.write(
            self.style.SUCCESS(f'Created test invitation for: {invitation.email}')
        )
        
        # Create test content
        try:
            from content.models import Content
            content = Content.objects.create(
                company=company,
                title='Test Content',
                content_type='text',
                content='This is some test content for development.'
            )
            self.stdout.write(
                self.style.SUCCESS(f'Created test content: {content.title}')
            )
        except ImportError:
            self.stdout.write(
                self.style.WARNING('Content app not available, skipping content creation')
            )
        
        # Create test listing
        try:
            from directory.models import CompanyListing
            listing = CompanyListing.objects.create(
                company=company,
                description='A test company listing',
                website='https://test.example.com'
            )
            self.stdout.write(
                self.style.SUCCESS(f'Created test listing for: {company.name}')
            )
        except ImportError:
            self.stdout.write(
                self.style.WARNING('Directory app not available, skipping listing creation')
            )
        
        self.stdout.write(
            self.style.SUCCESS('Test data setup completed successfully')
        )
        
        # Print credentials
        self.stdout.write('\nTest Credentials:')
        self.stdout.write('-----------------')
        self.stdout.write(f'Username: {test_user.username}')
        self.stdout.write('Password: testpass123')
        self.stdout.write(f'Email: {test_user.email}')
    
    def handle(self, *args, **options):
        if options['cleanup']:
            self.cleanup_test_data()
        else:
            if not settings.DEBUG:
                self.stderr.write(
                    self.style.ERROR('This command can only be run with DEBUG=True')
                )
                return
            
            try:
                self.create_test_data(force=options['force'])
            except Exception as e:
                self.stderr.write(
                    self.style.ERROR(f'Error creating test data: {str(e)}')
                )
                return
