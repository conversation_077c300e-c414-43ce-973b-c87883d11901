{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Moderation Dashboard - {{ assistant.name }}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/moderation-dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">Community Assistant Moderation Dashboard</h1>
                <div>
                    <a href="{% url 'assistants:community_dashboard' company.id assistant.id %}" class="btn btn-outline-primary me-2">
                        <i class="bi bi-arrow-left"></i> Back to Community Dashboard
                    </a>
                </div>
            </div>
            <p class="text-muted">Manage and moderate the {{ assistant.name }} community assistant</p>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="bi bi-people-fill text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0">Total Users</h6>
                            <h3 class="mb-0">{{ total_users }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="bi bi-file-text-fill text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0">Total Contexts</h6>
                            <h3 class="mb-0">{{ total_contexts }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="bi bi-flag-fill text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0">Pending Reports</h6>
                            <h3 class="mb-0">{{ pending_reports }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-danger bg-opacity-10 p-3 rounded">
                                <i class="bi bi-shield-fill-exclamation text-danger"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0">Active Bans</h6>
                            <h3 class="mb-0">{{ active_bans }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="bg-primary bg-opacity-10 p-3 rounded d-inline-block">
                            <i class="bi bi-people-fill text-primary" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <h4>User Management</h4>
                    <p class="text-muted">Manage users, reputation, and bans</p>
                    <a href="{% url 'assistants:moderation_users' company.id assistant.id %}" class="btn btn-primary">
                        Manage Users
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="bg-success bg-opacity-10 p-3 rounded d-inline-block">
                            <i class="bi bi-shield-fill text-success" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <h4>Content Moderation</h4>
                    <p class="text-muted">Review and moderate reported content</p>
                    <a href="{% url 'assistants:moderation_content' company.id assistant.id %}" class="btn btn-success">
                        Moderate Content
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <div class="bg-info bg-opacity-10 p-3 rounded d-inline-block">
                            <i class="bi bi-graph-up text-info" style="font-size: 2rem;"></i>
                        </div>
                    </div>
                    <h4>Statistics</h4>
                    <p class="text-muted">View community statistics and analytics</p>
                    <a href="{% url 'assistants:moderation_statistics' company.id assistant.id %}" class="btn btn-info">
                        View Statistics
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-3">
        <!-- Top Contributors -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Top Contributors</h5>
                </div>
                <div class="card-body">
                    {% if top_contributors %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>User</th>
                                        <th>Level</th>
                                        <th>Score</th>
                                        <th>Contributions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for reputation in top_contributors %}
                                        <tr>
                                            <td>{{ reputation.user.username }}</td>
                                            <td>
                                                <span class="badge bg-{{ reputation.level|lower|slugify }}-subtle text-{{ reputation.level|lower|slugify }}">
                                                    {{ reputation.level }}
                                                </span>
                                            </td>
                                            <td>{{ reputation.score }}</td>
                                            <td>{{ reputation.contributions_count }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center my-4">No contributors yet.</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Moderation Actions -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Recent Moderation Actions</h5>
                </div>
                <div class="card-body">
                    {% if recent_actions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Action</th>
                                        <th>Moderator</th>
                                        <th>Target</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for action in recent_actions %}
                                        <tr>
                                            <td>
                                                <span class="badge bg-{{ action.action_type|lower|slugify }}-subtle text-{{ action.action_type|lower|slugify }}">
                                                    {{ action.get_action_type_display }}
                                                </span>
                                            </td>
                                            <td>{{ action.moderator.username }}</td>
                                            <td>
                                                {% if action.target_user %}
                                                    {{ action.target_user.username }}
                                                {% elif action.reported_content %}
                                                    Content #{{ action.reported_content.id }}
                                                {% else %}
                                                    -
                                                {% endif %}
                                            </td>
                                            <td>{{ action.created_at|date:"M d, Y" }}</td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center my-4">No recent moderation actions.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/moderation-dashboard.js' %}"></script>
{% endblock %}
