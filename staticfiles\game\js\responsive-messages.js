// Responsive Messages Container
// This script adjusts the height of the messages container based on whether the preview container is visible

document.addEventListener('DOMContentLoaded', function() {
    // Get references to the containers
    const messagesContainer = document.getElementById('messages-container');
    const previewContainer = document.getElementById('preview-container');
    const mainContent = document.querySelector('.main-content');

    // Function to check if an element has a specific class
    function hasClass(element, className) {
        return element.classList.contains(className);
    }

    // Function to adjust container classes for styling
    function adjustContainerClasses() {
        if (!messagesContainer || !previewContainer) return;

        // Check if preview container is visible (doesn't have 'hidden' class)
        if (!hasClass(previewContainer, 'hidden')) {
            // Preview is visible
            messagesContainer.classList.add('preview-visible');

            // Add a class to the main content for additional styling if needed
            if (mainContent) {
                mainContent.classList.add('preview-active');
            }
        } else {
            // Preview is not visible
            messagesContainer.classList.remove('preview-visible');

            // Remove the preview active class
            if (mainContent) {
                mainContent.classList.remove('preview-active');
            }
        }
    }

    // Create a MutationObserver to watch for changes to the preview container's classes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'class') {
                adjustContainerClasses();
            }
        });
    });

    // Start observing the preview container for class changes
    if (previewContainer) {
        observer.observe(previewContainer, { attributes: true });
    }

    // Initial adjustment
    adjustContainerClasses();

    // Also adjust when window is resized
    window.addEventListener('resize', adjustContainerClasses);
});
