{% extends 'superadmin/base_superadmin.html' %}
{% load i18n %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{% url 'superadmin:user_list' %}">{% trans "Users" %}</a></li>
<li class="breadcrumb-item active">{{ user_obj.username }}</li>
{% endblock %}

{% block superadmin_content %}
<div class="row mb-4">
    <!-- User Profile Card -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">{% trans "User Profile" %}</h5>
            </div>
            <div class="card-body text-center">
                <div class="rounded-circle bg-light d-inline-flex align-items-center justify-content-center mb-3" style="width: 150px; height: 150px;">
                    <i class="bi bi-person text-secondary" style="font-size: 4rem;"></i>
                </div>

                <h4>{{ user_obj.get_full_name|default:user_obj.username }}</h4>
                <p class="text-muted">{{ user_obj.email }}</p>

                <div class="d-flex justify-content-center mb-3">
                    {% if user_obj.is_superuser %}
                    <span class="badge bg-warning me-2">{% trans "Superuser" %}</span>
                    {% endif %}

                    {% if user_obj.is_staff %}
                    <span class="badge bg-info me-2">{% trans "Staff" %}</span>
                    {% endif %}

                    {% if user_obj.is_active %}
                    <span class="badge bg-success">{% trans "Active" %}</span>
                    {% else %}
                    <span class="badge bg-danger">{% trans "Inactive" %}</span>
                    {% endif %}
                </div>



                <div class="text-start">
                    <p><strong>{% trans "Username:" %}</strong> {{ user_obj.username }}</p>
                    <p><strong>{% trans "Date Joined:" %}</strong> {{ user_obj.date_joined|date:"F d, Y" }}</p>
                    <p><strong>{% trans "Last Login:" %}</strong> {{ user_obj.last_login|date:"F d, Y H:i"|default:"Never" }}</p>
                </div>
            </div>
            <div class="card-footer">
                <div class="d-grid gap-2">
                    {% if user_obj != request.user %}
                    <form method="post" action="{% url 'superadmin:user_toggle_active' pk=user_obj.pk %}">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm {% if user_obj.is_active %}btn-danger{% else %}btn-success{% endif %} w-100">
                            {% if user_obj.is_active %}
                            <i class="bi bi-person-x"></i> {% trans "Deactivate User" %}
                            {% else %}
                            <i class="bi bi-person-check"></i> {% trans "Activate User" %}
                            {% endif %}
                        </button>
                    </form>

                    <form method="post" action="{% url 'superadmin:user_toggle_staff' pk=user_obj.pk %}">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm {% if user_obj.is_staff %}btn-warning{% else %}btn-info{% endif %} w-100">
                            {% if user_obj.is_staff %}
                            <i class="bi bi-person-dash"></i> {% trans "Remove Staff Status" %}
                            {% else %}
                            <i class="bi bi-person-plus"></i> {% trans "Grant Staff Status" %}
                            {% endif %}
                        </button>
                    </form>

                    <form method="post" action="{% url 'superadmin:user_toggle_superuser' pk=user_obj.pk %}">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm {% if user_obj.is_superuser %}btn-warning{% else %}btn-dark{% endif %} w-100">
                            {% if user_obj.is_superuser %}
                            <i class="bi bi-shield-x"></i> {% trans "Remove Superuser Status" %}
                            {% else %}
                            <i class="bi bi-shield-check"></i> {% trans "Grant Superuser Status" %}
                            {% endif %}
                        </button>
                    </form>

                    <form method="post" action="{% url 'superadmin:company_impersonate' user_obj.pk %}">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-sm btn-secondary w-100">
                            <i class="bi bi-person-badge"></i> {% trans "Impersonate User" %}
                        </button>
                    </form>
                    {% else %}
                    <div class="alert alert-info mb-0">
                        <i class="bi bi-info-circle"></i> {% trans "You cannot modify your own account." %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- User Details Tabs -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-tabs card-header-tabs" id="userDetailTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="game-sessions-tab" data-bs-toggle="tab" data-bs-target="#game-sessions" type="button" role="tab" aria-controls="game-sessions" aria-selected="true">
                            {% trans "Game Sessions" %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="leaderboard-tab" data-bs-toggle="tab" data-bs-target="#leaderboard" type="button" role="tab" aria-controls="leaderboard" aria-selected="false">
                            {% trans "Leaderboard Entries" %}
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="companies-tab" data-bs-toggle="tab" data-bs-target="#companies" type="button" role="tab" aria-controls="companies" aria-selected="false">
                            {% trans "Companies" %}
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="userDetailTabsContent">
                    <!-- Game Sessions Tab -->
                    <div class="tab-pane fade show active" id="game-sessions" role="tabpanel" aria-labelledby="game-sessions-tab">
                        <h5 class="mb-3">{% trans "Recent Game Sessions" %}</h5>
                        {% if game_sessions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "ID" %}</th>
                                        <th>{% trans "Company" %}</th>
                                        <th>{% trans "Started" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Score" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for session in game_sessions %}
                                    <tr>
                                        <td>{{ session.id }}</td>
                                        <td>{{ session.company.name }}</td>
                                        <td>{{ session.created_at|date:"M d, Y H:i" }}</td>
                                        <td>
                                            {% if session.game_completed %}
                                            <span class="badge bg-success">{% trans "Completed" %}</span>
                                            {% else %}
                                            <span class="badge bg-primary">{% trans "In Progress" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ session.performance_score|default:"0" }}</td>
                                        <td>
                                            <a href="{% url 'superadmin:game_session_detail' pk=session.pk %}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{% url 'superadmin:game_session_list' %}?q={{ user_obj.username }}" class="btn btn-outline-primary">
                                {% trans "View All Sessions" %}
                            </a>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            {% trans "No game sessions found for this user." %}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Leaderboard Entries Tab -->
                    <div class="tab-pane fade" id="leaderboard" role="tabpanel" aria-labelledby="leaderboard-tab">
                        <h5 class="mb-3">{% trans "All Leaderboard Entries" %}</h5>
                        {% if leaderboard_entries %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Leaderboard" %}</th>
                                        <th>{% trans "Company" %}</th>
                                        <th>{% trans "Score" %}</th>
                                        <th>{% trans "Rank" %}</th>
                                        <th>{% trans "Date" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in leaderboard_entries %}
                                    <tr>
                                        <td>{{ entry.leaderboard.name }}</td>
                                        <td>{{ entry.leaderboard.company.name|default:"Global" }}</td>
                                        <td>{{ entry.score }}</td>
                                        <td>{{ entry.rank|default:"N/A" }}</td>
                                        <td>{{ entry.recorded_at|date:"M d, Y" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            {% trans "No leaderboard entries found for this user." %}
                        </div>
                        {% endif %}
                    </div>

                    <!-- Companies Tab -->
                    <div class="tab-pane fade" id="companies" role="tabpanel" aria-labelledby="companies-tab">
                        <h5 class="mb-3">{% trans "Owned Companies" %}</h5>
                        {% if owned_companies %}
                        <div class="table-responsive mb-4">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>{% trans "Name" %}</th>
                                        <th>{% trans "Status" %}</th>
                                        <th>{% trans "Created" %}</th>
                                        <th>{% trans "Actions" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for company in owned_companies %}
                                    <tr>
                                        <td>{{ company.name }}</td>
                                        <td>
                                            {% if company.allow_leaderboard %}
                                            <span class="badge bg-success">{% trans "Active" %}</span>
                                            {% else %}
                                            <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ company.created_at|date:"M d, Y" }}</td>
                                        <td>
                                            <a href="{% url 'superadmin:company_list' %}?q={{ company.name }}" class="btn btn-sm btn-primary">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info mb-4">
                            {% trans "This user doesn't own any companies." %}
                        </div>
                        {% endif %}


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
