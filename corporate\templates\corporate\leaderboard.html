{% extends 'corporate/base.html' %}

{% block title %}Leaderboard - Corporate Prompt Master{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-3">Leaderboard</h2>

        <div class="card shadow mb-4 bg-dark">
            <div class="card-header bg-dark text-white border-bottom border-secondary">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
                </div>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">Time Period</label>
                        <div class="btn-group w-100" role="group">
                            <a href="?period=all_time{% if selected_game %}&game={{ selected_game }}{% endif %}{% if show_company_only %}&company_only=true{% endif %}" class="btn btn-outline-light {% if time_period == 'all_time' %}active{% endif %}" {% if time_period == 'all_time' %}style="color: #212529 !important; font-weight: bold !important;"{% endif %}>All Time</a>
                            <a href="?period=monthly{% if selected_game %}&game={{ selected_game }}{% endif %}{% if show_company_only %}&company_only=true{% endif %}" class="btn btn-outline-light {% if time_period == 'monthly' %}active{% endif %}" {% if time_period == 'monthly' %}style="color: #212529 !important; font-weight: bold !important;"{% endif %}>Monthly</a>
                            <a href="?period=weekly{% if selected_game %}&game={{ selected_game }}{% endif %}{% if show_company_only %}&company_only=true{% endif %}" class="btn btn-outline-light {% if time_period == 'weekly' %}active{% endif %}" {% if time_period == 'weekly' %}style="color: #212529 !important; font-weight: bold !important;"{% endif %}>Weekly</a>
                            <a href="?period=daily{% if selected_game %}&game={{ selected_game }}{% endif %}{% if show_company_only %}&company_only=true{% endif %}" class="btn btn-outline-light {% if time_period == 'daily' %}active{% endif %}" {% if time_period == 'daily' %}style="color: #212529 !important; font-weight: bold !important;"{% endif %}>Daily</a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Game</label>
                        <select name="game" class="form-select" onchange="this.form.submit()">
                            {% for game in games_list %}
                            <option value="{{ game.game_id }}" {% if selected_game == game.game_id %}selected{% endif %}>
                                {{ game.game_name }}
                            </option>
                            {% endfor %}
                        </select>
                        <input type="hidden" name="period" value="{{ time_period }}">
                        <div class="form-text">
                            <small>Select a game to filter the leaderboard. If you don't see any entries, try playing the game first.</small>
                        </div>
                    </div>

                </form>


            </div>
        </div>

        <ul class="nav nav-tabs mb-4 bg-dark" id="leaderboardTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active bg-dark text-white border-secondary" id="company-tab" data-bs-toggle="tab" data-bs-target="#company-leaderboard" type="button" role="tab">
                    <i class="fas fa-building me-2"></i>{{ company.name }} Leaderboard
                </button>
            </li>
        </ul>

        <div class="tab-content" id="leaderboardTabsContent">
            <!-- Company Leaderboard -->
            <div class="tab-pane fade show active" id="company-leaderboard" role="tabpanel">
                <div class="card shadow bg-dark">
                    <div class="card-header bg-dark text-white border-bottom border-secondary">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-building me-2"></i>{{ company.name }} Leaderboard
                                <span class="badge bg-secondary text-white ms-2">{{ time_period|title|cut:"_" }}</span>
                            </h5>
                            {% if user_company_rank %}
                            <span class="badge bg-success">Your Rank: #{{ user_company_rank }}</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-body">
                        {% if company_entries %}
                        <!-- Legend for highlighting -->
                        <div class="mb-3">
                            <small class="text-muted">
                                <span class="badge bg-primary me-2">You</span>
                                <span class="badge bg-secondary me-2">👥</span> Colleague from {{ company.name }}
                            </small>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover table-dark">
                                <thead class="bg-dark">
                                    <tr>
                                        <th>Rank</th>
                                        <th>User</th>
                                        <th>Score</th>
                                        <th>Highest Role</th>
                                        <th>Status</th>
                                        {% if not selected_game %}
                                        <th>Game</th>
                                        {% endif %}
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in company_entries %}
                                    <tr {% if entry.user == user %}class="bg-primary text-white"{% endif %}>
                                        <td>{{ forloop.counter }}</td>
                                        <td>
                                            {{ entry.user.get_full_name|default:entry.user.username }}
                                            <span class="badge bg-secondary ms-1" title="Colleague">👥</span>
                                        </td>
                                        <td>{{ entry.score }}</td>
                                        <td>{{ entry.highest_role|title }}</td>
                                        <td>
                                            {% if entry.game_status == "Completed" %}
                                                <span class="badge bg-success">{{ entry.game_status }}</span>
                                            {% elif entry.game_status == "In Progress" %}
                                                <span class="badge bg-warning text-dark">{{ entry.game_status }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ entry.game_status|default:"Unknown" }}</span>
                                            {% endif %}
                                        </td>
                                        {% if not selected_game %}
                                        <td>{{ entry.game_name|default:"Corporate Prompt Master" }}</td>
                                        {% endif %}
                                        <td>{{ entry.recorded_at|date:"M d, Y" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No entries in the company leaderboard yet. Be the first to complete a game!
                            <div class="mt-2">
                                <a href="{% url 'corporate:game_list' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-gamepad me-1"></i> Play Corporate Prompt Master
                                </a>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>
{% endblock %}
