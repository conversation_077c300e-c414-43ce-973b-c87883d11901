import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from django.db.models import Count
from corporate.models import Company, LeaderboardEntry

# Function to test the superadmin users view
def test_superadmin_users_view():
    print("Testing superadmin users view...")
    
    # Get all users
    users = User.objects.all()
    print(f"Found {users.count()} users")
    
    # Try to annotate with leaderboard_entries count
    try:
        users_with_counts = users.annotate(
            leaderboard_count=Count('leaderboard_entries', distinct=True)
        )
        print("Successfully annotated users with leaderboard_entries count")
        
        # Print some results
        for user in users_with_counts[:5]:  # Limit to first 5 users
            print(f"User: {user.username}, Leaderboard entries: {user.leaderboard_count}")
            
    except Exception as e:
        print(f"Error annotating users with leaderboard_entries count: {e}")
    
    # Try to annotate with leaderboardentry count (this should fail)
    try:
        users_with_counts = users.annotate(
            leaderboard_count=Count('leaderboardentry', distinct=True)
        )
        print("Successfully annotated users with leaderboardentry count (this should have failed)")
        
    except Exception as e:
        print(f"Expected error annotating users with leaderboardentry count: {e}")

# Run the test
if __name__ == "__main__":
    test_superadmin_users_view()
