from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from ..models import Company, CompanyInvitation

class AccountViewsTests(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )
        self.company.members.add(self.user)
    
    def test_register_view(self):
        """Test user registration"""
        response = self.client.get(reverse('accounts:register'))
        self.assertEqual(response.status_code, 200)
        
        # Test registration with valid data
        data = {
            'username': 'newuser',
            'email': '<EMAIL>',
            'password1': 'securepass123',
            'password2': 'securepass123'
        }
        response = self.client.post(reverse('accounts:register'), data)
        self.assertEqual(response.status_code, 302)  # Redirect after success
        self.assertTrue(User.objects.filter(username='newuser').exists())
        
        # Test registration with existing username
        response = self.client.post(reverse('accounts:register'), data)
        self.assertEqual(response.status_code, 200)  # Stay on page with errors
        self.assertContains(response, "already exists")
    
    def test_dashboard_view(self):
        """Test dashboard access and content"""
        # Test unauthenticated access
        response = self.client.get(reverse('accounts:dashboard'))
        self.assertEqual(response.status_code, 302)  # Redirect to login
        
        # Test authenticated access
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('accounts:dashboard'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Company')
    
    def test_company_create_view(self):
        """Test company creation"""
        self.client.login(username='testuser', password='testpass123')
        
        # Test GET request
        response = self.client.get(reverse('accounts:company_create'))
        self.assertEqual(response.status_code, 200)
        
        # Test POST request with valid data
        data = {
            'name': 'New Company',
            'mission': 'To test things',
            'founded': 2025,
            'website': 'https://example.com',
            'list_in_directory': True
        }
        response = self.client.post(reverse('accounts:company_create'), data)
        self.assertEqual(response.status_code, 302)  # Redirect after success
        self.assertTrue(Company.objects.filter(name='New Company').exists())
    
    def test_company_switch_view(self):
        """Test company switching"""
        self.client.login(username='testuser', password='testpass123')
        
        # Create another company
        company2 = Company.objects.create(
            name='Second Company',
            owner=self.user
        )
        company2.members.add(self.user)
        
        # Test GET request
        response = self.client.get(reverse('accounts:company_switch'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Test Company')
        self.assertContains(response, 'Second Company')
        
        # Test switching companies
        data = {'company_id': company2.id}
        response = self.client.post(reverse('accounts:company_switch'), data)
        self.assertEqual(response.status_code, 302)  # Redirect after switch
    
    def test_company_team_view(self):
        """Test team management"""
        self.client.login(username='testuser', password='testpass123')
        
        # Test GET request
        response = self.client.get(
            reverse('accounts:company_team', kwargs={'company_id': self.company.id})
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'Team Management')
        
        # Test inviting a member
        data = {'email': '<EMAIL>'}
        response = self.client.post(
            reverse('accounts:invite_member', kwargs={'company_id': self.company.id}),
            data
        )
        self.assertEqual(response.status_code, 302)  # Redirect after invitation
        self.assertTrue(
            CompanyInvitation.objects.filter(
                company=self.company,
                email='<EMAIL>'
            ).exists()
        )
    
    def test_accept_invitation_view(self):
        """Test invitation acceptance"""
        # Create an invitation
        invitation = CompanyInvitation.objects.create(
            company=self.company,
            email='<EMAIL>'
        )
        
        # Create user with matching email
        invited_user = User.objects.create_user(
            username='invited',
            email='<EMAIL>',
            password='invited123'
        )
        self.client.login(username='invited', password='invited123')
        
        # Test accepting invitation
        response = self.client.get(
            reverse('accounts:accept_invitation', kwargs={'token': invitation.token})
        )
        self.assertEqual(response.status_code, 302)  # Redirect after acceptance
        
        # Verify membership
        self.assertTrue(self.company.members.filter(id=invited_user.id).exists())
        invitation.refresh_from_db()
        self.assertTrue(invitation.accepted)
        self.assertIsNotNone(invitation.accepted_at)
    
    def test_remove_member_view(self):
        """Test member removal"""
        self.client.login(username='testuser', password='testpass123')
        
        # Create a member to remove
        member = User.objects.create_user(
            username='member',
            email='<EMAIL>',
            password='member123'
        )
        self.company.members.add(member)
        
        # Test removing member
        response = self.client.post(
            reverse('accounts:remove_member', kwargs={'company_id': self.company.id}),
            {'username': member.username}
        )
        self.assertEqual(response.status_code, 302)  # Redirect after removal
        self.assertFalse(self.company.members.filter(id=member.id).exists())
