/**
 * TinyMCE Word Limit
 * This script enforces a word limit on TinyMCE editors
 * and provides visual feedback to users.
 */

// Configuration
const WORD_LIMIT = 500;
const WARNING_THRESHOLD = 400; // Show warning at 80% of limit

/**
 * Count words in TinyMCE editor content
 * @param {string} editorId - The ID of the TinyMCE editor
 * @returns {number} Word count
 */
function countTinyMCEWords(editorId) {
    if (typeof tinymce === 'undefined' || !tinymce.get(editorId)) {
        return 0;
    }
    
    const content = tinymce.get(editorId).getContent();
    // Strip HTML tags
    const textOnly = content.replace(/<[^>]*>/g, ' ');
    // Count words (non-empty strings after splitting by whitespace)
    const words = textOnly.split(/\s+/).filter(word => word.length > 0);
    return words.length;
}

/**
 * Update word count display and styling
 * @param {string} editorId - The ID of the TinyMCE editor
 * @param {string} counterId - The ID of the counter element
 */
function updateWordCount(editorId, counterId = 'word-count') {
    const counter = document.getElementById(counterId);
    if (!counter) return;
    
    const wordCount = countTinyMCEWords(editorId);
    counter.textContent = wordCount;
    
    // Update styling based on count
    if (wordCount > WORD_LIMIT) {
        counter.classList.remove('text-muted', 'text-warning');
        counter.classList.add('text-danger');
        
        // Find the submit button and disable it
        const form = counter.closest('form');
        if (form) {
            const submitButtons = form.querySelectorAll('button[type="submit"]');
            submitButtons.forEach(button => {
                button.disabled = true;
                button.title = `Content exceeds ${WORD_LIMIT} word limit`;
            });
            
            // Add warning message if not already present
            if (!form.querySelector('.word-limit-warning')) {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-danger mt-2 word-limit-warning';
                warningDiv.innerHTML = `<i class="bi bi-exclamation-triangle-fill me-2"></i>Content exceeds the ${WORD_LIMIT} word limit. Please shorten your text to submit.`;
                counter.parentNode.after(warningDiv);
            }
        }
    } else if (wordCount > WARNING_THRESHOLD) {
        counter.classList.remove('text-muted', 'text-danger');
        counter.classList.add('text-warning');
        
        // Enable submit button
        const form = counter.closest('form');
        if (form) {
            const submitButtons = form.querySelectorAll('button[type="submit"]');
            submitButtons.forEach(button => {
                button.disabled = false;
                button.title = '';
            });
            
            // Remove warning if present
            const warning = form.querySelector('.word-limit-warning');
            if (warning) warning.remove();
            
            // Add caution message if not already present
            if (!form.querySelector('.word-limit-caution')) {
                const cautionDiv = document.createElement('div');
                cautionDiv.className = 'alert alert-warning mt-2 word-limit-caution';
                cautionDiv.innerHTML = `<i class="bi bi-exclamation-circle me-2"></i>Approaching ${WORD_LIMIT} word limit.`;
                counter.parentNode.after(cautionDiv);
            }
        }
    } else {
        counter.classList.remove('text-warning', 'text-danger');
        counter.classList.add('text-muted');
        
        // Enable submit button
        const form = counter.closest('form');
        if (form) {
            const submitButtons = form.querySelectorAll('button[type="submit"]');
            submitButtons.forEach(button => {
                button.disabled = false;
                button.title = '';
            });
            
            // Remove warning and caution if present
            const warning = form.querySelector('.word-limit-warning');
            if (warning) warning.remove();
            
            const caution = form.querySelector('.word-limit-caution');
            if (caution) caution.remove();
        }
    }
}

/**
 * Initialize word count functionality for a TinyMCE editor
 * @param {string} editorId - The ID of the TinyMCE editor
 * @param {string} counterId - The ID of the counter element
 */
function initWordCountLimit(editorId = 'id_text_content', counterId = 'word-count') {
    // Check if counter element exists
    const counter = document.getElementById(counterId);
    if (!counter) {
        console.warn(`Word count element with ID "${counterId}" not found`);
        return;
    }
    
    // Set up interval to check for TinyMCE initialization
    const checkInterval = setInterval(() => {
        if (typeof tinymce !== 'undefined' && tinymce.get(editorId)) {
            // Clear interval once TinyMCE is initialized
            clearInterval(checkInterval);
            
            // Initial count
            updateWordCount(editorId, counterId);
            
            // Add event listeners for content changes
            const editor = tinymce.get(editorId);
            editor.on('keyup', () => updateWordCount(editorId, counterId));
            editor.on('change', () => updateWordCount(editorId, counterId));
            
            // Add setup to TinyMCE to enforce limit on paste
            editor.on('PastePreProcess', function(e) {
                // Get current content and calculate word count with pasted content
                const currentContent = editor.getContent();
                const combinedContent = currentContent + e.content;
                const textOnly = combinedContent.replace(/<[^>]*>/g, ' ');
                const words = textOnly.split(/\s+/).filter(word => word.length > 0);
                
                if (words.length > WORD_LIMIT) {
                    // Alert user that paste would exceed limit
                    alert(`Pasting this content would exceed the ${WORD_LIMIT} word limit. The content has been truncated.`);
                    
                    // Truncate the pasted content to fit within limit
                    const currentWords = currentContent.replace(/<[^>]*>/g, ' ')
                        .split(/\s+/).filter(word => word.length > 0);
                    const remainingWords = WORD_LIMIT - currentWords.length;
                    
                    if (remainingWords <= 0) {
                        e.content = ''; // No room for more content
                    } else {
                        // Extract only the allowed number of words
                        const pastedWords = e.content.replace(/<[^>]*>/g, ' ')
                            .split(/\s+/).filter(word => word.length > 0);
                        const truncatedWords = pastedWords.slice(0, remainingWords);
                        e.content = truncatedWords.join(' ');
                    }
                }
            });
            
            // Add form submit handler to prevent submission if over limit
            const form = counter.closest('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const wordCount = countTinyMCEWords(editorId);
                    if (wordCount > WORD_LIMIT) {
                        e.preventDefault();
                        alert(`Your content exceeds the ${WORD_LIMIT} word limit. Please shorten your text to submit.`);
                        return false;
                    }
                    // Save TinyMCE content to form field before submission
                    editor.save();
                });
            }
        }
    }, 500);
    
    // Timeout after 10 seconds to prevent infinite checking
    setTimeout(() => clearInterval(checkInterval), 10000);
}

// Initialize on DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    // Default initialization for standard editor ID
    initWordCountLimit('id_text_content', 'word-count');
    
    // For multiple editors on a page, you can call initWordCountLimit with different IDs
});

// Export functions for use in other scripts
window.countTinyMCEWords = countTinyMCEWords;
window.updateWordCount = updateWordCount;
window.initWordCountLimit = initWordCountLimit;
