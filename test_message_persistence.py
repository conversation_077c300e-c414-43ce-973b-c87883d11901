import os
import sys
import django
import uuid

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from game.models import GameSession, Message, Company

# Display messages for a user
def display_user_messages(username):
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        print(f"User {username} does not exist")
        return
    
    try:
        game_session = GameSession.objects.get(user=user)
    except GameSession.DoesNotExist:
        print(f"No game session found for user {username}")
        return
    
    messages = Message.objects.filter(game_session=game_session).order_by('timestamp')
    
    print(f"\n=== Messages for {username} ===")
    print(f"Total messages: {messages.count()}")
    print(f"Current role: {game_session.current_role}")
    print(f"Performance score: {game_session.performance_score}")
    print(f"Challenges completed: {game_session.challenges_completed}")
    print(f"Completed roles: {game_session.get_completed_roles()}")
    
    # Display a sample of messages
    print("\nSample messages:")
    print(f"{'Sender':<15} {'Type':<12} {'Task ID':<15} {'Message Preview':<50}")
    print("-" * 90)
    
    # Group messages by task_id
    task_messages = {}
    for message in messages:
        task_id = message.task_id or "none"
        if task_id not in task_messages:
            task_messages[task_id] = []
        task_messages[task_id].append(message)
    
    # Display one message from each task
    for task_id, msgs in task_messages.items():
        if task_id == "none":
            # For non-task messages, show a few examples
            for i, msg in enumerate(msgs[:3]):
                message_type = "Challenge" if msg.is_challenge else "Regular"
                if msg.is_promotion:
                    message_type = "Promotion"
                
                # Truncate text for display
                text_preview = msg.text[:47] + "..." if len(msg.text) > 50 else msg.text
                
                print(f"{msg.sender:<15} {message_type:<12} {msg.task_id or 'N/A':<15} {text_preview:<50}")
        else:
            # For task messages, show just one example
            msg = msgs[0]
            message_type = "Challenge" if msg.is_challenge else "Regular"
            if msg.is_promotion:
                message_type = "Promotion"
            
            # Truncate text for display
            text_preview = msg.text[:47] + "..." if len(msg.text) > 50 else msg.text
            
            print(f"{msg.sender:<15} {message_type:<12} {msg.task_id or 'N/A':<15} {text_preview:<50}")
    
    return messages.count()

# Add a test message to a user's game session
def add_test_message(username):
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        print(f"User {username} does not exist")
        return
    
    try:
        game_session = GameSession.objects.get(user=user)
    except GameSession.DoesNotExist:
        print(f"No game session found for user {username}")
        return
    
    # Create a test message
    message = Message.objects.create(
        game_session=game_session,
        message_id=str(uuid.uuid4()),
        sender="system",
        text=f"Test message added at {django.utils.timezone.now()}",
        html=f"<p>Test message added at {django.utils.timezone.now()}</p>",
        is_challenge=False,
        is_markdown=True
    )
    
    print(f"Added test message to {username}'s game session: {message.text}")
    return message

# Main function
def main():
    print("=== Testing Message Persistence ===")
    
    # Display messages for each test user
    for username in ["testuser1", "testuser2", "testuser3"]:
        message_count = display_user_messages(username)
        
        # Add a test message to each user
        if message_count is not None:
            add_test_message(username)
            
            # Display messages again to verify the new message was added
            display_user_messages(username)

if __name__ == "__main__":
    main()
