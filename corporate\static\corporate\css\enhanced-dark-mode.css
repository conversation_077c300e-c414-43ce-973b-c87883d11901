/* Enhanced Dark Mode for Corporate Prompt Master
 * A sleek, professional dark theme with improved visual hierarchy and modern aesthetics
 */

:root {
  /* Modern color palette */
  --dark-bg-primary: #121212;
  --dark-bg-secondary: #1e1e1e;
  --dark-bg-elevated: #252525;
  --dark-bg-accent: #2d2d2d;
  
  /* Gradient backgrounds */
  --dark-gradient-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  --dark-gradient-header: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  --dark-gradient-card: linear-gradient(145deg, #1e1e1e 0%, #2a2a2a 100%);
  
  /* Accent colors */
  --accent-blue: #3a86ff;
  --accent-blue-hover: #2d6cdb;
  --accent-purple: #8b5cf6;
  --accent-teal: #0ea5e9;
  --accent-green: #10b981;
  
  /* Text colors */
  --text-primary: #ffffff;
  --text-secondary: #e0e0e0;
  --text-muted: #a0a0a0;
  
  /* Border colors */
  --border-subtle: rgba(255, 255, 255, 0.1);
  --border-strong: rgba(255, 255, 255, 0.15);
  
  /* Shadow effects */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
  
  /* Glow effects */
  --glow-blue: 0 0 15px rgba(58, 134, 255, 0.5);
  --glow-purple: 0 0 15px rgba(139, 92, 246, 0.5);
}

/* Base styles for dark mode */
.dark-mode {
  --background-color: var(--dark-bg-primary);
  --card-bg: var(--dark-bg-secondary);
  --footer-bg: var(--dark-bg-secondary);
  --text-color: var(--text-primary);
  --text-muted: var(--text-secondary);
  --primary-color: var(--accent-blue);
  --primary-light: var(--accent-blue);
  --border-color: var(--border-subtle);
}

/* Body background with subtle texture */
.dark-mode body {
  background-color: var(--dark-bg-primary);
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cg fill-rule='evenodd'%3E%3Cg fill='%23232323' fill-opacity='0.4'%3E%3Cpath opacity='.5' d='M96 95h4v1h-4v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4h-9v4h-1v-4H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15v-9H0v-1h15V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h9V0h1v15h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9h4v1h-4v9zm-1 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm9-10v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-10 0v-9h-9v9h9zm-9-10h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9zm10 0h9v-9h-9v9z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  color: var(--text-primary);
}

/* Enhanced header/navbar */
.dark-mode .navbar {
  background: var(--dark-gradient-header) !important;
  border-bottom: 1px solid var(--border-strong);
  box-shadow: var(--shadow-md);
  padding: 0.8rem 1.5rem;
}

.dark-mode .navbar-brand {
  font-weight: 600;
  letter-spacing: 0.5px;
}

.dark-mode .navbar-dark .navbar-nav .nav-link {
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.dark-mode .navbar-dark .navbar-nav .nav-link:hover {
  color: var(--text-primary);
  background-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .navbar-dark .navbar-nav .nav-link.active {
  color: var(--accent-blue);
  background-color: rgba(58, 134, 255, 0.15);
}

/* Hero section with gradient background */
.dark-mode .jumbotron.bg-primary {
  background: var(--dark-gradient-primary) !important;
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-strong);
  position: relative;
  overflow: hidden;
  padding: 4rem 2rem !important;
}

/* Add subtle animated gradient overlay to hero */
.dark-mode .jumbotron.bg-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(58, 134, 255, 0.15), rgba(139, 92, 246, 0.15));
  opacity: 0.6;
  z-index: 0;
}

/* Add subtle pattern overlay to hero */
.dark-mode .jumbotron.bg-primary::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
  z-index: 0;
}

/* Ensure hero content is above overlays */
.dark-mode .jumbotron .container {
  position: relative;
  z-index: 1;
}

/* Enhanced hero text */
.dark-mode .jumbotron h1 {
  font-weight: 700;
  letter-spacing: -0.5px;
  margin-bottom: 1.5rem;
  font-size: 3rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dark-mode .jumbotron .lead {
  font-size: 1.25rem;
  font-weight: 400;
  margin-bottom: 2rem;
  opacity: 0.9;
}

/* Enhanced buttons */
.dark-mode .btn-light {
  background: rgba(255, 255, 255, 0.9);
  border: none;
  color: #16213e;
  font-weight: 600;
  padding: 0.6rem 1.5rem;
  border-radius: 6px;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s ease;
}

.dark-mode .btn-light:hover {
  background: #ffffff;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.dark-mode .btn-outline-light {
  border: 2px solid rgba(255, 255, 255, 0.7);
  color: #ffffff;
  font-weight: 600;
  padding: 0.6rem 1.5rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.dark-mode .btn-outline-light:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: #ffffff;
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

/* Enhanced cards */
.dark-mode .card {
  background: var(--dark-gradient-card);
  border: 1px solid var(--border-strong);
  border-radius: 8px;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  overflow: hidden;
}

.dark-mode .card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
  border-color: var(--accent-blue);
}

.dark-mode .card-body {
  padding: 2rem;
}

/* Card icons */
.dark-mode .card .fa-4x {
  color: var(--accent-blue);
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.dark-mode .card:hover .fa-4x {
  transform: scale(1.1);
  color: var(--accent-teal);
}

/* Card headings */
.dark-mode .card h3 {
  font-weight: 700;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

/* Card buttons */
.dark-mode .card .btn-outline-primary {
  border: 2px solid var(--accent-blue);
  color: var(--accent-blue);
  font-weight: 600;
  padding: 0.5rem 1.25rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  margin-top: 1.5rem;
}

.dark-mode .card .btn-outline-primary:hover {
  background: var(--accent-blue);
  color: white;
}

/* Section headings */
.dark-mode h2, .dark-mode h3 {
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: var(--text-primary);
}

.dark-mode .lead {
  color: var(--text-secondary);
  font-size: 1.15rem;
  margin-bottom: 2rem;
}

/* Enhanced accordion */
.dark-mode .accordion-item {
  background-color: var(--dark-bg-elevated);
  border: 1px solid var(--border-strong);
  margin-bottom: 0.5rem;
  border-radius: 6px;
  overflow: hidden;
}

.dark-mode .accordion-button {
  background-color: var(--dark-bg-elevated);
  color: var(--text-primary);
  font-weight: 600;
  padding: 1rem 1.25rem;
}

.dark-mode .accordion-button:not(.collapsed) {
  background-color: var(--accent-blue);
  color: white;
}

.dark-mode .accordion-button:focus {
  box-shadow: none;
  border-color: var(--accent-blue);
}

.dark-mode .accordion-body {
  background-color: var(--dark-bg-accent);
  color: var(--text-secondary);
  padding: 1.25rem;
}

/* Lists */
.dark-mode ul li, .dark-mode ol li {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}
