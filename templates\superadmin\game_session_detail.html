{% extends 'superadmin/base_superadmin.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block superadmin_content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{% url 'superadmin:game_session_list' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="bi bi-arrow-left"></i> {% trans "Back to Game Sessions" %}
        </a>
    </div>

    <!-- Game Session Info Card -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "Game Session Information" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">{% trans "ID" %}</th>
                                <td>{{ game_session.id }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "User" %}</th>
                                <td>{{ game_session.user.username }} ({{ game_session.user.email }})</td>
                            </tr>
                            <tr>
                                <th>{% trans "Company" %}</th>
                                <td>{{ game_session.company.name }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Status" %}</th>
                                <td>
                                    {% if game_session.game_completed %}
                                    <span class="badge bg-info">{% trans "Completed" %}</span>
                                    {% else %}
                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% trans "Created" %}</th>
                                <td>{{ game_session.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Last Updated" %}</th>
                                <td>{{ game_session.updated_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Current Role" %}</th>
                                <td>{{ game_session.current_role }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Current Task" %}</th>
                                <td>{{ game_session.current_task }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Current Manager" %}</th>
                                <td>{{ game_session.current_manager }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Performance Score" %}</th>
                                <td>{{ game_session.performance_score }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Challenges Completed" %}</th>
                                <td>{{ game_session.challenges_completed }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Role Challenges Completed" %}</th>
                                <td>{{ game_session.role_challenges_completed }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "User Leaderboard Entries" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>{% trans "Leaderboard" %}</th>
                                    <th>{% trans "Score" %}</th>
                                    <th>{% trans "Rank" %}</th>
                                    <th>{% trans "Date" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in leaderboard_entries %}
                                <tr>
                                    <td>{{ entry.leaderboard.name }}</td>
                                    <td>{{ entry.score }}</td>
                                    <td>{{ entry.rank }}</td>
                                    <td>{{ entry.recorded_at|date:"Y-m-d" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">{% trans "No leaderboard entries found." %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Messages Card -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Game Messages" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th style="width: 15%">{% trans "Timestamp" %}</th>
                            <th style="width: 15%">{% trans "Type" %}</th>
                            <th>{% trans "Content" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for message in messages %}
                        <tr>
                            <td>{{ message.timestamp|date:"Y-m-d H:i:s" }}</td>
                            <td>
                                {% if message.is_user %}
                                <span class="badge bg-primary">{% trans "User" %}</span>
                                {% else %}
                                <span class="badge bg-secondary">{% trans "System" %}</span>
                                {% endif %}
                            </td>
                            <td>{{ message.content }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="3" class="text-center">{% trans "No messages found." %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock superadmin_content %}