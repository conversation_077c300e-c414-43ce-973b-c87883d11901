/* Dark Mode Improvements for Corporate Prompt Master
 * This file enhances contrast for better readability in dark mode
 */

/* Improved dark mode variables with better contrast */
.dark-mode {
    /* Brighter text colors for better contrast */
    --text-color: #ffffff;
    --text-muted: #cccccc;

    /* Darker backgrounds for better contrast */
    --background-color: #121212;
    --card-bg: #1e1e1e;
    --footer-bg: #1a1a1a;

    /* Brighter accent colors */
    --primary-color: #5c94f0;
    --primary-light: #7ca8ff;

    /* Improved border colors */
    --border-color: #444444;

    /* Improved input colors */
    --input-bg: #2a2a2a;
    --input-border: #555555;
}

/* Ensure body text has proper contrast */
.dark-mode body {
    color: var(--text-color);
    background-color: var(--background-color);
}

/* Improve contrast for cards */
.dark-mode .card {
    background-color: #ffffff;
    border-color: #dddddd;
    color: #000000;
}

/* Improve contrast for card headers with primary background */
.dark-mode .card-header.bg-primary {
    background-color: #0d6efd !important; /* Brighter blue for better contrast */
    color: #ffffff;
    border-bottom: 1px solid #0a58ca;
    font-weight: 600;
}

/* Improve contrast for jumbotron */
.dark-mode .jumbotron.bg-primary {
    background-color: #1a4b9c !important;
    color: #ffffff;
}

/* Improve contrast for buttons */
.dark-mode .btn-outline-primary {
    color: var(--primary-light);
    border-color: var(--primary-light);
}

.dark-mode .btn-outline-primary:hover {
    background-color: var(--primary-color);
    color: #ffffff;
}

/* Fix contrast for guest/login buttons */
.dark-mode .btn-primary {
    background-color: #2d62c9 !important; /* Darker blue */
    color: #ffffff !important; /* Pure white text */
    border-color: #2d62c9 !important;
    font-weight: 500; /* Slightly bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3); /* Text shadow for better readability */
}

.dark-mode .btn-primary:hover {
    background-color: #3a70d8 !important; /* Slightly lighter on hover */
    border-color: #3a70d8 !important;
}

.dark-mode .btn-light {
    background-color: #e0e0e0 !important; /* Light gray background */
    color: #1a1a1a !important; /* Very dark text */
    border-color: #d0d0d0 !important;
}

.dark-mode .btn-light:hover {
    background-color: #f0f0f0 !important;
    color: #000000 !important;
}

/* Fix contrast for sidebar buttons */
.dark-mode .sidebar .btn-primary {
    background-color: #2d62c9 !important;
    color: #ffffff !important;
}

/* Fix contrast specifically for the Play as Guest and Login buttons */
.dark-mode .alert-info .btn-primary,
.dark-mode .alert-info .btn-sm.btn-primary {
    background-color: #ffffff !important;
    color: #1a4b9c !important;
    border-color: #ffffff !important;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Fix contrast for the Start Game button in card footer */
.dark-mode .card-footer .btn-primary {
    background-color: #2d62c9 !important;
    color: #ffffff !important;
    border-color: #2d62c9 !important;
    font-weight: 600;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
}

.dark-mode .alert-info .btn-primary:hover,
.dark-mode .alert-info .btn-sm.btn-primary:hover {
    background-color: #f0f0f0 !important;
    color: #0d3b8c !important;
}

.dark-mode .alert-info .btn-outline-primary {
    background-color: transparent !important;
    color: #ffffff !important;
    border-color: #ffffff !important;
    font-weight: 500;
}

.dark-mode .alert-info .btn-outline-primary:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: #ffffff !important;
}

/* Improve contrast for tables */
.dark-mode .table {
    color: #000000 !important;
    background-color: #ffffff !important;
}

.dark-mode .table thead th {
    border-bottom-color: #0a58ca !important;
    color: #ffffff !important;
    background-color: #0d6efd !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;
    padding: 12px 8px !important;
}

.dark-mode .table tbody td {
    color: #000000 !important;
}

/* Force all text in tables to be black except headers */
.dark-mode .table tbody * {
    color: #000000 !important;
}

/* Ensure table headers are white on blue */
.dark-mode .table thead th {
    color: #ffffff !important;
}

/* Specific override for user names in tables */
.dark-mode .table .user-name {
    color: #000000 !important;
    font-weight: bold !important;
    font-size: 14px !important;
}

/* Specific override for user handles in tables */
.dark-mode .table .user-handle {
    color: #333333 !important;
    font-size: 12px !important;
}

.dark-mode .table-hover tbody tr:hover {
    background-color: #f0f0f0;
}

.dark-mode .table-primary,
.dark-mode .table-primary > td,
.dark-mode .table-primary > th {
    background-color: #d0e0ff;
    color: #000000;
}

/* Improve contrast for specific table elements */
.dark-mode .table .user-handle {
    color: #333333 !important;
}

.dark-mode .table .text-muted {
    color: #555555 !important;
}

/* Improve contrast for accordion */
.dark-mode .accordion-item {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

.dark-mode .accordion-button {
    background-color: #2a2a2a;
    color: var(--text-color);
    border-color: var(--border-color);
}

.dark-mode .accordion-button:not(.collapsed) {
    background-color: #1a4b9c;
    color: #ffffff;
}

.dark-mode .accordion-button:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(92, 148, 240, 0.25);
}

.dark-mode .accordion-button::after {
    filter: invert(1);
}

.dark-mode .accordion-body {
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* Improve contrast for footer */
.dark-mode .footer {
    background-color: var(--footer-bg) !important;
    border-top: 1px solid var(--border-color);
}

.dark-mode .footer .text-muted {
    color: #cccccc !important;
}

/* Improve contrast for headings */
.dark-mode h1, .dark-mode h2, .dark-mode h3,
.dark-mode h4, .dark-mode h5, .dark-mode h6,
.dark-mode .h1, .dark-mode .h2, .dark-mode .h3,
.dark-mode .h4, .dark-mode .h5, .dark-mode .h6 {
    color: #ffffff;
}

/* Improve contrast for lead text */
.dark-mode .lead {
    color: #e6e6e6;
}

/* Improve contrast for list items */
.dark-mode ul li, .dark-mode ol li {
    color: #e6e6e6;
}

/* Improve contrast for alerts */
.dark-mode .alert {
    border: 1px solid transparent;
}

.dark-mode .alert-info {
    background-color: #1a3c5c;
    color: #9ccef8;
    border-color: #2a5c8c;
}

.dark-mode .alert-success {
    background-color: #1a472e;
    color: #a3e9c1;
    border-color: #2a674e;
}

.dark-mode .alert-warning {
    background-color: #4d3c10;
    color: #ffeeba;
    border-color: #6d5c30;
}

.dark-mode .alert-danger {
    background-color: #4d1a1a;
    color: #f8b7bd;
    border-color: #6d3a3a;
}

/* Improve contrast for badges */
.dark-mode .badge.bg-light {
    background-color: #e6e6e6 !important;
    color: #212529 !important;
}

.dark-mode .badge.bg-primary {
    background-color: #0d6efd !important;
    color: #ffffff !important;
    font-weight: 600 !important;
}

.dark-mode .badge.bg-secondary {
    background-color: #495057 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid #000000 !important;
}

.dark-mode .badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    border: 1px solid #000000 !important;
}

/* Improve contrast for navbar */
.dark-mode .navbar-dark {
    background-color: #1a4b9c !important;
}

.dark-mode .navbar-dark .navbar-brand,
.dark-mode .navbar-dark .nav-link {
    color: #ffffff !important;
}

.dark-mode .navbar-dark .nav-link:hover {
    color: #ccccff !important;
}

/* Improve contrast for dropdown menus */
.dark-mode .dropdown-menu {
    background-color: #2a2a2a;
    border-color: #444444;
}

.dark-mode .dropdown-item {
    color: #ffffff;
}

.dark-mode .dropdown-item:hover,
.dark-mode .dropdown-item:focus {
    background-color: #1a4b9c;
    color: #ffffff;
}

.dark-mode .dropdown-divider {
    border-top-color: #444444;
}

/* Improve contrast for forms */
.dark-mode .form-control {
    background-color: #2a2a2a;
    border-color: #555555;
    color: #ffffff;
}

.dark-mode .form-control:focus {
    background-color: #333333;
    border-color: var(--primary-color);
    color: #ffffff;
}

.dark-mode .form-control::placeholder {
    color: #aaaaaa;
}

.dark-mode .form-label {
    color: #ffffff;
}

/* Improve contrast for icons */
.dark-mode .text-primary {
    color: var(--primary-light) !important;
}

.dark-mode .fa-4x.text-primary {
    color: var(--primary-light) !important;
}

/* Improve contrast for links */
.dark-mode a {
    color: var(--primary-light);
}

.dark-mode a:hover {
    color: #90b8ff;
}

/* Improve contrast for stat cards */
.dark-mode .stat-card {
    background-color: rgba(92, 148, 240, 0.15);
}

.dark-mode .stat-card-value {
    color: var(--primary-light);
}

.dark-mode .stat-card-label {
    color: #cccccc;
}
