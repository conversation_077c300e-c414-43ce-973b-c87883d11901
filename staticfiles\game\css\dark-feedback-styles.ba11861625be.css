/* Dark Feedback Styles - Enhanced dark theme for feedback containers */

/* Prompt Evaluation Container - Dark Theme */
.prompt-evaluation-container {
    margin-top: 15px;
    padding: 20px;
    border-radius: 8px;
    background-color: #2d333b; /* Dark blue-gray background */
    border: 1px solid #444c56; /* Darker border */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    overflow-y: visible;
    color: #e6edf3; /* Light text for readability */
}

.prompt-evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #444c56;
    padding-bottom: 10px;
}

.prompt-evaluation-label {
    font-weight: bold;
    color: #58a6ff; /* Bright blue for headings */
    font-size: 1.1em;
}

.prompt-evaluation-summary {
    font-size: 0.95em;
    line-height: 1.5;
    color: #e6edf3; /* Light text */
    margin-bottom: 15px;
    padding: 15px;
    background-color: #22272e; /* Slightly darker background for contrast */
    border-radius: 6px;
    border-left: 3px solid #58a6ff; /* Bright blue accent */
}

.prompt-dimensions-container {
    margin-top: 20px;
    margin-bottom: 15px;
    border-top: 1px solid #444c56;
    padding-top: 15px;
}

.prompt-dimensions-label {
    font-weight: bold;
    color: #58a6ff; /* Bright blue for headings */
    margin-bottom: 12px;
}

.dimensions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
    margin-bottom: 15px;
    overflow: visible;
}

.dimension-card {
    background-color: #22272e; /* Darker background for cards */
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    transition: transform 0.2s, box-shadow 0.2s;
    border: 1px solid #444c56; /* Subtle border */
}

.dimension-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    border-color: #58a6ff; /* Highlight border on hover */
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    border-bottom: 1px solid #444c56;
    padding-bottom: 8px;
}

.dimension-name {
    font-weight: bold;
    color: #e6edf3; /* Light text */
    font-size: 1em;
}

.dimension-score {
    font-weight: bold;
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 0.85em;
}

.dimension-score.high {
    background-color: #238636; /* Dark green */
    color: #e6edf3; /* Light text */
}

.dimension-score.medium {
    background-color: #9e6a03; /* Dark amber */
    color: #e6edf3; /* Light text */
}

.dimension-score.low {
    background-color: #da3633; /* Dark red */
    color: #e6edf3; /* Light text */
}

.dimension-feedback {
    font-size: 0.9em;
    color: #c9d1d9; /* Slightly dimmer text */
    margin-bottom: 12px;
    line-height: 1.5;
}

.dimension-suggestions {
    font-size: 0.9em;
    color: #58a6ff; /* Bright blue for tips */
    font-style: italic;
    padding: 8px;
    background-color: rgba(88, 166, 255, 0.1); /* Subtle blue background */
    border-radius: 4px;
    border-left: 2px solid #58a6ff;
}

.prompt-improvement-container {
    background-color: #22272e; /* Darker background */
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #444c56;
}

.prompt-improvement-label {
    font-weight: bold;
    color: #e6edf3; /* Light text */
    margin-bottom: 10px;
    font-size: 1em;
}

.prompt-improvement-list {
    margin: 0;
    padding-left: 25px;
}

.prompt-improvement-list li {
    margin-bottom: 10px;
    color: #c9d1d9; /* Slightly dimmer text */
    font-size: 0.95em;
    line-height: 1.5;
    padding: 3px 0;
}

.prompt-improvement-list li::marker {
    color: #58a6ff; /* Bright blue bullets */
    font-weight: bold;
}

/* Manager Feedback Container - Dark Theme */
.manager-feedback-container {
    margin-top: 20px;
    padding: 20px;
    background-color: #2d333b; /* Dark blue-gray background */
    border-radius: 8px;
    border: 1px solid #444c56;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.manager-feedback-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #444c56;
    padding-bottom: 10px;
}

.manager-feedback-label {
    font-weight: bold;
    color: #f0883e; /* Orange for manager feedback */
    font-size: 1.1em;
}

.manager-feedback-content {
    font-size: 0.95em;
    line-height: 1.6;
    color: #e6edf3; /* Light text */
    padding: 15px;
    background-color: #22272e; /* Slightly darker background */
    border-radius: 6px;
    border-left: 3px solid #f0883e; /* Orange accent */
    white-space: pre-line; /* Preserve line breaks */
}

/* Dark mode compatibility */
html.dark-mode .prompt-evaluation-container,
body.dark-mode .prompt-evaluation-container,
html.dark-mode .manager-feedback-container,
body.dark-mode .manager-feedback-container {
    /* These are already dark, so we don't need to change much for dark mode */
    background-color: #1e1e1e; /* Even darker in dark mode */
    border-color: #333;
}

html.dark-mode .prompt-evaluation-summary,
body.dark-mode .prompt-evaluation-summary,
html.dark-mode .dimension-card,
body.dark-mode .dimension-card,
html.dark-mode .prompt-improvement-container,
body.dark-mode .prompt-improvement-container,
html.dark-mode .manager-feedback-content,
body.dark-mode .manager-feedback-content {
    background-color: #252525; /* Darker background in dark mode */
    border-color: #333;
}

/* Highlight important text */
.manager-feedback-content strong {
    color: #f0883e; /* Orange for emphasis */
    font-weight: bold;
}

/* Highlight score in manager feedback */
.manager-feedback-content:first-line {
    color: #58a6ff; /* Blue for score line */
    font-weight: bold;
}

/* Add subtle hover effect to containers */
.prompt-evaluation-container:hover,
.manager-feedback-container:hover {
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}
