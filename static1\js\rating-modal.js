// Rating Modal Functionality
document.addEventListener('DOMContentLoaded', function() {
    try {
        // Initialize rating modal
        initRatingModal();
        console.log('Rating modal initialized successfully');
    } catch (error) {
        console.error('Error initializing rating modal:', error);
    }
});

function initRatingModal() {
    const ratingModalElement = document.getElementById('ratingModal');
    if (!ratingModalElement) {
        console.log("Rating modal not found on this page.");
        return;
    }

    // Don't try to get an instance at initialization time
    // Just ensure the modal element is properly set up for Bootstrap
    try {
        // Make sure Bootstrap is available
        if (typeof bootstrap === 'undefined') {
            console.warn("Bootstrap not found, modal functionality may be limited");
        }
    } catch (e) {
        console.error("Error checking Bootstrap availability:", e);
    }

    const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
    const submitRatingBtn = ratingModalElement.querySelector('#submitRatingBtn');
    const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
    let ratingAssistantId = null;
    let selectedRating = 0;

    // Set up modal when it's shown
    ratingModalElement.addEventListener('show.bs.modal', function (event) {
        try {
            const button = event.relatedTarget;
            if (!button) {
                console.warn('Modal opened without a relatedTarget button');
                return;
            }

            ratingAssistantId = button.getAttribute('data-assistant-id');
            const assistantName = button.getAttribute('data-assistant-name');
            const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
            const modalAssistantName = ratingModalElement.querySelector('#modalAssistantName');

            console.log('Rating modal opened for assistant:', assistantName, 'ID:', ratingAssistantId);

            if (modalTitle) modalTitle.textContent = `Rate ${assistantName}`;
            if (modalAssistantName) modalAssistantName.textContent = assistantName;

            selectedRating = 0;
            if (submitRatingBtn) submitRatingBtn.disabled = true;
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';
            if (modalStarsContainer) {
                const starButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                if (starButtons && starButtons.length > 0) {
                    starButtons.forEach(btn => {
                        btn.classList.remove('active');
                        const starIcon = btn.querySelector('i');
                        if (starIcon) {
                            starIcon.classList.remove('bi-star-fill', 'text-warning');
                            starIcon.classList.add('bi-star', 'text-secondary');
                        }
                    });
                }
            }
        } catch (error) {
            console.error('Error in modal show event:', error);
        }
    });

    // Handle star clicks
    if (modalStarsContainer) {
        modalStarsContainer.addEventListener('click', function (event) {
            try {
                const starButton = event.target.closest('.modal-star-btn');
                if (!starButton) return;

                const ratingValue = starButton.getAttribute('data-rating-value');
                if (!ratingValue) {
                    console.warn('Star button clicked but no rating value found');
                    return;
                }

                selectedRating = parseInt(ratingValue);
                console.log('Selected rating:', selectedRating);

                if (submitRatingBtn) submitRatingBtn.disabled = false;
                if (modalErrorMsg) modalErrorMsg.style.display = 'none';

                // Update star appearance
                const allStarButtons = modalStarsContainer.querySelectorAll('.modal-star-btn');
                if (allStarButtons && allStarButtons.length > 0) {
                    allStarButtons.forEach(btn => {
                        const starIcon = btn.querySelector('i');
                        if (!starIcon) return;

                        const btnValueAttr = btn.getAttribute('data-rating-value');
                        if (!btnValueAttr) return;

                        const btnValue = parseInt(btnValueAttr);

                        if (btnValue <= selectedRating) {
                            starIcon.classList.remove('bi-star', 'text-secondary');
                            starIcon.classList.add('bi-star-fill', 'text-warning');
                            btn.classList.add('active');
                        } else {
                            starIcon.classList.remove('bi-star-fill', 'text-warning');
                            starIcon.classList.add('bi-star', 'text-secondary');
                            btn.classList.remove('active');
                        }
                    });
                }
            } catch (error) {
                console.error('Error handling star click:', error);
            }
        });
    }

    // Handle submit button click
    if (submitRatingBtn) {
        submitRatingBtn.addEventListener('click', function() {
            try {
                if (!selectedRating || !ratingAssistantId) {
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = 'Please select a rating.';
                        modalErrorMsg.style.display = 'block';
                    }
                    return;
                }

                // Try to get CSRF token from multiple sources
                let csrfToken = null;

                // First try to get it from the rating form
                const ratingForm = document.getElementById('ratingForm');
                if (ratingForm) {
                    const formToken = ratingForm.querySelector('input[name="csrfmiddlewaretoken"]');
                    if (formToken) csrfToken = formToken.value;
                }

                // If not found, try other methods
                if (!csrfToken) {
                    csrfToken = getCsrfToken();
                }

                if (!csrfToken) {
                    console.error('CSRF token not found from any source');
                    if (modalErrorMsg) {
                        modalErrorMsg.textContent = 'CSRF token not found. Please refresh the page.';
                        modalErrorMsg.style.display = 'block';
                    }
                    return;
                }

                console.log('Using CSRF token:', csrfToken.substring(0, 5) + '...');

                // Save original button text for restoration
                const originalButtonText = submitRatingBtn.innerHTML;
                submitRatingBtn.disabled = true;
                submitRatingBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Submitting...';

                // Use the URL from the template if available, otherwise fallback to hardcoded URL
                let rateUrl = '/directory/rate-assistant/' + ratingAssistantId + '/'; // Default fallback with assistant ID
                if (typeof rateAssistantBaseUrl !== 'undefined' && rateAssistantBaseUrl) {
                    rateUrl = rateAssistantBaseUrl + ratingAssistantId + '/';
                }

                // Ensure the URL starts with a slash if it doesn't already
                if (!rateUrl.startsWith('/')) {
                    rateUrl = '/' + rateUrl;
                }

                console.log('Submitting rating to:', rateUrl, 'Assistant ID:', ratingAssistantId, 'Rating:', selectedRating);

                const formData = new URLSearchParams();
                formData.append('rating', selectedRating);

                console.log('Making AJAX request to:', rateUrl);
                console.log('Request payload:', {
                    rating: selectedRating
                });

                // Use fetch API to submit the rating
                fetch(rateUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: formData
                })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', [...response.headers.entries()]);

                    if (!response.ok) {
                        // Log detailed error information
                        console.error(`Rating request failed with status ${response.status}`);
                        console.error(`URL attempted: ${rateUrl}`);
                        console.error(`Request method: POST`);
                        console.error(`Request payload: rating=${selectedRating}`);

                        throw new Error(`HTTP error ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Rating response:', data);

                    if (data.status === 'success') {
                        // Update the rating display
                        const ratingDisplay = document.getElementById(`rating-display-${ratingAssistantId}`);
                        if (ratingDisplay && data.rendered_stars_html) {
                            ratingDisplay.innerHTML = data.rendered_stars_html;
                            console.log('Updated rating display for assistant ID:', ratingAssistantId);
                        }

                        // Close the modal
                        try {
                            let modalClosed = false;

                            // Try the standard Bootstrap way first
                            if (typeof bootstrap !== 'undefined') {
                                const modalInstance = bootstrap.Modal.getInstance(ratingModalElement);
                                if (modalInstance) {
                                    modalInstance.hide();
                                    modalClosed = true;
                                    console.log('Modal closed using Bootstrap instance');
                                }
                            }

                            // If the standard way didn't work, try jQuery if available
                            if (!modalClosed && typeof $ !== 'undefined') {
                                try {
                                    $(ratingModalElement).modal('hide');
                                    modalClosed = true;
                                    console.log('Modal closed using jQuery');
                                } catch (jqueryError) {
                                    console.warn('jQuery modal hide failed:', jqueryError);
                                }
                            }

                            // Last resort: manually remove modal classes and elements
                            if (!modalClosed) {
                                console.warn('Using manual modal closing as fallback');
                                ratingModalElement.classList.remove('show');
                                ratingModalElement.style.display = 'none';
                                document.body.classList.remove('modal-open');

                                // Remove backdrop if it exists
                                const backdrop = document.querySelector('.modal-backdrop');
                                if (backdrop) backdrop.parentNode.removeChild(backdrop);

                                // Re-enable scrolling
                                document.body.style.overflow = '';
                                document.body.style.paddingRight = '';
                            }
                        } catch (modalError) {
                            console.error('All modal closing methods failed:', modalError);
                        }

                        // Show success message
                        const ratingMsg = document.getElementById(`rating-msg-${ratingAssistantId}`);
                        if (ratingMsg) {
                            ratingMsg.textContent = 'Rating submitted successfully!';
                            ratingMsg.style.display = 'inline';
                            setTimeout(() => {
                                ratingMsg.style.display = 'none';
                            }, 3000);
                        } else {
                            // If no rating message element exists, show an alert
                            alert('Rating submitted successfully!');
                        }
                    } else {
                        if (modalErrorMsg) {
                            modalErrorMsg.textContent = data.message || 'Failed to submit rating.';
                            modalErrorMsg.style.display = 'block';
                        }
                    }
                })
                .catch(error => {
                    console.error('Error submitting rating:', error);

                    // Check if the error is a 404 (Not Found) error
                    if (error.message && error.message.includes('404')) {
                        console.error('The rating endpoint URL was not found. Please check the URL configuration.');
                        if (modalErrorMsg) {
                            modalErrorMsg.textContent = 'Rating service is currently unavailable. Please try again later.';
                            modalErrorMsg.style.display = 'block';
                        }
                    } else {
                        if (modalErrorMsg) {
                            modalErrorMsg.textContent = error.message || 'An unexpected error occurred.';
                            modalErrorMsg.style.display = 'block';
                        }
                    }
                })
                .finally(() => {
                    // Always re-enable the button and restore its text
                    if (submitRatingBtn) {
                        submitRatingBtn.disabled = false;
                        submitRatingBtn.innerHTML = originalButtonText || 'Submit Rating';
                    }
                });
            } catch (error) {
                console.error('Error in submit button click handler:', error);
                if (modalErrorMsg) {
                    modalErrorMsg.textContent = error.message || 'An unexpected error occurred.';
                    modalErrorMsg.style.display = 'block';
                }
            }
        });
    }
}

// Helper function to get CSRF token
function getCsrfToken() {
    try {
        // First try to get the token from the form
        const tokenElement = document.querySelector('[name=csrfmiddlewaretoken]');
        if (tokenElement && tokenElement.value) {
            return tokenElement.value;
        }

        // Next try to get it from meta tag
        const metaToken = document.querySelector('meta[name="csrf-token"]');
        if (metaToken && metaToken.getAttribute('content')) {
            return metaToken.getAttribute('content');
        }

        // Try to get from cookie as fallback
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.startsWith('csrftoken=')) {
                return cookie.substring('csrftoken='.length, cookie.length);
            }
        }

        console.warn('CSRF token not found in form, meta tag, or cookies');
        return null;
    } catch (error) {
        console.error('Error getting CSRF token:', error);
        return null;
    }
}
