from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.contrib.auth.models import User
from django.utils import timezone
from django.urls import reverse
from django.http import Http404
from django.conf import settings

from .models import Company, CorporateUser, CompanyInvitation, RegistrationLink
from .forms import CorporateUserInviteForm, BulkInvitationForm, RegistrationLinkForm, CorporateUserRegistrationForm

# Email sending
from django.core.mail import send_mail
from django.template.loader import render_to_string


@login_required
def company_invitations(request):
    """
    View to manage company invitations.
    """
    # Ensure session is created and saved
    if not request.session.session_key:
        request.session.save()

    # Explicitly mark the session as modified to ensure it's saved
    request.session.modified = True

    # Get the user's company
    try:
        corporate_profile = request.user.corporate_profile
        company = corporate_profile.company

        # Ensure active_company_id is set in session
        if 'active_company_id' not in request.session:
            request.session['active_company_id'] = str(company.id)
            request.session.modified = True
    except:
        messages.error(request, "You don't have a company profile.")
        return redirect('corporate:dashboard')

    # Check if user is a company admin
    if not corporate_profile.is_company_admin:
        messages.error(request, "You don't have permission to manage invitations.")
        return redirect('corporate:dashboard')

    # Get all pending invitations for this company
    pending_invitations = CompanyInvitation.objects.filter(
        company=company,
        status=CompanyInvitation.STATUS_PENDING
    ).order_by('-invited_at')

    # Get all registration links for this company
    registration_links = RegistrationLink.objects.filter(
        company=company
    ).order_by('-created_at')

    # Get all pending users
    pending_users = CorporateUser.objects.filter(
        company=company,
        status=CorporateUser.STATUS_PENDING
    ).select_related('user', 'registration_link').order_by('created_at')

    # Handle invitation form
    if request.method == 'POST':
        if 'submit_invite_form' in request.POST:
            invite_form = CorporateUserInviteForm(request.POST)
            if invite_form.is_valid():
                invitation = invite_form.save(company=company, invited_by=request.user)

                # Send invitation email
                send_invitation_email(invitation)

                messages.success(request, f"Invitation sent to {invitation.email}.")
                return redirect('corporate:company_invitations')

        elif 'submit_bulk_invite_form' in request.POST:
            bulk_invite_form = BulkInvitationForm(request.POST)
            if bulk_invite_form.is_valid():
                invitations = bulk_invite_form.save(company=company, invited_by=request.user)

                # Send invitation emails
                for invitation in invitations:
                    send_invitation_email(invitation)

                messages.success(request, f"Sent {len(invitations)} invitation(s).")
                return redirect('corporate:company_invitations')

        elif 'submit_reg_link_form' in request.POST:
            reg_link_form = RegistrationLinkForm(request.POST)
            if reg_link_form.is_valid():
                link = reg_link_form.save(company=company, created_by=request.user)
                messages.success(request, "Registration link created successfully.")
                return redirect('corporate:company_invitations')
    else:
        invite_form = CorporateUserInviteForm()
        bulk_invite_form = BulkInvitationForm()
        reg_link_form = RegistrationLinkForm()

    # Check if there's a POST action for approving or rejecting users
    if request.method == 'POST':
        if 'action' in request.POST and request.POST['action'] == 'approve_user':
            # Handle user approval
            user_id = request.POST.get('user_id')
            if user_id:
                try:
                    corp_user = CorporateUser.objects.get(user_id=user_id, company=company, status=CorporateUser.STATUS_PENDING)
                    corp_user.status = CorporateUser.STATUS_ACTIVE
                    corp_user.save()
                    messages.success(request, f"User {corp_user.user.get_full_name() or corp_user.user.username} has been approved.")

                    # Send notification email to the user
                    try:
                        subject = f"Your account has been approved - {company.name}"
                        message = f"Your account for {company.name} has been approved. You can now log in and access all company features."
                        from django.core.mail import send_mail
                        send_mail(
                            subject,
                            message,
                            settings.DEFAULT_FROM_EMAIL,
                            [corp_user.user.email],
                            fail_silently=True,
                        )
                    except Exception as e:
                        # Log the error but continue
                        import logging
                        logging.error(f"Error sending approval email: {str(e)}")

                    return redirect('corporate:company_invitations')
                except CorporateUser.DoesNotExist:
                    messages.error(request, "User not found or already approved.")

        elif 'action' in request.POST and request.POST['action'] == 'reject_user':
            # Handle user rejection
            user_id = request.POST.get('user_id')
            if user_id:
                try:
                    corp_user = CorporateUser.objects.get(user_id=user_id, company=company, status=CorporateUser.STATUS_PENDING)
                    user_email = corp_user.user.email
                    user_name = corp_user.user.get_full_name() or corp_user.user.username

                    # Delete the corporate profile
                    corp_user.delete()

                    messages.success(request, f"User {user_name} has been rejected.")

                    # Send notification email to the user
                    try:
                        subject = f"Your registration request was declined - {company.name}"
                        message = f"Your request to join {company.name} has been declined by an administrator."
                        from django.core.mail import send_mail
                        send_mail(
                            subject,
                            message,
                            settings.DEFAULT_FROM_EMAIL,
                            [user_email],
                            fail_silently=True,
                        )
                    except Exception as e:
                        # Log the error but continue
                        import logging
                        logging.error(f"Error sending rejection email: {str(e)}")

                    return redirect('corporate:company_invitations')
                except CorporateUser.DoesNotExist:
                    messages.error(request, "User not found or already processed.")

    return render(request, 'corporate/company_invitations.html', {
        'company': company,
        'pending_invitations': pending_invitations,
        'registration_links': registration_links,
        'pending_users': pending_users,
        'invite_form': invite_form,
        'bulk_invite_form': bulk_invite_form,
        'reg_link_form': reg_link_form,
    })


@login_required
def delete_invitation(request, invitation_id):
    """
    Delete a pending invitation.
    """
    # Get the user's company
    try:
        corporate_profile = request.user.corporate_profile
        company = corporate_profile.company
    except:
        messages.error(request, "You don't have a company profile.")
        return redirect('corporate:dashboard')

    # Check if user is a company admin
    if not corporate_profile.is_company_admin:
        messages.error(request, "You don't have permission to manage invitations.")
        return redirect('corporate:dashboard')

    # Get the invitation
    invitation = get_object_or_404(CompanyInvitation, id=invitation_id, company=company)

    # Delete the invitation
    invitation.delete()

    messages.success(request, f"Invitation to {invitation.email} has been deleted.")
    return redirect('corporate:company_invitations')


@login_required
def delete_registration_link(request, token):
    """
    Delete a registration link.
    """
    # Get the user's company
    try:
        corporate_profile = request.user.corporate_profile
        company = corporate_profile.company
    except:
        messages.error(request, "You don't have a company profile.")
        return redirect('corporate:dashboard')

    # Check if user is a company admin
    if not corporate_profile.is_company_admin:
        messages.error(request, "You don't have permission to manage registration links.")
        return redirect('corporate:dashboard')

    # Get the registration link
    link = get_object_or_404(RegistrationLink, token=token, company=company)

    # Delete the link
    link.delete()

    messages.success(request, "Registration link has been deleted.")
    return redirect('corporate:company_invitations')


def accept_invitation(request, token):
    """
    View for accepting an invitation.
    """
    # Get the invitation
    try:
        invitation = CompanyInvitation.objects.get(token=token, status=CompanyInvitation.STATUS_PENDING)
    except CompanyInvitation.DoesNotExist:
        messages.error(request, "Invalid or expired invitation.")
        return redirect('corporate:corporate_login')

    # Check if invitation is expired
    if invitation.is_expired():
        invitation.status = CompanyInvitation.STATUS_EXPIRED
        invitation.save()
        messages.error(request, "This invitation has expired.")
        return redirect('corporate:corporate_login')

    # If user is already logged in
    if request.user.is_authenticated:
        # Check if the logged-in user's email matches the invitation email
        if request.user.email.lower() == invitation.email.lower():
            # Create corporate profile for the user
            try:
                CorporateUser.objects.create(
                    user=request.user,
                    company=invitation.company,
                    job_title=invitation.job_title,
                    department=invitation.department,
                    is_company_admin=invitation.is_admin
                )

                # Mark invitation as accepted
                invitation.status = CompanyInvitation.STATUS_ACCEPTED
                invitation.accepted_at = timezone.now()
                invitation.save()

                messages.success(request, f"You have successfully joined {invitation.company.name}.")
                return redirect('corporate:dashboard')
            except:
                messages.error(request, "An error occurred while creating your company profile.")
                return redirect('corporate:dashboard')
        else:
            messages.error(request, f"This invitation was sent to {invitation.email}, but you're logged in as {request.user.email}.")
            return redirect('corporate:dashboard')

    # If user is not logged in, store invitation token in session and redirect to registration
    request.session['invitation_token'] = token
    return redirect('corporate:register_with_invitation')


def register_with_invitation(request):
    """
    Registration view for users with an invitation.
    """
    # Check if invitation token is in session
    token = request.session.get('invitation_token')
    if not token:
        messages.error(request, "No invitation found.")
        return redirect('corporate:corporate_login')

    # Get the invitation
    try:
        invitation = CompanyInvitation.objects.get(token=token, status=CompanyInvitation.STATUS_PENDING)
    except CompanyInvitation.DoesNotExist:
        messages.error(request, "Invalid or expired invitation.")
        return redirect('corporate:corporate_login')

    # Check if invitation is expired
    if invitation.is_expired():
        invitation.status = CompanyInvitation.STATUS_EXPIRED
        invitation.save()
        messages.error(request, "This invitation has expired.")
        return redirect('corporate:corporate_login')

    # Handle registration form
    if request.method == 'POST':
        form = CorporateUserRegistrationForm(request.POST)
        if form.is_valid():
            # Create user
            user = form.save()

            # Create corporate profile
            CorporateUser.objects.create(
                user=user,
                company=invitation.company,
                job_title=invitation.job_title,
                department=invitation.department,
                is_company_admin=invitation.is_admin
            )

            # Mark invitation as accepted
            invitation.status = CompanyInvitation.STATUS_ACCEPTED
            invitation.accepted_at = timezone.now()
            invitation.save()

            # Store the company ID in the session for use after login
            request.session['pending_company_id'] = str(invitation.company.id)

            messages.success(request, f"Registration successful! You have joined {invitation.company.name}.")
            return redirect('corporate:corporate_login')
    else:
        # Pre-fill email from invitation
        form = CorporateUserRegistrationForm(initial={'email': invitation.email})

    return render(request, 'corporate/register_with_invitation.html', {
        'form': form,
        'invitation': invitation
    })


def register_with_link(request, token):
    """
    Registration view for users with a registration link.
    """
    # Get the registration link
    try:
        link = RegistrationLink.objects.get(token=token)
    except RegistrationLink.DoesNotExist:
        messages.error(request, "Invalid registration link.")
        return redirect('corporate:corporate_login')

    # Check if link is valid
    if not link.is_valid():
        messages.error(request, "This registration link is no longer valid.")
        return redirect('corporate:corporate_login')

    # If user is already logged in
    if request.user.is_authenticated:
        # Check if user already has a corporate profile for this company
        if CorporateUser.objects.filter(user=request.user, company=link.company).exists():
            messages.info(request, f"You are already a member of {link.company.name}.")
            return redirect('corporate:dashboard')

        # Create corporate profile for the user
        try:
            # Determine status based on approval type
            status = CorporateUser.STATUS_ACTIVE
            success_message = f"You have successfully joined {link.company.name}."

            if link.approval_type == RegistrationLink.APPROVAL_ADMIN:
                status = CorporateUser.STATUS_PENDING
                success_message = f"Your request to join {link.company.name} is pending approval by an administrator."

            CorporateUser.objects.create(
                user=request.user,
                company=link.company,
                is_company_admin=(link.intended_role == 'admin'),
                status=status,
                registration_link=link
            )

            # Increment link usage count
            link.uses_count += 1
            link.save()

            # Notify company admins if approval is required
            if link.approval_type == RegistrationLink.APPROVAL_ADMIN:
                # Get all company admins
                admin_users = CorporateUser.objects.filter(
                    company=link.company,
                    is_company_admin=True,
                    status=CorporateUser.STATUS_ACTIVE
                ).values_list('user__email', flat=True)

                if admin_users:
                    # Send notification email to admins
                    subject = f"New user registration pending approval - {link.company.name}"
                    message = f"A new user ({request.user.get_full_name()} - {request.user.email}) has registered and is pending approval."
                    from django.core.mail import send_mail
                    send_mail(
                        subject,
                        message,
                        settings.DEFAULT_FROM_EMAIL,
                        list(admin_users),
                        fail_silently=True,
                    )

            messages.success(request, success_message)
            return redirect('corporate:dashboard')
        except:
            messages.error(request, "An error occurred while creating your company profile.")
            return redirect('corporate:dashboard')

    # If user is not logged in, store link token in session and redirect to registration
    request.session['registration_link_token'] = str(token)
    return redirect('corporate:register_with_link_form')


def register_with_link_form(request):
    """
    Registration form view for users with a registration link.
    """
    # Check if registration link token is in session
    token = request.session.get('registration_link_token')
    if not token:
        messages.error(request, "No registration link found.")
        return redirect('corporate:corporate_login')

    # Get the registration link
    try:
        link = RegistrationLink.objects.get(token=token)
    except RegistrationLink.DoesNotExist:
        messages.error(request, "Invalid registration link.")
        return redirect('corporate:corporate_login')

    # Check if link is valid
    if not link.is_valid():
        messages.error(request, "This registration link is no longer valid.")
        return redirect('corporate:corporate_login')

    # Handle registration form
    if request.method == 'POST':
        form = CorporateUserRegistrationForm(request.POST)
        if form.is_valid():
            # Create user
            user = form.save()

            # Determine status based on approval type
            status = CorporateUser.STATUS_ACTIVE
            success_message = f"Registration successful! You have joined {link.company.name}."

            if link.approval_type == RegistrationLink.APPROVAL_ADMIN:
                status = CorporateUser.STATUS_PENDING
                success_message = f"Registration successful! Your account is pending approval by an administrator of {link.company.name}."

            # Create corporate profile
            CorporateUser.objects.create(
                user=user,
                company=link.company,
                is_company_admin=(link.intended_role == 'admin'),
                status=status,
                registration_link=link
            )

            # Increment link usage count
            link.uses_count += 1
            link.save()

            # Store the company ID in the session for use after login
            request.session['pending_company_id'] = str(link.company.id)

            # Notify company admins if approval is required
            if link.approval_type == RegistrationLink.APPROVAL_ADMIN:
                # Get all company admins
                admin_users = CorporateUser.objects.filter(
                    company=link.company,
                    is_company_admin=True,
                    status=CorporateUser.STATUS_ACTIVE
                ).values_list('user__email', flat=True)

                if admin_users:
                    # Send notification email to admins
                    subject = f"New user registration pending approval - {link.company.name}"
                    message = f"A new user ({user.get_full_name()} - {user.email}) has registered and is pending approval."
                    from django.core.mail import send_mail
                    send_mail(
                        subject,
                        message,
                        settings.DEFAULT_FROM_EMAIL,
                        list(admin_users),
                        fail_silently=True,
                    )

            messages.success(request, success_message)
            return redirect('corporate:corporate_login')
    else:
        form = CorporateUserRegistrationForm()

    return render(request, 'corporate/register_with_link.html', {
        'form': form,
        'link': link
    })


def send_invitation_email(invitation):
    """
    Send invitation email to the invited user.
    """
    subject = f"Invitation to join {invitation.company.name}"

    # Build the accept URL
    accept_url = settings.BASE_URL + reverse('corporate:accept_invitation', kwargs={'token': invitation.token})

    # Render email templates
    html_message = render_to_string('corporate/email/invitation.html', {
        'invitation': invitation,
        'accept_url': accept_url,
        'expiry_days': 7,
    })

    plain_message = render_to_string('corporate/email/invitation.txt', {
        'invitation': invitation,
        'accept_url': accept_url,
        'expiry_days': 7,
    })

    # Send the email
    try:
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[invitation.email],
            html_message=html_message,
            fail_silently=False,
        )
        return True
    except Exception as e:
        print(f"Error sending invitation email: {str(e)}")
        return False
