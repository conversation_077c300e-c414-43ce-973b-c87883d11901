{% load static rating_tags %}

<div class="card-header bg-light d-flex align-items-center">
    <!-- Company Logo -->
    <img src="/media/company_logos/download_7.jpeg" alt="Kisay Logo" class="company-header-logo me-3">
    
    <!-- Company Name -->
    <h1 class="h3 mb-0 me-auto">Kisay</h1>
    
    <!-- Rating and Actions -->
    <div class="d-flex align-items-center">
        <!-- Rating Display -->
        <div class="me-3">
            <div id="rating-display-1">
                {% render_stars 4.5 12 %}
            </div>
            {% if user.is_authenticated %}
                <button type="button" class="btn btn-sm btn-link text-decoration-none p-0 mt-1"
                        data-bs-toggle="modal" data-bs-target="#ratingModal"
                        data-company-id="1"
                        data-company-name="Kisay">
                    <i class="bi bi-star me-1"></i>Rate
                </button>
            {% endif %}
        </div>
        
        <!-- Like Button -->
        {% if user.is_authenticated %}
            <button
                class="like-button btn btn-sm p-1 text-secondary me-2"
                data-item-id="1"
                data-item-type="company"
                title="Add to Favorites"
                style="background: none; border: none;">
                <i class="bi bi-heart"></i>
            </button>
        {% endif %}
        
        <!-- QR Code Button -->
        <button type="button" class="btn btn-sm btn-outline-secondary me-1" id="company-qr-code-btn" 
                title="Show Company QR Code" data-bs-toggle="modal" data-bs-target="#companyQrCodeModal" 
                data-qr-url="/media/company_qrcodes/company_qr_kisay.png"> 
            <i class="bi bi-qr-code"></i>
        </button>
        
        <!-- Share Button -->
        <button type="button" class="btn btn-sm btn-outline-secondary" id="copy-company-url-btn" 
                title="Copy Company Profile URL">
            <i class="bi bi-share me-1"></i> Share URL 
        </button>
    </div>
</div>

<!-- Rating Modal -->
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate <span id="modalCompanyName">Kisay</span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="ratingForm">
          {% csrf_token %}
          <p class="mb-3">How would you rate your experience with <strong id="modalCompanyNameInner">Kisay</strong>?</p>
          
          <div class="modal-stars text-center mb-4">
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="1">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="2">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="3">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="4">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="5">
              <i class="bi bi-star text-secondary"></i>
            </button>
          </div>
          
          <div id="modalErrorMsg" class="alert alert-danger" style="display: none;"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
      </div>
    </div>
  </div>
</div>

<!-- Folder Options Modal -->
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="folderOptionsModalLabel">Add to Favorites</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Add <strong id="modalFolderNameItemName">Kisay</strong> to favorites:</p>

        <!-- Option 1: Save without folder -->
        <div class="d-grid mb-3">
            <button type="button" class="btn btn-outline-primary text-start" id="saveNoFolderBtn">
                <i class="bi bi-bookmark-heart me-2"></i>Save (Uncategorized)
            </button>
        </div>

        <hr>

        <!-- Option 2: Add to existing folder -->
        <div id="existingFoldersSection" class="mb-3" style="display: none;">
            <label for="selectFolder" class="form-label small mb-1">Add to existing folder:</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-folder-symlink"></i></span>
                <select class="form-select" id="selectFolder">
                    <option selected disabled value="">Choose folder...</option>
                    <!-- Options will be populated by JS -->
                </select>
                <button class="btn btn-primary" type="button" id="addToFolderBtn" disabled>
                    <i class="bi bi-plus-lg"></i> Add
                </button>
            </div>
        </div>

        <!-- Option 3: Create new folder -->
        <div>
            <label for="newFolderName" class="form-label small mb-1">Or create a new folder:</label>
            <div class="input-group">
                 <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
                <input type="text" class="form-control" id="newFolderName" placeholder="New folder name...">
                <button class="btn btn-success" type="button" id="createFolderAndSaveBtn" disabled>
                   <i class="bi bi-check-lg"></i> Create & Save
                </button>
            </div>
        </div>

        <div id="folderModalErrorMsg" class="text-danger small mt-3" style="display: none;"></div>
      </div>
    </div>
  </div>
</div>
