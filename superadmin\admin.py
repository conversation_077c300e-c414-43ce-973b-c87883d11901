from django.contrib import admin
from .models import SiteConfiguration

@admin.register(SiteConfiguration)
class SiteConfigurationAdmin(admin.ModelAdmin):
    list_display = ('site_title', 'help_url', 'google_analytics_id')
    fieldsets = (
        ('Help Settings', {
            'fields': ('help_url',)
        }),
        ('SEO Settings', {
            'fields': ('site_title', 'site_description', 'site_keywords'),
            'description': 'Configure site-wide SEO settings'
        }),
        ('Analytics & Verification', {
            'fields': ('google_analytics_id', 'google_verification_tag'),
            'description': 'Configure Google Analytics and Search Console verification'
        }),
    )

    def has_add_permission(self, request):
        # Check if an instance already exists
        if SiteConfiguration.objects.exists():
            return False
        return super().has_add_permission(request)

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of the singleton instance
        return False
