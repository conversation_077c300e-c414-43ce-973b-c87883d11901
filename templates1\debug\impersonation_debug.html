{% if debug_is_impersonating %}
<div style="position: fixed; bottom: 20px; left: 20px; background-color: #333; color: #fff; padding: 10px; border-radius: 5px; z-index: 10000; max-width: 400px; font-family: monospace; font-size: 12px; opacity: 0.9;">
    <h4 style="margin: 0 0 10px 0; color: #ff9800;">Impersonation Debug Info</h4>
    <div style="max-height: 300px; overflow-y: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            {% for key, value in debug_impersonation_info.items %}
            <tr>
                <td style="padding: 3px; border-bottom: 1px solid #555; color: #8bc34a;">{{ key }}</td>
                <td style="padding: 3px; border-bottom: 1px solid #555;">{{ value }}</td>
            </tr>
            {% endfor %}
        </table>
    </div>
    <div style="margin-top: 10px; text-align: center;">
        <a href="{% url 'debug_impersonation' %}" target="_blank" style="color: #ff9800; text-decoration: underline;">View Full Debug Info</a>
    </div>
</div>
{% endif %}
