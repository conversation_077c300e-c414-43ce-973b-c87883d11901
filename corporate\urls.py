from django.urls import path
from . import views
from . import invitation_views

app_name = 'corporate'

urlpatterns = [
    # Home URL
    path('', views.home, name='home'),

    # Authentication URLs
    path('login/', views.corporate_login, name='corporate_login'),
    path('logout/', views.corporate_logout, name='corporate_logout'),
    path('register/', views.corporate_register, name='corporate_register'),

    # Dashboard URLs
    path('dashboard/', views.corporate_dashboard, name='corporate_dashboard'),
    path('profile/edit/', views.edit_profile, name='edit_profile'),
    path('company/edit/', views.edit_company, name='edit_company'),
    path('games/', views.game_list, name='game_list'),
    path('company/switch/', views.company_switch, name='company_switch'),
    path('company/create/', views.create_company, name='create_company'),

    # Leaderboard URLs
    path('leaderboard/', views.leaderboard, name='leaderboard'),

    # Certificate URLs
    path('certificates/', views.view_certificates, name='view_certificates'),
    path('certificates/<uuid:certificate_id>/download/', views.download_certificate, name='download_certificate'),
    path('verify-certificate/', views.verify_certificate_view, name='verify_certificate'),

    # Admin URLs
    path('users/', views.company_users, name='company_users'),
    path('users/progress/', views.user_progress, name='user_progress'),
    path('users/<int:user_id>/progress/', views.user_progress, name='user_progress_detail'),

    # Invitation URLs
    path('invitations/', invitation_views.company_invitations, name='company_invitations'),
    path('invitations/<int:invitation_id>/delete/', invitation_views.delete_invitation, name='delete_invitation'),
    path('invitations/accept/<str:token>/', invitation_views.accept_invitation, name='accept_invitation'),
    path('register/invitation/', invitation_views.register_with_invitation, name='register_with_invitation'),

    # Registration Link URLs
    path('registration-links/<uuid:token>/delete/', invitation_views.delete_registration_link, name='delete_registration_link'),
    path('join/<uuid:token>/', invitation_views.register_with_link, name='register_with_link'),
    path('register/link/', invitation_views.register_with_link_form, name='register_with_link_form'),

    # API URLs
    path('api/update-leaderboard/', views.update_leaderboard, name='update_leaderboard'),
]
