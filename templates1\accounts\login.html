{% extends 'base/layout.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Log In - 24seven{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <img src="{% static 'img/24seven-logo.svg' %}" alt="24seven" class="mb-4" height="48">
                        <h2 class="h4">Welcome Back</h2>
                        <p class="text-muted">Log in to access your company's workspace</p>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                Username or Email
                            </label>
                            {% render_field form.username class="form-control" placeholder="Enter your username or email" %}
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.username.errors|join:", " }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <label for="{{ form.password.id_for_label }}" class="form-label">
                                    Password
                                </label>
                                <a href="{% url 'accounts:password_reset' %}" class="small text-muted">
                                    Forgot Password?
                                </a>
                            </div>
                            {% render_field form.password class="form-control" placeholder="Enter your password" %}
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password.errors|join:", " }}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.captcha %}
                            <div class="mb-3">
                                {{ form.captcha }}
                            </div>
                        {% endif %}

                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                Log In
                            </button>
                        </div>

                        <input type="hidden" name="next" value="{{ next }}">
                    </form>

                    <div class="text-center">
                        <p class="text-muted">
                            Don't have an account?
                            <a href="{% url 'accounts:register' %}" class="text-decoration-none">
                                Sign up
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- SSO Options -->
            {% if social_auth_providers %}
                <div class="card shadow-sm mt-4">
                    <div class="card-body p-4">
                        <p class="text-center text-muted small mb-3">Or log in with</p>
                        <div class="d-grid gap-2">
                            {% for provider in social_auth_providers %}
                                <a href="{% url 'social:begin' provider.id %}"
                                   class="btn btn-outline-secondary">
                                    <i class="bi bi-{{ provider.icon }} me-2"></i>
                                    {{ provider.name }}
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Help Links -->
            <div class="text-center mt-4">
                <div class="btn-group">
                    <a href="{% url 'about' %}" class="btn btn-link btn-sm text-muted">About</a>
                    <a href="{% url 'privacy' %}" class="btn btn-link btn-sm text-muted">Privacy</a>
                    <a href="{% url 'terms' %}" class="btn btn-link btn-sm text-muted">Terms</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus username field
    document.getElementById('{{ form.username.id_for_label }}').focus();

    // Enable password toggle
    const togglePassword = document.createElement('button');
    togglePassword.type = 'button';
    togglePassword.className = 'btn btn-outline-secondary';
    togglePassword.innerHTML = '<i class="bi bi-eye"></i>';
    togglePassword.onclick = function() {
        const password = document.getElementById('{{ form.password.id_for_label }}');
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        this.innerHTML = type === 'password' ? '<i class="bi bi-eye"></i>' : '<i class="bi bi-eye-slash"></i>';
    };

    const passwordField = document.getElementById('{{ form.password.id_for_label }}');
    const wrapper = document.createElement('div');
    wrapper.className = 'input-group';
    passwordField.parentNode.insertBefore(wrapper, passwordField);
    wrapper.appendChild(passwordField);
    wrapper.appendChild(togglePassword);
});
</script>
{% endblock %}
