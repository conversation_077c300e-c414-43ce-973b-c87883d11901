/**
 * Enforce Solid Colors for Chat Messages
 * This script ensures that all chat messages have solid colors, not gradients
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initial enforcement
    enforceSolidColors();

    // Set up a MutationObserver to watch for new messages
    const chatBox = document.getElementById('chat-box');
    if (chatBox) {
        // Use a debounced version to prevent excessive calls
        let timeout;
        const observer = new MutationObserver(function(mutations) {
            // Clear any existing timeout
            if (timeout) {
                clearTimeout(timeout);
            }

            // Set a new timeout to run the function after a delay
            timeout = setTimeout(function() {
                // Check if any relevant nodes were added
                let shouldEnforce = false;
                for (const mutation of mutations) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        shouldEnforce = true;
                        break;
                    }
                }

                if (shouldEnforce) {
                    enforceSolidColors();
                }
            }, 500);
        });

        // Start observing the chat box for added messages with more specific parameters
        observer.observe(chatBox, { childList: true, subtree: false });
    }
});

/**
 * Enforce solid colors on all chat messages
 */
function enforceSolidColors() {
    // User messages
    document.querySelectorAll('.user-message .message-content').forEach(function(el) {
        el.style.background = '#3b7dd8';
        el.style.backgroundColor = '#3b7dd8';
        el.style.backgroundImage = 'none';
        el.style.color = '#ffffff';
        el.style.border = 'none';

        // Remove any pseudo-elements that might add gradients
        removeGradientPseudoElements(el);
    });

    // Assistant messages
    document.querySelectorAll('.assistant-message .message-content').forEach(function(el) {
        el.style.background = '#ffffff';
        el.style.backgroundColor = '#ffffff';
        el.style.backgroundImage = 'none';
        el.style.color = '#333333';
        el.style.border = '1px solid rgba(0, 0, 0, 0.05)';

        // Remove any pseudo-elements that might add gradients
        removeGradientPseudoElements(el);
    });

    // Navigation content bubbles
    document.querySelectorAll('.nav-content-bubble .message-content').forEach(function(el) {
        el.style.background = '#ffffff';
        el.style.backgroundColor = '#ffffff';
        el.style.backgroundImage = 'none';
        el.style.color = '#333333';
        el.style.border = '1px solid rgba(0, 0, 0, 0.05)';
        el.style.borderLeft = '3px solid #0d6efd';

        // Remove any pseudo-elements that might add gradients
        removeGradientPseudoElements(el);
    });
}

/**
 * Remove gradient pseudo-elements by adding a style tag
 */
function removeGradientPseudoElements(element) {
    // Create a unique class name for this element
    const uniqueClass = 'no-gradient-' + Math.random().toString(36).substr(2, 9);

    // Add the class to the element
    element.classList.add(uniqueClass);

    // Create a style tag to remove pseudo-elements
    const style = document.createElement('style');
    style.textContent = `
        .${uniqueClass}::before,
        .${uniqueClass}::after {
            display: none !important;
            content: none !important;
            background: none !important;
            background-image: none !important;
        }
    `;

    // Add the style tag to the head
    document.head.appendChild(style);
}
