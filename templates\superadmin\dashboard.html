{% extends "superadmin/base_superadmin.html" %}
{% load static %}
{% load i18n %}

{% block title %}Dashboard - Superadmin{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{% trans "Dashboard" %}</li>
{% endblock %}

{% block superadmin_content %}
<!-- Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body" style="background: linear-gradient(135deg, #1e2433, #252d40);">
                <div class="d-flex align-items-center mb-3">
                    <div class="me-4">
                        <i class="bi bi-speedometer2" style="font-size: 2.5rem; color: var(--accent-color-blue);"></i>
                    </div>
                    <div>
                        <h4 class="mb-1" style="color: var(--text-color-bright); font-weight: 600; letter-spacing: 0.5px;">Welcome to the Game Superadmin Dashboard</h4>
                        <p style="color: var(--text-color-muted); font-size: 0.95rem;">This dashboard provides an overview of the game platform's status and pending actions.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <!-- Companies -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card" style="border-left: 4px solid var(--accent-color-blue);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-label">Companies</div>
                    <div class="stat-value">{{ total_companies|default:"0" }}</div>
                </div>
                <div class="stat-icon" style="color: var(--accent-color-blue);">
                    <i class="bi bi-building"></i>
                </div>
            </div>
            <a href="{% url 'superadmin:company_list' %}" class="btn btn-sm btn-primary w-100 mt-3">
                <i class="bi bi-eye me-1"></i> View Companies
            </a>
        </div>
    </div>

    <!-- Game Sessions -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card" style="border-left: 4px solid var(--accent-color-green);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-label">Game Sessions</div>
                    <div class="stat-value">{{ total_game_sessions|default:"0" }}</div>
                </div>
                <div class="stat-icon" style="color: var(--accent-color-green);">
                    <i class="bi bi-controller"></i>
                </div>
            </div>
            <a href="{% url 'superadmin:game_session_list' %}" class="btn btn-sm btn-success w-100 mt-3">
                <i class="bi bi-eye me-1"></i> View Game Sessions
            </a>
        </div>
    </div>

    <!-- Users -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card" style="border-left: 4px solid #06b6d4;">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-label">Users</div>
                    <div class="stat-value">{{ total_users|default:"0" }}</div>
                </div>
                <div class="stat-icon" style="color: #06b6d4;">
                    <i class="bi bi-people"></i>
                </div>
            </div>
            <a href="{% url 'superadmin:user_list' %}" class="btn btn-sm btn-info w-100 mt-3">
                <i class="bi bi-eye me-1"></i> View Users
            </a>
        </div>
    </div>

    <!-- Leaderboard Entries -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="stat-card" style="border-left: 4px solid var(--accent-color-orange);">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-label">Leaderboard Entries</div>
                    <div class="stat-value">{{ total_leaderboard_entries|default:"0" }}</div>
                </div>
                <div class="stat-icon" style="color: var(--accent-color-orange);">
                    <i class="bi bi-trophy"></i>
                </div>
            </div>
            <a href="{% url 'superadmin:leaderboard_list' %}" class="btn btn-sm btn-warning w-100 mt-3">
                <i class="bi bi-eye me-1"></i> View Leaderboards
            </a>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card system-info-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>System Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="system-info-item">
                            <div class="system-info-icon">
                                <i class="bi bi-django"></i>
                            </div>
                            <div>
                                <div style="color: var(--text-color-muted); font-size: 0.85rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 4px;">Django Version</div>
                                <div style="color: var(--text-color-bright); font-weight: 600; font-size: 1.1rem;">{{ django_version }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="system-info-item">
                            <div class="system-info-icon" style="color: var(--accent-color-purple);">
                                <i class="bi bi-filetype-py"></i>
                            </div>
                            <div>
                                <div style="color: var(--text-color-muted); font-size: 0.85rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 4px;">Python Version</div>
                                <div style="color: var(--text-color-bright); font-weight: 600; font-size: 1.1rem;">{{ python_version }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="system-info-item">
                            <div class="system-info-icon" style="color: #06b6d4;">
                                <i class="bi bi-database"></i>
                            </div>
                            <div>
                                <div style="color: var(--text-color-muted); font-size: 0.85rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 4px;">Database Engine</div>
                                <div style="color: var(--text-color-bright); font-weight: 600; font-size: 1.1rem;">{{ database_engine }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pending Approvals -->
{% if pending_company_approvals > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card" style="border-left: 4px solid var(--accent-color-orange);">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Pending Approvals</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">{{ pending_company_approvals }} company/companies pending approval</h5>
                        <p class="mb-0">New companies require your approval before they can become active.</p>
                    </div>
                    <a href="{% url 'superadmin:company_list' %}?approval=pending" class="btn btn-warning">
                        <i class="bi bi-check-circle me-1"></i> Review Pending Companies
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning-charge me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card" style="border-top: 3px solid var(--accent-color-blue);">
                            <div class="quick-action-icon" style="color: var(--accent-color-blue);">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="quick-action-title">Manage Companies</div>
                            <a href="{% url 'superadmin:company_list' %}" class="btn btn-primary mt-3">
                                <i class="bi bi-arrow-right me-1"></i> Go
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card" style="border-top: 3px solid var(--accent-color-green);">
                            <div class="quick-action-icon" style="color: var(--accent-color-green);">
                                <i class="bi bi-controller"></i>
                            </div>
                            <div class="quick-action-title">Game Sessions</div>
                            <a href="{% url 'superadmin:game_session_list' %}" class="btn btn-success mt-3">
                                <i class="bi bi-arrow-right me-1"></i> Go
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card" style="border-top: 3px solid #06b6d4;">
                            <div class="quick-action-icon" style="color: #06b6d4;">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="quick-action-title">Users</div>
                            <a href="{% url 'superadmin:user_list' %}" class="btn btn-info mt-3">
                                <i class="bi bi-arrow-right me-1"></i> Go
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card" style="border-top: 3px solid var(--accent-color-orange);">
                            <div class="quick-action-icon" style="color: var(--accent-color-orange);">
                                <i class="bi bi-trophy"></i>
                            </div>
                            <div class="quick-action-title">Leaderboards</div>
                            <a href="{% url 'superadmin:leaderboard_list' %}" class="btn btn-warning mt-3">
                                <i class="bi bi-arrow-right me-1"></i> Go
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock superadmin_content %}
