<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Superadmin Dashboard - Corporate Prompt Master{% endblock %}</title>
    {% load static %}
    {% load i18n %}

    <!-- Always use dark mode -->
    <script>
        (function() {
            // Always apply dark mode
            document.documentElement.classList.add('dark-mode');
            document.documentElement.setAttribute('data-theme', 'dark');

            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('dark-mode');
                document.body.setAttribute('data-theme', 'dark');

                // Store theme preference
                localStorage.setItem('theme', 'dark');
                document.cookie = 'theme=dark; path=/; max-age=31536000';
            });
        })();
    </script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Font Awesome (needed for unified header) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Base Layout CSS -->
    <style>
        :root {
            --sidebar-width: 250px;
            --header-height: 56px;
            --primary-color: #4e73df;
            --secondary-color: #858796;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --light-color: #f8f9fc;
            --dark-color: #5a5c69;
        }

        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-header {
            height: var(--header-height);
            z-index: 1030;
            background-color: var(--primary-color);
            color: white;
        }

        .main-container {
            display: flex;
            flex: 1;
        }

        /* Sidebar styles are now handled by sidebar-hover.css */

        .main-content {
            flex: 1;
            /* margin-left is now handled by body padding in sidebar-hover.css */
            padding: 1.5rem;
            min-height: calc(100vh - var(--header-height)); /* Keep vertical spacing */
            margin-top: 0; /* Remove margin to eliminate white space */
            padding-top: var(--header-height); /* Use padding instead of margin */
            background-color: #0f1117; /* Match body background */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>

    <!-- Custom CSS -->
    {% block extra_css %}
    <link rel="stylesheet" href="{% static 'game/css/sidebar-hover.css' %}">
<style>
  /* Superadmin Custom Styles */
  .card {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    margin-bottom: 1.5rem;
  }

  .card-header {
    background-color: var(--card-bg, #1e2433);
    border-bottom: 1px solid var(--border-color-subtle, rgba(0, 0, 0, 0.125));
    padding: 1rem 1.25rem;
  }

  .breadcrumb {
    background-color: transparent !important;
    margin-bottom: 1.5rem;
    padding: 0;
  }

  /* Stats cards */
  .border-left-primary {
    border-left: 0.25rem solid var(--primary-color) !important;
  }

  .border-left-success {
    border-left: 0.25rem solid var(--success-color) !important;
  }

  .border-left-info {
    border-left: 0.25rem solid var(--info-color) !important;
  }

  .border-left-warning {
    border-left: 0.25rem solid var(--warning-color) !important;
  }

  .border-left-danger {
    border-left: 0.25rem solid var(--danger-color) !important;
  }

  .text-xs {
    font-size: 0.7rem;
  }

  .text-primary {
    color: var(--primary-color) !important;
  }

  .text-success {
    color: var(--success-color) !important;
  }

  .text-info {
    color: var(--info-color) !important;
  }

  .text-warning {
    color: var(--warning-color) !important;
  }

  .text-danger {
    color: var(--danger-color) !important;
  }

  .text-gray-300 {
    color: #dddfeb !important;
  }

  .text-gray-800 {
    color: #5a5c69 !important;
  }

  /* Override text colors for dark mode */
  body.dark-mode .text-gray-800 {
    color: var(--text-color-bright) !important;
  }

  body.dark-mode .text-gray-300 {
    color: var(--text-color-muted) !important;
  }

  /* Sidebar adjustments for unified header */
  .sidebar {
    top: 56px !important; /* Match unified header height */
    height: calc(100vh - 56px) !important; /* Adjust height for header */
  }

  /* Adjust body padding for sidebar with header */
  body {
    padding-top: 0 !important; /* Remove padding to eliminate white space */
    margin: 0;
    background-color: #0f1117;
  }

  /* Enhanced Dark mode styling */
  body.dark-mode {
    /* Base colors */
    --bg-color: #0f1117; /* Darker background */
    --main-bg: #1a1f2c; /* Richer panel background */
    --card-bg: #1e2433; /* Slightly lighter than main bg for cards */
    --text-color: #e0e0e0;
    --text-color-bright: #FFFFFF;
    --text-color-muted: #a0a0a0;
    --accent-color-blue: #3b82f6; /* Brighter blue */
    --accent-color-purple: #8b5cf6; /* Purple accent */
    --accent-color-green: #10b981; /* Green accent */
    --accent-color-orange: #f59e0b; /* Orange accent */
    --border-color-subtle: #2d3748;
    --card-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);

    background-color: var(--bg-color);
    color: var(--text-color);
  }

  /* Main content area */
  body.dark-mode .main-content {
    background-color: var(--bg-color);
    background-image:
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  }

  /* Card styling */
  body.dark-mode .card {
    background-color: var(--card-bg);
    border: none;
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
  }

  body.dark-mode .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5);
  }

  body.dark-mode .card-header {
    background: linear-gradient(to right, #1e2433, #252d40);
    border-bottom: 1px solid var(--border-color-subtle);
    color: var(--text-color-bright);
    font-weight: 600;
    padding: 1rem 1.25rem;
  }

  body.dark-mode .card-header h5,
  body.dark-mode .card-header h6 {
    margin-bottom: 0;
    font-weight: 600;
    letter-spacing: 0.5px;
  }

  body.dark-mode .card-body {
    padding: 1.5rem;
  }

  /* Breadcrumb styling */
  body.dark-mode .breadcrumb {
    background-color: transparent;
    padding: 0.75rem 0;
  }

  body.dark-mode .breadcrumb-item a {
    color: var(--accent-color-blue);
    font-weight: 500;
    transition: color 0.2s;
  }

  body.dark-mode .breadcrumb-item a:hover {
    color: #60a5fa;
    text-decoration: none;
  }

  body.dark-mode .breadcrumb-item.active {
    color: var(--text-color-muted);
  }

  /* Table styling for dark mode */
  body.dark-mode .table {
    color: var(--text-color);
    border-collapse: separate;
    border-spacing: 0;
  }

  body.dark-mode .table thead th {
    background-color: #252d40;
    border-bottom: none;
    color: var(--text-color-bright);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    padding: 1rem;
  }

  body.dark-mode .table td,
  body.dark-mode .table th {
    border-top: 1px solid var(--border-color-subtle);
    padding: 1rem;
    vertical-align: middle;
  }

  body.dark-mode .table-hover tbody tr:hover {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--text-color-bright);
  }

  /* Form controls in dark mode */
  body.dark-mode .form-control {
    background-color: #252d40;
    border: 1px solid var(--border-color-subtle);
    border-radius: 0.375rem;
    color: var(--text-color);
    padding: 0.75rem 1rem;
    transition: all 0.2s;
  }

  body.dark-mode .form-control:focus {
    background-color: #2d3748;
    border-color: var(--accent-color-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
    color: var(--text-color-bright);
  }

  body.dark-mode .form-select {
    background-color: #252d40;
    border: 1px solid var(--border-color-subtle);
    border-radius: 0.375rem;
    color: var(--text-color);
    padding: 0.75rem 1rem;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23a0a0a0' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  }

  body.dark-mode .form-select:focus {
    border-color: var(--accent-color-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
  }

  /* Badge styling */
  body.dark-mode .badge {
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: 0.375rem;
  }

  body.dark-mode .badge.bg-success {
    background-color: var(--accent-color-green) !important;
  }

  body.dark-mode .badge.bg-danger {
    background-color: #ef4444 !important;
  }

  body.dark-mode .badge.bg-primary {
    background-color: var(--accent-color-blue) !important;
  }

  body.dark-mode .badge.bg-warning {
    background-color: var(--accent-color-orange) !important;
    color: #1a1f2c;
  }

  body.dark-mode .badge.bg-info {
    background-color: #06b6d4 !important;
  }

  body.dark-mode .badge.bg-secondary {
    background-color: #6b7280 !important;
  }

  /* Button styling */
  body.dark-mode .btn {
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    transition: all 0.2s;
  }

  body.dark-mode .btn-primary {
    background-color: var(--accent-color-blue);
    border-color: var(--accent-color-blue);
  }

  body.dark-mode .btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
  }

  body.dark-mode .btn-success {
    background-color: var(--accent-color-green);
    border-color: var(--accent-color-green);
  }

  body.dark-mode .btn-info {
    background-color: #06b6d4;
    border-color: #06b6d4;
  }

  body.dark-mode .btn-warning {
    background-color: var(--accent-color-orange);
    border-color: var(--accent-color-orange);
  }

  body.dark-mode .btn-danger {
    background-color: #ef4444;
    border-color: #ef4444;
  }

  body.dark-mode .btn-outline-primary {
    color: var(--accent-color-blue);
    border-color: var(--accent-color-blue);
  }

  body.dark-mode .btn-outline-primary:hover {
    background-color: var(--accent-color-blue);
    color: white;
  }

  /* Dashboard stat cards */
  body.dark-mode .stat-card {
    background: linear-gradient(145deg, #1e2433, #252d40);
    border-radius: 0.5rem;
    box-shadow: var(--card-shadow);
    padding: 1.5rem;
    transition: transform 0.2s;
  }

  body.dark-mode .stat-card:hover {
    transform: translateY(-5px);
  }

  body.dark-mode .stat-card .stat-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
  }

  body.dark-mode .stat-card .stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-color-bright);
  }

  body.dark-mode .stat-card .stat-label {
    color: var(--text-color-muted);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 1px;
  }

  /* System information section */
  body.dark-mode .system-info-card {
    background: linear-gradient(to right, #1a1f2c, #252d40);
  }

  body.dark-mode .system-info-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgba(255, 255, 255, 0.05);
    margin-bottom: 1rem;
  }

  body.dark-mode .system-info-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    color: var(--accent-color-blue);
  }

  /* Quick actions section */
  body.dark-mode .quick-action-card {
    background-color: #252d40;
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.2s;
    height: 100%;
  }

  body.dark-mode .quick-action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
  }

  body.dark-mode .quick-action-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  body.dark-mode .quick-action-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-color-bright);
  }

  /* Pagination styling */
  body.dark-mode .page-link {
    background-color: #252d40;
    border-color: var(--border-color-subtle);
    color: var(--text-color);
    transition: all 0.2s;
  }

  body.dark-mode .page-link:hover {
    background-color: #2d3748;
    color: var(--text-color-bright);
    border-color: var(--accent-color-blue);
  }

  body.dark-mode .page-item.active .page-link {
    background-color: var(--accent-color-blue);
    border-color: var(--accent-color-blue);
    color: white;
  }

  body.dark-mode .page-item.disabled .page-link {
    background-color: #1e2433;
    border-color: var(--border-color-subtle);
    color: #6b7280;
  }
</style>
{% endblock %}

    <!-- Additional head content -->
    {% block head_extras %}{% endblock %}
</head>
<body class="dark-mode" data-theme="dark" style="background-color: #0f1117;">
    <!-- Include Unified Header -->
    {% include 'unified_header.html' %}

    <!-- Include Sidebar -->
    {% include 'superadmin/sidebar.html' %}

    <!-- Main Content -->
    <div class="main-content">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'superadmin:dashboard' %}">{% trans "Superadmin" %}</a></li>
                {% block breadcrumbs %}{% endblock %}
            </ol>
        </nav>

        <!-- Page Content -->
        <div class="container-fluid">
            {% block superadmin_content %}
            {# Specific superadmin page content goes here #}
            {% endblock superadmin_content %}
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>

    <!-- Base Layout JS -->
    <script>
        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleButton = document.querySelector('.navbar-toggler');
            const sidebar = document.querySelector('.sidebar');

            if (toggleButton && sidebar) {
                toggleButton.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>

    <!-- Custom JS -->
    {% block extra_js %}{% endblock %}

    <!-- Theme is always dark, no toggle needed -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Find any theme toggle buttons and update their appearance
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                const icon = themeToggle.querySelector('i');
                if (icon) {
                    icon.classList.remove('fa-moon');
                    icon.classList.add('fa-sun');
                }

                // Hide the toggle since we're always in dark mode
                themeToggle.style.display = 'none';
            }

            // Ensure dark mode is always applied
            document.documentElement.classList.add('dark-mode');
            document.documentElement.setAttribute('data-theme', 'dark');
            document.body.classList.add('dark-mode');
            document.body.setAttribute('data-theme', 'dark');
        });
    </script>
</body>
</html>
