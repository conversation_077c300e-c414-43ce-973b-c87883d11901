/**
 * Button Functionality JavaScript
 * This script ensures all buttons in the assistant chat interface work correctly
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all buttons
    initSidebarButtons();
    initResetButton();
    initSendButton();
    initShareUrlButton();
    initRatingButtons();
    initFavoriteButton();
    initQrCodeButton();
    initTabButtons();
    initNavigationButtons();
});

// Also run initialization after a short delay to ensure all elements are loaded
setTimeout(function() {
    initSidebarButtons();
}, 500);

/**
 * Initialize sidebar toggle buttons
 */
function initSidebarButtons() {
    // Desktop toggle button
    const sidebarToggleBtn = document.getElementById('test-sidebar-toggle');
    // Mobile toggle button
    const sidebarToggleBtnMobile = document.getElementById('test-sidebar-toggle-mobile');
    // Inside sidebar toggle button
    const sidebarToggleInside = document.getElementById('sidebar-toggle-inside');
    // Sidebar overlay
    const sidebarOverlay = document.getElementById('sidebar-overlay');

    // Direct click handler function
    function handleSidebarToggleClick(e) {
        if (e) {
            e.preventDefault();
            e.stopPropagation();
        }
        toggleSidebar();
        return false;
    }

    // Add click event listeners to all sidebar toggle buttons
    if (sidebarToggleBtn) {
        // Remove any existing event listeners first
        const newBtn = sidebarToggleBtn.cloneNode(true);
        sidebarToggleBtn.parentNode.replaceChild(newBtn, sidebarToggleBtn);
        newBtn.addEventListener('click', handleSidebarToggleClick);
    }

    if (sidebarToggleBtnMobile) {
        // Remove any existing event listeners first
        const newMobileBtn = sidebarToggleBtnMobile.cloneNode(true);
        sidebarToggleBtnMobile.parentNode.replaceChild(newMobileBtn, sidebarToggleBtnMobile);
        newMobileBtn.addEventListener('click', handleSidebarToggleClick);
    }

    if (sidebarToggleInside) {
        // Remove any existing event listeners first
        const newInsideBtn = sidebarToggleInside.cloneNode(true);
        sidebarToggleInside.parentNode.replaceChild(newInsideBtn, sidebarToggleInside);
        newInsideBtn.addEventListener('click', handleSidebarToggleClick);
    }

    // Add click event listener to sidebar overlay
    if (sidebarOverlay) {
        sidebarOverlay.onclick = handleSidebarToggleClick;
    }
}

/**
 * Toggle sidebar visibility
 */
function toggleSidebar() {
    const sidebar = document.getElementById('chat-sidebar');
    const sidebarOverlay = document.getElementById('sidebar-overlay');

    if (!sidebar) {
        return;
    }

    // Force the sidebar to be visible first to ensure transitions work
    sidebar.style.display = 'block';
    sidebar.style.visibility = 'visible';

    // Use a small timeout to ensure the display change takes effect
    setTimeout(() => {
        if (sidebar.classList.contains('active') || sidebar.style.left === '0px') {
            // Hide sidebar
            sidebar.classList.remove('active');
            sidebar.style.left = '-350px';

            // Update toggle text
            const toggleTexts = document.querySelectorAll('.toggle-text');
            toggleTexts.forEach(el => { el.textContent = 'Menu'; });

            // Hide overlay
            if (sidebarOverlay) {
                sidebarOverlay.style.opacity = '0';
                sidebarOverlay.style.pointerEvents = 'none';
                setTimeout(() => { sidebarOverlay.style.display = 'none'; }, 300);
            }
        } else {
            // Show sidebar
            sidebar.classList.add('active');
            sidebar.style.left = '0';
            sidebar.style.opacity = '1';

            // Update toggle text
            const toggleTexts = document.querySelectorAll('.toggle-text');
            toggleTexts.forEach(el => { el.textContent = 'Close'; });

            // Show overlay
            if (sidebarOverlay) {
                sidebarOverlay.style.display = 'block';
                sidebarOverlay.style.pointerEvents = 'auto';
                setTimeout(() => { sidebarOverlay.style.opacity = '1'; }, 10);
            }
        }
    }, 10);
}

// Export toggleSidebar function to window object so it can be called from other scripts
window.toggleSidebar = toggleSidebar;

/**
 * Initialize reset conversation button
 */
function initResetButton() {
    const resetButton = document.getElementById('reset-chat-btn');

    if (resetButton) {
        // Remove any existing event listeners
        const newResetButton = resetButton.cloneNode(true);
        resetButton.parentNode.replaceChild(newResetButton, resetButton);

        // Add new event listener
        newResetButton.addEventListener('click', function() {
            // Confirm before resetting
            if (confirm('Are you sure you want to reset the conversation? This will clear all messages.')) {
                resetConversation();
            }
        });
    }
}

/**
 * Reset the conversation
 */
function resetConversation() {
    const chatBox = document.getElementById('chat-box');
    const initialDisplay = document.getElementById('initial-display-area');

    if (chatBox) {
        // Remove all messages
        const messages = chatBox.querySelectorAll('.message:not(.initial-greeting)');
        messages.forEach(message => {
            message.remove();
        });

        // Show initial display area
        if (initialDisplay) {
            initialDisplay.style.display = 'block';
        }

        // Clear message history
        window.messageHistory = [];
    }
}

// Export resetConversation function to window object so it can be called from other scripts
window.resetConversation = resetConversation;

/**
 * Initialize send button
 */
function initSendButton() {
    const sendButton = document.getElementById('send-button');
    const messageInput = document.getElementById('message-input');
    const chatForm = document.getElementById('chat-form');

    if (sendButton && messageInput && chatForm) {
        // Remove any existing event listeners
        const newSendButton = sendButton.cloneNode(true);
        sendButton.parentNode.replaceChild(newSendButton, sendButton);

        // Add new event listener to send button
        newSendButton.addEventListener('click', function() {
            const message = messageInput.value.trim();
            if (message) {
                sendMessage(message);
            }
        });

        // Add event listener to input field for Enter key
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                const message = messageInput.value.trim();
                if (message) {
                    sendMessage(message);
                }
            }
        });

        // Add submit event listener to form
        chatForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const message = messageInput.value.trim();
            if (message) {
                sendMessage(message);
            }
        });
    }
}

/**
 * Send a message to the assistant
 */
function sendMessage(message) {
    // Check if chat.js has already defined a sendMessage function
    if (typeof window.sendMessage === 'function') {
        // Use the existing function
        window.sendMessage(message);
    } else {
        // Use our implementation
        const chatBox = document.getElementById('chat-box');
        const messageInput = document.getElementById('message-input');
        const initialDisplay = document.getElementById('initial-display-area');

        if (chatBox && messageInput) {
            // Hide initial display area
            if (initialDisplay) {
                initialDisplay.style.display = 'none';
            }

            // Add user message
            const userMessageDiv = document.createElement('div');
            userMessageDiv.className = 'message user-message mb-3';
            userMessageDiv.innerHTML = `<span class="message-content">${escapeHtml(message)}</span>`;
            chatBox.appendChild(userMessageDiv);

            // Clear input
            messageInput.value = '';

            // Add loading indicator
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'message assistant-message loading mb-3';
            loadingDiv.innerHTML = '<span class="message-content"><div class="spinner-border spinner-border-sm" role="status"></div> Thinking...</span>';
            chatBox.appendChild(loadingDiv);

            // Scroll to bottom
            chatBox.scrollTop = chatBox.scrollHeight;

            // Get path components from current URL
            const pathParts = window.location.pathname.split('/');
            let companyId, assistantId, slug, useSlugUrl = false;

            if (pathParts.includes('company')) {
                // Format: /company/{company_id}/assistants/{assistant_id}/chat/
                companyId = pathParts[pathParts.indexOf('company') + 1];
                assistantId = pathParts[pathParts.indexOf('assistants') + 1];
            } else if (pathParts.includes('assistant')) {
                // Format: /assistant/{slug}/chat/
                // Need to get IDs from the page for API calls
                companyId = document.querySelector('meta[name="company-id"]').content;
                assistantId = document.querySelector('meta[name="assistant-id"]').content;

                // Get the slug from the URL
                const assistantIndex = pathParts.indexOf('assistant');
                if (assistantIndex >= 0 && assistantIndex + 1 < pathParts.length) {
                    slug = pathParts[assistantIndex + 1];
                    useSlugUrl = true;
                }
            }

            // Prepare request data
            const data = {
                message: message,
                history: window.messageHistory || [],
                use_community_context: true
            };

            // Determine which URL to use
            let apiUrl;
            if (useSlugUrl && slug) {
                apiUrl = `/assistant/interact/${slug}/`;
            } else {
                apiUrl = `/assistant/company/${companyId}/assistants/${assistantId}/interact/`;
            }

            // Send message to server
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Server error: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Remove loading indicator
                loadingDiv.remove();

                if (data.status === 'error') {
                    throw new Error(data.error || 'Error processing message');
                }

                // Add response to history
                if (!window.messageHistory) {
                    window.messageHistory = [];
                }
                window.messageHistory.push({ role: 'user', content: message });
                window.messageHistory.push({ role: 'assistant', content: data.content });

                // Add assistant message
                const assistantMessageDiv = document.createElement('div');
                assistantMessageDiv.className = 'message assistant-message mb-3';

                // Process response content
                let processedContent = data.content;

                // Add context links if available
                if (data.used_contexts && typeof addContextLinks === 'function') {
                    processedContent = addContextLinks(processedContent, data.used_contexts);
                }

                assistantMessageDiv.innerHTML = `<span class="message-content">${processedContent}</span>`;
                chatBox.appendChild(assistantMessageDiv);

                // Scroll to bottom
                chatBox.scrollTop = chatBox.scrollHeight;
            })
            .catch(error => {
                // Remove loading indicator
                loadingDiv.remove();

                // Add error message
                const errorDiv = document.createElement('div');
                errorDiv.className = 'message assistant-message mb-3';
                errorDiv.innerHTML = `<span class="message-content text-danger"><i class="bi bi-exclamation-triangle-fill me-2"></i>${error.message}</span>`;
                chatBox.appendChild(errorDiv);

                // Scroll to bottom
                chatBox.scrollTop = chatBox.scrollHeight;
            });
        }
    }
}

// Export sendMessage function to window object so it can be called from other scripts
window.sendMessage = sendMessage;

/**
 * Initialize share URL button
 */
function initShareUrlButton() {
    const shareUrlButton = document.getElementById('copy-url-btn');

    if (shareUrlButton) {
        // Remove any existing event listeners
        const newShareUrlButton = shareUrlButton.cloneNode(true);
        shareUrlButton.parentNode.replaceChild(newShareUrlButton, shareUrlButton);

        // Add new event listener
        newShareUrlButton.addEventListener('click', function() {
            // Get current URL
            const url = window.location.href;

            // Copy to clipboard
            navigator.clipboard.writeText(url).then(function() {
                // Show success message
                alert('Chat URL copied to clipboard!');

                // Change button text temporarily
                const originalText = newShareUrlButton.textContent;
                newShareUrlButton.textContent = 'Copied!';

                // Reset button text after 2 seconds
                setTimeout(function() {
                    newShareUrlButton.textContent = originalText;
                }, 2000);
            }).catch(function() {
                alert('Failed to copy URL. Please copy it manually: ' + url);
            });
        });
    }
}

/**
 * Initialize rating buttons
 */
function initRatingButtons() {
    const rateButton = document.querySelector('.rate-assistant-btn');
    const modalStarButtons = document.querySelectorAll('.modal-star-btn');
    const submitRatingBtn = document.getElementById('submitRatingBtn');
    const ratingModal = document.getElementById('ratingModal');

    if (rateButton) {
        rateButton.addEventListener('click', function() {
            // Get assistant name
            const assistantName = this.getAttribute('data-assistant-name');

            // Update modal title
            const modalAssistantName = document.getElementById('modalAssistantName');
            if (modalAssistantName) {
                modalAssistantName.textContent = assistantName || 'this assistant';
            }

            // Reset star selection
            modalStarButtons.forEach(btn => {
                const star = btn.querySelector('i');
                if (star) {
                    star.className = 'bi bi-star';
                }
            });

            // Disable submit button
            if (submitRatingBtn) {
                submitRatingBtn.disabled = true;
            }
        });
    }

    // Add click event listeners to star buttons
    modalStarButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Get rating value
            const ratingValue = parseInt(this.getAttribute('data-rating-value'));

            // Update star appearance
            modalStarButtons.forEach(starBtn => {
                const star = starBtn.querySelector('i');
                if (star) {
                    const starValue = parseInt(starBtn.getAttribute('data-rating-value'));
                    star.className = starValue <= ratingValue ? 'bi bi-star-fill' : 'bi bi-star';
                }
            });

            // Enable submit button
            if (submitRatingBtn) {
                submitRatingBtn.disabled = false;
                submitRatingBtn.setAttribute('data-rating-value', ratingValue);
            }
        });
    });

    // Add click event listener to submit button
    if (submitRatingBtn) {
        submitRatingBtn.addEventListener('click', function() {
            // Get rating value
            const ratingValue = parseInt(this.getAttribute('data-rating-value'));
            if (!ratingValue) {
                return;
            }

            // Get assistant ID
            const rateButton = document.querySelector('.rate-assistant-btn');
            if (!rateButton) {
                return;
            }

            const assistantId = rateButton.getAttribute('data-assistant-id');
            if (!assistantId) {
                return;
            }

            // Send rating to server
            fetch(`/assistants/rate/${assistantId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: new URLSearchParams({
                    'rating': ratingValue
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Server error: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    // Close modal
                    const ratingModal = document.getElementById('ratingModal');
                    if (ratingModal && typeof bootstrap !== 'undefined') {
                        const modal = bootstrap.Modal.getInstance(ratingModal);
                        if (modal) {
                            modal.hide();

                            // Ensure modal backdrop is removed
                            setTimeout(() => {
                                const backdrop = document.querySelector('.modal-backdrop');
                                if (backdrop) {
                                    backdrop.remove();
                                }

                                // Clean up body classes
                                document.body.classList.remove('modal-open');
                                document.body.style.removeProperty('overflow');
                                document.body.style.removeProperty('padding-right');
                            }, 300);
                        }
                    }

                    // Show success message
                    const ratingMsg = document.getElementById(`rating-msg-${assistantId}`);
                    if (ratingMsg) {
                        ratingMsg.textContent = 'Rating submitted!';
                        ratingMsg.style.display = 'inline-block';

                        // Hide message after 3 seconds
                        setTimeout(function() {
                            ratingMsg.style.display = 'none';
                        }, 3000);
                    }

                    // Update rating display
                    const ratingDisplay = document.getElementById(`rating-display-${assistantId}`);
                    if (ratingDisplay && data.avg_rating && data.total_ratings) {
                        // Update stars (implementation depends on your star rendering)
                    }
                } else {
                    // Show error message
                    const errorMsg = document.getElementById('modalErrorMsg');
                    if (errorMsg) {
                        errorMsg.textContent = data.message || 'Error submitting rating';
                        errorMsg.style.display = 'block';
                    }
                }
            })
            .catch(error => {
                // Show error message
                const errorMsg = document.getElementById('modalErrorMsg');
                if (errorMsg) {
                    errorMsg.textContent = error.message || 'Error submitting rating';
                    errorMsg.style.display = 'block';
                }
            });
        });
    }

    // Add event listener for modal hidden event to ensure proper cleanup
    if (ratingModal) {
        ratingModal.addEventListener('hidden.bs.modal', function() {
            // Remove any lingering backdrop
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }

            // Ensure body classes are cleaned up
            document.body.classList.remove('modal-open');
            document.body.style.removeProperty('overflow');
            document.body.style.removeProperty('padding-right');
        });
    }
}

/**
 * Initialize favorite button
 */
function initFavoriteButton() {
    const favoriteButton = document.querySelector('.like-button');

    if (favoriteButton) {
        // Remove any existing event listeners
        const newFavoriteButton = favoriteButton.cloneNode(true);
        favoriteButton.parentNode.replaceChild(newFavoriteButton, favoriteButton);

        // Add new event listener
        newFavoriteButton.addEventListener('click', function() {

            const itemId = this.getAttribute('data-item-id');
            const itemType = this.getAttribute('data-item-type');
            const icon = this.querySelector('i');

            // Get CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
            if (!csrfToken) {
                return;
            }

            // Send request to toggle saved status
            fetch('/directory/toggle_saved_item/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({
                    'item_id': itemId,
                    'item_type': itemType
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    // Update button appearance
                    if (data.action === 'unfavorited') {
                        // Item was unfavorited
                        icon.classList.remove('bi-heart-fill', 'text-danger');
                        icon.classList.add('bi-heart');
                        this.textContent = '';
                        this.appendChild(icon);
                        this.appendChild(document.createTextNode(' Favorite'));
                        this.title = 'Add to Favorites';
                    } else {
                        // Item was favorited
                        icon.classList.remove('bi-heart');
                        icon.classList.add('bi-heart-fill', 'text-danger');
                        this.textContent = '';
                        this.appendChild(icon);
                        this.appendChild(document.createTextNode(' Favorited'));
                        this.title = 'Remove from Favorites';
                    }
                } else if (data.status === 'options') {
                    // Item is not saved, save it directly without showing folder options
                    // Send another request to save without folder
                    fetch('/directory/save_without_folder/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: new URLSearchParams({
                            'item_id': itemId,
                            'item_type': itemType
                        })
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(saveData => {
                        if (saveData.status === 'success') {
                            // Update button appearance
                            icon.classList.remove('bi-heart');
                            icon.classList.add('bi-heart-fill', 'text-danger');
                            this.textContent = '';
                            this.appendChild(icon);
                            this.appendChild(document.createTextNode(' Favorited'));
                            this.title = 'Remove from Favorites';
                        } else {
                            // Error saving item
                        }
                    })
                    .catch(() => {
                        // Handle error silently
                    });
                } else {
                    // Error toggling saved status
                }
            })
            .catch(() => {
                // Handle error silently
            });
        });
    }
}

/**
 * Initialize QR code button
 */
function initQrCodeButton() {
    const qrCodeBtn = document.getElementById('qr-code-btn');

    if (qrCodeBtn) {
        qrCodeBtn.addEventListener('click', function() {

            // Get QR code URL
            const qrUrl = this.getAttribute('data-qr-url');
            const isCompany = this.getAttribute('data-is-company') === 'true';
            const companyName = this.getAttribute('data-company-name');

            // Update QR code image
            const qrCodeImage = document.getElementById('qrCodeImage');
            const qrCodeDownloadLink = document.getElementById('qrCodeDownloadLink');
            const qrCodeDescription = document.getElementById('qrCodeDescription');

            if (qrCodeImage && qrUrl) {
                qrCodeImage.src = qrUrl;

                // Update download link
                if (qrCodeDownloadLink) {
                    qrCodeDownloadLink.href = qrUrl;
                    qrCodeDownloadLink.download = isCompany ? `${companyName}_qr_code.png` : 'assistant_qr_code.png';
                }

                // Update description
                if (qrCodeDescription) {
                    qrCodeDescription.textContent = isCompany
                        ? `Scan this code to access ${companyName}.`
                        : 'Scan this code to open the chat.';
                }
            }
        });
    }
}

/**
 * Initialize tab buttons
 */
function initTabButtons() {
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {

            // Get target tab
            const targetId = this.getAttribute('data-bs-target');
            if (!targetId) return;

            // Hide all tab panes
            const tabPanes = document.querySelectorAll('.tab-pane');
            tabPanes.forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            // Show target tab pane
            const targetPane = document.querySelector(targetId);
            if (targetPane) {
                targetPane.classList.add('show', 'active');
            }

            // Update active state of tab buttons
            tabButtons.forEach(btn => {
                btn.classList.remove('active');
                btn.setAttribute('aria-selected', 'false');
            });

            // Set this button as active
            this.classList.add('active');
            this.setAttribute('aria-selected', 'true');

            // Update URL hash
            const tabId = targetId.replace('#', '');
            history.replaceState(null, null, `#${tabId}`);
        });
    });
}

/**
 * Initialize navigation buttons
 */
function initNavigationButtons() {
    // Get all navigation items
    const navButtons = document.querySelectorAll('.nav-button, .list-group-item[data-section-id]');

    // Direct click handler function
    function handleNavigationClick(e) {
        e.preventDefault();
        e.stopPropagation();

        // Get section ID from data attribute
        const sectionId = this.getAttribute('data-section-id');
        if (!sectionId) {
            return;
        }

        // Call showContentById function
        showContentById(sectionId);

        // Close sidebar on mobile after clicking a navigation item
        if (window.innerWidth < 768) {
            const sidebar = document.getElementById('chat-sidebar');
            if (sidebar && sidebar.classList.contains('active')) {
                toggleSidebar();
            }
        }
    }

    // Add click event listener to each navigation item
    navButtons.forEach(button => {
        // Use direct onclick assignment instead of addEventListener
        button.onclick = handleNavigationClick;
    });
}

/**
 * Show content by section ID
 */
function showContentById(sectionId) {

    // Get company and assistant IDs from meta tags
    const companyId = document.querySelector('meta[name="company-id"]').content;
    const assistantId = document.querySelector('meta[name="assistant-id"]').content;

    if (!companyId || !assistantId) {
        return;
    }

    // Show loading message in chat box
    const chatBox = document.getElementById('chat-box');
    if (!chatBox) {
        return;
    }

    // Add loading indicator
    const loadingDiv = document.createElement('div');
    loadingDiv.className = 'message assistant-message loading';
    loadingDiv.innerHTML = '<div class="message-content"><div class="spinner-border spinner-border-sm" role="status"></div> Loading content...</div>';
    chatBox.appendChild(loadingDiv);
    chatBox.scrollTop = chatBox.scrollHeight;

    // Hide initial display area if it exists
    const initialDisplay = document.getElementById('initial-display-area');
    if (initialDisplay) {
        initialDisplay.style.display = 'none';
    }

    // Prepare request data
    const data = {
        navigation_click: true,
        section_id: sectionId
    };

    // Determine API URL
    const apiUrl = `/assistant/company/${companyId}/assistants/${assistantId}/interact/`;

    // Send request to server
    fetch(apiUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Server error: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // Remove loading indicator
        loadingDiv.remove();

        if (data.status === 'error') {
            throw new Error(data.error || 'Error loading content');
        }

        // Display the content in a chat bubble with enhanced formatting
        if (data.content) {
            // Create the message container
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message assistant-message nav-content-bubble';

            // Get the section label
            let sectionLabel = data.section_label || 'Information';

            // Format the content with a header
            const formattedContent = `
                <div class="message-content">
                    <h4 class="nav-content-header">${sectionLabel}</h4>
                    <div class="nav-content-body">${data.content}</div>
                </div>
            `;
            messageDiv.innerHTML = formattedContent;

            chatBox.appendChild(messageDiv);
            chatBox.scrollTop = chatBox.scrollHeight;
        }
    })
    .catch(error => {
        // Remove loading indicator
        loadingDiv.remove();

        // Add error message
        const errorDiv = document.createElement('div');
        errorDiv.className = 'message assistant-message';
        errorDiv.innerHTML = `<div class="message-content text-danger"><i class="bi bi-exclamation-triangle-fill me-2"></i>${error.message}</div>`;
        chatBox.appendChild(errorDiv);
        chatBox.scrollTop = chatBox.scrollHeight;
    });
}

// Export showContentById function to window object so it can be called from other scripts
window.showContentById = showContentById;

/**
 * Helper function to safely escape HTML
 */
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}
