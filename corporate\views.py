from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import login, authenticate, logout
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Max, Count
from django.utils import timezone
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from django.conf import settings
import json
import datetime
import logging

# Set up logger
logger = logging.getLogger(__name__)

from .models import (
    Company, CorporateUser, Certificate,
    Leaderboard, LeaderboardEntry, GameAccess
)
from .forms import (
    CorporateLoginForm, CompanyRegistrationForm,
    CorporateUserRegistrationForm, CorporateProfileForm,
    CorporateUserInviteForm, CompanyEditForm
)
from .certificate_generator import generate_certificate_pdf, verify_certificate, generate_certificate_html
from game.models import GameSession


# Home View
def home(request):
    """
    Homepage view showing featured content.
    """
    # Get available games (for authenticated users)
    available_games = []
    if request.user.is_authenticated:
        try:
            corporate_profile = request.user.corporate_profile
            company = corporate_profile.company

            available_games = GameAccess.objects.filter(
                company=company,
                is_active=True
            ).order_by('game_name')
        except:
            pass

    return render(request, 'corporate/home.html', {
        'available_games': available_games
    })


# Authentication Views
def corporate_login(request):
    """
    Handle corporate user login.
    """
    # Ensure session is created
    if not request.session.session_key:
        request.session.save()

    if request.method == 'POST':
        form = CorporateLoginForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            remember_me = form.cleaned_data.get('remember_me')

            user = authenticate(username=username, password=password)
            if user is not None:
                # Check if user has a corporate profile
                try:
                    corporate_profile = user.corporate_profile

                    # Store session ID before login to migrate anonymous game sessions
                    session_id = request.session.session_key

                    # Log the user in
                    login(request, user, backend='django.contrib.auth.backends.ModelBackend')

                    # Set session expiry based on remember_me
                    if remember_me:
                        # Session will expire according to SESSION_COOKIE_AGE in settings
                        request.session.set_expiry(86400)  # 24 hours
                    else:
                        request.session.set_expiry(0)  # Session expires when browser closes

                    # Migrate anonymous game sessions if any
                    if session_id:
                        try:
                            from game.views import migrate_anonymous_session
                            migrated_session = migrate_anonymous_session(session_id, user)
                            if migrated_session:
                                logging.info(f"Successfully migrated anonymous game session to user {user.username}")
                        except Exception as e:
                            logging.error(f"Error migrating game session: {str(e)}")

                    # Check if there's a pending company ID from registration
                    if 'pending_company_id' in request.session:
                        # Set the active company ID
                        request.session['active_company_id'] = request.session['pending_company_id']
                        # Remove the pending company ID
                        del request.session['pending_company_id']
                    else:
                        # Set the active company ID to the user's company
                        request.session['active_company_id'] = str(corporate_profile.company.id)

                    messages.success(request, f"Welcome back, {user.first_name or username}!")
                    return redirect('corporate:corporate_dashboard')
                except CorporateUser.DoesNotExist:
                    messages.error(request, "This account is not associated with any company.")
            else:
                messages.error(request, "Invalid username or password.")
    else:
        form = CorporateLoginForm()

    return render(request, 'corporate/login.html', {'form': form})


def corporate_logout(request):
    """
    Handle corporate user logout.
    """
    logout(request)
    messages.info(request, "You have been logged out.")
    return redirect('corporate:corporate_login')


def corporate_register(request):
    """
    Handle new company and admin user registration.
    """
    if request.method == 'POST':
        company_form = CompanyRegistrationForm(request.POST, request.FILES)
        user_form = CorporateUserRegistrationForm(request.POST)

        if company_form.is_valid() and user_form.is_valid():
            # Store session ID before creating user to migrate anonymous game sessions
            session_id = request.session.session_key

            # Create user first
            user = user_form.save()

            # Create company with user as owner
            company = company_form.save(commit=False)
            company.owner = user
            company.is_active = False  # Company starts as inactive
            company.pending_approval = True  # Mark as pending approval
            company.save()

            # Create corporate profile
            CorporateUser.objects.create(
                user=user,
                company=company,
                is_company_admin=True  # First user is admin
            )

            # Create default leaderboard for company
            Leaderboard.objects.create(
                name=f"{company.name} Leaderboard",
                company=company,
                is_global=False,
                time_period='all_time'
            )

            # Create game access for the company
            GameAccess.objects.create(
                company=company,
                game_id='prompt_master',
                game_name='Corporate Prompt Master',
                is_active=True
            )

            # Log the user in
            login(request, user, backend='django.contrib.auth.backends.ModelBackend')

            # Migrate anonymous game sessions if any
            if session_id:
                try:
                    from game.views import migrate_anonymous_session
                    migrated_session = migrate_anonymous_session(session_id, user)
                    if migrated_session:
                        logging.info(f"Successfully migrated anonymous game session to new user {user.username}")
                except Exception as e:
                    logging.error(f"Error migrating game session for new user: {str(e)}")

            messages.success(request, f"Welcome to Corporate Prompt Master, {user.first_name}! Your company account has been created and is pending approval by an administrator.")
            return redirect('corporate:corporate_dashboard')
    else:
        company_form = CompanyRegistrationForm()
        user_form = CorporateUserRegistrationForm()

    return render(request, 'corporate/register.html', {
        'company_form': company_form,
        'user_form': user_form
    })


# Dashboard Views
@login_required
def edit_profile(request):
    """
    Edit user profile information.
    """
    try:
        corporate_profile = request.user.corporate_profile
        company = corporate_profile.company
    except CorporateUser.DoesNotExist:
        messages.error(request, "Your account is not associated with any company.")
        return redirect('corporate:corporate_login')

    if request.method == 'POST':
        form = CorporateProfileForm(request.POST, request.FILES, instance=corporate_profile)
        if form.is_valid():
            form.save()
            messages.success(request, "Your profile has been updated successfully.")
            return redirect('corporate:edit_profile')
    else:
        form = CorporateProfileForm(instance=corporate_profile)

    context = {
        'form': form,
        'company': company,
        'corporate_profile': corporate_profile,
        'is_admin': corporate_profile.is_company_admin
    }

    return render(request, 'corporate/edit_profile.html', context)


@login_required
def edit_company(request):
    """
    Allow company owners to edit company details.
    """
    try:
        corporate_profile = request.user.corporate_profile
        company = corporate_profile.company
    except CorporateUser.DoesNotExist:
        messages.error(request, "Your account is not associated with any company.")
        return redirect('corporate:corporate_login')

    # Check if user is the company owner
    if request.user != company.owner:
        messages.error(request, "Only the company owner can edit company details.")
        return redirect('corporate:corporate_dashboard')

    if request.method == 'POST':
        form = CompanyEditForm(request.POST, request.FILES, instance=company)
        if form.is_valid():
            form.save()
            messages.success(request, "Company details updated successfully.")
            return redirect('corporate:edit_company')
    else:
        form = CompanyEditForm(instance=company)

    return render(request, 'corporate/edit_company.html', {
        'form': form,
        'company': company,
        'is_admin': corporate_profile.is_company_admin
    })


@login_required
def corporate_dashboard(request):
    """
    Main dashboard for corporate users.
    """
    # Get user's corporate profile
    try:
        corporate_profile = request.user.corporate_profile

        # Check if there's an active company ID in the session
        active_company_id = request.session.get('active_company_id')
        if active_company_id:
            # Try to get the company from the active company ID
            try:
                company = Company.objects.get(id=active_company_id)
                # Verify that the user is associated with this company
                if not CorporateUser.objects.filter(user=request.user, company=company).exists():
                    # If not, fall back to the user's default company
                    company = corporate_profile.company
                    # Update the session
                    request.session['active_company_id'] = str(company.id)
            except Company.DoesNotExist:
                # If the company doesn't exist, fall back to the user's default company
                company = corporate_profile.company
                # Update the session
                request.session['active_company_id'] = str(company.id)
        else:
            # If there's no active company ID, use the user's default company
            company = corporate_profile.company
            # Update the session
            request.session['active_company_id'] = str(company.id)
    except CorporateUser.DoesNotExist:
        messages.error(request, "Your account is not associated with any company.")
        return redirect('corporate:corporate_login')

    # Get available games for this company
    # Only show games if the company is active
    if company.is_active:
        available_games = GameAccess.objects.filter(
            company=company,
            is_active=True
        ).order_by('game_name')
    else:
        available_games = GameAccess.objects.none()

    # Get recent game sessions for this company
    if corporate_profile.is_company_admin:
        # Admins can see all company sessions
        # Get sessions directly by company
        recent_sessions = GameSession.objects.filter(
            company=company
        ).order_by('-updated_at')[:10]

        # Log the number of sessions found
        import logging
        logging.info(f"Found {recent_sessions.count()} game sessions for company {company.name}")

        # If no sessions found by company, fall back to user IDs
        if recent_sessions.count() == 0:
            company_users = CorporateUser.objects.filter(company=company).values_list('user_id', flat=True)
            recent_sessions = GameSession.objects.filter(
                user_id__in=company_users
            ).order_by('-updated_at')[:10]

            # Log the number of sessions found
            logging.info(f"Found {recent_sessions.count()} game sessions for company {company.name} by user IDs")
    else:
        # Regular users only see their own sessions
        recent_sessions = GameSession.objects.filter(
            user=request.user
        ).order_by('-updated_at')[:10]

    # Get company leaderboard
    company_leaderboard = Leaderboard.objects.filter(
        company=company,
        is_global=False
    ).first()

    if company_leaderboard:
        top_performers = LeaderboardEntry.objects.filter(
            leaderboard=company_leaderboard
        ).order_by('-score')[:5]
    else:
        top_performers = []

    # Get user's certificates
    user_certificates = Certificate.objects.filter(
        user=request.user
    ).order_by('-issue_date')

    # Get count of pending users if admin
    pending_users_count = 0
    if corporate_profile.is_company_admin:
        pending_users_count = CorporateUser.objects.filter(
            company=company,
            status=CorporateUser.STATUS_PENDING
        ).count()

    context = {
        'corporate_profile': corporate_profile,
        'company': company,
        'available_games': available_games,
        'recent_sessions': recent_sessions,
        'top_performers': top_performers,
        'user_certificates': user_certificates,
        'is_admin': corporate_profile.is_company_admin,
        'pending_users_count': pending_users_count
    }

    return render(request, 'corporate/dashboard.html', context)


def game_list(request):
    """
    View available games for the company or public games for anonymous users.
    """
    company = None
    user_sessions = []

    # Get public games that are available to all users
    public_games = GameAccess.objects.filter(
        is_public=True,
        is_active=True
    ).order_by('game_name')

    # If no public games exist, create one
    if not public_games.exists():
        # Get the first company (or create one if none exists)
        company_for_public = Company.objects.first()
        if not company_for_public:
            company_for_public = Company.objects.create(
                name="Public Access Company",
                description="This company is used for public game access"
            )

        # Create public game access
        public_game = GameAccess.objects.create(
            company=company_for_public,
            game_id='prompt_master',
            game_name='Corporate Prompt Master',
            is_active=True,
            is_public=True
        )
        # Refresh the public games queryset
        public_games = GameAccess.objects.filter(
            is_public=True,
            is_active=True
        ).order_by('game_name')

    # If user is authenticated, get their company games too
    if request.user.is_authenticated:
        try:
            corporate_profile = request.user.corporate_profile
            company = corporate_profile.company

            # Get available games for this company
            # Only show company games if the company is active
            if company.is_active:
                company_games = GameAccess.objects.filter(
                    company=company,
                    is_active=True
                ).order_by('game_name')
            else:
                company_games = GameAccess.objects.none()

            # Combine public and company games, removing duplicates
            available_games = list(public_games)
            for game in company_games:
                if not any(pg.game_id == game.game_id for pg in available_games):
                    available_games.append(game)

            # Get user's game sessions
            user_sessions = GameSession.objects.filter(
                user=request.user
            ).order_by('-updated_at')
        except CorporateUser.DoesNotExist:
            # User is authenticated but doesn't have a corporate profile
            available_games = public_games
    else:
        # User is not authenticated, only show public games
        available_games = public_games

    return render(request, 'corporate/game_list.html', {
        'available_games': available_games,
        'company': company,
        'user_sessions': user_sessions,
        'is_public_view': not request.user.is_authenticated
    })


@login_required
def leaderboard(request):
    """
    View company and global leaderboards.
    """
    # Log the current user for debugging
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"Leaderboard view accessed by user: {request.user.username} (ID: {request.user.id})")

    try:
        corporate_profile = request.user.corporate_profile
        company = corporate_profile.company
        logger.info(f"User's company: {company.name} (ID: {company.id})")
    except CorporateUser.DoesNotExist:
        messages.error(request, "Your account is not associated with any company.")
        return redirect('corporate:corporate_login')

    # Get time period filter
    time_period = request.GET.get('period', 'all_time')

    # Get game filter
    game_id = request.GET.get('game', '')

    # Always filter by company users - users can only see players from their own company

    # Get available games for the company
    # Only show games if the company is active
    if company.is_active:
        available_games = GameAccess.objects.filter(
            company=company,
            is_active=True
        ).order_by('game_name')
    else:
        available_games = GameAccess.objects.none()
        messages.warning(request, "Your company is currently inactive. Leaderboard functionality is limited.")

    # Just use "All Games" and "Corporate Prompt Master" - remove duplicates
    games_list = [
        {'game_id': '', 'game_name': 'All Games'},
        {'game_id': 'corporate_prompt_master', 'game_name': 'Corporate Prompt Master'}
    ]

    # Get company leaderboard for all_time (we'll filter by date later)
    company_leaderboard = Leaderboard.objects.filter(
        company=company,
        time_period='all_time'
    ).first()

    if not company_leaderboard:
        # Create it if it doesn't exist
        company_leaderboard = Leaderboard.objects.create(
            name=f"{company.name} Leaderboard",
            company=company,
            is_global=False,
            time_period='all_time'
        )

    # Get global leaderboard for all_time (we'll filter by date later)
    global_leaderboard = Leaderboard.objects.filter(
        is_global=True,
        time_period='all_time'
    ).first()

    # Get entries for company leaderboard
    # First, get all users from this company
    company_users = CorporateUser.objects.filter(company=company).values_list('user_id', flat=True)

    # Get entries for company leaderboard, filtering to only show users from this company
    company_entries_query = LeaderboardEntry.objects.filter(
        leaderboard=company_leaderboard
    ).select_related('user', 'user__corporate_profile', 'user__corporate_profile__company')

    # Apply date filtering based on time_period
    from django.utils import timezone
    import datetime

    now = timezone.now()
    if time_period == 'daily':
        # Filter for entries from today
        today_start = now.replace(hour=0, minute=0, second=0, microsecond=0)
        company_entries_query = company_entries_query.filter(recorded_at__gte=today_start)
    elif time_period == 'weekly':
        # Filter for entries from the past 7 days
        week_start = now - datetime.timedelta(days=7)
        company_entries_query = company_entries_query.filter(recorded_at__gte=week_start)
    elif time_period == 'monthly':
        # Filter for entries from the past 30 days
        month_start = now - datetime.timedelta(days=30)
        company_entries_query = company_entries_query.filter(recorded_at__gte=month_start)

    # Apply game filter if specified
    if game_id:
        # Log the game_id being used for filtering
        logger.info(f"Filtering company leaderboard by game_id: {game_id}")
        company_entries_query = company_entries_query.filter(game_id=game_id)
    # Don't filter by game_id if none is specified - show all games

    # Log the query and results for debugging
    import logging
    logger = logging.getLogger(__name__)
    logger.info(f"Company leaderboard query: {company_entries_query.query}")
    logger.info(f"Company users: {list(company_users)}")

    company_entries = company_entries_query.order_by('-score')[:100]

    # Log the entries that will be displayed
    for entry in company_entries:
        logger.info(f"Company entry: User={entry.user.username}, Score={entry.score}, Game ID={entry.game_id}")

    # Add a flag to identify users from the same company and update game status
    for entry in company_entries:
        try:
            entry.is_same_company = (entry.user.corporate_profile.company.id == company.id)

            # Update game status from the most recent game session
            latest_session = GameSession.objects.filter(user=entry.user).order_by('-updated_at').first()
            if latest_session:
                if latest_session.game_completed:
                    entry.game_status = "Completed"
                else:
                    entry.game_status = "In Progress"

                # Also update the database entry to ensure consistency
                LeaderboardEntry.objects.filter(id=entry.id).update(game_status=entry.game_status)
        except Exception as e:
            logger.error(f"Error updating game status for user {entry.user.username}: {e}")
            entry.is_same_company = False

    # Get entries for global leaderboard
    if global_leaderboard and company.allow_leaderboard:
        global_entries_query = LeaderboardEntry.objects.filter(
            leaderboard=global_leaderboard
        ).select_related('user', 'user__corporate_profile', 'user__corporate_profile__company')

        # Apply date filtering based on time_period
        if time_period == 'daily':
            # Filter for entries from today
            global_entries_query = global_entries_query.filter(recorded_at__gte=today_start)
        elif time_period == 'weekly':
            # Filter for entries from the past 7 days
            global_entries_query = global_entries_query.filter(recorded_at__gte=week_start)
        elif time_period == 'monthly':
            # Filter for entries from the past 30 days
            global_entries_query = global_entries_query.filter(recorded_at__gte=month_start)

        # Apply game filter if specified
        if game_id:
            # Log the game_id being used for filtering
            logger.info(f"Filtering global leaderboard by game_id: {game_id}")
            global_entries_query = global_entries_query.filter(game_id=game_id)
        # Don't filter by game_id if none is specified - show all games

        # Always filter global leaderboard by company users
        # Users can only see players from their own company
        # We're using a join to filter by company rather than user_id list
        global_entries_query = global_entries_query.filter(
            user__corporate_profile__company=company
        )

        # Log the global query for debugging
        logger.info(f"Global leaderboard query: {global_entries_query.query}")

        global_entries = global_entries_query.order_by('-score')[:100]

        # Log the global entries that will be displayed
        for entry in global_entries:
            logger.info(f"Global entry: User={entry.user.username}, Score={entry.score}, Game ID={entry.game_id}")

        # Add a flag to identify users from the same company and update game status
        for entry in global_entries:
            try:
                entry.is_same_company = (entry.user.corporate_profile.company.id == company.id)

                # Update game status from the most recent game session
                latest_session = GameSession.objects.filter(user=entry.user).order_by('-updated_at').first()
                if latest_session:
                    if latest_session.game_completed:
                        entry.game_status = "Completed"
                    else:
                        entry.game_status = "In Progress"

                    # Also update the database entry to ensure consistency
                    LeaderboardEntry.objects.filter(id=entry.id).update(game_status=entry.game_status)
            except Exception as e:
                logger.error(f"Error updating game status for user {entry.user.username}: {e}")
                entry.is_same_company = False
    else:
        global_entries = []

    # Find user's rank in company leaderboard
    try:
        # Get list of user IDs from company entries
        company_user_ids = [entry.user_id for entry in company_entries]
        user_company_rank = company_user_ids.index(request.user.id) + 1
    except (ValueError, IndexError):
        user_company_rank = None

    # Find user's rank in global leaderboard
    try:
        # Get list of user IDs from global entries
        global_user_ids = [entry.user_id for entry in global_entries]
        user_global_rank = global_user_ids.index(request.user.id) + 1
    except (ValueError, IndexError):
        user_global_rank = None

    context = {
        'company': company,
        'company_leaderboard': company_leaderboard,
        'global_leaderboard': global_leaderboard,
        'company_entries': company_entries,
        'global_entries': global_entries,
        'time_period': time_period,
        'games_list': games_list,
        'selected_game': game_id,  # Pass the selected game_id to the template
        'user_company_rank': user_company_rank,
        'user_global_rank': user_global_rank,
        'time_period': time_period,
        'is_admin': corporate_profile.is_company_admin
    }

    return render(request, 'corporate/leaderboard.html', context)


# Certificate Views
@login_required
def view_certificates(request):
    """
    View user's certificates.
    """
    certificates = Certificate.objects.filter(
        user=request.user
    ).order_by('-issue_date')

    return render(request, 'corporate/certificates.html', {
        'certificates': certificates
    })


@login_required
def download_certificate(request, certificate_id):
    """
    Download a certificate as PDF or display HTML version if PDF generation is not available.
    """
    certificate = get_object_or_404(Certificate, id=certificate_id, user=request.user)

    # Generate PDF
    pdf_bytes, _ = generate_certificate_pdf(
        user=certificate.user,
        game_session=certificate.game_session,
        template=certificate.template
    )

    # If PDF generation is not available, render HTML version
    if pdf_bytes is None:
        html_content = generate_certificate_html(
            user=certificate.user,
            game_session=certificate.game_session,
            template=certificate.template
        )
        return HttpResponse(html_content, content_type='text/html')

    # Create response for PDF
    response = HttpResponse(pdf_bytes, content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="certificate_{certificate.verification_code}.pdf"'

    return response


def verify_certificate_view(request):
    """
    Verify a certificate using its verification code.
    """
    verification_code = request.GET.get('code', '')
    certificate = None

    if verification_code:
        certificate = verify_certificate(verification_code)

    return render(request, 'corporate/verify_certificate.html', {
        'verification_code': verification_code,
        'certificate': certificate
    })


# Company Switcher
@login_required
def create_company(request):
    """
    Allow logged-in users to create a new company without logging out.
    """
    # Check if the user already has a corporate profile
    has_profile = hasattr(request.user, 'corporate_profile')

    if request.method == 'POST':
        company_form = CompanyRegistrationForm(request.POST, request.FILES)

        if company_form.is_valid():
            # Create company with user as owner
            company = company_form.save(commit=False)
            company.owner = request.user
            company.is_active = False  # Company starts as inactive
            company.pending_approval = True  # Mark as pending approval
            company.save()

            # Handle corporate profile based on whether the user already has one
            if has_profile:
                # Store the old company for the success message
                old_company = request.user.corporate_profile.company

                # Update the user's corporate profile to point to the new company
                request.user.corporate_profile.company = company
                request.user.corporate_profile.is_company_admin = True  # User is admin
                request.user.corporate_profile.save()

                messages.info(request, f"Your profile has been moved from '{old_company.name}' to '{company.name}'.")
            else:
                # Create a new corporate profile for the user
                CorporateUser.objects.create(
                    user=request.user,
                    company=company,
                    is_company_admin=True  # User is admin
                )

            # Create default leaderboard for company
            Leaderboard.objects.create(
                name=f"{company.name} Leaderboard",
                company=company,
                is_global=False,
                time_period='all_time'
            )

            # Set the new company as active
            request.session['active_company_id'] = str(company.id)

            messages.success(request, f"Your company '{company.name}' has been created and is pending approval by an administrator.")
            return redirect('corporate:corporate_dashboard')
    else:
        company_form = CompanyRegistrationForm()

    return render(request, 'corporate/create_company.html', {
        'company_form': company_form,
        'has_profile': has_profile,
        'current_company': request.user.corporate_profile.company if has_profile else None
    })


def company_switch(request):
    """
    Allow users to switch between companies they belong to.
    """
    user = request.user

    # Ensure session is created
    if not request.session.session_key:
        request.session.save()

    # Get all companies the user belongs to
    try:
        # Get the user's primary company from their corporate profile
        primary_company = user.corporate_profile.company

        # Start with the primary company
        user_companies = [primary_company]

        # Find all other companies the user belongs to
        other_companies = Company.objects.filter(
            employees__user=user
        ).exclude(id=primary_company.id).order_by('name')

        # Add other companies to the list
        user_companies.extend(other_companies)

        # Get the active company ID from the session
        active_company_id = request.session.get('active_company_id')
        active_company = None

        if active_company_id:
            try:
                active_company = Company.objects.get(id=active_company_id)
                # Verify that the user is associated with this company
                if not CorporateUser.objects.filter(user=user, company=active_company).exists():
                    active_company = primary_company
                    # Update session with primary company
                    request.session['active_company_id'] = str(primary_company.id)
                    request.session.modified = True
            except Company.DoesNotExist:
                active_company = primary_company
                # Update session with primary company
                request.session['active_company_id'] = str(primary_company.id)
                request.session.modified = True
        else:
            active_company = primary_company
            # Set primary company as active in session
            request.session['active_company_id'] = str(primary_company.id)
            request.session.modified = True

        # Handle GET request with company_id parameter (for dropdown quick switch)
        company_id_param = request.GET.get('company_id')
        if company_id_param:
            try:
                company = Company.objects.get(id=company_id_param)
                # Verify that the user is associated with this company
                if CorporateUser.objects.filter(user=user, company=company).exists():
                    # Update the session
                    request.session['active_company_id'] = str(company.id)
                    request.session.modified = True
                    messages.success(request, f"Switched to {company.name}.")
                    return redirect('corporate:corporate_dashboard')
                else:
                    messages.error(request, "You don't have access to this company.")
            except Company.DoesNotExist:
                messages.error(request, "Company not found.")

        # Handle form submission
        if request.method == 'POST':
            company_id = request.POST.get('company_id')
            if company_id:
                try:
                    company = Company.objects.get(id=company_id)
                    # Verify that the user is associated with this company
                    if CorporateUser.objects.filter(user=user, company=company).exists():
                        # Update the session
                        request.session['active_company_id'] = str(company.id)
                        request.session.modified = True
                        messages.success(request, f"Switched to {company.name}.")
                        return redirect('corporate:corporate_dashboard')
                    else:
                        messages.error(request, "You don't have access to this company.")
                except Company.DoesNotExist:
                    messages.error(request, "Company not found.")

        context = {
            'user_companies': user_companies,
            'active_company': active_company
        }

        return render(request, 'corporate/company_switch.html', context)

    except CorporateUser.DoesNotExist:
        messages.error(request, "Your account is not associated with any company.")
        return redirect('corporate:corporate_login')


# Admin Views
@login_required
def company_users(request):
    """
    View and manage company users (admin only).
    """
    # Ensure session is created and saved
    if not request.session.session_key:
        request.session.save()

    # Explicitly mark the session as modified to ensure it's saved
    request.session.modified = True

    try:
        corporate_profile = request.user.corporate_profile
        if not corporate_profile.is_company_admin:
            messages.error(request, "You don't have permission to access this page.")
            return redirect('corporate:corporate_dashboard')

        company = corporate_profile.company

        # Ensure active_company_id is set in session
        if 'active_company_id' not in request.session:
            request.session['active_company_id'] = str(company.id)
            request.session.modified = True
    except CorporateUser.DoesNotExist:
        messages.error(request, "Your account is not associated with any company.")
        return redirect('corporate:corporate_login')

    # Get all active users for this company
    company_users = CorporateUser.objects.filter(
        company=company,
        status=CorporateUser.STATUS_ACTIVE
    ).select_related('user').order_by('user__first_name', 'user__last_name')

    # Get pending users for this company
    pending_users = CorporateUser.objects.filter(
        company=company,
        status=CorporateUser.STATUS_PENDING
    ).select_related('user', 'registration_link').order_by('created_at')

    # Initialize invite_form at the beginning to ensure it's always defined
    invite_form = CorporateUserInviteForm()

    # Handle user invite form and user approval/rejection
    if request.method == 'POST':
        if 'action' in request.POST and request.POST['action'] == 'approve_user':
            # Handle user approval
            user_id = request.POST.get('user_id')
            if user_id:
                try:
                    corp_user = CorporateUser.objects.get(user_id=user_id, company=company, status=CorporateUser.STATUS_PENDING)
                    corp_user.status = CorporateUser.STATUS_ACTIVE
                    corp_user.save()
                    messages.success(request, f"User {corp_user.user.get_full_name() or corp_user.user.username} has been approved.")

                    # Send notification email to the user
                    try:
                        subject = f"Your account has been approved - {company.name}"
                        message = f"Your account for {company.name} has been approved. You can now log in and access all company features."
                        from django.core.mail import send_mail
                        send_mail(
                            subject,
                            message,
                            settings.DEFAULT_FROM_EMAIL,
                            [corp_user.user.email],
                            fail_silently=True,
                        )
                    except Exception as e:
                        logger.error(f"Error sending approval email: {str(e)}")

                    return redirect('corporate:company_users')
                except CorporateUser.DoesNotExist:
                    messages.error(request, "User not found or already approved.")

        elif 'action' in request.POST and request.POST['action'] == 'reject_user':
            # Handle user rejection
            user_id = request.POST.get('user_id')
            if user_id:
                try:
                    corp_user = CorporateUser.objects.get(user_id=user_id, company=company, status=CorporateUser.STATUS_PENDING)
                    user_email = corp_user.user.email
                    user_name = corp_user.user.get_full_name() or corp_user.user.username

                    # Delete the corporate profile
                    corp_user.delete()

                    messages.success(request, f"User {user_name} has been rejected.")

                    # Send notification email to the user
                    try:
                        subject = f"Your account request was declined - {company.name}"
                        message = f"Your request to join {company.name} has been declined. Please contact the company administrator for more information."
                        from django.core.mail import send_mail
                        send_mail(
                            subject,
                            message,
                            settings.DEFAULT_FROM_EMAIL,
                            [user_email],
                            fail_silently=True,
                        )
                    except Exception as e:
                        logger.error(f"Error sending rejection email: {str(e)}")

                    return redirect('corporate:company_users')
                except CorporateUser.DoesNotExist:
                    messages.error(request, "User not found.")

        elif 'action' in request.POST and request.POST['action'] == 'remove_user':
            # Handle existing user removal logic
            pass
        else:
            # Handle user invite form
            invite_form = CorporateUserInviteForm(request.POST)
            if invite_form.is_valid():
                # TODO: Implement user invitation logic
                messages.success(request, f"Invitation sent to {invite_form.cleaned_data['email']}.")
                return redirect('corporate:company_users')

    return render(request, 'corporate/company_users.html', {
        'company': company,
        'company_users': company_users,
        'pending_users': pending_users,
        'pending_users_count': pending_users.count(),
        'invite_form': invite_form
    })


@login_required
def user_progress(request, user_id=None):
    """
    View detailed progress for a specific user or all users.
    """
    # Ensure session is created and saved
    if not request.session.session_key:
        request.session.save()

    # Explicitly mark the session as modified to ensure it's saved
    request.session.modified = True

    try:
        corporate_profile = request.user.corporate_profile
        company = corporate_profile.company

        # Ensure active_company_id is set in session
        if 'active_company_id' not in request.session:
            request.session['active_company_id'] = str(company.id)
            request.session.modified = True

        # Check permissions
        if not corporate_profile.is_company_admin and str(request.user.id) != user_id:
            messages.error(request, "You don't have permission to view this user's progress.")
            return redirect('corporate:corporate_dashboard')
    except CorporateUser.DoesNotExist:
        messages.error(request, "Your account is not associated with any company.")
        return redirect('corporate:corporate_login')

    # Get company users
    company_users = CorporateUser.objects.filter(
        company=company
    ).select_related('user')

    # If user_id is provided, show detailed progress for that user
    if user_id:
        target_user = get_object_or_404(CorporateUser, user_id=user_id, company=company).user

        # Get all game sessions for this user
        user_sessions = GameSession.objects.filter(
            user=target_user
        ).order_by('-updated_at')

        # Get certificates for this user
        user_certificates = Certificate.objects.filter(
            user=target_user
        ).order_by('-issue_date')

        context = {
            'company': company,
            'target_user': target_user,
            'user_sessions': user_sessions,
            'user_certificates': user_certificates,
            'is_admin': corporate_profile.is_company_admin
        }

        return render(request, 'corporate/user_progress_detail.html', context)
    else:
        # Show summary progress for all users
        if not corporate_profile.is_company_admin:
            messages.error(request, "You don't have permission to view all users' progress.")
            return redirect('corporate:corporate_dashboard')

        # Get summary data for all users
        user_progress = []

        for corp_user in company_users:
            user = corp_user.user

            # Get highest score and role
            user_stats = GameSession.objects.filter(
                user=user
            ).aggregate(
                highest_score=Max('performance_score'),
                total_sessions=Count('id')
            )

            # Get latest certificate
            latest_cert = Certificate.objects.filter(
                user=user
            ).order_by('-issue_date').first()

            user_progress.append({
                'user': user,
                'corporate_profile': corp_user,
                'highest_score': user_stats['highest_score'] or 0,
                'total_sessions': user_stats['total_sessions'] or 0,
                'latest_certificate': latest_cert
            })

        # Sort by highest score
        user_progress.sort(key=lambda x: x['highest_score'], reverse=True)

        return render(request, 'corporate/user_progress.html', {
            'company': company,
            'user_progress': user_progress
        })


# API Views for AJAX requests
@login_required
@require_POST
def update_leaderboard(request):
    """
    Update leaderboard with new game session results.
    Ensures canonical game_id and game_name are used from GameSession.
    """
    try:
        data = json.loads(request.body)
        session_id = data.get('session_id')

        if not session_id:
            return JsonResponse({'status': 'error', 'message': 'Session ID is required.'}, status=400)

        # Get the game session
        game_session = get_object_or_404(GameSession, id=session_id, user=request.user)

        # Determine the authoritative game_id and game_name from the GameSession
        # Assuming GameSession has a ForeignKey 'game_definition' to the GameAccess model,
        # and GameAccess model has 'game_id' (slug) and 'game_name' (display name) fields.
        if hasattr(game_session, 'game_definition') and game_session.game_definition:
            authoritative_game_id = game_session.game_definition.game_id
            authoritative_game_name = game_session.game_definition.game_name
        else:
            logger.warning(
                f"GameSession {game_session.id} lacks 'game_definition' or it's None. "
                f"Falling back to payload/defaults for game_id and game_name. "
                f"Payload game_id: {data.get('game_id')}, Payload game_name: {data.get('game_name')}"
            )
            # Fallback to original logic if game_definition is not available
            authoritative_game_id = data.get('game_id', 'prompt_master')
            authoritative_game_name = data.get('game_name', 'Corporate Prompt Master')

        # Always use the standard game_id and game_name for consistency
        authoritative_game_id = 'corporate_prompt_master'
        authoritative_game_name = 'Corporate Prompt Master'
        logger.info(f"Using standard game_id: {authoritative_game_id} and game_name: {authoritative_game_name}")


        # Get user's corporate profile
        corporate_profile = request.user.corporate_profile
        company = corporate_profile.company

        # Define time periods to update
        time_periods = ['daily', 'weekly', 'monthly', 'all_time']
        now = timezone.now()

        for period in time_periods:
            # Update company leaderboard for the period
            company_leaderboard, _ = Leaderboard.objects.get_or_create(
                company=company,
                time_period=period,
                defaults={'name': f"{company.name} Leaderboard ({period.title()})"}
            )

            # Determine game status
            if game_session.game_completed:
                game_status = "Completed"
            else:
                game_status = "In Progress"

            # Use update_or_create for company entry
            # Only create entries for users who belong to this company
            LeaderboardEntry.objects.update_or_create(
                leaderboard=company_leaderboard,
                user=request.user,
                game_id=authoritative_game_id, # Use authoritative game_id
                defaults={
                    'score': game_session.performance_score,
                    'highest_role': game_session.current_role,
                    'game_name': authoritative_game_name, # Use authoritative game_name
                    'game_status': game_status, # Add game status
                    'recorded_at': now # Ensure timestamp is updated
                }
            )

            # Update global leaderboard for the period if allowed
            if company.allow_leaderboard:
                global_leaderboard, _ = Leaderboard.objects.get_or_create(
                    is_global=True,
                    time_period=period,
                    defaults={'name': f"Global Leaderboard ({period.title()})"}
                )

                # Use update_or_create for global entry
                LeaderboardEntry.objects.update_or_create(
                    leaderboard=global_leaderboard,
                    user=request.user,
                    game_id=authoritative_game_id, # Use authoritative game_id
                    defaults={
                        'score': game_session.performance_score,
                        'highest_role': game_session.current_role,
                        'game_name': authoritative_game_name, # Use authoritative game_name
                        'game_status': game_status, # Add game status
                        'recorded_at': now # Ensure timestamp is updated
                    }
                )

        return JsonResponse({'status': 'success', 'message': 'Leaderboard updated successfully.'})
    except GameSession.DoesNotExist:
        logger.warning(f"Game session not found for id: {session_id}", exc_info=True)
        return JsonResponse({'status': 'error', 'message': 'Game session not found.'}, status=404)
    except CorporateUser.DoesNotExist: # Make sure CorporateUser is imported
        logger.warning(f"Corporate profile not found for user: {request.user.id}", exc_info=True)
        return JsonResponse({'status': 'error', 'message': 'Corporate profile not found.'}, status=403)
    except json.JSONDecodeError:
        logger.warning("Invalid JSON payload received.", exc_info=True)
        return JsonResponse({'status': 'error', 'message': 'Invalid JSON payload.'}, status=400)
    except Exception as e:
        logger.error(f"Unexpected error in update_leaderboard: {e}", exc_info=True)
        return JsonResponse({'status': 'error', 'message': f'An unexpected error occurred: {str(e)}'}, status=500)
