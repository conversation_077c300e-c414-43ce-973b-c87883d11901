# Generated by Django 5.2 on 2025-05-21 16:06

import django.utils.timezone
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Certificate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "title",
                    models.Char<PERSON>ield(
                        default="Prompt Engineering Excellence", max_length=255
                    ),
                ),
                (
                    "description",
                    models.TextField(
                        default="Has successfully completed the Corporate Prompt Master training program."
                    ),
                ),
                ("issue_date", models.DateTimeField(default=django.utils.timezone.now)),
                ("template", models.CharField(default="standard", max_length=50)),
                (
                    "verification_code",
                    models.CharField(editable=False, max_length=50, unique=True),
                ),
                ("final_score", models.IntegerField(default=0)),
                ("highest_role", models.Char<PERSON>ield(blank=True, max_length=100)),
                (
                    "completed_games",
                    models.TextField(
                        default="[]", help_text="JSON list of completed games"
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Company",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "logo",
                    models.ImageField(
                        blank=True, null=True, upload_to="company_logos/"
                    ),
                ),
                ("description", models.TextField(blank=True)),
                (
                    "phone_number",
                    models.CharField(
                        help_text="Company contact phone number", max_length=20
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "is_active",
                    models.BooleanField(
                        default=False,
                        help_text="Whether the company is approved and active on the platform",
                    ),
                ),
                (
                    "pending_approval",
                    models.BooleanField(
                        default=True,
                        help_text="Whether the company is pending approval by a superadmin",
                    ),
                ),
                ("approved_at", models.DateTimeField(blank=True, null=True)),
                (
                    "allow_leaderboard",
                    models.BooleanField(
                        default=True,
                        help_text="Allow employees to appear on leaderboards",
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Companies",
            },
        ),
        migrations.CreateModel(
            name="CompanyInvitation",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("email", models.EmailField(max_length=254)),
                ("token", models.CharField(max_length=100, unique=True)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("accepted", "Accepted"),
                            ("declined", "Declined"),
                            ("expired", "Expired"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("invited_at", models.DateTimeField(auto_now_add=True)),
                ("expires_at", models.DateTimeField()),
                ("accepted_at", models.DateTimeField(blank=True, null=True)),
                ("job_title", models.CharField(blank=True, max_length=255)),
                ("department", models.CharField(blank=True, max_length=255)),
                ("is_admin", models.BooleanField(default=False)),
                ("message", models.TextField(blank=True)),
            ],
            options={
                "verbose_name": "Company Invitation",
                "verbose_name_plural": "Company Invitations",
            },
        ),
        migrations.CreateModel(
            name="CorporateUser",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("job_title", models.CharField(blank=True, max_length=255)),
                ("department", models.CharField(blank=True, max_length=255)),
                ("employee_id", models.CharField(blank=True, max_length=50)),
                (
                    "is_company_admin",
                    models.BooleanField(
                        default=False,
                        help_text="Can manage company settings and view all employee data",
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[("active", "Active"), ("pending", "Pending Approval")],
                        default="active",
                        help_text="Whether the user is active or pending admin approval",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="GameAccess",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "game_id",
                    models.CharField(
                        help_text="Identifier for the game", max_length=100
                    ),
                ),
                ("game_name", models.CharField(max_length=255)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_public",
                    models.BooleanField(
                        default=False,
                        help_text="If true, this game is accessible to anonymous users",
                    ),
                ),
                ("access_granted_at", models.DateTimeField(auto_now_add=True)),
                ("access_expires_at", models.DateTimeField(blank=True, null=True)),
                ("custom_settings", models.JSONField(blank=True, default=dict)),
            ],
            options={
                "verbose_name_plural": "Game access",
            },
        ),
        migrations.CreateModel(
            name="Leaderboard",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=255)),
                (
                    "is_global",
                    models.BooleanField(
                        default=False,
                        help_text="If true, this leaderboard is visible to all companies",
                    ),
                ),
                (
                    "time_period",
                    models.CharField(
                        choices=[
                            ("all_time", "All Time"),
                            ("monthly", "Monthly"),
                            ("weekly", "Weekly"),
                            ("daily", "Daily"),
                        ],
                        default="all_time",
                        max_length=20,
                    ),
                ),
                ("max_entries", models.IntegerField(default=100)),
                ("active", models.BooleanField(default=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="LeaderboardEntry",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("score", models.IntegerField()),
                ("highest_role", models.CharField(blank=True, max_length=100)),
                (
                    "game_id",
                    models.CharField(
                        blank=True, help_text="Identifier for the game", max_length=100
                    ),
                ),
                (
                    "game_name",
                    models.CharField(
                        blank=True, help_text="Name of the game", max_length=255
                    ),
                ),
                (
                    "game_status",
                    models.CharField(
                        blank=True,
                        help_text="Status of the game (completed, in progress, etc.)",
                        max_length=50,
                    ),
                ),
                ("recorded_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name_plural": "Leaderboard entries",
                "ordering": ["-score"],
            },
        ),
        migrations.CreateModel(
            name="RegistrationLink",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "token",
                    models.UUIDField(default=uuid.uuid4, editable=False, unique=True),
                ),
                (
                    "intended_role",
                    models.CharField(
                        choices=[("admin", "Administrator"), ("member", "Member")],
                        default="member",
                        help_text="The role the user will be assigned upon joining via this link.",
                        max_length=50,
                    ),
                ),
                (
                    "approval_type",
                    models.CharField(
                        choices=[
                            ("auto", "Automatic Activation"),
                            ("admin", "Requires Admin Approval"),
                        ],
                        default="auto",
                        help_text="Whether users joining via this link need admin approval or are automatically activated.",
                        max_length=20,
                    ),
                ),
                (
                    "expires_at",
                    models.DateTimeField(
                        blank=True,
                        help_text="Optional date/time when this link expires.",
                        null=True,
                    ),
                ),
                (
                    "max_uses",
                    models.PositiveIntegerField(
                        blank=True,
                        help_text="Optional limit on the number of times this link can be used.",
                        null=True,
                    ),
                ),
                ("uses_count", models.PositiveIntegerField(default=0, editable=False)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "notes",
                    models.CharField(
                        blank=True,
                        help_text="Optional notes for the creator about this link's purpose.",
                        max_length=255,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Registration Link",
                "verbose_name_plural": "Registration Links",
                "ordering": ["-created_at"],
            },
        ),
    ]
