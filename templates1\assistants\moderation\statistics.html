{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Statistics - {{ assistant.name }}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/moderation-dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">Community Statistics</h1>
                <div>
                    <a href="{% url 'assistants:moderation_dashboard' company.id assistant.id %}" class="btn btn-outline-primary me-2">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
            <p class="text-muted">View statistics and analytics for {{ assistant.name }}</p>
        </div>
    </div>

    <!-- Time Period Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-6">
                            <label for="period" class="form-label">Time Period</label>
                            <select class="form-select" id="period" name="period" onchange="this.form.submit()">
                                <option value="week" {% if period == 'week' %}selected{% endif %}>Last 7 Days</option>
                                <option value="month" {% if period == 'month' %}selected{% endif %}>Last 30 Days</option>
                                <option value="year" {% if period == 'year' %}selected{% endif %}>Last Year</option>
                                <option value="all" {% if period == 'all' %}selected{% endif %}>All Time</option>
                            </select>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards -->
    <div class="row g-3 mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="bi bi-people-fill text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0">New Users</h6>
                            <h3 class="mb-0">{{ new_users }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="bi bi-file-text-fill text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0">New Contexts</h6>
                            <h3 class="mb-0">{{ new_contexts }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 p-3 rounded">
                                <i class="bi bi-flag-fill text-warning"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0">New Reports</h6>
                            <h3 class="mb-0">{{ new_reports }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="bi bi-shield-fill text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted mb-0">Moderation Actions</h6>
                            <h3 class="mb-0">{{ moderation_actions }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row g-3">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">User Activity</h5>
                </div>
                <div class="card-body">
                    <canvas id="userActivityChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Content Distribution</h5>
                </div>
                <div class="card-body">
                    <canvas id="contentDistributionChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-3 mt-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Report Types</h5>
                </div>
                <div class="card-body">
                    <canvas id="reportTypesChart" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Moderation Actions</h5>
                </div>
                <div class="card-body">
                    <canvas id="moderationActionsChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'js/moderation-dashboard.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sample data for charts - in a real implementation, this would come from the backend
        
        // User Activity Chart
        const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');
        const userActivityChart = new Chart(userActivityCtx, {
            type: 'line',
            data: {
                labels: ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7'],
                datasets: [{
                    label: 'New Users',
                    data: [5, 8, 12, 7, 10, 15, 9],
                    borderColor: 'rgba(13, 110, 253, 1)',
                    backgroundColor: 'rgba(13, 110, 253, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Active Users',
                    data: [20, 25, 30, 22, 28, 35, 32],
                    borderColor: 'rgba(25, 135, 84, 1)',
                    backgroundColor: 'rgba(25, 135, 84, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Content Distribution Chart
        const contentDistributionCtx = document.getElementById('contentDistributionChart').getContext('2d');
        const contentDistributionChart = new Chart(contentDistributionCtx, {
            type: 'bar',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: 'New Contexts',
                    data: [45, 60, 75, 55],
                    backgroundColor: 'rgba(25, 135, 84, 0.7)'
                }, {
                    label: 'Upvotes',
                    data: [120, 150, 180, 140],
                    backgroundColor: 'rgba(13, 110, 253, 0.7)'
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
        
        // Report Types Chart
        const reportTypesCtx = document.getElementById('reportTypesChart').getContext('2d');
        const reportTypesChart = new Chart(reportTypesCtx, {
            type: 'pie',
            data: {
                labels: ['Spam', 'Inappropriate', 'Offensive', 'Misinformation', 'Other'],
                datasets: [{
                    data: [15, 25, 20, 30, 10],
                    backgroundColor: [
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(111, 66, 193, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: false
                    }
                }
            }
        });
        
        // Moderation Actions Chart
        const moderationActionsCtx = document.getElementById('moderationActionsChart').getContext('2d');
        const moderationActionsChart = new Chart(moderationActionsCtx, {
            type: 'doughnut',
            data: {
                labels: ['Approved', 'Rejected', 'Removed', 'Warned', 'Banned'],
                datasets: [{
                    data: [40, 20, 15, 10, 5],
                    backgroundColor: [
                        'rgba(25, 135, 84, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(13, 110, 253, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    title: {
                        display: false
                    }
                }
            }
        });
    });
</script>
{% endblock %}
