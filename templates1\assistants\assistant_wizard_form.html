{% extends "assistants/base.html" %}
{% load widget_tweaks %}

{% block title %}Create Assistant - {{ step_title }}{% endblock %}

{% block main_content %} {# Changed from assistant_content to match base.html #}
<div class="card shadow-sm">
    <div class="card-header">
        <h4 class="mb-0">{{ step_title }}</h4>
    </div>
    {% if wizard.steps.current == 'basic' %}
    {% if is_community_entity or is_community_section %}
    <div class="alert alert-info m-3">
        <i class="bi bi-info-circle-fill me-2"></i>
        <strong>Note:</strong> Within the Community Assistants section, only community assistants can be created. The assistant type has been pre-selected for you.
    </div>
    {% else %}
    <div class="alert alert-info m-3">
        <i class="bi bi-info-circle-fill me-2"></i>
        <strong>Note:</strong> Community assistants can only be created within community entities or from the Community Assistants section. Please select either General Purpose or Customer Support type.
    </div>
    {% endif %}
    {% endif %}
    <div class="card-body">
        <form action="" method="post" enctype="multipart/form-data"> {# Added enctype #}
            {% csrf_token %}
            {{ wizard.management_form }}

            {% if wizard.form.errors %}
                <div class="alert alert-danger">
                    Please correct the errors below.
                </div>
            {% endif %}

            {% for field in wizard.form %}
                <div class="mb-3">
                    <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                    {% render_field field class+="form-control" %}
                    {% if field.help_text %}
                        <div class="form-text">{{ field.help_text }}</div>
                    {% endif %}
                    {% for error in field.errors %}
                        <div class="invalid-feedback d-block">{{ error }}</div>
                    {% endfor %}
                    {# Add word count display specifically for the description field #}
                    {% if field.name == 'description' %}
                        <div id="description-word-count" class="form-text text-muted small mt-1">0/500 words</div>
                    {% endif %}
                </div>
            {% endfor %}

            <hr>

            <div class="d-flex justify-content-between mt-4">
                {% if wizard.steps.prev %}
                    <button name="wizard_goto_step" type="submit" value="{{ wizard.steps.prev }}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Previous Step
                    </button>
                {% else %}
                    <span></span> {# Placeholder to keep alignment #}
                {% endif %}

                <button type="submit" class="btn btn-primary">
                    {% if wizard.steps.next %}
                        Next Step <i class="bi bi-arrow-right"></i>
                    {% else %}
                        Create Assistant <i class="bi bi-check-lg"></i>
                    {% endif %}
                </button>
            </div>
        </form>
    </div>
    <div class="card-footer text-muted">
        Step {{ wizard.steps.step1 }} of {{ wizard.steps.count }}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // --- Word Count Logic for Wizard ---
    // Wizard forms often prefix fields with step number, e.g., '0-description'
    // We need to find the description field regardless of the prefix.
    const descriptionTextarea = document.querySelector('textarea[name$="-description"]'); // Find textarea whose name ends with -description
    const wordCountDisplay = document.getElementById('description-word-count');
    const wordLimit = 500;

    function updateWordCount() {
        // Ensure elements exist, especially since description is only on step 1 (index 0)
        if (!descriptionTextarea || !wordCountDisplay) return;

        const text = descriptionTextarea.value.trim();
        // Simple word count based on spaces, filtering empty strings from split.
        const words = text === '' ? 0 : text.split(/\s+/).filter(Boolean).length;

        wordCountDisplay.textContent = `${words}/${wordLimit} words`;

        if (words > wordLimit) {
            wordCountDisplay.classList.add('text-danger');
            wordCountDisplay.classList.remove('text-muted');
            descriptionTextarea.classList.add('is-invalid'); // Add Bootstrap invalid class
            // Optionally disable submit button
            // document.querySelector('button[type="submit"]').disabled = true;
        } else {
            wordCountDisplay.classList.remove('text-danger');
            wordCountDisplay.classList.add('text-muted');
            descriptionTextarea.classList.remove('is-invalid'); // Remove Bootstrap invalid class
            // Optionally enable submit button
            // document.querySelector('button[type="submit"]').disabled = false;
        }
    }

    // Add listener only if the description field exists on the current step
    if (descriptionTextarea) {
        descriptionTextarea.addEventListener('input', updateWordCount);
        // Initial count on page load
        updateWordCount();
    } else {
         // Optional: Log if the description field isn't found on this step
         // console.log("Description textarea not found on this wizard step.");
    }
    // --- End Word Count Logic ---
});
</script>
{% endblock %}
