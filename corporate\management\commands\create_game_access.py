from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from corporate.models import Company, GameAccess


class Command(BaseCommand):
    help = 'Create game access for a company'

    def add_arguments(self, parser):
        parser.add_argument('company_id', type=int, help='Company ID')
        parser.add_argument('--game-id', type=str, default='prompt_master', help='Game ID')
        parser.add_argument('--game-name', type=str, default='Corporate Prompt Master', help='Game name')

    def handle(self, *args, **options):
        company_id = options['company_id']
        game_id = options['game_id']
        game_name = options['game_name']

        try:
            company = Company.objects.get(id=company_id)
        except Company.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Company with ID {company_id} does not exist'))
            return

        # Check if game access already exists
        if GameAccess.objects.filter(company=company, game_id=game_id).exists():
            self.stdout.write(self.style.WARNING(f'Game access for {game_id} already exists for {company.name}'))
            return

        # Create game access
        game_access = GameAccess.objects.create(
            company=company,
            game_id=game_id,
            game_name=game_name,
            is_active=True
        )

        self.stdout.write(self.style.SUCCESS(f'Successfully created game access for {company.name}: {game_name}'))
