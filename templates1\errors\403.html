{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Access Denied - 24seven{% endblock %}

{% block body_class %}error-page{% endblock %}

{# Removed empty navbar block to inherit from base #}

{% block content %}
<div class="min-vh-100 d-flex align-items-center">
    <div class="container text-center py-5">
        <div class="display-1 text-danger mb-3">
            <i class="bi bi-shield-lock"></i>
        </div>

        <h1 class="display-4 fw-bold mb-3">Access Denied</h1>
        <h2 class="h4 mb-4">You don't have permission to access this page</h2>

        <p class="lead text-muted mb-4">
            {% if user.is_authenticated %}
                Please contact your company administrator if you believe this is a mistake.
            {% else %}
                Please log in to access this page.
            {% endif %}
        </p>

        <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
            {% if user.is_authenticated %}
                <a href="{% url 'home' %}" class="btn btn-primary"> {# Changed URL to 'home' #}
                    <i class="bi bi-house-door me-2"></i> {# Changed icon to 'home' #}
                    Go to Home Page {# Changed text #}
                </a>
                {% if request.user.is_staff %}
                    <a href="{% url 'admin:index' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-gear me-2"></i>
                        Admin Panel
                    </a>
                {% endif %}
            {% else %}
                <a href="{% url 'accounts:login' %}?next={{ request.path }}" class="btn btn-primary">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    Log In
                </a>
                <a href="{% url 'home' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-house-door me-2"></i>
                    Home Page
                </a>
            {% endif %}
        </div>

        {% if user.is_staff %}
            <div class="mt-4 text-muted">
                <small>
                    <strong>Debug Info:</strong><br>
                    Request Path: {{ request.path }}<br>
                    User: {{ user.username }}<br>
                    Permissions: {{ user.get_all_permissions|join:", "|default:"None" }}
                </small>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{# Removed empty footer block to inherit from base #}

{% block extra_css %}
<style>
.bi-shield-lock {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
</style>
{% endblock %}
