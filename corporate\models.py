from django.db import models
from django.contrib.auth.models import User, Group
from django.utils import timezone
from django.urls import reverse
import uuid
import secrets

class Company(models.Model):
    """
    Model to represent a corporate entity that can have multiple users.
    """
    name = models.CharField(max_length=255)
    logo = models.ImageField(upload_to='company_logos/', null=True, blank=True)
    description = models.TextField(blank=True)
    phone_number = models.CharField(max_length=20, help_text="Company contact phone number")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Company owner
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='owned_corporate_companies', null=True, blank=True)

    # Company status
    is_active = models.BooleanField(default=False, help_text="Whether the company is approved and active on the platform")
    pending_approval = models.BooleanField(default=True, help_text="Whether the company is pending approval by a superadmin")
    approved_at = models.DateTimeField(null=True, blank=True)
    approved_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='approved_companies')

    # Company settings
    allow_leaderboard = models.BooleanField(default=True, help_text="Allow employees to appear on leaderboards")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name_plural = "Companies"


class CorporateUser(models.Model):
    """
    Extension of the User model to add company affiliation and other corporate-specific fields.
    """
    # Status choices for approval
    STATUS_ACTIVE = 'active'
    STATUS_PENDING = 'pending'

    STATUS_CHOICES = [
        (STATUS_ACTIVE, 'Active'),
        (STATUS_PENDING, 'Pending Approval'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='corporate_profile')
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='employees')
    job_title = models.CharField(max_length=255, blank=True)
    department = models.CharField(max_length=255, blank=True)
    employee_id = models.CharField(max_length=50, blank=True)
    is_company_admin = models.BooleanField(default=False, help_text="Can manage company settings and view all employee data")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=STATUS_ACTIVE,
        help_text="Whether the user is active or pending admin approval"
    )
    registration_link = models.ForeignKey(
        'RegistrationLink',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='registered_users',
        help_text="The registration link used to join the company, if any"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} ({self.company.name})"


class Certificate(models.Model):
    """
    Model to store certificate information for completed games.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='certificates')
    game_session = models.OneToOneField('game.GameSession', on_delete=models.CASCADE, related_name='certificate')

    title = models.CharField(max_length=255, default="Prompt Engineering Excellence")
    description = models.TextField(default="Has successfully completed the Corporate Prompt Master training program.")
    issue_date = models.DateTimeField(default=timezone.now)

    # Certificate customization
    template = models.CharField(max_length=50, default="standard")

    # Verification
    verification_code = models.CharField(max_length=50, unique=True, editable=False)

    # Performance data
    final_score = models.IntegerField(default=0)
    highest_role = models.CharField(max_length=100, blank=True)

    # Completed games data
    completed_games = models.TextField(default="[]", help_text="JSON list of completed games")

    def save(self, *args, **kwargs):
        # Generate verification code if not set
        if not self.verification_code:
            self.verification_code = str(uuid.uuid4())[:8].upper()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Certificate for {self.user.username} - {self.issue_date.strftime('%Y-%m-%d')}"

    def get_completed_games(self):
        """Get completed games as a list"""
        try:
            return json.loads(self.completed_games)
        except:
            return []

    def set_completed_games(self, games_list):
        """Set completed games from a list"""
        self.completed_games = json.dumps(games_list)


class Leaderboard(models.Model):
    """
    Model to store leaderboard configurations.
    """
    name = models.CharField(max_length=255)
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='leaderboards', null=True, blank=True)
    is_global = models.BooleanField(default=False, help_text="If true, this leaderboard is visible to all companies")

    # Time period settings
    PERIOD_CHOICES = [
        ('all_time', 'All Time'),
        ('monthly', 'Monthly'),
        ('weekly', 'Weekly'),
        ('daily', 'Daily'),
    ]
    time_period = models.CharField(max_length=20, choices=PERIOD_CHOICES, default='all_time')

    # Leaderboard settings
    max_entries = models.IntegerField(default=100)
    active = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.is_global:
            return f"Global: {self.name} ({self.get_time_period_display()})"
        return f"{self.company.name}: {self.name} ({self.get_time_period_display()})"


class LeaderboardEntry(models.Model):
    """
    Model to store individual leaderboard entries.
    """
    leaderboard = models.ForeignKey(Leaderboard, on_delete=models.CASCADE, related_name='entries')
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='leaderboard_entries')
    score = models.IntegerField()
    highest_role = models.CharField(max_length=100, blank=True)

    # Game information
    game_id = models.CharField(max_length=100, blank=True, help_text="Identifier for the game")
    game_name = models.CharField(max_length=255, blank=True, help_text="Name of the game")
    game_status = models.CharField(max_length=50, blank=True, help_text="Status of the game (completed, in progress, etc.)")

    # Timestamp for when this entry was recorded
    recorded_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-score']
        verbose_name_plural = "Leaderboard entries"
        indexes = [
            models.Index(fields=['game_id']),
        ]

    def __str__(self):
        game_info = f" ({self.game_name})" if self.game_name else ""
        return f"{self.user.username}: {self.score} points{game_info}"


class GameAccess(models.Model):
    """
    Model to control which companies have access to which games.
    """
    company = models.ForeignKey(Company, on_delete=models.CASCADE, related_name='game_access')
    game_id = models.CharField(max_length=100, help_text="Identifier for the game")
    game_name = models.CharField(max_length=255)

    # Access control
    is_active = models.BooleanField(default=True)
    is_public = models.BooleanField(default=False, help_text="If true, this game is accessible to anonymous users")
    access_granted_at = models.DateTimeField(auto_now_add=True)
    access_expires_at = models.DateTimeField(null=True, blank=True)

    # Custom settings for this game-company combination
    custom_settings = models.JSONField(default=dict, blank=True)

    def __str__(self):
        return f"{self.company.name} - {self.game_name}"

    class Meta:
        unique_together = ('company', 'game_id')
        verbose_name_plural = "Game access"


class CompanyInvitation(models.Model):
    """
    Model to store invitations for users to join a company.
    """
    STATUS_PENDING = 'pending'
    STATUS_ACCEPTED = 'accepted'
    STATUS_DECLINED = 'declined'
    STATUS_EXPIRED = 'expired'

    STATUS_CHOICES = [
        (STATUS_PENDING, 'Pending'),
        (STATUS_ACCEPTED, 'Accepted'),
        (STATUS_DECLINED, 'Declined'),
        (STATUS_EXPIRED, 'Expired'),
    ]

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='invitations'
    )
    email = models.EmailField()
    token = models.CharField(max_length=100, unique=True)
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default=STATUS_PENDING
    )
    invited_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='sent_corporate_invitations'
    )
    invited_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    accepted_at = models.DateTimeField(null=True, blank=True)

    # Additional fields for corporate context
    job_title = models.CharField(max_length=255, blank=True)
    department = models.CharField(max_length=255, blank=True)
    is_admin = models.BooleanField(default=False)
    message = models.TextField(blank=True)

    def __str__(self):
        return f"Invitation for {self.email} to join {self.company.name}"

    def save(self, *args, **kwargs):
        if not self.pk and not self.token:
            # Generate a unique token
            self.token = secrets.token_urlsafe(32)

        if not self.pk and not self.expires_at:
            # Set default expiration to 7 days from now
            self.expires_at = timezone.now() + timezone.timedelta(days=7)

        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('corporate:accept_invitation', kwargs={'token': self.token})

    def is_expired(self):
        return timezone.now() > self.expires_at

    class Meta:
        unique_together = ('company', 'email', 'status')
        verbose_name = "Company Invitation"
        verbose_name_plural = "Company Invitations"


class RegistrationLink(models.Model):
    """
    A shareable link for users to join a company.
    """
    # Approval type choices
    APPROVAL_AUTO = 'auto'
    APPROVAL_ADMIN = 'admin'

    APPROVAL_CHOICES = [
        (APPROVAL_AUTO, 'Automatic Activation'),
        (APPROVAL_ADMIN, 'Requires Admin Approval'),
    ]

    company = models.ForeignKey(
        Company,
        on_delete=models.CASCADE,
        related_name='registration_links'
    )
    token = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_corporate_registration_links'
    )
    intended_role = models.CharField(
        max_length=50,
        default='member',
        choices=[
            ('admin', 'Administrator'),
            ('member', 'Member'),
        ],
        help_text="The role the user will be assigned upon joining via this link."
    )
    approval_type = models.CharField(
        max_length=20,
        choices=APPROVAL_CHOICES,
        default=APPROVAL_AUTO,
        help_text="Whether users joining via this link need admin approval or are automatically activated."
    )
    expires_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Optional date/time when this link expires."
    )
    max_uses = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="Optional limit on the number of times this link can be used."
    )
    uses_count = models.PositiveIntegerField(default=0, editable=False)
    is_active = models.BooleanField(default=True)
    notes = models.CharField(
        max_length=255,
        blank=True,
        help_text="Optional notes for the creator about this link's purpose."
    )
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Registration link for {self.company.name} ({self.intended_role})"

    def get_absolute_url(self):
        return reverse('corporate:register_with_link', kwargs={'token': self.token})

    def is_expired(self):
        if not self.expires_at:
            return False
        return timezone.now() > self.expires_at

    def is_valid(self):
        if not self.is_active:
            return False
        if self.is_expired():
            return False
        if self.max_uses and self.uses_count >= self.max_uses:
            return False
        return True

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Registration Link"
        verbose_name_plural = "Registration Links"
