<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Corporate Prompt Master{% endblock %}</title>
    {% load static %}

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'corporate/css/corporate_styles.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/dark-mode-improvements.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/avatar-styles.css' %}">

    <!-- Always use dark mode -->
    <script>
        (function() {
            // Always apply dark mode
            document.documentElement.classList.add('dark-mode');
            document.documentElement.setAttribute('data-theme', 'dark');

            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('dark-mode');
                document.body.setAttribute('data-theme', 'dark');

                // Store theme preference
                localStorage.setItem('theme', 'dark');
                document.cookie = 'theme=dark; path=/; max-age=31536000';
            });
        })();
    </script>

    <style>
        /* Auth page specific styles */
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 0;
            background-color: #121212;
            background-image: linear-gradient(135deg, #121212 0%, #1a1a1a 100%);
        }

        .auth-card {
            width: 100%;
            max-width: 500px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .auth-logo {
            margin-bottom: 1.5rem;
        }

        .auth-footer {
            margin-top: 2rem;
            text-align: center;
            color: #adb5bd;
        }

        body.dark-mode .auth-footer {
            color: #adb5bd;
        }

        /* Form floating label fix for dark mode */
        body.dark-mode .form-floating > label {
            color: #888888;
        }

        body.dark-mode .form-floating > .form-control:focus ~ label,
        body.dark-mode .form-floating > .form-control:not(:placeholder-shown) ~ label {
            color: #0d6efd;
            opacity: 1;
        }

        /* Badge styling */
        body.dark-mode .badge.bg-warning.text-dark {
            color: #212529 !important;
            background-color: #ffc107 !important;
        }

        body.dark-mode .badge.bg-success {
            color: #ffffff !important;
            background-color: #198754 !important;
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="dark-mode" data-theme="dark">
    <!-- Messages -->
    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Auth Content -->
    <div class="auth-container">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-8 col-lg-6">
                    {% block auth_content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div class="auth-footer">
        <div class="container text-center">
            <span>© {% now "Y" %} Corporate Prompt Master. All rights reserved.</span>
            <div class="mt-2">
                <a href="{% url 'corporate:home' %}" class="text-decoration-none">Home</a> |
                <a href="{% url 'corporate:corporate_login' %}" class="text-decoration-none">Login</a> |
                <a href="{% url 'corporate:corporate_register' %}" class="text-decoration-none">Register</a>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Dark Mode Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Find any theme toggle buttons and hide them
            const themeToggle = document.getElementById('theme-toggle');
            if (themeToggle) {
                themeToggle.style.display = 'none';
            }

            // Ensure dark mode is always applied
            const body = document.body;
            const html = document.documentElement;

            body.classList.add('dark-mode');
            html.classList.add('dark-mode');

            body.setAttribute('data-theme', 'dark');
            html.setAttribute('data-theme', 'dark');

            // Store preference
            localStorage.setItem('theme', 'dark');
            document.cookie = 'theme=dark; path=/; max-age=31536000';
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
