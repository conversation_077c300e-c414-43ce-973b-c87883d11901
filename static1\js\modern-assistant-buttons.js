/**
 * Modern Assistant Buttons JavaScript
 * Applies modern button styling to buttons on the manage assistants page
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Modern Assistant Buttons: Initializing');
    applyModernButtonStyling();

    // Also run after a short delay to ensure all elements are loaded
    setTimeout(applyModernButtonStyling, 500);

    // Set up a MutationObserver to watch for changes to the DOM
    const observer = new MutationObserver(function(mutations) {
        applyModernButtonStyling();
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

/**
 * Apply modern button styling to all buttons on the manage assistants page
 */
function applyModernButtonStyling() {
    // Apply to all buttons in the assistant list
    const assistantListButtons = document.querySelectorAll('.list-group-item .btn');
    assistantListButtons.forEach(button => {
        button.classList.add('assistant-list-modern-btn');
    });

    // Apply to the "Create New Assistant" button
    const createAssistantButton = document.querySelector('a.btn-primary[href*="create"]');
    if (createAssistantButton) {
        createAssistantButton.classList.add('assistant-list-modern-btn');
    }

    // Apply to the "Add Folder" button
    const addFolderButton = document.querySelector('.js-trigger-create-folder-modal');
    if (addFolderButton) {
        addFolderButton.classList.add('assistant-list-modern-btn');
    }

    // Apply to filter buttons
    const filterButtons = document.querySelectorAll('.filter-form .btn');
    filterButtons.forEach(button => {
        button.classList.add('assistant-list-modern-btn');
    });

    // Apply to folder filter buttons
    const folderFilterButtons = document.querySelectorAll('.folder-filter-buttons .btn-outline-secondary');
    folderFilterButtons.forEach(button => {
        button.classList.add('assistant-list-modern-btn');
    });

    // Apply to modal buttons
    const modalButtons = document.querySelectorAll('.modal .btn');
    modalButtons.forEach(button => {
        button.classList.add('assistant-list-modern-btn');
    });

    // Add ripple effect to buttons
    addRippleEffect();

    // Add tilt effect to buttons
    addTiltEffect();
}

/**
 * Add ripple effect to buttons
 */
function addRippleEffect() {
    const buttons = document.querySelectorAll('.assistant-list-modern-btn');

    buttons.forEach(button => {
        // Skip if already has ripple handler
        if (button.dataset.hasRipple === 'true') return;

        button.dataset.hasRipple = 'true';

        button.addEventListener('click', function(e) {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const ripple = document.createElement('span');
            ripple.classList.add('ripple-effect');
            ripple.style.left = `${x}px`;
            ripple.style.top = `${y}px`;

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // Add ripple style if not already present
    if (!document.getElementById('ripple-style')) {
        const style = document.createElement('style');
        style.id = 'ripple-style';
        style.textContent = `
            .assistant-list-modern-btn {
                position: relative;
                overflow: hidden;
            }

            .ripple-effect {
                position: absolute;
                border-radius: 50%;
                background-color: rgba(255, 255, 255, 0.4);
                width: 100px;
                height: 100px;
                margin-top: -50px;
                margin-left: -50px;
                animation: ripple 0.6s linear;
                transform: scale(0);
                pointer-events: none;
            }

            @keyframes ripple {
                to {
                    transform: scale(2.5);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// Add 3D tilt effect to buttons
function addTiltEffect() {
    const buttons = document.querySelectorAll('.assistant-list-modern-btn');

    buttons.forEach(button => {
        // Skip if already has tilt handler
        if (button.dataset.hasTilt === 'true') return;

        button.dataset.hasTilt = 'true';

        button.addEventListener('mousemove', function(e) {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            const centerX = rect.width / 2;
            const centerY = rect.height / 2;

            const deltaX = (x - centerX) / centerX;
            const deltaY = (y - centerY) / centerY;

            button.style.transform = `perspective(500px) rotateX(${-deltaY * 5}deg) rotateY(${deltaX * 5}deg) scale3d(1.05, 1.05, 1.05)`;
        });

        button.addEventListener('mouseleave', function() {
            button.style.transform = '';
        });
    });
}
