/**
 * Mobile Enhancements JavaScript
 * Improves the mobile experience across the site
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('[Mobile Enhancements] Initializing mobile enhancements');

    // Check device type
    const isMobile = window.innerWidth <= 768;
    const isTablet = window.innerWidth > 768 && window.innerWidth <= 992;

    // Apply enhancements based on device type
    if (isMobile) {
        console.log('[Mobile Enhancements] Mobile device detected');

        // Apply mobile-specific enhancements
        enhanceNavbar();
        enhanceForms();
        enhanceDirectoryCards();
        enhanceChatInterface();
        setupTouchEvents();
        fixMobileScrolling();
        optimizeContainerPadding();

        // Listen for orientation changes
        window.addEventListener('orientationchange', function() {
            console.log('[Mobile Enhancements] Orientation changed');
            setTimeout(function() {
                adjustViewportHeight();
                fixMobileScrolling();
                optimizeContainerPadding(); // Re-apply container padding optimization
                enhanceFeaturedCarousel(); // Re-apply featured carousel enhancements
            }, 300);
        });

        // Listen for window resize to update featured carousel and container padding
        window.addEventListener('resize', function() {
            console.log('[Mobile Enhancements] Window resized');
            setTimeout(function() {
                enhanceFeaturedCarousel(); // Re-apply featured carousel enhancements
                optimizeContainerPadding(); // Re-apply container padding optimization
            }, 300);
        });

        // Initial viewport height adjustment
        adjustViewportHeight();
    }
    else if (isTablet) {
        console.log('[Mobile Enhancements] Tablet device detected');

        // Apply tablet-specific enhancements
        enhanceFormsForTablet();
        enhanceDirectoryCardsForTablet();
        setupTouchEventsForTablet();

        // Listen for orientation changes on tablets
        window.addEventListener('orientationchange', function() {
            console.log('[Mobile Enhancements] Tablet orientation changed');
            setTimeout(function() {
                // Refresh layout after orientation change
                enhanceDirectoryCardsForTablet();
            }, 300);
        });
    }

    /**
     * Enhances the navbar for mobile devices
     */
    function enhanceNavbar() {
        console.log('[Mobile Enhancements] Enhancing navbar');

        // Improve navbar toggle button
        const navbarToggler = document.querySelector('.navbar-toggler');
        if (navbarToggler) {
            navbarToggler.addEventListener('click', function() {
                // Add a small delay to allow the collapse animation to start
                setTimeout(function() {
                    const isExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
                    if (isExpanded) {
                        // Add overlay when navbar is expanded
                        const overlay = document.createElement('div');
                        overlay.id = 'navbar-overlay';
                        overlay.style.position = 'fixed';
                        overlay.style.top = '0';
                        overlay.style.left = '0';
                        overlay.style.right = '0';
                        overlay.style.bottom = '0';
                        overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                        overlay.style.zIndex = '1040';
                        overlay.style.opacity = '0';
                        overlay.style.transition = 'opacity 0.3s ease';
                        document.body.appendChild(overlay);

                        // Fade in the overlay
                        setTimeout(function() {
                            overlay.style.opacity = '1';
                        }, 10);

                        // Close navbar when overlay is clicked
                        overlay.addEventListener('click', function() {
                            navbarToggler.click();
                        });
                    } else {
                        // Remove overlay when navbar is collapsed
                        const overlay = document.getElementById('navbar-overlay');
                        if (overlay) {
                            overlay.style.opacity = '0';
                            setTimeout(function() {
                                overlay.remove();
                            }, 300);
                        }
                    }
                }, 10);
            });
        }

        // Make dropdown items more touch-friendly
        const dropdownItems = document.querySelectorAll('.dropdown-item');
        dropdownItems.forEach(function(item) {
            item.style.padding = '0.75rem 1rem';
        });
    }

    /**
     * Enhances forms for mobile devices
     */
    function enhanceForms() {
        console.log('[Mobile Enhancements] Enhancing forms');

        // Make form controls more touch-friendly
        const formControls = document.querySelectorAll('.form-control, .form-select, .btn');
        formControls.forEach(function(control) {
            if (control.offsetHeight < 44) {
                control.style.minHeight = '44px';
            }
        });

        // Improve filter forms
        const filterForms = document.querySelectorAll('.filter-form');
        filterForms.forEach(function(form) {
            // Add collapsible behavior to filter forms on mobile
            const formHeading = form.querySelector('h5');
            if (formHeading) {
                // Add toggle icon
                const toggleIcon = document.createElement('i');
                toggleIcon.className = 'bi bi-chevron-down float-end';
                formHeading.appendChild(toggleIcon);

                // Make form content collapsible
                const formContent = form.querySelector('.row');
                if (formContent) {
                    formHeading.style.cursor = 'pointer';

                    // Toggle form content when heading is clicked
                    formHeading.addEventListener('click', function(e) {
                        if (e.target === formHeading || e.target === toggleIcon) {
                            formContent.style.display = formContent.style.display === 'none' ? 'flex' : 'none';
                            toggleIcon.className = formContent.style.display === 'none' ? 'bi bi-chevron-down float-end' : 'bi bi-chevron-up float-end';
                        }
                    });
                }
            }
        });
    }

    /**
     * Enhances directory cards for mobile devices
     */
    function enhanceDirectoryCards() {
        console.log('[Mobile Enhancements] Enhancing directory cards');

        // Make directory cards more touch-friendly
        const directoryCards = document.querySelectorAll('.directory-card');
        directoryCards.forEach(function(card) {
            // Add touch feedback
            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            });

            card.addEventListener('touchend', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });

            // Ensure all content is visible
            card.style.overflow = 'visible';
            card.style.height = 'auto';

            // Ensure text doesn't overflow
            const headings = card.querySelectorAll('h5, h6');
            headings.forEach(function(heading) {
                heading.style.whiteSpace = 'nowrap';
                heading.style.overflow = 'hidden';
                heading.style.textOverflow = 'ellipsis';
                heading.style.maxWidth = '100%';
            });

            // Ensure paragraphs don't overflow
            const paragraphs = card.querySelectorAll('p');
            paragraphs.forEach(function(paragraph) {
                paragraph.style.whiteSpace = 'nowrap';
                paragraph.style.overflow = 'hidden';
                paragraph.style.textOverflow = 'ellipsis';
                paragraph.style.maxWidth = '100%';
            });
        });

        // Enhance featured carousel for mobile
        enhanceFeaturedCarousel();
    }

    /**
     * Enhances featured carousel for mobile devices
     */
    function enhanceFeaturedCarousel() {
        console.log('[Mobile Enhancements] Enhancing featured carousel');

        const carousel = document.querySelector('.featured-carousel-items');
        if (!carousel) return;

        // Ensure carousel is visible and properly sized
        carousel.style.width = 'max-content';
        carousel.style.display = 'flex';
        carousel.style.alignItems = 'center';

        // Restore original animation speed (60s)
        carousel.style.animationDuration = '60s';
        carousel.style.animationTimingFunction = 'linear';
        carousel.style.animationIterationCount = 'infinite';

        // Make carousel items more touch-friendly
        const carouselItems = carousel.querySelectorAll('.featured-carousel-item');

        // Ensure all items are visible for the animation with proper sizing
        carouselItems.forEach(function(item) {
            // Set proper sizing instead of scaling
            item.style.transform = 'none';
            item.style.margin = '0 15px'; // Reduced spacing between items
            item.style.width = '95vw'; // Take up most of the viewport width
            item.style.maxWidth = '95vw'; // Maximum width for larger phones
            item.style.minWidth = '320px'; // Minimum width for smaller phones
            item.style.display = 'flex';
            item.style.justifyContent = 'center';
            item.style.alignItems = 'center';

            // Ensure wrapper is properly sized and styled like tier cards
            const wrapper = item.querySelector('.featured-item-wrapper');
            if (wrapper) {
                wrapper.style.width = '100%'; // Take full width of the item
                wrapper.style.padding = '25px'; // Increased padding
                wrapper.style.margin = '0 auto';
                wrapper.style.borderRadius = '8px'; // Match tier card style
                wrapper.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.2)'; // Match tier card shadow
                wrapper.style.display = 'flex';
                wrapper.style.flexDirection = 'column';
                wrapper.style.alignItems = 'center';
                wrapper.style.transition = 'all 0.3s ease';
                wrapper.style.backgroundColor = '#222222'; // Dark background like tier cards
                wrapper.style.border = '1px solid #333333'; // Dark border like tier cards
                wrapper.style.overflow = 'visible';
                wrapper.style.minHeight = '400px'; // Ensure minimum height for square appearance
                wrapper.style.minWidth = '360px'; // Ensure minimum width for square appearance
                wrapper.style.height = 'auto';
            }

            // Text styling to match tier cards
            const headings = item.querySelectorAll('h5, h6');
            headings.forEach(function(heading) {
                heading.style.whiteSpace = 'nowrap';
                heading.style.overflow = 'hidden';
                heading.style.textOverflow = 'ellipsis';
                heading.style.width = '100%';
                heading.style.textAlign = 'center';
                heading.style.fontSize = '1.2rem'; // Match tier card title size
                heading.style.marginBottom = '0.5rem';
                heading.style.fontWeight = '600'; // Semi-bold text
                heading.style.color = '#4da3ff'; // Blue color like in tier cards
            });

            // Paragraph styling to match tier cards
            const paragraphs = item.querySelectorAll('p');
            paragraphs.forEach(function(paragraph) {
                paragraph.style.width = '100%';
                paragraph.style.textAlign = 'center';
                paragraph.style.fontSize = '0.9rem'; // Match tier card description size
                paragraph.style.marginBottom = '0.5rem';
                paragraph.style.color = '#adb5bd'; // Light gray for better contrast on dark background
                paragraph.style.whiteSpace = 'normal'; // Allow wrapping
                paragraph.style.overflow = 'hidden';
                // Use standard CSS for line clamping when possible
                paragraph.style.display = '-webkit-box';
                paragraph.style.webkitLineClamp = '2'; // Show 2 lines
                // Use setAttribute for vendor prefixed properties to avoid deprecation warnings
                paragraph.setAttribute('style', paragraph.getAttribute('style') + '-webkit-box-orient: vertical;');
                paragraph.style.lineHeight = '1.3';
            });

            // Logo container styled like tier cards
            const logoContainer = item.querySelector('.logo-container');
            if (logoContainer) {
                logoContainer.style.height = '240px'; // Much larger logo size
                logoContainer.style.width = '240px';
                logoContainer.style.minHeight = '240px';
                logoContainer.style.minWidth = '240px';
                logoContainer.style.maxHeight = '240px';
                logoContainer.style.maxWidth = '240px';
                logoContainer.style.margin = '0 auto 15px auto';
                logoContainer.style.borderRadius = '4px'; // Match tier card image style
                logoContainer.style.display = 'flex';
                logoContainer.style.justifyContent = 'center';
                logoContainer.style.alignItems = 'center';
                logoContainer.style.overflow = 'hidden';
                logoContainer.style.border = '1px solid #333333';
                logoContainer.style.backgroundColor = '#252525';

                // Logo placeholder icon
                const logoPlaceholder = logoContainer.querySelector('.logo-placeholder i');
                if (logoPlaceholder) {
                    logoPlaceholder.style.fontSize = '180px'; // Much larger icon size
                    logoPlaceholder.style.color = '#4da3ff';
                }
            }

            // Add or style community badge
            let communityBadge = item.querySelector('.badge');
            if (!communityBadge) {
                // Try to find meta info that might contain company or community info
                const metaInfo = item.querySelector('.item-meta');
                if (metaInfo) {
                    communityBadge = document.createElement('span');
                    communityBadge.className = 'badge';
                    communityBadge.textContent = metaInfo.textContent.trim();
                    metaInfo.parentNode.insertBefore(communityBadge, metaInfo);
                    metaInfo.style.display = 'none';
                }
            }

            if (communityBadge) {
                communityBadge.style.backgroundColor = '#333333';
                communityBadge.style.color = '#adb5bd';
                communityBadge.style.fontSize = '0.8rem';
                communityBadge.style.padding = '0.25rem 0.5rem';
                communityBadge.style.borderRadius = '4px';
                communityBadge.style.marginBottom = '10px';
            }

            // Style rating text if present
            const ratingText = item.querySelector('.rating-text');
            if (ratingText) {
                ratingText.style.fontSize = '0.8rem';
                ratingText.style.color = '#adb5bd';
                ratingText.style.marginTop = '5px';
                ratingText.style.textAlign = 'right';
                ratingText.style.width = '100%';
            }

            // Style like button
            const likeButton = item.querySelector('.like-button');
            if (likeButton) {
                likeButton.style.width = '40px'; // Match tier card like button
                likeButton.style.height = '40px';
                likeButton.style.position = 'absolute';
                likeButton.style.bottom = '15px';
                likeButton.style.right = '15px';
                likeButton.style.top = 'auto'; // Override top positioning
                likeButton.style.borderRadius = '50%';
                likeButton.style.display = 'flex';
                likeButton.style.justifyContent = 'center';
                likeButton.style.alignItems = 'center';
                likeButton.style.backgroundColor = '#333333';
                likeButton.style.border = '1px solid #444444';
                likeButton.style.color = '#ffffff';
            }

            // Style buttons if present
            const buttons = item.querySelectorAll('.btn');
            buttons.forEach(function(button) {
                if (button.classList.contains('btn-primary')) {
                    button.style.backgroundColor = '#0d6efd';
                    button.style.borderColor = '#0d6efd';
                    button.style.color = 'white';
                    button.style.width = '100%';
                    button.style.marginBottom = '10px';
                    button.style.borderRadius = '4px';
                    button.style.padding = '8px 16px';
                    button.style.fontSize = '0.9rem';
                } else if (button.classList.contains('btn-secondary')) {
                    button.style.backgroundColor = '#333333';
                    button.style.borderColor = '#444444';
                    button.style.color = 'white';
                    button.style.width = '100%';
                    button.style.borderRadius = '4px';
                    button.style.padding = '8px 16px';
                    button.style.fontSize = '0.9rem';
                }
            });

            // Style featured badge if present
            const featuredBadge = item.querySelector('.featured-badge');
            if (featuredBadge) {
                featuredBadge.style.position = 'absolute';
                featuredBadge.style.top = '10px';
                featuredBadge.style.right = '10px';
                featuredBadge.style.backgroundColor = '#198754';
                featuredBadge.style.color = 'white';
                featuredBadge.style.fontSize = '0.8rem';
                featuredBadge.style.padding = '0.25rem 0.5rem';
                featuredBadge.style.borderRadius = '4px';
            }
        });

        // Ensure the container is properly sized to show only one item
        const carouselContainer = document.querySelector('.featured-carousel-container');
        if (carouselContainer) {
            carouselContainer.style.padding = '10px 0'; // Minimal padding
            carouselContainer.style.display = 'flex';
            carouselContainer.style.justifyContent = 'center';
            carouselContainer.style.alignItems = 'center';
            carouselContainer.style.width = '100%'; // Take full width
            carouselContainer.style.height = 'auto'; // Allow height to adapt to content
            carouselContainer.style.overflow = 'hidden'; // Hide overflow items
            carouselContainer.style.margin = '0 auto';
            carouselContainer.style.position = 'relative';
        }

        // Ensure the featured section has appropriate styling
        const featuredSection = document.querySelector('.featured-section');
        if (featuredSection) {
            featuredSection.style.minHeight = '500px'; // Ensure enough vertical space
            featuredSection.style.padding = '15px 10px'; // Appropriate padding
            featuredSection.style.marginBottom = '20px';
        }

        // Apply small screen adjustments if needed
        if (window.innerWidth <= 576) {
            applySmallScreenAdjustments(carouselItems);
        }

        // Apply light mode styling if needed
        if (document.documentElement.getAttribute('data-theme') === 'light') {
            applyLightModeStyles(carouselItems);
        }
    }

    /**
     * Applies specific adjustments for very small screens
     */
    function applySmallScreenAdjustments(carouselItems) {
        carouselItems.forEach(function(item) {
            item.style.margin = '0 10px'; // Reduced spacing
            item.style.width = '95vw'; // Take up most of the viewport width
            item.style.maxWidth = '95vw'; // Maximum width for small phones
            item.style.minWidth = '300px'; // Minimum width for very small phones

            const wrapper = item.querySelector('.featured-item-wrapper');
            if (wrapper) {
                wrapper.style.padding = '20px'; // Increased padding
                wrapper.style.minWidth = '320px'; // Ensure minimum width for square appearance
                wrapper.style.minHeight = '360px'; // Ensure minimum height for square appearance
            }

            const logoContainer = item.querySelector('.logo-container');
            if (logoContainer) {
                logoContainer.style.height = '200px'; // Much larger size
                logoContainer.style.width = '200px';
                logoContainer.style.minHeight = '200px';
                logoContainer.style.minWidth = '200px';
                logoContainer.style.maxHeight = '200px';
                logoContainer.style.maxWidth = '200px';
                logoContainer.style.margin = '0 auto 12px auto';

                const logoPlaceholder = logoContainer.querySelector('.logo-placeholder i');
                if (logoPlaceholder) {
                    logoPlaceholder.style.fontSize = '160px'; // Much larger icon size
                }
            }

            const headings = item.querySelectorAll('h5, h6');
            headings.forEach(function(heading) {
                heading.style.fontSize = '1.1rem'; // Appropriate text size
                heading.style.marginBottom = '0.4rem';
            });

            const paragraphs = item.querySelectorAll('p');
            paragraphs.forEach(function(paragraph) {
                paragraph.style.fontSize = '0.85rem'; // Appropriate text size
                paragraph.style.marginBottom = '0.4rem';
                paragraph.style.lineHeight = '1.2';
            });

            const badge = item.querySelector('.badge');
            if (badge) {
                badge.style.fontSize = '0.75rem';
                badge.style.padding = '0.2rem 0.4rem';
                badge.style.marginBottom = '8px';
            }

            const ratingText = item.querySelector('.rating-text');
            if (ratingText) {
                ratingText.style.fontSize = '0.75rem';
                ratingText.style.marginTop = '4px';
            }

            const likeButton = item.querySelector('.like-button');
            if (likeButton) {
                likeButton.style.width = '36px'; // Smaller
                likeButton.style.height = '36px';
                likeButton.style.bottom = '12px';
                likeButton.style.right = '12px';
            }

            const buttons = item.querySelectorAll('.btn');
            buttons.forEach(function(button) {
                button.style.padding = '6px 12px';
                button.style.fontSize = '0.85rem';
                button.style.marginBottom = '8px';
            });

            const featuredBadge = item.querySelector('.featured-badge');
            if (featuredBadge) {
                featuredBadge.style.fontSize = '0.75rem';
                featuredBadge.style.padding = '0.2rem 0.4rem';
                featuredBadge.style.top = '8px';
                featuredBadge.style.right = '8px';
            }
        });
    }

    /**
     * Applies light mode styling to featured items
     */
    function applyLightModeStyles(carouselItems) {
        carouselItems.forEach(function(item) {
            const wrapper = item.querySelector('.featured-item-wrapper');
            if (wrapper) {
                wrapper.style.backgroundColor = '#ffffff';
                wrapper.style.borderColor = '#dee2e6';
            }

            const logoContainer = item.querySelector('.logo-container');
            if (logoContainer) {
                logoContainer.style.backgroundColor = '#f8f9fa';
                logoContainer.style.borderColor = '#dee2e6';
            }

            const headings = item.querySelectorAll('h5, h6');
            headings.forEach(function(heading) {
                heading.style.color = '#0d6efd';
            });

            const paragraphs = item.querySelectorAll('p');
            paragraphs.forEach(function(paragraph) {
                paragraph.style.color = '#495057';
            });

            const badge = item.querySelector('.badge');
            if (badge) {
                badge.style.backgroundColor = '#e9ecef';
                badge.style.color = '#495057';
            }

            const likeButton = item.querySelector('.like-button');
            if (likeButton) {
                likeButton.style.backgroundColor = '#f8f9fa';
                likeButton.style.borderColor = '#dee2e6';
                likeButton.style.color = '#212529';
            }

            const secondaryButtons = item.querySelectorAll('.btn-secondary');
            secondaryButtons.forEach(function(button) {
                button.style.backgroundColor = '#6c757d';
                button.style.borderColor = '#6c757d';
            });
        });
    }

    /**
     * Enhances chat interface for mobile devices
     */
    function enhanceChatInterface() {
        console.log('[Mobile Enhancements] Enhancing chat interface');

        // Fix chat form on mobile
        const chatForm = document.getElementById('chat-form');
        if (chatForm) {
            // Ensure chat form is fixed at the bottom
            chatForm.style.position = 'fixed';
            chatForm.style.bottom = '0';
            chatForm.style.left = '0';
            chatForm.style.right = '0';
            chatForm.style.padding = '0.75rem 1rem';
            chatForm.style.backgroundColor = '#ffffff';
            chatForm.style.borderTop = '1px solid rgba(0, 0, 0, 0.1)';
            chatForm.style.zIndex = '1000';

            // Add padding to chat box to prevent content from being hidden behind the fixed chat form
            const chatBox = document.getElementById('chat-box');
            if (chatBox) {
                chatBox.style.paddingBottom = '70px';
            }

            // Apply dark mode styling if needed
            if (document.documentElement.getAttribute('data-theme') === 'dark') {
                chatForm.style.backgroundColor = '#1a1a1a';
                chatForm.style.borderTop = '1px solid #333333';
            }
        }
    }

    /**
     * Sets up touch events for better mobile interaction
     */
    function setupTouchEvents() {
        console.log('[Mobile Enhancements] Setting up touch events');

        // Add touch feedback to buttons
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(function(button) {
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.opacity = '0.9';
            });

            button.addEventListener('touchend', function() {
                this.style.transform = '';
                this.style.opacity = '';
            });
        });
    }

    /**
     * Fixes mobile scrolling issues
     */
    function fixMobileScrolling() {
        console.log('[Mobile Enhancements] Fixing mobile scrolling');

        // Fix iOS momentum scrolling
        const scrollableElements = document.querySelectorAll('.chat-box, .sidebar, #chat-sidebar');
        scrollableElements.forEach(function(element) {
            element.style.webkitOverflowScrolling = 'touch';
        });
    }

    /**
     * Adjusts viewport height for mobile browsers
     */
    function adjustViewportHeight() {
        console.log('[Mobile Enhancements] Adjusting viewport height');

        // Fix viewport height for mobile browsers
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);

        // Apply the custom viewport height to relevant elements
        const fullHeightElements = document.querySelectorAll('.chat-box, .sidebar, #chat-sidebar');
        fullHeightElements.forEach(function(element) {
            element.style.height = 'calc(var(--vh, 1vh) * 100)';
        });
    }

    /**
     * Enhances forms for tablet devices
     */
    function enhanceFormsForTablet() {
        console.log('[Mobile Enhancements] Enhancing forms for tablet');

        // Make form controls more touch-friendly on tablets
        const formControls = document.querySelectorAll('.form-control, .form-select, .btn');
        formControls.forEach(function(control) {
            if (control.offsetHeight < 44) {
                control.style.minHeight = '44px';
            }
        });

        // Improve filter forms for tablets
        const filterForms = document.querySelectorAll('.filter-form');
        filterForms.forEach(function(form) {
            // Ensure proper spacing and sizing for tablet view
            const formInputs = form.querySelectorAll('.form-control, .form-select');
            formInputs.forEach(function(input) {
                input.style.fontSize = '16px';
                input.style.padding = '0.5rem 0.75rem';
            });

            const formButtons = form.querySelectorAll('.btn');
            formButtons.forEach(function(button) {
                button.style.padding = '0.5rem 0.75rem';
                button.style.fontSize = '0.9rem';
            });
        });
    }

    /**
     * Enhances directory cards for tablet devices
     */
    function enhanceDirectoryCardsForTablet() {
        console.log('[Mobile Enhancements] Enhancing directory cards for tablet');

        // Make directory cards more touch-friendly for tablets
        const directoryCards = document.querySelectorAll('.directory-card');
        directoryCards.forEach(function(card) {
            // Add touch feedback for tablets
            card.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.99)';
                this.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
            });

            card.addEventListener('touchend', function() {
                this.style.transform = '';
                this.style.boxShadow = '';
            });

            // Ensure all content is visible
            card.style.overflow = 'visible';
            card.style.height = 'auto';

            // Optimize layout for tablets
            const row = card.querySelector('.row');
            if (row) {
                row.style.flexWrap = 'wrap';
            }

            // Optimize logo column for tablets
            const logoCol = card.querySelector('.directory-item-link-wrapper .col-md-2');
            if (logoCol) {
                logoCol.style.width = '25%';
                logoCol.style.display = 'flex';
                logoCol.style.justifyContent = 'center';
                logoCol.style.alignItems = 'center';
                logoCol.style.padding = '0 0.5rem';
            }

            // Optimize name and company column for tablets
            const nameCol = card.querySelector('.directory-item-link-wrapper .col-md-3');
            if (nameCol) {
                nameCol.style.width = '75%';
                nameCol.style.padding = '0 0.5rem';
            }

            // Optimize description column for tablets
            const descCol = card.querySelector('.directory-item-link-wrapper .col-md-7');
            if (descCol) {
                descCol.style.width = '100%';
                descCol.style.padding = '0 0.5rem';
                descCol.style.clear = 'both';
            }

            // Optimize action buttons column for tablets
            const actionCol = card.querySelector('.col-md-2.text-end');
            if (actionCol) {
                actionCol.style.width = '100%';
                actionCol.style.textAlign = 'right';
                actionCol.style.marginTop = '0.75rem';
                actionCol.style.padding = '0 0.5rem';
            }

            // Ensure text doesn't overflow
            const headings = card.querySelectorAll('h5, h6');
            headings.forEach(function(heading) {
                heading.style.whiteSpace = 'nowrap';
                heading.style.overflow = 'hidden';
                heading.style.textOverflow = 'ellipsis';
                heading.style.maxWidth = '100%';
            });

            // Ensure paragraphs don't overflow
            const paragraphs = card.querySelectorAll('p');
            paragraphs.forEach(function(paragraph) {
                paragraph.style.whiteSpace = 'nowrap';
                paragraph.style.overflow = 'hidden';
                paragraph.style.textOverflow = 'ellipsis';
                paragraph.style.maxWidth = '100%';
            });
        });

        // Enhance featured carousel for tablets
        enhanceFeaturedCarouselForTablet();
    }

    /**
     * Enhances featured carousel for tablet devices
     */
    function enhanceFeaturedCarouselForTablet() {
        console.log('[Mobile Enhancements] Enhancing featured carousel for tablet');

        const carousel = document.querySelector('.featured-carousel-items');
        if (!carousel) return;

        // Ensure carousel is visible and properly sized
        carousel.style.width = 'max-content';
        carousel.style.display = 'flex';

        // Adjust animation speed for better performance on tablets
        carousel.style.animationDuration = '80s';

        // Make carousel items more touch-friendly
        const carouselItems = carousel.querySelectorAll('.featured-carousel-item');
        carouselItems.forEach(function(item) {
            // Ensure wrapper is properly sized
            const wrapper = item.querySelector('.featured-item-wrapper');
            if (wrapper) {
                wrapper.style.overflow = 'visible';
                wrapper.style.minHeight = 'auto';
                wrapper.style.height = 'auto';
                wrapper.style.width = '220px';
            }

            // Ensure text doesn't overflow
            const headings = item.querySelectorAll('h5, h6');
            headings.forEach(function(heading) {
                heading.style.whiteSpace = 'nowrap';
                heading.style.overflow = 'hidden';
                heading.style.textOverflow = 'ellipsis';
                heading.style.maxWidth = '100%';
            });

            // Ensure paragraphs don't overflow
            const paragraphs = item.querySelectorAll('p');
            paragraphs.forEach(function(paragraph) {
                paragraph.style.whiteSpace = 'nowrap';
                paragraph.style.overflow = 'hidden';
                paragraph.style.textOverflow = 'ellipsis';
                paragraph.style.maxWidth = '100%';
            });
        });
    }

    /**
     * Sets up touch events for better tablet interaction
     */
    function setupTouchEventsForTablet() {
        console.log('[Mobile Enhancements] Setting up touch events for tablet');

        // Add touch feedback to buttons on tablets
        const buttons = document.querySelectorAll('.btn');
        buttons.forEach(function(button) {
            button.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.opacity = '0.9';
            });

            button.addEventListener('touchend', function() {
                this.style.transform = '';
                this.style.opacity = '';
            });
        });

        // Improve touch feedback for links on tablets
        const links = document.querySelectorAll('a');
        links.forEach(function(link) {
            link.addEventListener('touchstart', function() {
                this.style.opacity = '0.8';
            });

            link.addEventListener('touchend', function() {
                this.style.opacity = '';
            });
        });
    }

    /**
     * Optimizes container padding for mobile devices and applies 3D glow effect
     */
    function optimizeContainerPadding() {
        console.log('[Mobile Enhancements] Optimizing container padding and applying 3D glow');

        // Adjust padding for containers with py-5 class
        const py5Containers = document.querySelectorAll('.container.py-5');
        py5Containers.forEach(function(container) {
            // Check if it's in the hero section
            const isHeroContainer = container.closest('.hero-section') !== null;
            const isFeatureSection = container.closest('.features-section') !== null;
            const isCtaSection = container.closest('.cta-section') !== null;

            // Apply padding based on section type
            if (isHeroContainer) {
                container.style.paddingTop = '2rem';
                container.style.paddingBottom = '2rem';
            } else {
                container.style.paddingTop = '2.5rem';
                container.style.paddingBottom = '2.5rem';
            }

            // Apply 3D glow effect
            if (!container.classList.contains('glow-applied')) {
                // Add class to prevent reapplying
                container.classList.add('glow-applied');

                // Apply specific styling based on section type
                if (isHeroContainer) {
                    container.classList.add('hero-container-glow');
                } else if (isFeatureSection) {
                    container.classList.add('feature-container-glow');
                } else if (isCtaSection) {
                    container.classList.add('cta-container-glow');
                } else {
                    container.classList.add('standard-container-glow');
                }

                // Add hover effect
                container.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';

                    // Check if dark mode is active
                    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

                    if (isDarkMode) {
                        // Enhanced blue glow for dark mode - EDGE GLOW ONLY
                        this.style.boxShadow = '0 0 25px rgba(74, 125, 255, 0.3)';
                        this.style.borderColor = 'rgba(74, 125, 255, 0.5)';
                    } else {
                        // Enhanced blue glow for light mode - EDGE GLOW ONLY
                        this.style.boxShadow = '0 0 25px rgba(0, 102, 255, 0.3)';
                        this.style.borderColor = 'rgba(0, 102, 255, 0.5)';
                    }
                });

                container.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = '';
                    this.style.borderColor = '';
                });
            }
        });

        // Specifically target the hero section
        const heroSection = document.querySelector('.hero-section');
        if (heroSection) {
            heroSection.style.paddingTop = '2rem';
            heroSection.style.paddingBottom = '2rem';

            // Find the container inside hero section
            const heroContainer = heroSection.querySelector('.container.py-5');
            if (heroContainer) {
                heroContainer.style.paddingTop = '1.5rem';
                heroContainer.style.paddingBottom = '1.5rem';
            }
        }
    }
});
