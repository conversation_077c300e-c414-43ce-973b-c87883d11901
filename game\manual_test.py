"""
Manual test script for the Context-Aware Game Django application.
Run this script to verify the game functionality.
"""

import os
import sys
import json
import logging
import requests
from urllib.parse import urljoin

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('manual_test')

# Set up the base URL for the API
BASE_URL = 'http://127.0.0.1:8000/'

def test_api_endpoint(endpoint, method='GET', data=None, params=None):
    """
    Test an API endpoint and return the response.
    
    Args:
        endpoint: The API endpoint to test
        method: The HTTP method to use (GET or POST)
        data: The data to send in the request body (for POST requests)
        params: The query parameters to send in the request (for GET requests)
        
    Returns:
        The response from the API
    """
    url = urljoin(BASE_URL, endpoint)
    logger.info(f"Testing {method} {url}")
    
    try:
        if method == 'GET':
            response = requests.get(url, params=params)
        elif method == 'POST':
            response = requests.post(url, json=data)
        else:
            logger.error(f"Unsupported method: {method}")
            return None
        
        # Check if the response is valid JSON
        try:
            response_data = response.json()
            logger.info(f"Response status code: {response.status_code}")
            logger.info(f"Response data: {json.dumps(response_data, indent=2)[:200]}...")
            return response_data
        except json.JSONDecodeError:
            logger.error(f"Response is not valid JSON: {response.text[:200]}...")
            return None
    
    except requests.exceptions.RequestException as e:
        logger.error(f"Error making request to {url}: {str(e)}")
        return None

def run_manual_tests():
    """
    Run manual tests for the game API endpoints.
    """
    logger.info("Starting manual tests for the Context-Aware Game API")
    
    # Test the index page
    logger.info("Testing the index page")
    try:
        response = requests.get(BASE_URL)
        logger.info(f"Index page status code: {response.status_code}")
        if response.status_code == 200:
            logger.info("Index page loaded successfully")
        else:
            logger.error(f"Failed to load index page: {response.text[:200]}...")
    except requests.exceptions.RequestException as e:
        logger.error(f"Error making request to index page: {str(e)}")
    
    # Test the start_game API endpoint
    logger.info("Testing the start_game API endpoint")
    start_game_response = test_api_endpoint('api/start_game/')
    if start_game_response and start_game_response.get('status') == 'success':
        logger.info("start_game API endpoint test passed")
    else:
        logger.error("start_game API endpoint test failed")
    
    # Test the get_game_state API endpoint
    logger.info("Testing the get_game_state API endpoint")
    game_state_response = test_api_endpoint('api/get_game_state/')
    if game_state_response and game_state_response.get('status') == 'success':
        logger.info("get_game_state API endpoint test passed")
        
        # Check that the game state contains the expected fields
        expected_fields = ['current_role', 'performance_score', 'challenges_completed', 
                          'role_challenges_completed', 'current_task', 'current_manager', 
                          'messages', 'role_progression_html', 'org_chart_html']
        
        missing_fields = [field for field in expected_fields if field not in game_state_response]
        if missing_fields:
            logger.error(f"Game state is missing fields: {missing_fields}")
        else:
            logger.info("Game state contains all expected fields")
    else:
        logger.error("get_game_state API endpoint test failed")
    
    # Test the fetch_first_task API endpoint
    logger.info("Testing the fetch_first_task API endpoint")
    first_task_response = test_api_endpoint('api/fetch_first_task/')
    if first_task_response and first_task_response.get('status') == 'success':
        logger.info("fetch_first_task API endpoint test passed")
        
        # Check that the task contains the expected fields
        expected_fields = ['task_id', 'manager', 'description', 'response_template']
        
        missing_fields = [field for field in expected_fields if field not in first_task_response]
        if missing_fields:
            logger.error(f"Task is missing fields: {missing_fields}")
        else:
            logger.info("Task contains all expected fields")
            
            # Get the task ID for the next test
            task_id = first_task_response.get('task_id')
            
            # Test the preview_response API endpoint
            logger.info("Testing the preview_response API endpoint")
            preview_data = {
                'prompt': 'Write a cover letter for a software engineer position',
                'task_id': task_id
            }
            preview_response = test_api_endpoint('api/preview_response/', method='POST', data=preview_data)
            if preview_response and preview_response.get('status') == 'success':
                logger.info("preview_response API endpoint test passed")
            else:
                logger.error("preview_response API endpoint test failed")
            
            # Test the submit_prompt API endpoint
            logger.info("Testing the submit_prompt API endpoint")
            submit_data = {
                'prompt': 'Write a cover letter for a software engineer position',
                'task_id': task_id
            }
            submit_response = test_api_endpoint('api/submit_prompt/', method='POST', data=submit_data)
            if submit_response and submit_response.get('status') == 'success':
                logger.info("submit_prompt API endpoint test passed")
            else:
                logger.error("submit_prompt API endpoint test failed")
    else:
        logger.error("fetch_first_task API endpoint test failed")
    
    logger.info("Manual tests completed")

if __name__ == '__main__':
    run_manual_tests()
