// Organization Chart Integration with Existing Game
// This file connects the organization chart progression system with the existing game

// Wait for both the main game and org chart to be loaded
document.addEventListener('DOMContentLoaded', () => {
    // Check if both gameState and OrgChart are available
    if (window.gameState && window.OrgChart) {
        initOrgChartIntegration();
    } else {
        // Wait for components to load
        const checkInterval = setInterval(() => {
            if (window.gameState && window.OrgChart) {
                clearInterval(checkInterval);
                initOrgChartIntegration();
            }
        }, 100);
    }
});

// Initialize the integration between the org chart and the game
function initOrgChartIntegration() {
    console.log('Initializing org chart integration with game...');

    // Map game roles to org chart positions
    const roleToPositionMap = {
        'applicant': 'applicant',
        'hr_associate': 'hr_associate',
        'marketing_associate': 'marketing_associate',
        'advertising_manager': 'advertising_manager',
        'service_associate': 'service_associate',
        'production_associate': 'production_associate',
        'facilities_associate': 'facilities_associate',
        'service_manager': 'service_manager',
        'production_manager': 'production_manager',
        'facilities_manager': 'facilities_manager',
        'vp_operations': 'vp_operations',
        'accounts_receivable_associate': 'accounts_receivable_associate',
        'accounts_payable_associate': 'accounts_payable_associate',
        'accounts_receivable_manager': 'accounts_receivable_manager',
        'accounts_payable_manager': 'accounts_payable_manager',
        'vp_finance': 'vp_finance',
        'coo': 'coo',
        'shareholders': 'shareholders'
    };

    // Map game managers to org chart positions
    const managerToPositionMap = {
        'hr': 'hr_associate',
        'sales_manager': 'sales_manager',
        'advertising_manager': 'advertising_manager',
        'service_manager': 'service_manager',
        'production_manager': 'production_manager',
        'facilities_manager': 'facilities_manager',
        'accounts_receivable_manager': 'accounts_receivable_manager',
        'accounts_payable_manager': 'accounts_payable_manager',
        'vp_marketing': 'vp_marketing',
        'vp_operations': 'vp_operations',
        'vp_finance': 'vp_finance',
        'coo': 'coo',
        'shareholders': 'shareholders'
    };

    // Map task IDs to org chart task IDs
    const taskIdMap = {
        'cover_letter': 'cover_letter',
        'resume': 'resume',
        'interview': 'interview',
        'onboarding_checklist': 'onboarding_checklist',
        'employee_handbook': 'employee_handbook',
        'training_program': 'training_program',
        'market_research': 'market_research',
        'social_media': 'social_media',
        'content_calendar': 'content_calendar',
        'sales_strategy': 'sales_strategy',
        'sales_presentation': 'sales_presentation',
        'sales_training': 'sales_training',
        'ad_campaign': 'ad_campaign',
        'market_analysis': 'market_analysis',
        'brand_guidelines': 'brand_guidelines',
        'customer_service': 'customer_service',
        'service_metrics': 'service_metrics',
        'feedback_system': 'feedback_system',
        'production_efficiency': 'production_efficiency',
        'quality_control': 'quality_control',
        'inventory_management': 'inventory_management',
        'workspace_optimization': 'workspace_optimization',
        'maintenance_schedule': 'maintenance_schedule',
        'safety_protocols': 'safety_protocols',
        'service_improvement': 'service_improvement',
        'team_training': 'team_training',
        'service_automation': 'service_automation',
        'production_planning': 'production_planning',
        'process_improvement': 'process_improvement',
        'team_efficiency': 'team_efficiency',
        'facility_expansion': 'facility_expansion',
        'energy_efficiency': 'energy_efficiency',
        'vendor_management': 'vendor_management',
        'invoice_process': 'invoice_process',
        'payment_tracking': 'payment_tracking',
        'collection_strategy': 'collection_strategy',
        'vendor_payments': 'vendor_payments',
        'expense_reporting': 'expense_reporting',
        'payment_scheduling': 'payment_scheduling',
        'collection_improvement': 'collection_improvement',
        'credit_policy': 'credit_policy',
        'revenue_forecasting': 'revenue_forecasting',
        'payment_optimization': 'payment_optimization',
        'vendor_relationships': 'vendor_relationships',
        'cost_reduction': 'cost_reduction',
        'operations_strategy': 'operations_strategy',
        'department_coordination': 'department_coordination',
        'operational_efficiency': 'operational_efficiency',
        'financial_planning': 'financial_planning',
        'budget_optimization': 'budget_optimization',
        'investment_strategy': 'investment_strategy',
        'strategic_planning': 'strategic_planning',
        'supply_chain_optimization': 'supply_chain_optimization',
        'annual_report': 'annual_report',
        'growth_strategy': 'growth_strategy',
        'succession_planning': 'succession_planning'
    };

    // Sync the game state with the org chart state
    function syncGameState() {
        // Get current role and task from game state
        const gameRole = window.gameState.currentRole;
        const gameTask = window.gameState.currentTask;

        // Map to org chart position and task
        const orgPosition = roleToPositionMap[gameRole] || 'applicant';
        const orgTask = taskIdMap[gameTask] || null;

        console.log(`Syncing game state: Role=${gameRole}, Task=${gameTask} => Position=${orgPosition}, OrgTask=${orgTask}`);

        // Update org chart state
        OrgChart.orgChartState.currentPosition = orgPosition;

        // Find the task index if task is available
        if (orgTask) {
            const tasks = OrgChart.getCurrentTasks();
            const taskIndex = tasks.findIndex(task => task.id === orgTask);
            if (taskIndex >= 0) {
                OrgChart.orgChartState.currentTaskIndex = taskIndex;
            }
        }

        // Update completed tasks based on challenges completed
        if (window.gameState.challengesCompleted > 0) {
            // This is a simplified approach - in a real implementation, you would
            // need to track which specific tasks were completed
            const completedCount = Math.min(window.gameState.challengesCompleted, 3);

            // Clear existing completed tasks for current position
            OrgChart.orgChartState.completedTasks = OrgChart.orgChartState.completedTasks.filter(
                taskId => !taskId.startsWith(orgPosition)
            );

            // Add completed tasks
            const tasks = OrgChart.getCurrentTasks();
            for (let i = 0; i < completedCount && i < tasks.length; i++) {
                const taskId = `${orgPosition}_${tasks[i].id}`;
                if (!OrgChart.orgChartState.completedTasks.includes(taskId)) {
                    OrgChart.orgChartState.completedTasks.push(taskId);
                }
            }
        }

        // Update completed positions
        // In a real implementation, you would need to track which positions were completed
        // This is a simplified approach based on the progression path
        const currentIndex = OrgChart.progressionPath.indexOf(orgPosition);
        OrgChart.orgChartState.completedPositions = OrgChart.progressionPath.slice(0, currentIndex);

        // Update the org chart UI
        OrgChart.updateOrgChart();

        // Save the updated state
        OrgChart.saveGameState();
    }

    // Listen for game state changes
    const originalUpdateUI = window.updateUI || function() {};
    window.updateUI = function() {
        // Call the original updateUI function
        originalUpdateUI.apply(this, arguments);

        // Sync the game state with the org chart
        syncGameState();
    };

    // Add org chart tab to the game UI if it doesn't exist
    function addOrgChartTab() {
        // Check if tabs container exists
        const tabsContainer = document.querySelector('.tabs-container');
        if (!tabsContainer) return;

        // Check if org chart tab already exists
        if (document.querySelector('.tab[data-tab="org-chart"]')) return;

        // Create org chart tab
        const orgChartTab = document.createElement('div');
        orgChartTab.className = 'tab';
        orgChartTab.setAttribute('data-tab', 'org-chart');
        orgChartTab.textContent = 'Organization Chart';

        // Add tab to container
        tabsContainer.appendChild(orgChartTab);

        // Create org chart content
        const contentContainer = document.querySelector('.tab-content-container');
        if (contentContainer) {
            const orgChartContent = document.createElement('div');
            orgChartContent.className = 'tab-content';
            orgChartContent.id = 'org-chart-content';
            orgChartContent.style.display = 'none';

            // Add org chart container
            const orgChartContainer = document.createElement('div');
            orgChartContainer.id = 'org-chart-container';
            orgChartContainer.className = 'org-chart-container';
            orgChartContent.appendChild(orgChartContainer);

            // Add task list container
            const taskListContainer = document.createElement('div');
            taskListContainer.className = 'task-list-container';
            taskListContainer.innerHTML = `
                <h3>Current Tasks</h3>
                <ul id="task-list" class="task-list"></ul>
            `;
            orgChartContent.appendChild(taskListContainer);

            contentContainer.appendChild(orgChartContent);

            // Add tab click handler
            orgChartTab.addEventListener('click', () => {
                // Hide all tab content
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.style.display = 'none';
                });

                // Remove active class from all tabs
                document.querySelectorAll('.tab').forEach(tab => {
                    tab.classList.remove('active');
                });

                // Show org chart content and mark tab as active
                orgChartContent.style.display = 'block';
                orgChartTab.classList.add('active');

                // Initialize org chart
                OrgChart.initOrgChart();
                updateTaskList();
            });
        }
    }

    // Update the task list in the org chart tab
    function updateTaskList() {
        const taskList = document.getElementById('task-list');
        if (!taskList) return;

        taskList.innerHTML = '';

        const position = OrgChart.orgChartState.currentPosition;
        const tasks = OrgChart.getCurrentTasks();

        tasks.forEach((task, index) => {
            const taskItem = document.createElement('li');
            taskItem.className = 'task-item';

            // Check if task is completed
            const isCompleted = OrgChart.orgChartState.completedTasks.includes(`${position}_${task.id}`);
            if (isCompleted) {
                taskItem.classList.add('completed');
            } else if (index === OrgChart.orgChartState.currentTaskIndex) {
                taskItem.classList.add('current');
            }

            taskItem.innerHTML = `
                <div class="task-title">${task.title}</div>
                <div class="task-description">${task.description}</div>
            `;

            taskList.appendChild(taskItem);
        });
    }

    // Initialize the integration
    addOrgChartTab();
    syncGameState();

    console.log('Org chart integration initialized successfully');
}

// Handle task completion in the game
function completeOrgChartTask(taskId) {
    if (!window.OrgChart) return;

    // Map game task ID to org chart task ID if needed
    const orgTaskId = taskId; // Use mapping if IDs differ

    // Complete the task in the org chart
    OrgChart.completeTask(orgTaskId);

    // Save the updated state
    OrgChart.saveGameState();
}

// Export functions for use in other modules
window.OrgChartIntegration = {
    completeTask: completeOrgChartTask
};
