import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from corporate.models import Company, Leaderboard, LeaderboardEntry

def update_leaderboard_entries():
    """Update leaderboard entries to ensure they're visible in the UI"""
    # Get the company
    try:
        company = Company.objects.get(name='Test Company')
        print(f"Found company: {company.name}")
    except Company.DoesNotExist:
        print("Test company not found. Please run test_leaderboard.py first.")
        return

    # Get the leaderboard
    try:
        leaderboard = Leaderboard.objects.get(company=company, time_period='all_time', is_global=False)
        print(f"Found leaderboard: {leaderboard.name}")
    except Leaderboard.DoesNotExist:
        print("Leaderboard not found. Creating a new one.")
        leaderboard = Leaderboard.objects.create(
            company=company,
            name=f"{company.name} Leaderboard",
            time_period='all_time',
            is_global=False,
            active=True
        )

    # Get all test users
    test_users = User.objects.filter(username__startswith='testuser')
    print(f"Found {test_users.count()} test users")

    # Update leaderboard entries for each user
    for user in test_users:
        # Get the user's game session
        from game.models import GameSession
        try:
            game_session = GameSession.objects.get(user=user)
            print(f"Found game session for {user.username}")

            # Get or create leaderboard entry
            entry, created = LeaderboardEntry.objects.update_or_create(
                leaderboard=leaderboard,
                user=user,
                game_id='corporate_prompt_master',
                defaults={
                    'score': game_session.performance_score,
                    'highest_role': game_session.current_role,
                    'game_name': 'Corporate Prompt Master'
                }
            )

            if created:
                print(f"Created new leaderboard entry for {user.username}")
            else:
                print(f"Updated existing leaderboard entry for {user.username}")
        except GameSession.DoesNotExist:
            print(f"No game session found for {user.username}")

    # Check all leaderboard entries
    entries = LeaderboardEntry.objects.filter(leaderboard=leaderboard)
    print(f"\nLeaderboard entries: {entries.count()}")

    for entry in entries:
        print(f"- {entry.user.username}: {entry.score} points, {entry.highest_role}")

def main():
    print("=== Updating Leaderboard Entries ===")
    update_leaderboard_entries()

if __name__ == "__main__":
    main()
