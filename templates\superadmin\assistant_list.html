{% extends "superadmin/base_superadmin.html" %}
{% load static %}

{% block title %}Assistants - Superadmin{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{% url 'superadmin:dashboard' %}">Dashboard</a></li>
<li class="breadcrumb-item active" aria-current="page">Assistants</li>
{% endblock %}

{% block superadmin_content %}
<!-- Filter Form -->
<div class="superadmin-card mb-4">
    <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-funnel me-2"></i>Filters</h5>
    </div>
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label class="form-label">Status</label>
                <select name="status" class="form-select">
                    <option value="">All</option>
                    <option value="active" {% if request.GET.status == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if request.GET.status == 'inactive' %}selected{% endif %}>Inactive</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Tier Pending</label>
                <select name="tier_pending" class="form-select">
                    <option value="">All</option>
                    <option value="true" {% if request.GET.tier_pending == 'true' %}selected{% endif %}>Yes</option>
                    <option value="false" {% if request.GET.tier_pending == 'false' %}selected{% endif %}>No</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Featured</label>
                <select name="featured" class="form-select">
                    <option value="">All</option>
                    <option value="true" {% if request.GET.featured == 'true' %}selected{% endif %}>Yes</option>
                    <option value="false" {% if request.GET.featured == 'false' %}selected{% endif %}>No</option>
                </select>
            </div>
            <div class="col-md-3">
                <label class="form-label">Search</label>
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Assistant name..." value="{{ request.GET.q|default:'' }}">
                    <button class="btn btn-primary" type="submit">
                        <i class="bi bi-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Assistants Table -->
<div class="superadmin-card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-robot me-2"></i>Assistants</h5>
        <span class="badge bg-primary rounded-pill">{{ assistants|length }}</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Company</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for assistant in assistants %}
                    <tr>
                        <td>{{ assistant.name }}</td>
                        <td>{{ assistant.company.name }}</td>
                        <td>
                            {% if assistant.is_active %}
                            <span class="badge bg-success">Active</span>
                            {% else %}
                            <span class="badge bg-danger">Inactive</span>
                            {% endif %}
                        </td>
                        <td>{{ assistant.created_at|date:"Y-m-d" }}</td>
                        <td>
                            <div class="btn-group">
                                <form action="{% url 'superadmin:assistant_activate_toggle' assistant.pk %}" method="post" class="d-inline-block">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-sm {% if assistant.is_active %}btn-outline-danger{% else %}btn-outline-success{% endif %}">
                                        {% if assistant.is_active %}Deactivate{% else %}Activate{% endif %}
                                    </button>
                                </form>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="5" class="text-center py-4">No assistants found.</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if is_paginated %}
<nav aria-label="Page navigation" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if page_obj.has_previous %}
        <li class="page-item">
            <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                <span aria-hidden="true">&laquo;&laquo;</span>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <a class="page-link" href="#" aria-label="First">
                <span aria-hidden="true">&laquo;&laquo;</span>
            </a>
        </li>
        <li class="page-item disabled">
            <a class="page-link" href="#" aria-label="Previous">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>
        {% endif %}
        
        {% for i in paginator.page_range %}
            {% if page_obj.number == i %}
            <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
            {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
            <li class="page-item"><a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a></li>
            {% endif %}
        {% endfor %}
        
        {% if page_obj.has_next %}
        <li class="page-item">
            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
        <li class="page-item">
            <a class="page-link" href="?page={{ paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                <span aria-hidden="true">&raquo;&raquo;</span>
            </a>
        </li>
        {% else %}
        <li class="page-item disabled">
            <a class="page-link" href="#" aria-label="Next">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
        <li class="page-item disabled">
            <a class="page-link" href="#" aria-label="Last">
                <span aria-hidden="true">&raquo;&raquo;</span>
            </a>
        </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}
