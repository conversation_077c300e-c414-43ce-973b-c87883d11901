from django.db.models.signals import post_save
from django.dispatch import receiver
from django.urls import reverse
from django.contrib.auth.models import Permission, Group # Import Group
from django.contrib.contenttypes.models import ContentType # Import ContentType
from guardian.shortcuts import assign_perm # Import assign_perm
from .models import Company
from .utils import generate_qr_code
from .permissions import OWNER_PERMS_COMPANY # Import owner permissions list

@receiver(post_save, sender=Company)
def company_post_save(sender, instance, created, **kwargs):
    """
    - Generate QR code after a Company is saved (if needed).
    - Assign owner permissions when a Company is first created.
    - Assign default group permissions (like adding assistants) to the Company object.
    """
    if created:
        # --- Assign Owner Permissions ---
        owner = instance.owner
        if owner:
            print(f"Assigning Owner permissions for new Company '{instance.name}' to User '{owner.username}'")
            for perm_codename in OWNER_PERMS_COMPANY:
                try:
                    # Assign permission to the owner for this specific company instance
                    assign_perm(perm_codename, owner, instance)
                except Permission.DoesNotExist:
                    print(f"  Warning: Permission '{perm_codename}' not found during owner assignment. Skipping.")
                except Exception as e:
                    print(f"  Error assigning perm '{perm_codename}' to owner '{owner.username}' for new company '{instance.name}': {e}")
        else:
             print(f"  Warning: New Company '{instance.name}' (ID: {instance.id}) was created without an owner. Cannot assign owner permissions.")

        # --- Assign Default Group Permissions to Company Object ---
        try:
            admin_group = Group.objects.get(name='Company Administrators')
            member_group = Group.objects.get(name='Company Members')
            groups_to_assign = [admin_group, member_group]

            # Permissions to assign directly to the Company object for these groups
            company_level_perms = [
                'accounts.manage_company_assistants', # Use the new permission from the accounts app
                # Add other company-level permissions for groups here if needed
            ]

            for group in groups_to_assign:
                for perm_name in company_level_perms:
                    try:
                        assign_perm(perm_name, group, instance)
                        print(f"Assigned Company perm '{perm_name}' for Company ID {instance.id} to Group '{group.name}'")
                    except Permission.DoesNotExist:
                         print(f"  Warning: Permission '{perm_name}' not found. Skipping assignment for Group '{group.name}'.")
                    except Exception as e:
                         print(f"  Error assigning company perm '{perm_name}' to group '{group.name}' for company {instance.id}: {e}")

        except Group.DoesNotExist as e:
            print(f"Warning: Could not find required group during company permission assignment: {e}. Permissions may not be fully assigned.")
        except Exception as e:
            print(f"Error assigning default group permissions to company {instance.id}: {e}")


        # --- Generate QR Code (existing logic) ---
        # Only generate if it doesn't have one (might be redundant with 'created' but safe)
        if not instance.qr_code:
            try:
                # Use the correct URL name 'accounts:company_detail' for the public view
                url_path = reverse('accounts:company_detail', kwargs={'slug': instance.slug})
                success = generate_qr_code(instance, url_path, field_name='qr_code')
                if success:
                    # Save again ONLY if QR code was successfully generated and needs saving
                    # Use update_fields to prevent recursive signal calls
                    instance.save(update_fields=['qr_code'])
                    print(f"Successfully generated QR code for new Company: {instance.name}")
                else:
                    print(f"Failed to generate QR code for new Company: {instance.name}")
            except Exception as e:
                print(f"Error generating QR code in company_post_save signal for {instance.name}: {e}")

    # Also generate QR code if the instance is being updated and the qr_code field is empty
    elif not instance.qr_code:
        print(f"Attempting to generate missing QR code for existing Company: {instance.name}")
        try:
            # Use the correct URL name 'accounts:company_detail' for the public view
            url_path = reverse('accounts:company_detail', kwargs={'slug': instance.slug})
            success = generate_qr_code(instance, url_path, field_name='qr_code')
            if success:
                # Save again ONLY if QR code was successfully generated and needs saving
                # Use update_fields to prevent recursive signal calls
                instance.save(update_fields=['qr_code'])
                print(f"Successfully generated missing QR code for Company: {instance.name}")
            else:
                print(f"Failed to generate missing QR code for Company: {instance.name}")
        except Exception as e:
            print(f"Error generating missing QR code in company_post_save signal for {instance.name}: {e}")


# --- New Signal Handler for RegistrationLink ---
    #         url_path = reverse('accounts:public_company_detail', kwargs={'slug': instance.slug})
    #         success = generate_qr_code(instance, url_path, field_name='qr_code')
    #         if success:
    #             instance.save(update_fields=['qr_code'])
    #             print(f"Regenerated missing QR code for Company: {instance.name}")
    #     except Exception as e:
    #         print(f"Error regenerating QR code in company_post_save signal for {instance.name}: {e}")


# --- New Signal Handler for RegistrationLink ---
from .models import RegistrationLink # Import RegistrationLink model

@receiver(post_save, sender=RegistrationLink)
def registration_link_post_save(sender, instance, created, **kwargs):
    """
    Generate QR code for a RegistrationLink after it's saved.
    """
    # Only generate if created or if qr_code field is empty
    if created or not instance.qr_code:
        try:
            # Get the absolute URL for the link
            url_path = instance.get_absolute_url() # This method already exists on the model
            if url_path: # Ensure URL was generated
                # Generate and save the QR code image to the qr_code field
                # Note: generate_qr_code uses save=False internally, so we need to save here
                success = generate_qr_code(instance, url_path, field_name='qr_code')
                if success:
                    # Save again ONLY if QR code was successfully generated
                    # Use update_fields to prevent recursive signal calls if this signal is triggered again
                    instance.save(update_fields=['qr_code'])
                    print(f"Successfully generated QR code for RegistrationLink: {instance.token}")
                else:
                    print(f"Failed to generate QR code for RegistrationLink: {instance.token}")
            else:
                print(f"Could not generate URL for RegistrationLink: {instance.token}, skipping QR code generation.")
        except Exception as e:
            # Catch potential errors like URL reversing failure or QR generation issues
            print(f"Error generating QR code in registration_link_post_save signal for {instance.token}: {e}")
