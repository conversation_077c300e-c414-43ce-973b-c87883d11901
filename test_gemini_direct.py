#!/usr/bin/env python
"""
Test script to check Gemini API directly without OpenAI wrapper.
"""
import requests
import json

def test_gemini_direct():
    """Test Gemini API directly using REST calls."""
    print("Testing Gemini API directly...")
    
    # Gemini API key
    api_key = "AIzaSyDTjwdVbjGu5Q0v-dP6QsfC4IoKM36g8l8"
    
    # Try the direct Gemini API endpoint
    url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key={api_key}"
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "contents": [{
            "parts": [{
                "text": "Write a short cover letter for a junior assistant position."
            }]
        }]
    }
    
    try:
        print(f"Making request to: {url}")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            print("Direct Gemini API test PASSED")
        else:
            print(f"Error response: {response.text}")
            print("Direct Gemini API test FAILED")
            
    except Exception as e:
        print(f"Direct Gemini API test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()

def test_openai_compatible_endpoint():
    """Test the OpenAI-compatible endpoint."""
    print("\nTesting OpenAI-compatible endpoint...")
    
    # Gemini API key
    api_key = "AIzaSyDTjwdVbjGu5Q0v-dP6QsfC4IoKM36g8l8"
    
    # Try the OpenAI-compatible endpoint
    url = "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "gemini-2.5-flash-lite-preview-06-17",
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Write a short cover letter for a junior assistant position."}
        ],
        "temperature": 0.7,
        "max_tokens": 500
    }
    
    try:
        print(f"Making request to: {url}")
        response = requests.post(url, headers=headers, json=data, timeout=30)
        
        print(f"Response status: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Response: {json.dumps(result, indent=2)}")
            print("OpenAI-compatible endpoint test PASSED")
        else:
            print(f"Error response: {response.text}")
            print("OpenAI-compatible endpoint test FAILED")
            
    except Exception as e:
        print(f"OpenAI-compatible endpoint test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting Gemini API tests...\n")
    
    test_gemini_direct()
    test_openai_compatible_endpoint()
    
    print("\nAll tests completed!")
