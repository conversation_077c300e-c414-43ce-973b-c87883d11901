/* Role Progression Visualization Styles */
.role-progression-container {
    margin: 20px 0;
    background-color: #f5f5f5;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
    position: relative; /* For positioning the scroll indicator */
}

.role-progression-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.role-progression-header h3 {
    margin: 0;
    font-size: 16px;
    color: #d1d5d9;
}

.toggle-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 18px;
    color: #555;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.toggle-button:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.role-progression-content {
    padding: 15px;
    max-height: 70vh; /* Set max height to ensure scrolling is available */
    overflow-y: auto; /* Changed to auto to enable scrolling */
    transition: max-height 0.5s ease, background-color 0.3s ease, color 0.3s ease;
    scroll-behavior: smooth; /* Add smooth scrolling */
}

.role-progression-content.collapsed {
    max-height: 0;
    overflow: hidden;
    padding: 0;
}

/* Role progression visualization */
.role-progression {
    margin-bottom: 15px;
}

.role-progression h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #dbd4d4;
    font-size: 16px;
    text-align: center;
    font-weight: bold;
}

/* Department headers */
.department-headers {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 8px;
}

.department-header {
    font-size: 12px;
    font-weight: bold;
    color: #666;
    padding: 5px;
    border-radius: 4px;
    text-align: center;
    flex: 1;
}

.department-header.active-department {
    background-color: #e3f2fd;
    color: #1976D2;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Role path */
.role-path {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    overflow-x: auto;
    padding: 5px 0;
    scroll-behavior: smooth; /* Add smooth scrolling */
    scrollbar-width: thin; /* Thinner scrollbar */
    scrollbar-color: #cbd5e1 #f1f5f9; /* Customize scrollbar colors */
}

.role-node {
    min-width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: white;
    position: relative;
    z-index: 2;
    flex-shrink: 0;
    transition: all 0.3s ease;
    font-size: 12px;
}

.role-node.completed {
    background-color: #4CAF50;
}

.role-node.current {
    background-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.3);
    transform: scale(1.2);
}

.role-node.future {
    background-color: #9E9E9E;
}

.role-connector {
    flex-grow: 1;
    height: 2px;
    margin: 0 3px;
    z-index: 1;
    min-width: 5px;
}

.role-connector.completed {
    background-color: #4CAF50;
}

.role-connector.future {
    background-color: #9E9E9E;
}

.current-role {
    font-size: 14px;
    color: #333;
    margin-bottom: 10px;
    text-align: center;
}

.current-role strong {
    color: #2196F3;
}

/* Career progress bar */
.career-progress {
    font-size: 12px;
    color: #555;
    margin-top: 15px;
    display: flex;
    align-items: center;
}

.progress-bar {
    flex-grow: 1;
    height: 6px;
    background-color: #e0e0e0;
    border-radius: 3px;
    margin: 0 10px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #4CAF50;
    border-radius: 3px;
    transition: width 0.5s ease;
}

/* Tooltip styles */
.role-node:hover {
    cursor: pointer;
}

.role-node::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #333;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    pointer-events: none;
    z-index: 10;
}

.role-node:hover::after {
    opacity: 1;
    visibility: visible;
}

/* Scrollbar styles for role path */
.role-path::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.role-path::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 10px;
}

.role-path::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 10px;
    border: 2px solid #f1f5f9;
}

.role-path::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
}

/* Role progression scroll indicator */
.role-progression-scroll-indicator {
    position: absolute;
    bottom: 10px;
    right: 10px;
    background-color: #3b82f6;
    color: white;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 5;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    opacity: 0.8;
    transition: all 0.3s ease;
    cursor: pointer;
}

.role-progression-scroll-indicator:hover {
    opacity: 1;
    transform: scale(1.1);
}

.role-progression-scroll-indicator::after {
    content: 'Scroll to see more';
    position: absolute;
    bottom: -25px;
    right: 0;
    background-color: #334155;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(5px);
    transition: all 0.2s ease;
    pointer-events: none;
}

.role-progression-scroll-indicator:hover::after {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .role-progression-container {
        margin: 15px 0;
    }

    .role-node {
        min-width: 20px;
        height: 20px;
        font-size: 10px;
    }

    .department-header {
        font-size: 10px;
        padding: 3px;
    }

    /* Mobile styles for scroll indicator */
    .role-progression-scroll-indicator {
        width: 28px;
        height: 28px;
        bottom: 8px;
        right: 8px;
    }

    .role-progression-scroll-indicator::after {
        content: 'Scroll';
        font-size: 9px;
        padding: 3px 6px;
        bottom: -20px;
    }
}
