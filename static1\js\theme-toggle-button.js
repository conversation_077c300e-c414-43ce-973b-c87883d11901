/**
 * Theme Toggle Button - Disabled
 * Dark mode is now the only theme, so the toggle button is no longer needed
 */

(function() {
    // Function to ensure dark mode is always applied
    function ensureDarkMode() {
        // Always use dark mode
        const theme = 'dark';

        // Apply dark mode
        document.documentElement.setAttribute('data-theme', theme);
        document.body.setAttribute('data-theme', theme);

        // Store theme preference
        localStorage.setItem('theme', theme);

        // Remove any existing theme toggle buttons
        const themeToggleBtn = document.querySelector('.theme-toggle-btn');
        if (themeToggleBtn) {
            themeToggleBtn.remove();
        }

        // Dispatch theme changed event to ensure all components update
        document.dispatchEvent(new CustomEvent('themeChanged', { detail: { theme } }));
    }

    // Run on initial page load
    document.addEventListener('DOMContentLoaded', ensureDarkMode);

    // Also run when navigating between pages
    window.addEventListener('popstate', ensureDarkMode);
    window.addEventListener('pushstate', ensureDarkMode);
    window.addEventListener('replacestate', ensureDarkMode);

    // For frameworks that dispatch custom events on navigation
    window.addEventListener('page:load', ensureDarkMode);
    window.addEventListener('turbolinks:load', ensureDarkMode);
    window.addEventListener('navigate', ensureDarkMode);
})();
