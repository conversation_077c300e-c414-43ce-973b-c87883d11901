/**
 * Like Button Text Hide JavaScript
 * Prevents text from being added to like buttons when toggling state
 */

document.addEventListener('DOMContentLoaded', function() {
    // Override the updateHeartIcon function if it exists
    if (window.updateHeartIcon) {
        const originalUpdateHeartIcon = window.updateHeartIcon;
        window.updateHeartIcon = function(button, isSaved) {
            if (!button) return;
            
            // Update button classes
            if (isSaved) {
                button.classList.remove('text-secondary');
                button.classList.add('text-danger');
                button.title = 'Unlike';
                
                // Update icon but don't add text
                const icon = button.querySelector('i');
                if (icon) {
                    icon.classList.remove('bi-heart');
                    icon.classList.add('bi-heart-fill');
                }
            } else {
                button.classList.remove('text-danger');
                button.classList.add('text-secondary');
                button.title = 'Like';
                
                // Update icon but don't add text
                const icon = button.querySelector('i');
                if (icon) {
                    icon.classList.remove('bi-heart-fill');
                    icon.classList.add('bi-heart');
                }
            }
        };
    }
    
    // Override the handleFavoriteClick function in favorites-functionality.js
    if (window.handleFavoriteClick) {
        const originalHandleFavoriteClick = window.handleFavoriteClick;
        window.handleFavoriteClick = async function(button, itemId, itemType) {
            // Call the original function
            await originalHandleFavoriteClick(button, itemId, itemType);
            
            // Remove any text nodes from the button
            Array.from(button.childNodes).forEach(node => {
                if (node.nodeType === Node.TEXT_NODE) {
                    button.removeChild(node);
                }
            });
        };
    }
    
    // Patch the updateHeartIcon function in favorites-functionality.js
    const originalUpdateHeartIconFunc = window.updateHeartIcon;
    if (typeof originalUpdateHeartIconFunc === 'function') {
        window.updateHeartIcon = function(button, isSaved) {
            // Call the original function
            originalUpdateHeartIconFunc(button, isSaved);
            
            // Remove any text nodes from the button
            Array.from(button.childNodes).forEach(node => {
                if (node.nodeType === Node.TEXT_NODE) {
                    button.removeChild(node);
                }
            });
        };
    }
});

// Function to clean up all like buttons on the page
function cleanupLikeButtons() {
    const likeButtons = document.querySelectorAll('.like-button, .btn-like, .btn-favorite, .favorite-button');
    
    likeButtons.forEach(button => {
        // Remove any text nodes
        Array.from(button.childNodes).forEach(node => {
            if (node.nodeType === Node.TEXT_NODE) {
                button.removeChild(node);
            }
        });
        
        // Remove margin from heart icon
        const icon = button.querySelector('i');
        if (icon) {
            icon.classList.remove('me-1');
        }
    });
}

// Run cleanup on page load
document.addEventListener('DOMContentLoaded', cleanupLikeButtons);

// Run cleanup after a short delay to catch any dynamically added buttons
setTimeout(cleanupLikeButtons, 500);

// Run cleanup periodically to catch any buttons added by AJAX
setInterval(cleanupLikeButtons, 2000);
