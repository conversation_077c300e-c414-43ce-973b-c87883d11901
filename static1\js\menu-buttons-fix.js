/**
 * Menu Buttons Fix
 * This script specifically fixes issues with buttons interfering with the menu
 */

(function() {
    // Function to fix floating buttons
    function fixFloatingButtons() {

        // Fix theme toggle button
        const themeToggleBtn = document.querySelector('.theme-toggle-btn');
        if (themeToggleBtn) {
            themeToggleBtn.style.bottom = '100px';
            themeToggleBtn.style.zIndex = '990';
        }

        // Fix impersonation button
        const impersonationBtn = document.querySelector('.impersonation-float-button, #impersonation-float-button');
        if (impersonationBtn) {
            impersonationBtn.style.bottom = '170px';
            impersonationBtn.style.zIndex = '990';
        }

        // Fix any other floating buttons
        const fixButtons = document.querySelectorAll('button[id^="fix-"], button[class*="fix-button"]');
        fixButtons.forEach(function(button) {
            button.style.bottom = '240px';
            button.style.zIndex = '990';
        });
    }

    // Function to ensure menu has higher z-index
    function ensureMenuZIndex() {

        // Fix navbar
        const navbar = document.querySelector('.navbar');
        if (navbar) {
            navbar.style.zIndex = '1100';
            navbar.style.position = 'relative';
        }

        // Fix navbar collapse
        const navbarCollapse = document.querySelector('.navbar-collapse');
        if (navbarCollapse) {
            navbarCollapse.style.zIndex = '1100';
        }

        // Fix navbar toggler
        const navbarToggler = document.querySelector('.navbar-toggler');
        if (navbarToggler) {
            navbarToggler.style.zIndex = '1120';
            navbarToggler.style.position = 'relative';
            navbarToggler.style.pointerEvents = 'auto';
        }

        // Fix nav links
        const navLinks = document.querySelectorAll('.navbar .nav-link, .navbar .dropdown-item, .navbar-nav .nav-item a');
        navLinks.forEach(function(link) {
            link.style.zIndex = '1110';
            link.style.position = 'relative';
            link.style.pointerEvents = 'auto';
        });

        // Fix dropdown menus
        const dropdownMenus = document.querySelectorAll('.dropdown-menu');
        dropdownMenus.forEach(function(menu) {
            menu.style.zIndex = '1115';
            menu.style.pointerEvents = 'auto';
        });
    }

    // Run the fixes immediately
    fixFloatingButtons();
    ensureMenuZIndex();

    // Also run when the document is fully loaded
    document.addEventListener('DOMContentLoaded', function() {
        fixFloatingButtons();
        ensureMenuZIndex();

        // Run again after a short delay
        setTimeout(fixFloatingButtons, 1000);
        setTimeout(ensureMenuZIndex, 1000);
    });

    // Run the fixes once more after a longer delay to ensure they're applied
    setTimeout(fixFloatingButtons, 5000);
    setTimeout(ensureMenuZIndex, 5000);
})();
