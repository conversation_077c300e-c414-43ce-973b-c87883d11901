/**
 * Company Directory Dark Mode CSS
 * Specific dark mode styling for company directory pages
 */

/* Company Directory container */
[data-theme="dark"] body.bg-light {
  background-color: #121212 !important;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(30, 30, 30, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(20, 20, 20, 0.2) 0%, transparent 50%),
    linear-gradient(to bottom, #121212, #0a0a0a) !important;
  background-attachment: fixed !important;
  color: #ffffff !important;
}

[data-theme="dark"] .bg-light {
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Company Directory main container */
[data-theme="dark"] .company-directory-container,
[data-theme="dark"] .container-fluid,
[data-theme="dark"] .container {
  background-color: #121212 !important;
  color: #ffffff !important;
  padding: 20px !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2) !important;
}

/* Main content area */
[data-theme="dark"] .content-area,
[data-theme="dark"] .main-content {
  background-color: #121212 !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  padding: 20px !important;
}

/* Company Directory header */
[data-theme="dark"] .company-directory-header {
  border-bottom: 1px solid #333333 !important;
  margin-bottom: 30px !important;
  padding-bottom: 15px !important;
}

[data-theme="dark"] .company-directory-header h1 {
  color: #ffffff !important;
  font-weight: 600 !important;
}

/* Featured Companies section */
[data-theme="dark"] .featured-section,
[data-theme="dark"] .featured-companies {
  background-color: #1a1a1a !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
  padding: 20px !important;
  margin-bottom: 30px !important;
  border: 1px solid #333333 !important;
}

[data-theme="dark"] .featured-section h2,
[data-theme="dark"] .featured-companies h2 {
  color: #ffffff !important;
  border-bottom: 1px solid #333333 !important;
  padding-bottom: 10px !important;
  margin-bottom: 20px !important;
}

/* Featured carousel container */
[data-theme="dark"] .featured-carousel-container {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Featured carousel items */
[data-theme="dark"] .featured-carousel-items {
  background-color: transparent !important;
}

/* Featured item wrapper */
[data-theme="dark"] .featured-item-wrapper {
  background-color: #252525 !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4) !important;
}

[data-theme="dark"] .featured-carousel-item .item-info h5 {
  color: #ffffff !important;
}

[data-theme="dark"] .featured-carousel-item .item-info p {
  color: #cccccc !important;
}

/* Logo containers */
[data-theme="dark"] .featured-carousel-item .logo-container,
[data-theme="dark"] .company-logo-container,
[data-theme="dark"] .logo-container {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  padding: 15px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .featured-carousel-item .logo-placeholder,
[data-theme="dark"] .company-logo-placeholder,
[data-theme="dark"] .logo-placeholder {
  background-color: #1a1a1a !important;
  color: #0077ff !important;
  font-weight: bold !important;
  font-size: 1.5rem !important;
  text-align: center !important;
}

/* Company logo images */
[data-theme="dark"] .company-logo,
[data-theme="dark"] .featured-logo,
[data-theme="dark"] img.logo {
  max-width: 100% !important;
  max-height: 80px !important;
  object-fit: contain !important;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
}

/* Company cards */
[data-theme="dark"] .company-card {
  background-color: #252525 !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
  transition: transform 0.3s ease, box-shadow 0.3s ease !important;
  margin-bottom: 20px !important;
  overflow: hidden !important;
}

[data-theme="dark"] .company-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .company-card .company-logo-container {
  background-color: #1a1a1a !important;
  border-bottom: 1px solid #333333 !important;
  padding: 15px !important;
  text-align: center !important;
}

[data-theme="dark"] .company-card .company-logo {
  max-height: 80px !important;
  max-width: 100% !important;
}

[data-theme="dark"] .company-card .company-details {
  padding: 15px !important;
}

[data-theme="dark"] .company-card .company-name {
  color: #ffffff !important;
  font-weight: 600 !important;
  margin-bottom: 10px !important;
}

[data-theme="dark"] .company-card .company-description {
  color: #cccccc !important;
  margin-bottom: 15px !important;
}

[data-theme="dark"] .company-card .company-rating {
  color: #ffcc00 !important;
  margin-bottom: 10px !important;
}

[data-theme="dark"] .company-card .company-actions {
  border-top: 1px solid #333333 !important;
  padding: 10px 15px !important;
  background-color: rgba(0, 0, 0, 0.2) !important;
}

/* Company search container and filter form */
[data-theme="dark"] .company-search-container,
[data-theme="dark"] .filter-form {
  background-color: #1a1a1a !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  padding: 20px !important;
  margin-bottom: 30px !important;
  border: 1px solid #333333 !important;
}

[data-theme="dark"] .company-search-container h3,
[data-theme="dark"] .filter-form h3,
[data-theme="dark"] .filter-form h5,
[data-theme="dark"] .filter-form .form-label {
  color: #ffffff !important;
  margin-bottom: 15px !important;
}

[data-theme="dark"] .search-form {
  display: flex !important;
  gap: 10px !important;
}

/* Search inputs and form controls */
[data-theme="dark"] .search-input,
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select,
[data-theme="dark"] input[type="text"],
[data-theme="dark"] input[type="search"],
[data-theme="dark"] select {
  background-color: #252525 !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
  padding: 10px 15px !important;
  border-radius: 4px !important;
}

/* Search bar specific styling */
[data-theme="dark"] .search-bar,
[data-theme="dark"] .search-container {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

[data-theme="dark"] .search-bar input,
[data-theme="dark"] .search-container input {
  background-color: #252525 !important;
  color: #ffffff !important;
  border: none !important;
  padding: 12px 15px !important;
}

[data-theme="dark"] .search-bar button,
[data-theme="dark"] .search-container button {
  background-color: #0077ff !important;
  color: #ffffff !important;
  border: none !important;
  padding: 12px 20px !important;
}

[data-theme="dark"] .search-bar button:hover,
[data-theme="dark"] .search-container button:hover {
  background-color: #0066dd !important;
}

/* Company directory specific search bar */
[data-theme="dark"] .company-directory-container .input-group,
[data-theme="dark"] .filter-form .input-group {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  padding: 0 !important; /* Remove excessive padding */
  margin-bottom: 0 !important; /* Remove bottom margin */
  box-shadow: none !important; /* Remove shadow */
}

[data-theme="dark"] .company-directory-container .input-group input,
[data-theme="dark"] .filter-form .input-group input {
  background-color: #252525 !important;
  color: #ffffff !important;
  border: none !important;
  padding: 12px 15px !important;
  height: auto !important;
}

[data-theme="dark"] .company-directory-container .input-group .btn,
[data-theme="dark"] .filter-form .input-group .btn {
  background-color: #0077ff !important;
  background-image: none !important; /* Remove gradient */
  color: #ffffff !important;
  border: none !important;
  padding: 12px 20px !important;
  height: auto !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important; /* Subtle shadow */
}

[data-theme="dark"] .company-directory-container .input-group .btn:hover,
[data-theme="dark"] .filter-form .input-group .btn:hover {
  background-color: #0066dd !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
}

[data-theme="dark"] .company-directory-container .input-group .input-group-text,
[data-theme="dark"] .filter-form .input-group .input-group-text {
  background-color: #252525 !important;
  border: none !important;
  border-right: 1px solid #333333 !important;
  color: #ffffff !important;
}

/* Filter form specific styling */
[data-theme="dark"] .filter-form,
[data-theme="dark"] form.filter-form,
[data-theme="dark"] #form-filter-form,
[data-theme="dark"] .form-filter-form {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Ensure all elements inside filter form have dark background */
[data-theme="dark"] .filter-form *,
[data-theme="dark"] form.filter-form *,
[data-theme="dark"] #form-filter-form *,
[data-theme="dark"] .form-filter-form * {
  background-color: transparent !important;
}

/* Direct styling for the form-filter-form ID */
[data-theme="dark"] #form-filter-form,
[data-theme="dark"] form#form-filter-form,
[data-theme="dark"] form[id="form-filter-form"] {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Target all form elements in the company directory */
[data-theme="dark"] .company-directory-container form,
[data-theme="dark"] .company-directory form,
[data-theme="dark"] form.filter-form {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* Force background color for all elements with white background */
[data-theme="dark"] [style*="background-color: white"],
[data-theme="dark"] [style*="background-color: #fff"],
[data-theme="dark"] [style*="background-color:#fff"],
[data-theme="dark"] [style*="background-color:#ffffff"],
[data-theme="dark"] [style*="background-color: #ffffff"],
[data-theme="dark"] [style*="background: white"],
[data-theme="dark"] [style*="background: #fff"],
[data-theme="dark"] [style*="background:#fff"],
[data-theme="dark"] [style*="background:#ffffff"],
[data-theme="dark"] [style*="background: #ffffff"] {
  background-color: #1a1a1a !important;
  background: #1a1a1a !important;
}

/* Extremely specific selector for the filter form */
[data-theme="dark"] form[id="form-filter-form"],
[data-theme="dark"] form#form-filter-form,
[data-theme="dark"] #form-filter-form,
[data-theme="dark"] .form-filter-form,
[data-theme="dark"] form.filter-form,
[data-theme="dark"] div > form#form-filter-form,
[data-theme="dark"] div > #form-filter-form,
[data-theme="dark"] body form#form-filter-form {
  background-color: #1a1a1a !important;
  background: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .filter-form h5,
[data-theme="dark"] .filter-form .h5 {
  color: #ffffff !important;
  margin-bottom: 15px !important;
}

[data-theme="dark"] .filter-form .text-muted,
[data-theme="dark"] .filter-form .text-secondary {
  color: #aaaaaa !important;
}

[data-theme="dark"] .filter-form .bi-info-circle,
[data-theme="dark"] .filter-form .bi-funnel-fill {
  color: #0077ff !important;
}

[data-theme="dark"] .search-input:focus,
[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus,
[data-theme="dark"] input[type="text"]:focus,
[data-theme="dark"] input[type="search"]:focus,
[data-theme="dark"] select:focus {
  border-color: #0077ff !important;
  box-shadow: 0 0 0 3px rgba(0, 119, 255, 0.25) !important;
  outline: none !important;
}

[data-theme="dark"] .search-input::placeholder,
[data-theme="dark"] .form-control::placeholder,
[data-theme="dark"] input[type="text"]::placeholder,
[data-theme="dark"] input[type="search"]::placeholder {
  color: #777777 !important;
}

/* Input group text */
[data-theme="dark"] .input-group-text {
  background-color: #333333 !important;
  border: 1px solid #444444 !important;
  color: #ffffff !important;
}

/* Search and filter buttons */
[data-theme="dark"] .search-button,
[data-theme="dark"] .btn-primary {
  background-color: #0077ff !important;
  border: none !important;
  color: #ffffff !important;
  padding: 10px 20px !important;
  border-radius: 4px !important;
  cursor: pointer !important;
  font-weight: 500 !important;
  transition: background-color 0.2s ease !important;
}

[data-theme="dark"] .search-button:hover,
[data-theme="dark"] .btn-primary:hover {
  background-color: #0066dd !important;
}

/* Secondary buttons */
[data-theme="dark"] .btn-outline-secondary {
  color: #cccccc !important;
  border: 1px solid #555555 !important;
  background-color: transparent !important;
}

[data-theme="dark"] .btn-outline-secondary:hover {
  background-color: #333333 !important;
  color: #ffffff !important;
}

/* Company filters */
[data-theme="dark"] .company-filters {
  background-color: #1a1a1a !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
  padding: 20px !important;
  margin-bottom: 30px !important;
}

[data-theme="dark"] .company-filters h3 {
  color: #ffffff !important;
  margin-bottom: 15px !important;
  border-bottom: 1px solid #333333 !important;
  padding-bottom: 10px !important;
}

[data-theme="dark"] .filter-group {
  margin-bottom: 15px !important;
}

[data-theme="dark"] .filter-group label {
  color: #ffffff !important;
  display: block !important;
  margin-bottom: 5px !important;
  font-weight: 500 !important;
}

[data-theme="dark"] .filter-group select,
[data-theme="dark"] .filter-group input[type="checkbox"] {
  background-color: #252525 !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  width: 100% !important;
}

[data-theme="dark"] .filter-group select:focus {
  border-color: #0077ff !important;
  box-shadow: 0 0 0 3px rgba(0, 119, 255, 0.25) !important;
  outline: none !important;
}

/* Company pagination */
[data-theme="dark"] .company-pagination {
  margin-top: 30px !important;
  display: flex !important;
  justify-content: center !important;
}

[data-theme="dark"] .pagination .page-item .page-link {
  background-color: #252525 !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .pagination .page-item.active .page-link {
  background-color: #0077ff !important;
  border-color: #0077ff !important;
  color: #ffffff !important;
}

[data-theme="dark"] .pagination .page-item .page-link:hover {
  background-color: #333333 !important;
}

/* Tier sections */
[data-theme="dark"] .tier-section {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin-bottom: 30px !important;
}

[data-theme="dark"] .tier-section h3 {
  color: #ffffff !important;
  border-bottom: 1px solid #333333 !important;
  padding-bottom: 10px !important;
  margin-bottom: 20px !important;
}

/* List group and cards */
[data-theme="dark"] .list-group-item,
[data-theme="dark"] .company-cards-container .card {
  background-color: #252525 !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .list-group-item:hover,
[data-theme="dark"] .company-cards-container .card:hover {
  background-color: #2a2a2a !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-5px) !important;
}

[data-theme="dark"] .list-group-item a,
[data-theme="dark"] .company-cards-container .card a {
  color: #0088ff !important;
}

[data-theme="dark"] .list-group-item a:hover,
[data-theme="dark"] .company-cards-container .card a:hover {
  color: #00aaff !important;
}

/* Like and favorite buttons */
[data-theme="dark"] .like-button,
[data-theme="dark"] .favorite-button,
[data-theme="dark"] .btn-like,
[data-theme="dark"] .btn-favorite,
[data-theme="dark"] .btn-circle {
  background-color: #252525 !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
  border-radius: 50% !important;
  width: 36px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.2s ease !important;
}

[data-theme="dark"] .like-button:hover,
[data-theme="dark"] .favorite-button:hover,
[data-theme="dark"] .btn-like:hover,
[data-theme="dark"] .btn-favorite:hover,
[data-theme="dark"] .btn-circle:hover {
  background-color: #333333 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
}

[data-theme="dark"] .like-button.active,
[data-theme="dark"] .favorite-button.active,
[data-theme="dark"] .btn-like.active,
[data-theme="dark"] .btn-favorite.active,
[data-theme="dark"] .btn-circle.active {
  background-color: #0077ff !important;
  border-color: #0077ff !important;
  color: #ffffff !important;
}

[data-theme="dark"] .like-button i,
[data-theme="dark"] .favorite-button i,
[data-theme="dark"] .btn-like i,
[data-theme="dark"] .btn-favorite i,
[data-theme="dark"] .btn-circle i {
  font-size: 16px !important;
}

/* Tier badges */
[data-theme="dark"] .tier-badge {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .tier-gold {
  background-color: #ffc107 !important;
  color: #212529 !important;
}

[data-theme="dark"] .tier-silver {
  background-color: #adb5bd !important;
  color: #212529 !important;
}

[data-theme="dark"] .tier-bronze {
  background-color: #cd7f32 !important;
  color: #ffffff !important;
}

/* Alert styling */
[data-theme="dark"] .alert-light {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .badge {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

/* Table styling */
[data-theme="dark"] .table {
  color: #ffffff !important;
  background-color: #1a1a1a !important;
  border-radius: 8px !important;
  overflow: hidden !important;
  border: 1px solid #333333 !important;
}

[data-theme="dark"] .table thead th {
  background-color: #252525 !important;
  color: #ffffff !important;
  border-bottom: 2px solid #333333 !important;
  padding: 12px 15px !important;
  font-weight: 600 !important;
}

[data-theme="dark"] .table tbody tr {
  border-bottom: 1px solid #333333 !important;
}

[data-theme="dark"] .table tbody tr:hover {
  background-color: #252525 !important;
}

[data-theme="dark"] .table tbody td {
  padding: 12px 15px !important;
  color: #cccccc !important;
  border-top: 1px solid #333333 !important;
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
  background-color: #1d1d1d !important;
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(even) {
  background-color: #1a1a1a !important;
}

/* Modal styling */
[data-theme="dark"] .modal-content {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .modal-header {
  border-bottom: 1px solid #333333 !important;
  padding: 15px 20px !important;
}

[data-theme="dark"] .modal-header .modal-title {
  color: #ffffff !important;
  font-weight: 600 !important;
}

[data-theme="dark"] .modal-header .close {
  color: #ffffff !important;
  opacity: 0.8 !important;
}

[data-theme="dark"] .modal-header .close:hover {
  opacity: 1 !important;
}

[data-theme="dark"] .modal-body {
  padding: 20px !important;
  color: #cccccc !important;
}

[data-theme="dark"] .modal-footer {
  border-top: 1px solid #333333 !important;
  padding: 15px 20px !important;
}

/* Company empty state */
[data-theme="dark"] .company-empty-state {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 30px !important;
  text-align: center !important;
  margin: 30px 0 !important;
}

[data-theme="dark"] .company-empty-state h3 {
  color: #ffffff !important;
  margin-bottom: 10px !important;
}

[data-theme="dark"] .company-empty-state p {
  color: #cccccc !important;
  margin-bottom: 20px !important;
}
