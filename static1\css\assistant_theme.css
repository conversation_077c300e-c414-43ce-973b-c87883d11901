/* General Theme Styles Extracted from assistant_chat.html */

/* Base styles */
html, body {
    /* Solid background color */
    background: #e8f4ff !important; /* Solid light blue background */
    color: #333333 !important; /* Dark text for better readability */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    /* height, overflow-x, background-attachment might conflict with base layout, removed for broader compatibility */
}

/* General Link Styling */
a {
    color: #7d8bd2 !important; /* Light blue for dark theme */
    text-decoration: none !important;
}

a:hover {
    text-decoration: underline !important;
}

/* Basic Content Styling */
strong {
    font-weight: 600 !important;
}

code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace !important;
    background-color: rgba(255, 255, 255, 0.1) !important;
    padding: 0.1em 0.3em !important;
    border-radius: 3px !important;
    font-size: 0.9em !important;
    color: #e3e3e7 !important;
}

pre {
    background-color: rgba(255, 255, 255, 0.1) !important;
    padding: 0.5rem !important;
    border-radius: 5px !important;
    overflow-x: auto !important;
    margin: 0.75rem 0 !important;
}

pre code {
    background-color: transparent !important;
    padding: 0 !important;
    font-size: 0.9em !important;
}


/* Input Field Styling (General) */
.form-control { /* Apply to general form controls */
    border-radius: 1rem;
    padding: 0.7rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1) !important; /* Light theme border */
    background-color: #ffffff !important; /* Light theme background */
    transition: all 0.3s ease;
    color: #333333 !important; /* Light theme text */
    font-size: 0.95rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    letter-spacing: 0.01em;
    font-weight: 450;
}

.form-control:focus {
    background-color: #ffffff !important; /* Keep white on focus */
    border-color: rgba(74, 144, 226, 0.5) !important; /* Light blue border */
    box-shadow: 0 0 0 0.25rem rgba(74, 144, 226, 0.25) !important;
    color: #333333 !important;
}

/* Apply similar style to select elements */
.form-select {
    border-radius: 1rem;
    padding: 0.7rem 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    background-color: #40414f !important;
    color: #ececf1 !important;
    font-size: 0.95rem;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23adb5bd' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"); /* Dark theme arrow */
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
}
.form-select:focus {
     border-color: rgba(255, 255, 255, 0.2) !important;
     box-shadow: none;
}


/* Button Styling (General) */
.btn-primary { /* Apply to primary buttons */
    border-radius: 1rem;
    padding: 0.5rem 1rem;
    background: #4a90e2 !important; /* Light blue button */
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    font-size: 0.9rem;
    font-weight: 500;
    letter-spacing: 0.01em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.btn-primary:hover {
    background: #3a80d2 !important; /* Darker blue on hover */
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.btn-secondary {
     border-radius: 1rem;
     padding: 0.5rem 1rem;
     background: #6c757d !important;
     color: #ffffff !important;
     border: 1px solid rgba(255, 255, 255, 0.1) !important;
     transition: all 0.3s ease;
     font-size: 0.9rem;
     font-weight: 500;
}
.btn-secondary:hover {
    background: #5a6268 !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
}

.btn-outline-secondary {
    border-radius: 1rem;
    padding: 0.5rem 1rem;
    color: #adb5bd !important;
    border-color: #6c757d !important;
    transition: all 0.3s ease;
}
.btn-outline-secondary:hover {
    color: #ececf1 !important;
    background-color: #6c757d !important;
    border-color: #6c757d !important;
}

/* Add other button types (.btn-success, .btn-danger, .btn-warning, .btn-info) if needed */
.btn-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: #ffffff !important;
    /* Add other shared styles */
}
.btn-success:hover {
    background-color: #157347 !important;
    border-color: #146c43 !important;
}
.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
}
.btn-danger:hover {
    background-color: #bb2d3b !important;
    border-color: #b02a37 !important;
}
.btn-warning {
     background-color: #ffc107 !important;
     border-color: #ffc107 !important;
     color: #000 !important;
}
.btn-warning:hover {
     background-color: #ffca2c !important;
     border-color: #ffc720 !important;
}
.btn-info {
     background-color: #0dcaf0 !important;
     border-color: #0dcaf0 !important;
     color: #000 !important;
}
.btn-info:hover {
     background-color: #31d2f2 !important;
     border-color: #25cff2 !important;
}


/* Modal Styling */
.modal-content {
    background-color: #444654; /* Dark background */
    color: #ececf1; /* Light text */
    border: 1px solid rgba(255, 255, 255, 0.1);
}
.modal-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.modal-footer {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}
.btn-close {
    filter: invert(1) grayscale(100%) brightness(200%); /* Make close button visible on dark background */
}

/* Context Modal Specifics (Keep if modals are used elsewhere) */
.context-content { /* Style for content inside modals */
    font-size: 0.95rem;
    line-height: 1.65;
    white-space: pre-wrap;
    padding: 1.1rem;
    background: #50515f; /* Slightly lighter dark background */
    border-radius: 0.7rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    letter-spacing: 0.01em;
    color: #ececf1;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-weight: 450;
}

.context-metadata { /* Style for metadata in modals */
    margin-left: 1rem;
    padding: 0.3rem 0.6rem;
    background: #343541; /* Darker background */
    border-radius: 0.4rem;
    font-size: 0.8rem;
    font-weight: 450;
    color: #adb5bd; /* Lighter gray */
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
    letter-spacing: 0.01em;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Tab styling (Keep if tabs are used elsewhere, adapt selectors if needed) */
.nav-tabs {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

.nav-tabs .nav-link {
    border: 1px solid transparent;
    border-top-left-radius: 0.25rem;
    border-top-right-radius: 0.25rem;
    color: #adb5bd; /* Lighter gray for inactive tabs */
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-size: 0.95rem;
    font-weight: 500;
    padding: 0.75rem 1.25rem;
    transition: all 0.2s ease;
    margin-right: 0.25rem;
    background: transparent; /* Dark theme */
}

.nav-tabs .nav-link:hover {
    border-color: rgba(255, 255, 255, 0.05);
    color: #ececf1; /* Light text on hover */
    background-color: rgba(255, 255, 255, 0.05); /* Subtle hover background */
}

.nav-tabs .nav-link.active {
    background-color: #444654; /* Match modal content background */
    border-color: rgba(255, 255, 255, 0.1) rgba(255, 255, 255, 0.1) #444654;
    color: #ffffff; /* White text for active tab */
    font-weight: 600;
    border-bottom: 2px solid #7d8bd2; /* Use link color for active indicator */
}

/* Card Styling (Basic dark theme adaptation) */
.card {
    background-color: #444654 !important; /* Dark background */
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #ececf1 !important;
}
.card-header {
    background-color: #50515f !important; /* Slightly lighter header */
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}
.card-body {
     color: #ececf1 !important;
}
.card-footer {
     background-color: #50515f !important;
     border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Alert Styling (Dark theme adaptation) */
.alert {
    border-radius: 0.5rem; /* Consistent border radius */
}
.alert-info {
    color: #cff4fc !important;
    background-color: #032830 !important;
    border-color: #043c4a !important;
}
.alert-warning {
    color: #664d03 !important; /* Darker text for better contrast */
    background-color: #fff3cd !important;
    border-color: #ffecb5 !important;
}
.alert-danger {
    color: #f8d7da !important;
    background-color: #58151c !important;
    border-color: #7d1f28 !important;
}
.alert-success {
    color: #d1e7dd !important;
    background-color: #0a3622 !important;
    border-color: #0f5132 !important;
}

/* Avatar/Icon Styling (General) */
.chat-avatar-icon { /* Can be reused for general icons */
    font-size: 1.5rem; /* Slightly smaller default */
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #adb5bd; /* Lighter gray */
    vertical-align: middle;
}

.assistant-profile-pic { /* General profile picture style */
     border-radius: 50%; /* Make profile pics round */
     object-fit: cover;
     /* Define a default size or expect context-specific sizing */
     width: 40px;
     height: 40px;
     vertical-align: middle;
}

/* List Group Styling (Dark theme adaptation) */
.list-group-item {
    background-color: #444654;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ececf1;
}
.list-group-item-action:hover, .list-group-item-action:focus {
    color: #ffffff;
    background-color: #50515f;
}
.list-group-item.active {
    z-index: 2;
    color: #fff;
    background-color: #7d8bd2; /* Use link color for active item */
    border-color: #7d8bd2;
}

/* Ensure !important is used sparingly and only where necessary to override */