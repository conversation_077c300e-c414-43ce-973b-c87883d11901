import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from game.models import GameSession, Message

# Test creating a game session
session = GameSession.objects.create(
    session_id="test_session",
    current_role="applicant",
    performance_score=0,
    challenges_completed=0,
    role_challenges_completed=0,
    game_completed=False,
    current_manager="hr",
    current_task="cover_letter",
    completed_roles="[]",
    first_task_pending=True
)

# Test creating a message
message = Message.objects.create(
    game_session=session,
    message_id="test_message",
    sender="hr",
    text="Welcome to the game!",
    html="<p>Welcome to the game!</p>",
    is_challenge=False,
    is_markdown=True
)

# Print results
print(f"Created game session: {session}")
print(f"Created message: {message}")

# Test retrieving game session
retrieved_session = GameSession.objects.get(session_id="test_session")
print(f"Retrieved game session: {retrieved_session}")

# Test retrieving messages for a session
messages = retrieved_session.messages.all()
print(f"Retrieved {len(messages)} messages for session")
for msg in messages:
    print(f"- {msg.sender}: {msg.text[:30]}...")

# Clean up
session.delete()
print("Test completed successfully!")
