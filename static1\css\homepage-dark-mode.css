/**
 * Homepage Dark Mode CSS
 * Specific dark mode styling for the homepage with blue background
 */

/* Homepage hero section with blue background */
[data-theme="dark"] .hero-section,
[data-theme="dark"] [style*="background-color: #0066ff"],
[data-theme="dark"] [style*="background-color: rgb(0, 102, 255)"],
[data-theme="dark"] [style*="background: #0066ff"],
[data-theme="dark"] [style*="background: rgb(0, 102, 255)"],
[data-theme="dark"] [style*="background-color: blue"],
[data-theme="dark"] [style*="background: blue"],
[data-theme="dark"] [class*="bg-primary"],
[data-theme="dark"] [class*="bg-blue"] {
  background-color: #121212 !important;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(0, 102, 255, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(0, 60, 180, 0.2) 0%, transparent 50%),
    linear-gradient(to bottom, #121212, #0a0a0a) !important;
  background-attachment: fixed !important;
  color: #ffffff !important;
}

/* Homepage hero text */
[data-theme="dark"] .hero-section h1,
[data-theme="dark"] .hero-section h2,
[data-theme="dark"] .hero-section h3,
[data-theme="dark"] .hero-section h4,
[data-theme="dark"] .hero-section h5,
[data-theme="dark"] .hero-section h6 {
  color: #ffffff !important;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3) !important;
}

/* Homepage hero paragraph */
[data-theme="dark"] .hero-section p {
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Homepage buttons */
[data-theme="dark"] .hero-section .btn-primary {
  background: linear-gradient(to bottom, #0077ff, #0055cc) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 10px rgba(0, 102, 255, 0.3) !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .hero-section .btn-primary:hover {
  background: linear-gradient(to bottom, #0088ff, #0066dd) !important;
  box-shadow: 0 6px 15px rgba(0, 102, 255, 0.4) !important;
  transform: translateY(-2px) !important;
}

[data-theme="dark"] .hero-section .btn-outline-primary,
[data-theme="dark"] .hero-section .btn-outline-light {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: #ffffff !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .hero-section .btn-outline-primary:hover,
[data-theme="dark"] .hero-section .btn-outline-light:hover {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-2px) !important;
}

/* Homepage search box */
[data-theme="dark"] .hero-section .search-box,
[data-theme="dark"] .hero-section .form-control {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
}

[data-theme="dark"] .hero-section .search-box:focus,
[data-theme="dark"] .hero-section .form-control:focus {
  border-color: rgba(255, 255, 255, 0.5) !important;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.25), 0 4px 10px rgba(0, 0, 0, 0.2) !important;
}

/* Homepage search button */
[data-theme="dark"] .hero-section .btn-search {
  background: linear-gradient(to bottom, #0077ff, #0055cc) !important;
  border: none !important;
  color: white !important;
  box-shadow: 0 4px 10px rgba(0, 102, 255, 0.3) !important;
}

[data-theme="dark"] .hero-section .btn-search:hover {
  background: linear-gradient(to bottom, #0088ff, #0066dd) !important;
  box-shadow: 0 6px 15px rgba(0, 102, 255, 0.4) !important;
}

/* Homepage search directory container */
[data-theme="dark"] .search-directory-container {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 10px !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* Homepage tabs */
[data-theme="dark"] .hero-section .nav-tabs {
  border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .hero-section .nav-tabs .nav-link {
  color: rgba(255, 255, 255, 0.7) !important;
  border: none !important;
  padding: 0.75rem 1rem !important;
  border-radius: 0 !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .hero-section .nav-tabs .nav-link:hover {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .hero-section .nav-tabs .nav-link.active {
  color: #ffffff !important;
  background-color: transparent !important;
  border-bottom: 2px solid #ffffff !important;
  font-weight: 600 !important;
}

/* Homepage illustrations and images */
[data-theme="dark"] .hero-section img,
[data-theme="dark"] .hero-section svg {
  filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.3)) !important;
}

/* Homepage features section */
[data-theme="dark"] .features-section {
  background-color: #121212 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .features-section .feature-card {
  background: linear-gradient(145deg, #1e1e1e, #252525) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  border-radius: 10px !important;
  padding: 1.5rem !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .features-section .feature-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.6), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
}

/* Homepage call-to-action section */
[data-theme="dark"] .cta-section {
  background: linear-gradient(145deg, #1a1a1a, #121212) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4) !important;
  color: #ffffff !important;
}

/* Homepage footer */
[data-theme="dark"] footer {
  background-color: #1a1a1a !important;
  border-top: 1px solid #333333 !important;
  color: #cccccc !important;
}

/* Homepage specific elements */
[data-theme="dark"] .search-directory {
  background-color: rgba(0, 0, 0, 0.3) !important;
  border-radius: 10px !important;
  padding: 1.5rem !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
}

/* Homepage search input */
[data-theme="dark"] .search-directory input[type="text"] {
  background-color: rgba(0, 0, 0, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
  border-radius: 24px !important;
  padding: 0.75rem 1.25rem !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
}

[data-theme="dark"] .search-directory input[type="text"]:focus {
  border-color: rgba(255, 255, 255, 0.5) !important;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.25), inset 0 1px 3px rgba(0, 0, 0, 0.2) !important;
}

/* Homepage search button */
[data-theme="dark"] .search-directory .btn-search {
  background: linear-gradient(to bottom, #0077ff, #0055cc) !important;
  border: none !important;
  color: white !important;
  border-radius: 50% !important;
  width: 46px !important;
  height: 46px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4px 10px rgba(0, 102, 255, 0.3) !important;
}

/* Homepage tabs */
[data-theme="dark"] .search-directory .nav-tabs {
  border-bottom: none !important;
  margin-bottom: 1rem !important;
}

[data-theme="dark"] .search-directory .nav-tabs .nav-link {
  color: rgba(255, 255, 255, 0.7) !important;
  background-color: transparent !important;
  border: none !important;
  padding: 0.5rem 1rem !important;
  border-radius: 20px !important;
  transition: all 0.3s ease !important;
  margin-right: 0.5rem !important;
}

[data-theme="dark"] .search-directory .nav-tabs .nav-link:hover {
  color: #ffffff !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
}

[data-theme="dark"] .search-directory .nav-tabs .nav-link.active {
  color: #ffffff !important;
  background-color: rgba(0, 119, 255, 0.2) !important;
  font-weight: 600 !important;
}

/* Homepage robot illustration */
[data-theme="dark"] .robot-illustration {
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.4)) !important;
}
