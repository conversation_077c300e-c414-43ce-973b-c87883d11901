{% extends 'superadmin/base_superadmin.html' %}
{% load i18n %}

{% block breadcrumbs %}
<li class="breadcrumb-item active">{% trans "Users" %}</li>
{% endblock %}

{% block superadmin_content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{% trans "User Management" %}</h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <!-- Stats Cards -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-primary h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            {% trans "Total Users" %}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_users }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-people fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-success h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                            {% trans "Active Users" %}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_users }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-person-check fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-info h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                            {% trans "Staff Users" %}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ staff_users }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-person-badge fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card border-left-warning h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            {% trans "Superusers" %}
                                        </div>
                                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ superusers }}</div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="bi bi-person-fill-lock fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">{% trans "Filters" %}</h6>
                            </div>
                            <div class="card-body">
                                <form method="get" class="row g-3">
                                    <div class="col-md-3">
                                        <div class="input-group">
                                            <span class="input-group-text">
                                                <i class="bi bi-search"></i>
                                            </span>
                                            <input type="text" class="form-control" name="q" value="{{ filter_q }}" placeholder="{% trans 'Search users...' %}">
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" name="status">
                                            <option value="">{% trans "All Statuses" %}</option>
                                            <option value="active" {% if filter_status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
                                            <option value="inactive" {% if filter_status == 'inactive' %}selected{% endif %}>{% trans "Inactive" %}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <select class="form-select" name="role">
                                            <option value="">{% trans "All Roles" %}</option>
                                            <option value="superuser" {% if filter_role == 'superuser' %}selected{% endif %}>{% trans "Superusers" %}</option>
                                            <option value="staff" {% if filter_role == 'staff' %}selected{% endif %}>{% trans "Staff" %}</option>
                                            <option value="regular" {% if filter_role == 'regular' %}selected{% endif %}>{% trans "Regular Users" %}</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <select class="form-select" name="company">
                                            <option value="">{% trans "All Companies" %}</option>
                                            {% for company in companies %}
                                            <option value="{{ company.id }}" {% if filter_company == company.id|stringformat:"i" %}selected{% endif %}>{{ company.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary w-100">{% trans "Filter" %}</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>{% trans "Username" %}</th>
                                <th>{% trans "Email" %}</th>
                                <th>{% trans "Name" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Role" %}</th>
                                <th>{% trans "Companies" %}</th>
                                <th>{% trans "Date Joined" %}</th>
                                <th>{% trans "Game Sessions" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td>{{ user.email }}</td>
                                <td>{{ user.get_full_name|default:"-" }}</td>
                                <td>
                                    {% if user.is_active %}
                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                    {% else %}
                                    <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.is_superuser %}
                                    <span class="badge bg-warning">{% trans "Superuser" %}</span>
                                    {% elif user.is_staff %}
                                    <span class="badge bg-info">{% trans "Staff" %}</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{% trans "Regular" %}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.company_count > 0 %}
                                    <span class="badge bg-primary">{{ user.company_count }}</span>
                                    {% else %}
                                    <span class="badge bg-light text-dark">0</span>
                                    {% endif %}
                                </td>
                                <td>{{ user.date_joined|date:"M d, Y" }}</td>
                                <td>{{ user.session_count }}</td>
                                <td>
                                    <a href="{% url 'superadmin:user_detail' pk=user.pk %}" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="9" class="text-center">{% trans "No users found." %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Page navigation" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if filter_q %}&q={{ filter_q }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_role %}&role={{ filter_role }}{% endif %}{% if filter_company %}&company={{ filter_company }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if filter_q %}&q={{ filter_q }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_role %}&role={{ filter_role }}{% endif %}{% if filter_company %}&company={{ filter_company }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% endif %}

                        {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% if filter_q %}&q={{ filter_q }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_role %}&role={{ filter_role }}{% endif %}{% if filter_company %}&company={{ filter_company }}{% endif %}">{{ num }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if filter_q %}&q={{ filter_q }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_role %}&role={{ filter_role }}{% endif %}{% if filter_company %}&company={{ filter_company }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if filter_q %}&q={{ filter_q }}{% endif %}{% if filter_status %}&status={{ filter_status }}{% endif %}{% if filter_role %}&role={{ filter_role }}{% endif %}{% if filter_company %}&company={{ filter_company }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
