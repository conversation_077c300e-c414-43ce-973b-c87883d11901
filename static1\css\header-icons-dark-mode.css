/**
 * Header Icons Dark Mode CSS
 * Styling for header icons in dark mode
 */

/* Apply the specific styling for nav icons in dark mode */
[data-theme="dark"] .nav-icon-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #121212;
    border-radius: 0.25rem;
    width: 24px;
    height: 24px;
    margin-right: 6px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Ensure icons are visible in dark mode */
[data-theme="dark"] .nav-icon-container i,
[data-theme="dark"] .nav-icon-container .bi,
[data-theme="dark"] .nav-icon-container svg {
    color: #ffffff;
    fill: #ffffff;
}

/* Ensure the container is properly sized for different icon types */
[data-theme="dark"] .nav-icon-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Add hover effect */
[data-theme="dark"] .nav-icon-container:hover {
    background-color: #1e1e1e;
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Active state */
[data-theme="dark"] .nav-icon-container:active,
[data-theme="dark"] .nav-link.active .nav-icon-container {
    background-color: #252525;
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Light mode version for comparison */
[data-theme="light"] .nav-icon-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    width: 24px;
    height: 24px;
    margin-right: 6px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

/* Ensure icons are visible in light mode */
[data-theme="light"] .nav-icon-container i,
[data-theme="light"] .nav-icon-container .bi,
[data-theme="light"] .nav-icon-container svg {
    color: #212529;
    fill: #212529;
}

/* Add hover effect for light mode */
[data-theme="light"] .nav-icon-container:hover {
    background-color: #e9ecef;
    border-color: rgba(0, 0, 0, 0.12);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Active state for light mode */
[data-theme="light"] .nav-icon-container:active,
[data-theme="light"] .nav-link.active .nav-icon-container {
    background-color: #dee2e6;
    border-color: rgba(0, 0, 0, 0.15);
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}
