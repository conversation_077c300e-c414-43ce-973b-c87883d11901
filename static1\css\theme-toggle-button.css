/**
 * Theme Toggle Button CSS
 * <PERSON><PERSON> is now hidden as dark mode is the only theme
 */

/* Hide the theme toggle button completely */
.theme-toggle-btn {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Add shine effect */
.theme-toggle-btn::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transform: rotate(45deg);
  animation: shine 3s infinite linear;
  pointer-events: none;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(45deg);
  }
  20%, 100% {
    transform: translateX(100%) rotate(45deg);
  }
}

/* Hover effect - with scale and glow */
.theme-toggle-btn:hover {
  transform: scale(1.15);
  box-shadow: 0 10px 30px rgba(0, 102, 255, 0.5), 0 0 0 4px rgba(0, 102, 255, 0.3), 0 0 20px rgba(0, 102, 255, 0.4);
}

/* Active effect - with bounce */
.theme-toggle-btn:active {
  transform: scale(0.9);
  box-shadow: 0 5px 15px rgba(0, 102, 255, 0.3), 0 0 0 2px rgba(0, 102, 255, 0.3);
  transition: all 0.1s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Dark mode specific styling - with different gradient */
[data-theme="dark"] .theme-toggle-btn {
  background: linear-gradient(145deg, #ffcc00, #ff9900);
  box-shadow: 0 8px 25px rgba(255, 153, 0, 0.4), 0 0 0 3px rgba(255, 153, 0, 0.2);
}

[data-theme="dark"] .theme-toggle-btn:hover {
  box-shadow: 0 10px 30px rgba(255, 153, 0, 0.5), 0 0 0 4px rgba(255, 153, 0, 0.3), 0 0 20px rgba(255, 153, 0, 0.4);
}

[data-theme="dark"] .theme-toggle-btn:active {
  box-shadow: 0 5px 15px rgba(255, 153, 0, 0.3), 0 0 0 2px rgba(255, 153, 0, 0.3);
}

/* Icon styling */
.theme-toggle-btn i {
  font-size: 1.75rem;
  transition: all 0.3s ease;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .theme-toggle-btn i {
  color: #121212;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

/* Ensure the button is visible on all screen sizes */
@media (max-width: 768px) {
  .theme-toggle-btn {
    width: 50px;
    height: 50px;
    font-size: 1.25rem;
    bottom: 15px;
    right: 15px;
  }
}

/* Ensure the button doesn't overlap with other fixed elements */
@media (max-width: 576px) {
  .theme-toggle-btn {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
    bottom: 10px;
    right: 10px;
  }
}

/* Add a subtle pulse animation to draw attention to the button */
@keyframes pulse {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(255, 255, 255, 0.1);
  }
  50% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 0 0 0 5px rgba(255, 255, 255, 0.2);
  }
  100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 3px rgba(255, 255, 255, 0.1);
  }
}

/* Apply the pulse animation when the page loads */
.theme-toggle-btn.pulse {
  animation: pulse 2s ease-in-out 3;
}

/* Dark mode version of the pulse animation */
@keyframes pulse-dark {
  0% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 3px rgba(74, 125, 255, 0.3);
  }
  50% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4), 0 0 0 5px rgba(74, 125, 255, 0.5);
  }
  100% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 3px rgba(74, 125, 255, 0.3);
  }
}

/* Apply the dark mode pulse animation */
[data-theme="dark"] .theme-toggle-btn.pulse {
  animation: pulse-dark 2s ease-in-out 3;
}
