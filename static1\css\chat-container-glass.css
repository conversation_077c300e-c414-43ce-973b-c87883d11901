/**
 * <PERSON><PERSON> Container and Assistant Header Glass Effect CSS
 * Adds a light blue glass effect to chat containers and assistant headers
 */

/* Chat container with light blue glass background - LIGHT MODE */
[data-theme="light"] .chat-container,
[data-theme="light"] .general-chat-container,
[data-theme="light"] div.chat-container,
[data-theme="light"] div.general-chat-container {
    background: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(214, 240, 253, 0.85)) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.5) !important;
    box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.7) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* Chat box with subtle background - LIGHT MODE */
[data-theme="light"] .chat-box,
[data-theme="light"] .general-chat-box,
[data-theme="light"] #chat-box,
[data-theme="light"] div.chat-box,
[data-theme="light"] div.general-chat-box {
    background: rgba(250, 253, 255, 0.7) !important;
    border: none !important;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.03) !important;
    padding: 1.5rem !important;
}

/* High specificity selectors for chat container - LIGHT MODE */
html[data-theme="light"] body .chat-container,
html[data-theme="light"] body .general-chat-container,
html[data-theme="light"] body div.chat-container,
html[data-theme="light"] body div.general-chat-container {
    background: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(214, 240, 253, 0.85)) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.5) !important;
    box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.7) !important;
}

/* High specificity selectors for chat box - LIGHT MODE */
html[data-theme="light"] body .chat-box,
html[data-theme="light"] body .general-chat-box,
html[data-theme="light"] body #chat-box,
html[data-theme="light"] body div.chat-box,
html[data-theme="light"] body div.general-chat-box {
    background: rgba(250, 253, 255, 0.7) !important;
    border: none !important;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.03) !important;
}

/* User message bubbles matching dark mode - LIGHT MODE */
[data-theme="light"] .user-message .message-content,
[data-theme="light"] div.message.user-message.mb-3 span.message-content,
[data-theme="light"] div.message.user-message span.message-content,
[data-theme="light"] .message.user-message .message-content,
[data-theme="light"] span.message-content.user-message {
    background: linear-gradient(145deg, #0066ff, #0055cc) !important;
    background-color: #0066ff !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 5px 15px rgba(0, 102, 255, 0.3) !important;
    border-radius: 18px 18px 4px 18px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 500 !important;
    letter-spacing: 0.01em !important;
}

/* High specificity selectors for user message bubbles - LIGHT MODE */
html[data-theme="light"] body .user-message .message-content,
html[data-theme="light"] body div.message.user-message.mb-3 span.message-content,
html[data-theme="light"] body div.message.user-message span.message-content,
html[data-theme="light"] body .message.user-message .message-content,
html[data-theme="light"] body span.message-content.user-message {
    background: linear-gradient(145deg, #0066ff, #0055cc) !important;
    background-color: #0066ff !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    color: #ffffff !important;
    border: none !important;
    box-shadow: 0 5px 15px rgba(0, 102, 255, 0.3) !important;
    border-radius: 18px 18px 4px 18px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 500 !important;
    letter-spacing: 0.01em !important;
}

/* Assistant header with light blue glass background - LIGHT MODE */
[data-theme="light"] .assistant-header,
[data-theme="light"] .general-assistant-header,
[data-theme="light"] div.assistant-header,
[data-theme="light"] div.general-assistant-header {
    background: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(214, 240, 253, 0.85)) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
    box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.7) !important;
    overflow: hidden !important;
}

/* High specificity selectors for assistant header - LIGHT MODE */
html[data-theme="light"] body .assistant-header,
html[data-theme="light"] body .general-assistant-header,
html[data-theme="light"] body div.assistant-header,
html[data-theme="light"] body div.general-assistant-header {
    background: linear-gradient(135deg, rgba(240, 249, 255, 0.9), rgba(214, 240, 253, 0.85)) !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.5) !important;
    box-shadow: 0 8px 32px rgba(0, 102, 255, 0.1), 0 0 0 1px rgba(255, 255, 255, 0.7) !important;
}

/* Assistant header headings - LIGHT MODE */
[data-theme="light"] .assistant-header h4,
[data-theme="light"] .general-assistant-header h4 {
    color: #0052cc !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}

[data-theme="light"] .assistant-header h5,
[data-theme="light"] .general-assistant-header h5 {
    color: #333333 !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05) !important;
}

/* Assistant header with dark glass background - DARK MODE */
[data-theme="dark"] .assistant-header,
[data-theme="dark"] .general-assistant-header,
[data-theme="dark"] div.assistant-header,
[data-theme="dark"] div.general-assistant-header {
    background: linear-gradient(135deg, #1e1e1e 0%, #252525 100%) !important;
    background-color: #1e1e1e !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid #333333 !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* High specificity selectors for assistant header - DARK MODE */
html[data-theme="dark"] body .assistant-header,
html[data-theme="dark"] body .general-assistant-header,
html[data-theme="dark"] body div.assistant-header,
html[data-theme="dark"] body div.general-assistant-header {
    background: linear-gradient(135deg, #1e1e1e 0%, #252525 100%) !important;
    background-color: #1e1e1e !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid #333333 !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Assistant header headings - DARK MODE */
[data-theme="dark"] .assistant-header h4,
[data-theme="dark"] .general-assistant-header h4 {
    color: #0066ff !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .assistant-header h5,
[data-theme="dark"] .general-assistant-header h5 {
    color: #ffffff !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) !important;
}

/* Chat container with dark glass background - DARK MODE */
[data-theme="dark"] .chat-container,
[data-theme="dark"] .general-chat-container,
[data-theme="dark"] div.chat-container,
[data-theme="dark"] div.general-chat-container {
    background: linear-gradient(135deg, #1e1e1e 0%, #252525 100%) !important;
    background-color: #1e1e1e !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
}

/* High specificity selectors for chat container - DARK MODE */
html[data-theme="dark"] body .chat-container,
html[data-theme="dark"] body .general-chat-container,
html[data-theme="dark"] body div.chat-container,
html[data-theme="dark"] body div.general-chat-container {
    background: linear-gradient(135deg, #1e1e1e 0%, #252525 100%) !important;
    background-color: #1e1e1e !important;
    backdrop-filter: blur(10px) !important;
    -webkit-backdrop-filter: blur(10px) !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Chat box with dark background - DARK MODE */
[data-theme="dark"] .chat-box,
[data-theme="dark"] .general-chat-box,
[data-theme="dark"] #chat-box,
[data-theme="dark"] div.chat-box,
[data-theme="dark"] div.general-chat-box {
    background: #1a1a1a !important;
    background-color: #1a1a1a !important;
    border: none !important;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.15) !important;
    padding: 1.5rem !important;
}

/* High specificity selectors for chat box - DARK MODE */
html[data-theme="dark"] body .chat-box,
html[data-theme="dark"] body .general-chat-box,
html[data-theme="dark"] body #chat-box,
html[data-theme="dark"] body div.chat-box,
html[data-theme="dark"] body div.general-chat-box {
    background: #1a1a1a !important;
    background-color: #1a1a1a !important;
    border: none !important;
    box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.15) !important;
    padding: 1.5rem !important;
}

/* Assistant message bubbles matching dark mode - LIGHT MODE */
[data-theme="light"] .assistant-message .message-content,
[data-theme="light"] div.message.assistant-message.mb-3 span.message-content,
[data-theme="light"] div.message.assistant-message span.message-content,
[data-theme="light"] .message.assistant-message .message-content,
[data-theme="light"] span.message-content.assistant-message {
    background: linear-gradient(145deg, #2a2a2a, #222222) !important;
    background-color: #2a2a2a !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    background-position: initial !important;
    background-size: initial !important;
    background-repeat: initial !important;
    background-origin: initial !important;
    background-clip: initial !important;
    background-attachment: initial !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    border-radius: 18px 18px 18px 4px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 400 !important;
    letter-spacing: 0.01em !important;
}

/* High specificity selectors for assistant message bubbles - LIGHT MODE */
html[data-theme="light"] body .assistant-message .message-content,
html[data-theme="light"] body div.message.assistant-message.mb-3 span.message-content,
html[data-theme="light"] body div.message.assistant-message span.message-content,
html[data-theme="light"] body .message.assistant-message .message-content,
html[data-theme="light"] body span.message-content.assistant-message {
    background: linear-gradient(145deg, #2a2a2a, #222222) !important;
    background-color: #2a2a2a !important;
    background-image: none !important;
    background-blend-mode: normal !important;
    color: #ffffff !important;
    border: 1px solid #333333 !important;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
    border-radius: 18px 18px 18px 4px !important;
    padding: 1rem 1.25rem !important;
    font-weight: 400 !important;
    letter-spacing: 0.01em !important;
}
