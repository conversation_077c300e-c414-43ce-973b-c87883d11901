from django.contrib.sitemaps import Sitemap
from django.urls import reverse
from corporate.models import Company
from game.models import CompanyGameSettings


class StaticViewSitemap(Sitemap):
    """
    Sitemap for static views that don't have a model.
    """
    priority = 0.5
    changefreq = 'weekly'

    def items(self):
        return ['corporate:home', 'corporate:corporate_login', 'corporate:corporate_register']

    def location(self, item):
        return reverse(item)


class CompanySitemap(Sitemap):
    """
    Sitemap for company pages.
    """
    changefreq = 'weekly'
    priority = 0.7

    def items(self):
        # Only include active and public companies
        return Company.objects.filter(is_active=True)

    def location(self, obj):
        return reverse('corporate:company_detail', args=[obj.slug])

    def lastmod(self, obj):
        return obj.updated_at


class GameSitemap(Sitemap):
    """
    Sitemap for game pages.
    """
    changefreq = 'weekly'
    priority = 0.6

    def items(self):
        # Only include public games
        return CompanyGameSettings.objects.filter(is_public=True)

    def location(self, obj):
        return reverse('game:index')  # This will need to be updated if you have company-specific game URLs


# Combine all sitemaps
sitemaps = {
    'static': StaticViewSitemap,
    'companies': CompanySitemap,
    'games': GameSitemap,
}
