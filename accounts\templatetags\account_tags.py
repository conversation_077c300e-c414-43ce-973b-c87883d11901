from django import template
from django.utils.safestring import mark_safe
from django.utils.html import escape
from django.utils.timezone import template_localtime
from django.contrib.auth.models import User
from django.template.defaultfilters import timesince
import hashlib

register = template.Library()

@register.filter
def gravatar_url(email, size=40):
    """Generate Gravatar URL for an email address."""
    if not email:
        return ''
    email_hash = hashlib.md5(email.lower().strip().encode('utf-8')).hexdigest()
    return f"https://www.gravatar.com/avatar/{email_hash}?s={size}&d=mp"

@register.filter
def initials(name):
    """Get initials from a name."""
    if not name:
        return ''
    words = name.split()
    if len(words) >= 2:
        return (words[0][0] + words[-1][0]).upper()
    return words[0][0].upper() if words else ''

@register.filter
def company_role(user, company):
    """Get user's role in a company."""
    if not user or not company:
        return ''
    if user == company.owner:
        return 'owner'

    # Check group membership (priority: Admin > Member > Guest)
    # Assumes group names are 'Company Administrators', 'Company Members', 'Company Guests'
    if user.groups.filter(name='Company Administrators').exists():
        return 'admin'
    elif user.groups.filter(name='Company Members').exists():
        return 'member'
    elif user.groups.filter(name='Company Guests').exists():
        return 'guest'

    # Fallback: If user has a membership record but no specific group, maybe default to 'guest'?
    # Or return empty string if they shouldn't have access without a group?
    # Let's check if a membership exists at all for non-owners
    if user.memberships.filter(company=company).exists():
         return 'guest' # Default to guest if membership exists but no specific role group found

    return '' # Return empty if not owner and no membership/group found

@register.filter
def can_manage_company(user, company):
    """Check if user can manage company settings."""
    if not user or not company:
        return False
    return user == company.owner or user.companymembership_set.filter(
        company=company,
        role='admin'
    ).exists()

@register.simple_tag(takes_context=True)
def active_company(context):
    """Get the active company for the current user."""
    request = context.get('request')
    if not request or not request.user.is_authenticated:
        return None

    # Try to get from session
    company_id = request.session.get('active_company_id')
    if company_id:
        try:
            return request.user.companies.get(id=company_id)
        except:
            pass

    # Try to get from owned companies
    company = request.user.owned_companies.first()
    if company:
        request.session['active_company_id'] = str(company.id)
        return company

    # Try to get from member companies
    company = request.user.companies.first()
    if company:
        request.session['active_company_id'] = str(company.id)
        return company

    return None

@register.inclusion_tag('accounts/tags/company_member_card.html')
def company_member_card(member, company):
    """Render a company member card."""
    return {
        'member': member,
        'company': company,
        'role': company_role(member, company),
        'gravatar_url': gravatar_url(member.email) if member.email else None,
        'initials': initials(member.get_full_name() or member.username),
    }

@register.inclusion_tag('accounts/tags/activity_item.html')
def activity_item(activity):
    """Render an activity log item."""
    icon_map = {
        'created': 'plus-circle',
        'updated': 'pencil',
        'deleted': 'trash',
        'accessed': 'eye',
        'invited': 'envelope',
        'joined': 'person-plus',
        'left': 'person-dash',
    }
    
    color_map = {
        'created': 'success',
        'updated': 'info',
        'deleted': 'danger',
        'accessed': 'secondary',
        'invited': 'primary',
        'joined': 'success',
        'left': 'warning',
    }
    
    return {
        'activity': activity,
        'icon': icon_map.get(activity.activity_type, 'circle'), # Use activity_type
        'color': color_map.get(activity.activity_type, 'secondary'), # Use activity_type
        'timestamp': template_localtime(activity.created_at),
        'timesince': timesince(activity.created_at),
    }

@register.filter
def format_phone(phone_number):
    """Format a phone number consistently."""
    from ..utils import format_phone_number
    return format_phone_number(phone_number)

@register.filter
def company_size_display(employee_count):
    """Convert employee count to display format."""
    from ..utils import get_company_size_display
    return get_company_size_display(employee_count)

@register.simple_tag
def company_metrics(company):
    """Get company metrics."""
    from ..utils import calculate_company_metrics
    return calculate_company_metrics(company)

@register.filter
def mask_email(email):
    """Mask part of an email address for privacy."""
    if not email or '@' not in email:
        return email
    username, domain = email.split('@')
    if len(username) <= 3:
        masked_username = username[0] + '*' * (len(username) - 1)
    else:
        masked_username = username[0] + '*' * (len(username) - 2) + username[-1]
    return f"{masked_username}@{domain}"

@register.filter
def role_badge(role):
    """Generate a color-coded badge for a role."""
    colors = {
        'owner': 'danger',
        'admin': 'primary',
        'member': 'success',
        'guest': 'secondary',
    }
    color = colors.get(role, 'secondary')
    return mark_safe(f'<span class="badge bg-{color}">{escape(role.title())}</span>')

# Moved from directory_tags.py as a workaround for loading issues
@register.filter(name='filter_by_type')
def filter_by_type(queryset, item_type):
    """
    Filters a queryset of SavedItem objects by item_type.
    """
    if not item_type:
        return queryset
    # Ensure queryset is evaluated if it's lazy
    items = list(queryset) 
    return [item for item in items if item.item_type == item_type]
