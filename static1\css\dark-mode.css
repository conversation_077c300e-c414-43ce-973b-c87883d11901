/* Dark Mode Styles */
[data-theme="dark"] {
    --bs-body-color: #f8f9fa;
    --bs-body-bg: #212529;
    --bs-primary: #0d6efd;
    --bs-secondary: #6c757d;
    color-scheme: dark;
}

/* Ensure all icons have transparent backgrounds in dark mode */
[data-theme="dark"] i.bi,
[data-theme="dark"] i.fa,
[data-theme="dark"] i.fas,
[data-theme="dark"] i.far,
[data-theme="dark"] i.fab,
[data-theme="dark"] button i,
[data-theme="dark"] .btn i {
    background-color: transparent !important;
    background: none !important;
    box-shadow: none !important;
}

/* Ensure header button icons are properly styled in dark mode */
[data-theme="dark"] .general-assistant-header button i,
[data-theme="dark"] .general-assistant-header .btn i,
[data-theme="dark"] .general-assistant-header .btn-outline-secondary i,
[data-theme="dark"] .general-assistant-header .btn-outline-primary i,
[data-theme="dark"] .general-assistant-header .btn-sm i,
[data-theme="dark"] .general-assistant-header .like-button i,
[data-theme="dark"] .general-assistant-header .rate-assistant-btn i,
[data-theme="dark"] .general-assistant-header .bi {
    color: #ffffff !important;
    text-shadow: 0 0 5px rgba(0, 102, 255, 0.3) !important;
    background-color: transparent !important;
    background: none !important;
    box-shadow: none !important;
}

/* Special styling for heart icon in like button */
[data-theme="dark"] .general-assistant-header .like-button .bi-heart-fill {
    color: #ff3366 !important;
    background-color: transparent !important;
    background: none !important;
}

/* Ensure nav icons have transparent backgrounds in dark mode */
[data-theme="dark"] .nav-icon-container i,
[data-theme="dark"] .navbar-nav i,
[data-theme="dark"] .navbar i {
    background-color: transparent !important;
    background: none !important;
}

/* Ensure sidebar icons have transparent backgrounds */
[data-theme="dark"] #chat-sidebar i,
[data-theme="dark"] .sidebar i {
    background-color: transparent !important;
    background: none !important;
}

/* Ensure rating stars have transparent backgrounds */
[data-theme="dark"] .star-rating i,
[data-theme="dark"] .interactive-star-rating i,
[data-theme="dark"] .rating-stars i {
    background-color: transparent !important;
    background: none !important;
}

/* Ensure social media icons have transparent backgrounds */
[data-theme="dark"] .social-icons i,
[data-theme="dark"] .social-icons-mobile i {
    background-color: transparent !important;
    background: none !important;
}

/* Ensure feature icons have transparent backgrounds */
[data-theme="dark"] .feature-icon i {
    background-color: transparent !important;
    background: none !important;
}

/* Ensure send button icon has transparent background */
[data-theme="dark"] #send-button i,
[data-theme="dark"] .general-send-btn i {
    background-color: transparent !important;
    background: none !important;
}
