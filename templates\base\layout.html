{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ SEO_TITLE }}{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'img/favicons/favicon.ico' %}">
    <link rel="icon" type="image/svg+xml" href="{% static 'img/favicons/favicon.svg' %}">
    <link rel="apple-touch-icon" href="{% static 'img/favicons/favicon.ico' %}">

    {% if GOOGLE_ANALYTICS_ID %}
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ GOOGLE_ANALYTICS_ID }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ GOOGLE_ANALYTICS_ID }}');
    </script>
    {% endif %}

    <!-- SEO Meta tags -->
    <meta name="description" content="{% block meta_description %}{{ SEO_DESCRIPTION }}{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}{{ SEO_KEYWORDS }}{% endblock %}">
    <meta name="author" content="{{ SEO_TITLE }}">
    <meta name="robots" content="{% block meta_robots %}index, follow{% endblock %}">
    <link rel="canonical" href="{{ request.build_absolute_uri }}">
    {% if GOOGLE_VERIFICATION_TAG %}
    <meta name="google-site-verification" content="{{ GOOGLE_VERIFICATION_TAG }}">
    {% endif %}

    <!-- Open Graph tags for social sharing -->
    <meta property="og:title" content="{% block og_title %}{{ self.title }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ self.meta_description }}{% endblock %}">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Corporate Prompt Master">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Base Layout CSS -->
    <style>
        :root {
            --sidebar-width: 250px;
            --header-height: 56px;
            --primary-color: #4e73df;
            --secondary-color: #858796;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --light-color: #f8f9fc;
            --dark-color: #5a5c69;
        }

        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-header {
            height: var(--header-height);
            z-index: 1030;
        }

        .main-container {
            display: flex;
            flex: 1;
        }

        /* Removed inline sidebar styles - now handled by sidebar-hover.css */

        .main-content {
            flex: 1;
            /* margin-left is now handled by body padding in sidebar-hover.css */
            padding: 1.5rem;
            min-height: calc(100vh - var(--header-height)); /* Keep vertical spacing */
        }

        /* For pages that don't need a sidebar */
        .no-sidebar .main-content {
            margin-left: 0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>

    <!-- Custom CSS -->
    {% block extra_css %}
    <link rel="stylesheet" href="{% static 'game/css/sidebar-hover.css' %}">
    {% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- Header -->
    {% include 'base/header.html' %}

    <div class="main-container">
        <!-- Sidebar (only shown if not explicitly hidden) -->
        {% if not hide_sidebar %}
        {% include 'base/sidebar.html' %}
        {% endif %}

        <!-- Main Content -->
        <div class="main-content">
            {% block content %}
            <!-- Main content goes here -->
            {% endblock %}
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>

    <!-- Base Layout JS -->
    <script>
        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleButton = document.querySelector('.navbar-toggler');
            const sidebar = document.querySelector('.sidebar');

            if (toggleButton && sidebar) {
                toggleButton.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }
        });
    </script>

    <!-- Custom JS -->
    {% block extra_js %}{% endblock %}

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Corporate Prompt Master",
        "url": "{{ request.scheme }}://{{ request.get_host }}/",
        "description": "AI-powered game for corporate training and prompt engineering skills",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    {% block structured_data %}{% endblock %}
</body>
</html>
