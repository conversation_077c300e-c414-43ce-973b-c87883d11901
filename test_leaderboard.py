import os
import sys
import django
import random
import uuid
from datetime import datetime

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from game.models import GameSession, Message
from corporate.models import Company, Leaderboard, LeaderboardEntry, CorporateUser
from game.characters import CHARACTERS
from game.role_progression import ROLE_PROGRESSION
from game.all_role_tasks import get_all_role_tasks

# Create a test company if it doesn't exist
def create_test_company():
    # Create a superuser if it doesn't exist
    try:
        admin = User.objects.get(username='admin')
    except User.DoesNotExist:
        admin = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpassword'
        )
        print(f"Created admin user: {admin.username}")

    # Create a test company if it doesn't exist
    company_name = "Test Company"
    try:
        company = Company.objects.get(name=company_name)
        print(f"Using existing company: {company.name}")
    except Company.DoesNotExist:
        company = Company.objects.create(
            name=company_name,
            description="A test company for leaderboard testing",
            owner=admin,
            allow_leaderboard=True
        )
        print(f"Created new company: {company.name}")

    # Create a company leaderboard if it doesn't exist
    try:
        leaderboard = Leaderboard.objects.get(company=company, time_period='all_time', is_global=False)
        print(f"Using existing leaderboard for {company.name}")
    except Leaderboard.DoesNotExist:
        leaderboard = Leaderboard.objects.create(
            company=company,
            name=f"{company.name} All-Time Leaderboard",
            time_period='all_time',
            is_global=False
        )
        print(f"Created new leaderboard for {company.name}")

    return company, leaderboard

# Create test users
def create_test_users(num_users=3):
    users = []
    for i in range(1, num_users + 1):
        username = f"testuser{i}"
        try:
            user = User.objects.get(username=username)
            print(f"Using existing user: {user.username}")
        except User.DoesNotExist:
            user = User.objects.create_user(
                username=username,
                email=f"{username}@example.com",
                password=f"{username}password"
            )
            print(f"Created new user: {user.username}")
        users.append(user)
    return users

# Simulate game progress for a user
def simulate_game_progress(user, company, roles_to_complete=None):
    # If no roles specified, use a random number between 1 and 5
    if roles_to_complete is None:
        roles_to_complete = random.randint(1, 5)

    # Get or create a game session for this user
    try:
        game_session = GameSession.objects.get(user=user)
        print(f"Using existing game session for {user.username}")
    except GameSession.DoesNotExist:
        game_session = GameSession.objects.create(
            user=user,
            company=company,
            current_role="applicant",
            performance_score=0,
            challenges_completed=0,
            role_challenges_completed=0,
            game_completed=False,
            current_manager="hr",
            current_task="cover_letter",
            completed_roles="[]",
            first_task_pending=True
        )
        print(f"Created new game session for {user.username}")

    # Get the role progression path
    role_path = list(ROLE_PROGRESSION.keys())

    # Start from the current role
    current_role_index = role_path.index(game_session.current_role)
    completed_roles = game_session.get_completed_roles()

    # Simulate completing roles
    for i in range(roles_to_complete):
        if current_role_index >= len(role_path) - 1:
            print(f"User {user.username} has already reached the highest role")
            break

        # Complete the current role
        current_role = role_path[current_role_index]

        # Get tasks for this role
        role_tasks = get_all_role_tasks(current_role)

        # Simulate completing all tasks for this role
        for task in role_tasks:
            # Create a message for this task
            Message.objects.create(
                game_session=game_session,
                message_id=str(uuid.uuid4()),
                sender=task["manager"],
                text=task["description"],
                html=f"<p>{task['description']}</p>",
                is_challenge=True,
                is_markdown=True,
                task_id=task["id"]
            )

            # Create a user response
            Message.objects.create(
                game_session=game_session,
                message_id=str(uuid.uuid4()),
                sender="user",
                text=f"User response to {task['id']}",
                html=f"<p>User response to {task['id']}</p>",
                is_challenge=False,
                is_markdown=True
            )

            # Create feedback message
            Message.objects.create(
                game_session=game_session,
                message_id=str(uuid.uuid4()),
                sender=task["manager"],
                text=f"Good job on {task['id']}!",
                html=f"<p>Good job on {task['id']}!</p>",
                is_challenge=False,
                is_markdown=True
            )

            # Update game session
            game_session.challenges_completed += 1
            game_session.role_challenges_completed += 1
            game_session.performance_score += 10

        # Mark this role as completed
        completed_roles.append(current_role)
        game_session.set_completed_roles(completed_roles)

        # Move to the next role
        current_role_index += 1
        if current_role_index < len(role_path):
            game_session.current_role = role_path[current_role_index]
            game_session.role_challenges_completed = 0
        else:
            game_session.game_completed = True

    # Save the game session
    game_session.save()

    # Update the leaderboard directly
    # Find the company leaderboard
    company_leaderboard = Leaderboard.objects.filter(
        company=company,
        time_period='all_time',
        is_global=False
    ).first()

    if company_leaderboard:
        # Create or update the leaderboard entry
        LeaderboardEntry.objects.update_or_create(
            leaderboard=company_leaderboard,
            user=user,
            defaults={
                'score': game_session.performance_score,
                'highest_role': game_session.current_role
            }
        )
        print(f"Updated leaderboard entry for {user.username}")

    print(f"Simulated progress for {user.username}: {game_session.current_role}, Score: {game_session.performance_score}")
    return game_session

# Display the leaderboard
def display_leaderboard(leaderboard):
    print(f"\n=== {leaderboard.name} ===")
    entries = LeaderboardEntry.objects.filter(leaderboard=leaderboard).order_by('-score')

    if entries.count() == 0:
        print("No entries in the leaderboard yet.")
        return

    print(f"{'Rank':<5} {'Username':<15} {'Score':<10} {'Highest Role':<20}")
    print("-" * 50)

    for i, entry in enumerate(entries, 1):
        print(f"{i:<5} {entry.user.username:<15} {entry.score:<10} {entry.highest_role.replace('_', ' ').title():<20}")

# Function to test the company filter
def test_company_filter():
    print("\nTesting company filter functionality...")

    # Get all companies
    companies = Company.objects.all()
    print(f"Found {companies.count()} companies")

    # Create a second test company if needed
    if companies.count() < 2:
        print("Creating a second test company...")

        # Get admin user
        try:
            admin = User.objects.get(username='admin')
        except User.DoesNotExist:
            admin = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='adminpassword'
            )

        # Create second company
        company2 = Company.objects.create(
            name="Test Company 2",
            description="A second test company for leaderboard testing",
            owner=admin,
            allow_leaderboard=True
        )

        # Create leaderboard for second company
        leaderboard2 = Leaderboard.objects.create(
            company=company2,
            name=f"{company2.name} All-Time Leaderboard",
            time_period='all_time',
            is_global=False
        )

        # Create a user for the second company
        user2 = User.objects.create_user(
            username="testuser_company2",
            email="<EMAIL>",
            password="password"
        )

        # Create corporate profile for the user
        CorporateUser.objects.create(
            user=user2,
            company=company2
        )

        # Refresh companies list
        companies = Company.objects.all()
        print(f"Now have {companies.count()} companies")

    # Get the first two companies
    company1 = companies[0]
    company2 = companies[1]

    print(f"\nCompany 1: {company1.name}")
    print(f"Company 2: {company2.name}")

    # Get users for each company
    company1_users = CorporateUser.objects.filter(company=company1).values_list('user_id', flat=True)
    company2_users = CorporateUser.objects.filter(company=company2).values_list('user_id', flat=True)

    print(f"Company 1 Users: {len(company1_users)}")
    print(f"Company 2 Users: {len(company2_users)}")

    # Get or create leaderboards for each company
    company1_leaderboard = Leaderboard.objects.filter(company=company1, is_global=False).first()
    if not company1_leaderboard:
        print(f"Creating leaderboard for {company1.name}")
        company1_leaderboard = Leaderboard.objects.create(
            company=company1,
            name=f"{company1.name} All-Time Leaderboard",
            time_period='all_time',
            is_global=False
        )

    company2_leaderboard = Leaderboard.objects.filter(company=company2, is_global=False).first()
    if not company2_leaderboard:
        print(f"Creating leaderboard for {company2.name}")
        company2_leaderboard = Leaderboard.objects.create(
            company=company2,
            name=f"{company2.name} All-Time Leaderboard",
            time_period='all_time',
            is_global=False
        )

    print(f"Company 1 Leaderboard: {company1_leaderboard.name}")
    print(f"Company 2 Leaderboard: {company2_leaderboard.name}")

    # Get entries for each leaderboard
    company1_entries = LeaderboardEntry.objects.filter(leaderboard=company1_leaderboard)
    company2_entries = LeaderboardEntry.objects.filter(leaderboard=company2_leaderboard)

    print(f"Company 1 Entries: {company1_entries.count()}")
    print(f"Company 2 Entries: {company2_entries.count()}")

    # Test filtering company1 entries by company1 users
    filtered_entries = company1_entries.filter(user_id__in=company1_users)
    print(f"Company 1 Entries filtered by Company 1 Users: {filtered_entries.count()}")

    # Test filtering company1 entries by company2 users
    filtered_entries = company1_entries.filter(user_id__in=company2_users)
    print(f"Company 1 Entries filtered by Company 2 Users: {filtered_entries.count()}")

# Main function
def main():
    print("=== Testing Game Persistence and Leaderboard ===")

    # Create test company and leaderboard
    company, leaderboard = create_test_company()

    # Create test users
    users = create_test_users(3)

    # Simulate different progress for each user
    simulate_game_progress(users[0], company, 1)  # Complete 1 role
    simulate_game_progress(users[1], company, 3)  # Complete 3 roles
    simulate_game_progress(users[2], company, 5)  # Complete 5 roles

    # Display the leaderboard
    display_leaderboard(leaderboard)

    # Test company filter
    test_company_filter()

if __name__ == "__main__":
    main()
