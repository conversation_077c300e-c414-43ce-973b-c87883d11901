/**
 * <PERSON><PERSON> Click Fix
 * General fix for buttons that aren't working properly
 */

(function() {
    // Run immediately and also after DOM is loaded to ensure it works in all cases
    fixButtons();
    
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Button Click Fix: Initializing on DOMContentLoaded');
        fixButtons();
        
        // Also run after a short delay to ensure all other scripts have run
        setTimeout(fixButtons, 500);
        setTimeout(fixButtons, 1000);
        
        // Set up a MutationObserver to watch for changes to the DOM
        const observer = new MutationObserver(function(mutations) {
            fixButtons();
        });
        
        // Start observing the document with the configured parameters
        observer.observe(document.body, { 
            childList: true, 
            subtree: true 
        });
    });
    
    // Function to fix buttons
    function fixButtons() {
        // Fix all buttons
        const buttons = document.querySelectorAll('button, a.btn, input[type="button"], input[type="submit"]');
        
        buttons.forEach(function(button) {
            // Skip if already fixed
            if (button.dataset.buttonFixed === 'true') {
                return;
            }
            
            // Ensure button has proper styling
            button.style.cursor = 'pointer';
            button.style.pointerEvents = 'auto';
            
            // Mark as fixed
            button.dataset.buttonFixed = 'true';
            
            // Add a direct click handler for submit buttons
            if (button.type === 'submit' || button.getAttribute('type') === 'submit') {
                button.addEventListener('click', function(e) {
                    const form = button.closest('form');
                    if (form) {
                        // Let the default form submission happen
                        return true;
                    }
                });
            }
        });
    }
})();
