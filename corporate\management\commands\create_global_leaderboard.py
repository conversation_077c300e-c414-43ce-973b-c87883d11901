from django.core.management.base import BaseCommand
from corporate.models import Leaderboard


class Command(BaseCommand):
    help = 'Create global leaderboards for different time periods'

    def add_arguments(self, parser):
        parser.add_argument('--name', type=str, default='Global Leaderboard', help='Leaderboard name')

    def handle(self, *args, **options):
        name = options['name']
        time_periods = ['all_time', 'monthly', 'weekly', 'daily']
        
        for period in time_periods:
            # Check if global leaderboard for this period already exists
            if Leaderboard.objects.filter(is_global=True, time_period=period).exists():
                self.stdout.write(self.style.WARNING(f'Global {period} leaderboard already exists'))
                continue
                
            # Create global leaderboard
            leaderboard = Leaderboard.objects.create(
                name=f"{name} ({period.replace('_', ' ').title()})",
                is_global=True,
                time_period=period,
                active=True
            )
            
            self.stdout.write(self.style.SUCCESS(f'Successfully created global {period} leaderboard'))
