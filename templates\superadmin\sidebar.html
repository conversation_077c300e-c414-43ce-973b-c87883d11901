{% load i18n %}
<!-- Sidebar component for navigation -->
<div class="sidebar">
    <div class="sidebar-header">
        <a href="{% url 'superadmin:dashboard' %}" class="sidebar-brand-link">
            <div class="sidebar-brand">
                <i class="bi bi-shield-lock"></i>
                <span>Superadmin</span>
            </div>
        </a>
    </div>

    <div class="sidebar-menu">
        <ul class="nav flex-column">
            <li class="nav-header">{% trans "Core" %}</li>
            <!-- Dashboard -->
            <li class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}"
                   href="{% url 'superadmin:dashboard' %}">
                    <i class="bi bi-speedometer2"></i>
                    <span>{% trans "Dashboard" %}</span>
                </a>
            </li>

            <li class="nav-divider"></li>
            <li class="nav-header">{% trans "Management" %}</li>

            <!-- Companies -->
            <li class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'company_list' %}active{% endif %}"
                   href="{% url 'superadmin:company_list' %}">
                    <i class="bi bi-building"></i>
                    <span>{% trans "Companies" %}</span>
                </a>
            </li>

            <!-- Users -->
            <li class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'user_list' %}active{% endif %}"
                   href="{% url 'superadmin:user_list' %}">
                    <i class="bi bi-people"></i>
                    <span>{% trans "Users" %}</span>
                </a>
            </li>

            <!-- Game Sessions -->
            <li class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'game_session_list' %}active{% endif %}"
                   href="{% url 'superadmin:game_session_list' %}">
                    <i class="bi bi-controller"></i>
                    <span>{% trans "Game Sessions" %}</span>
                </a>
            </li>

            <!-- Leaderboards -->
            <li class="nav-item">
                <a class="nav-link {% if request.resolver_match.url_name == 'leaderboard_list' %}active{% endif %}"
                   href="{% url 'superadmin:leaderboard_list' %}">
                    <i class="bi bi-trophy"></i>
                    <span>{% trans "Leaderboards" %}</span>
                </a>
            </li>

            <li class="nav-divider"></li>
            <li class="nav-header">{% trans "System" %}</li>

            <!-- Django Admin -->
            <li class="nav-item">
                <a class="nav-link" href="/admin/"> {# Standard Django admin URL #}
                    <i class="bi bi-gear"></i>
                    <span>{% trans "Django Admin" %}</span>
                </a>
            </li>

            <!-- Back to Site -->
            <li class="nav-item">
                <a class="nav-link" href="{% url 'corporate:home' %}">
                    <i class="bi bi-house"></i>
                    <span>{% trans "Back to Site" %}</span>
                </a>
            </li>
        </ul>
    </div>
</div>
