/* Responsive Preview Area Styles */

/* Main preview container */
.preview-container {
    display: flex;
    flex-direction: column;
    height: auto; /* Auto height to fit content */
    min-height: auto; /* No minimum height */
    max-height: none; /* No maximum height */
    overflow: visible; /* No scrollbar, let page handle scrolling */
    flex: 0 0 auto; /* Don't grow, don't shrink, use auto height */
}

/* When preview container is visible (not hidden) */
.preview-container:not(.hidden) {
    flex: 0 0 auto; /* Don't grow, don't shrink, use auto height */
    display: flex;
    flex-direction: column;
    height: auto; /* Auto height to fit content */
    margin-bottom: 20px; /* Add some space at the bottom */
}

/* Preview scroll container - wraps all content */
.preview-scroll-container {
    flex: 1;
    overflow: visible; /* No scrollbar, let page handle scrolling */
    max-height: none; /* Remove height restriction */
    height: auto; /* Allow content to determine height */
    padding-right: 0;
    display: flex;
    flex-direction: column;
}

/* Preview content area */
.preview-content {
    min-height: 150px;
    background-color: #e6f2ff; /* Changed to match styles.css */
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    white-space: pre-wrap;
    height: auto; /* Allow content to determine height */
    overflow: visible; /* No scrollbar */
    border: 1px solid #b8d4ff; /* Changed to match styles.css */
}

/* Similarity container - the area with feedback */
.similarity-container {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #f5f5f5;
    overflow-y: visible; /* Let the parent container handle scrolling */
}

/* Manager feedback container */
.manager-feedback-container {
    margin-top: 10px;
    margin-bottom: 10px;
    overflow-y: visible; /* Let the parent container handle scrolling */
}

/* Manager feedback content */
.manager-feedback-content {
    overflow-y: visible; /* Let the parent container handle scrolling */
    padding: 10px;
    background-color: #fffdf7;
    border-radius: 4px;
    border-left: 3px solid #ffca28;
}

/* Preview actions - buttons area */
.preview-actions {
    flex: 0 0 auto;
    margin-top: 10px;
    background-color: #fff;
    padding: 10px 0;
    border-top: 1px solid #e0e0e0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .preview-container:not(.hidden) {
        height: auto; /* Auto height to fit content */
    }

    .preview-scroll-container {
        max-height: none; /* Remove max-height restriction */
        height: auto; /* Auto height to fit content */
        overflow: visible; /* No scrollbar */
    }

    .messages-container.preview-visible {
        height: 30vh !important; /* Smaller height on mobile when preview is visible */
        max-height: 30vh !important;
        overflow-y: scroll !important; /* Always show scrollbar */
        flex: 0 0 auto; /* Don't grow when preview is visible */
    }

    .preview-content {
        min-height: 100px;
        padding: 10px;
    }

    .similarity-container {
        padding: 10px;
    }

    .manager-feedback-container {
        margin: 5px 0;
    }

    .preview-actions {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 5px;
    }

    .preview-actions button {
        flex: 1;
        min-width: 120px;
        margin: 5px 0;
    }
}

/* Extra small screens */
@media (max-width: 480px) {
    .preview-scroll-container {
        max-height: none; /* Remove max-height restriction */
        height: auto; /* Auto height to fit content */
        overflow: visible; /* No scrollbar */
    }

    .messages-container.preview-visible {
        height: 25vh !important; /* Even smaller height on very small screens */
        max-height: 25vh !important;
        overflow-y: scroll !important; /* Always show scrollbar */
        flex: 0 0 auto; /* Don't grow when preview is visible */
    }

    .preview-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .preview-header-actions {
        width: 100%;
        display: flex;
        justify-content: space-between;
        margin-top: 5px;
    }

    .preview-actions button {
        font-size: 0.9rem;
        padding: 8px;
    }
}

/* Ensure the main content area has proper layout */
.main-content {
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* At least full viewport height */
    height: auto; /* Allow content to determine height */
    overflow-y: auto; /* Allow scrolling on main content */
}

/* When preview is active, adjust layout */
.main-content.preview-active {
    display: flex;
    flex-direction: column;
    min-height: 100vh; /* At least full viewport height */
    height: auto; /* Allow content to determine height */
    overflow-y: auto; /* Allow scrolling on main content */
}

.messages-container {
    flex: 0 0 auto; /* Don't grow, don't shrink, use auto height */
    overflow-y: scroll !important; /* Always show scrollbar */
    height: 40vh; /* Fixed height */
    /* Height controlled by CSS classes instead of inline styles */
}

/* When preview is visible, limit the messages container height */
.messages-container.preview-visible {
    height: 40vh !important; /* Fixed height when preview is visible */
    max-height: 40vh !important; /* Limit height when preview is visible */
    overflow-y: scroll !important; /* Always show scrollbar */
    flex: 0 0 auto; /* Don't grow when preview is visible */
}

/* When preview is not visible, messages can take more space */
.messages-container:not(.preview-visible) {
    min-height: 70vh; /* Minimum height when preview is not visible */
    overflow-y: scroll !important; /* Always show scrollbar */
}

.input-area {
    flex: 0 0 auto;
    max-height: none; /* Remove max height */
    overflow: visible; /* No scrollbar */
    border-top: 1px solid #e0e0e0;
    background-color: #fff;
    padding: 15px;
    position: relative; /* For proper stacking */
    z-index: 5; /* Ensure it's above other elements */
}

/* Ensure the input area doesn't take up too much space on small screens */
@media (max-height: 700px) {
    .input-area {
        max-height: none; /* No max height */
    }

    .preview-scroll-container {
        max-height: none; /* Remove max-height restriction */
        height: auto; /* Auto height to fit content */
        overflow: visible; /* No scrollbar */
    }

    .preview-container:not(.hidden) {
        height: auto; /* Auto height to fit content */
    }
}

/* Handle very small screens */
@media (max-height: 500px) {
    .input-area {
        max-height: none; /* No max height */
    }

    .preview-scroll-container {
        max-height: none; /* Remove max-height restriction */
        height: auto; /* Auto height to fit content */
        overflow: visible; /* No scrollbar */
    }
}

/* Fix for the weird space issue */
.app-container {
    display: flex;
    min-height: 100vh;
    height: auto; /* Auto height to fit content */
    overflow-y: auto; /* Allow scrolling */
}

/* Make the preview container take up maximum available space */
#preview-container {
    max-height: none; /* Remove max-height restriction */
    height: auto; /* Auto height to fit content */
    overflow: visible; /* No scrollbar, let page handle scrolling */
}
