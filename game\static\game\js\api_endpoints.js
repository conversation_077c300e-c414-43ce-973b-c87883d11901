// API endpoints for the Corporate Prompt Master game
// This file defines the API endpoints for the Django version of the game

// API endpoints
const API_ENDPOINTS = {
    // Use Django-style endpoints with /game/ prefix
    // Using relative URLs to work with any domain and port
    START_GAME: '/game/api/start_game/',
    SUBMIT_PROMPT: '/game/api/submit_prompt/',
    GET_GAME_STATE: '/game/api/get_game_state/',
    PREVIEW_RESPONSE: '/game/api/preview_response/',
    FETCH_FIRST_TASK: '/game/api/fetch_first_task/',
    FETCH_NEXT_TASK: '/game/api/fetch_next_task/'
};

// Function to start a new game - renamed to avoid conflicts
async function apiStartGame() {
    console.log('API: Starting game...');
    showLoading();
    try {
        console.log('API: Fetching game state...');
        const response = await fetch(API_ENDPOINTS.START_GAME);
        console.log('API: Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('API: Game state data:', data);

        if (data.status === 'success') {
            // Update game state
            updateGameState(data);

            // Show welcome message
            if (data.messages && data.messages.length > 0) {
                displayMessages(data.messages);
            }

            // Set up first task if pending
            if (data.first_task_pending) {
                console.log('API: First task is pending, will fetch it after delay');
                setTimeout(fetchFirstTask, 2000);
            }
        } else {
            console.error('API: Error starting game:', data.message);
            showError('Error starting game: ' + data.message);
        }
    } catch (error) {
        console.error('API: Error starting game:', error);
        showError('Error starting game: ' + error.message);
    } finally {
        hideLoading();
    }
}

// Function to submit a prompt
async function submitPrompt(prompt, previewOnly = false) {
    console.log('Submitting prompt:', prompt, 'Preview only:', previewOnly);
    showLoading();
    try {
        const response = await fetch(API_ENDPOINTS.SUBMIT_PROMPT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                prompt: prompt,
                preview_only: previewOnly,
                current_manager: gameState.currentManager,
                current_task: gameState.currentTask
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Submit prompt response:', data);

        if (data.status === 'success') {
            return data;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    } catch (error) {
        console.error('Error submitting prompt:', error);
        throw error;
    } finally {
        hideLoading();
    }
}

// Function to get the current game state
async function getGameState() {
    console.log('Getting game state...');
    showLoading();
    try {
        const response = await fetch(API_ENDPOINTS.GET_GAME_STATE);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Game state data:', data);

        if (data.status === 'success') {
            return data;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    } catch (error) {
        console.error('Error getting game state:', error);
        throw error;
    } finally {
        hideLoading();
    }
}

// Function to preview a response
async function previewResponse(prompt) {
    console.log('Previewing response for prompt:', prompt);
    showLoading();
    try {
        // Use GET request with query parameters to match original Flask implementation
        const response = await fetch(`${API_ENDPOINTS.PREVIEW_RESPONSE}?prompt=${encodeURIComponent(prompt)}&task_id=${encodeURIComponent(gameState.currentTask)}`);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Preview response data:', data);

        if (data.status === 'success') {
            return data;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    } catch (error) {
        console.error('Error previewing response:', error);
        throw error;
    } finally {
        hideLoading();
    }
}

// Function to fetch the first task
async function fetchFirstTask() {
    console.log('Fetching first task...');
    showLoading();
    try {
        const response = await fetch(API_ENDPOINTS.FETCH_FIRST_TASK);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('First task data:', data);

        if (data.status === 'success' && data.message) {
            // Display the first task message
            displayMessage(data.message);
            return data.message;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    } catch (error) {
        console.error('Error fetching first task:', error);
        throw error;
    } finally {
        hideLoading();
    }
}

// Function to fetch the next task
async function fetchNextTask() {
    console.log('Fetching next task...');
    showLoading();
    try {
        const response = await fetch(API_ENDPOINTS.FETCH_NEXT_TASK);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Next task data:', data);

        if (data.status === 'success' && data.message) {
            // Display the next task message
            displayMessage(data.message);
            return data.message;
        } else {
            throw new Error(data.message || 'Unknown error');
        }
    } catch (error) {
        console.error('Error fetching next task:', error);
        throw error;
    } finally {
        hideLoading();
    }
}
