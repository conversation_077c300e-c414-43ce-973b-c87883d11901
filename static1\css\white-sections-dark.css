/**
 * White Sections Dark Mode CSS
 * Ensures all white sections are properly styled in dark mode
 */

/* All white backgrounds */
[data-theme="dark"] .bg-white,
[data-theme="dark"] [style*="background-color: white"],
[data-theme="dark"] [style*="background-color: #fff"],
[data-theme="dark"] [style*="background-color: rgb(255, 255, 255)"],
[data-theme="dark"] [style*="background-color: rgba(255, 255, 255"],
[data-theme="dark"] [style*="background: white"],
[data-theme="dark"] [style*="background: #fff"],
[data-theme="dark"] [style*="background: rgb(255, 255, 255)"],
[data-theme="dark"] [style*="background: rgba(255, 255, 255"] {
  background-color: #121212 !important;
  background: #121212 !important;
  color: #ffffff !important;
}

/* Main content area */
[data-theme="dark"] main,
[data-theme="dark"] .main-content,
[data-theme="dark"] .content {
  background-color: #121212 !important;
  background-image: radial-gradient(circle at 25% 25%, rgba(30, 30, 30, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(20, 20, 20, 0.2) 0%, transparent 50%),
    linear-gradient(to bottom, #121212, #0a0a0a) !important;
  background-attachment: fixed !important;
  color: #ffffff !important;
}

/* Containers */
[data-theme="dark"] .container,
[data-theme="dark"] .container-fluid {
  background-color: transparent !important;
  color: #ffffff !important;
}

/* Rows */
[data-theme="dark"] .row {
  background-color: transparent !important;
}

/* Columns */
[data-theme="dark"] [class*="col-"] {
  background-color: transparent !important;
}

/* Sections */
[data-theme="dark"] section {
  background-color: #121212 !important;
  color: #ffffff !important;
}

/* Assistant Headers - ensure they're always visible in dark mode */
[data-theme="dark"] .general-assistant-header,
[data-theme="dark"] .assistant-header,
[data-theme="dark"] div.general-assistant-header,
[data-theme="dark"] div.assistant-header {
  display: block !important;
  visibility: visible !important;
  background: linear-gradient(135deg, #1e1e1e 0%, #252525 100%) !important;
  background-color: #1e1e1e !important;
  border-bottom: 1px solid #333333 !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

/* Chat Containers - ensure they're always visible in dark mode */
[data-theme="dark"] .chat-container,
[data-theme="dark"] .general-chat-container,
[data-theme="dark"] div.chat-container,
[data-theme="dark"] div.general-chat-container {
  display: block !important;
  visibility: visible !important;
  background: linear-gradient(145deg, #1a1a1a, #151515) !important;
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

/* Chat Boxes - ensure they're always visible in dark mode */
[data-theme="dark"] .chat-box,
[data-theme="dark"] .general-chat-box,
[data-theme="dark"] #chat-box,
[data-theme="dark"] div.chat-box,
[data-theme="dark"] div.general-chat-box {
  display: block !important;
  visibility: visible !important;
  background: #121212 !important;
  background-color: #121212 !important;
  border: none !important;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.2) !important;
  padding: 1.5rem !important;
}

/* Assistant Header Text */
[data-theme="dark"] .general-assistant-header h1,
[data-theme="dark"] .general-assistant-header h2,
[data-theme="dark"] .general-assistant-header h3,
[data-theme="dark"] .general-assistant-header h4,
[data-theme="dark"] .general-assistant-header h5,
[data-theme="dark"] .assistant-header h1,
[data-theme="dark"] .assistant-header h2,
[data-theme="dark"] .assistant-header h3,
[data-theme="dark"] .assistant-header h4,
[data-theme="dark"] .assistant-header h5 {
  color: #ffffff !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .general-assistant-header h4,
[data-theme="dark"] .assistant-header h4 {
  color: #0066ff !important;
  font-weight: 700 !important;
}

[data-theme="dark"] .general-assistant-header .btn-outline-secondary,
[data-theme="dark"] .assistant-header .btn-outline-secondary {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: #333333 !important;
  color: #ffffff !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* Jumbotron */
[data-theme="dark"] .jumbotron {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4) !important;
}

/* Call to action section */
[data-theme="dark"] .cta-section {
  background: linear-gradient(145deg, #1a1a1a, #121212) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4) !important;
  color: #ffffff !important;
  padding: 2rem !important;
  border-radius: 10px !important;
}

/* Footer */
[data-theme="dark"] footer {
  background-color: #1a1a1a !important;
  border-top: 1px solid #333333 !important;
  color: #cccccc !important;
}

/* Modals */
[data-theme="dark"] .modal-content {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .modal-header {
  border-bottom: 1px solid #333333 !important;
}

[data-theme="dark"] .modal-footer {
  border-top: 1px solid #333333 !important;
}

/* Alerts */
[data-theme="dark"] .alert {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

/* Tables */
[data-theme="dark"] .table {
  color: #ffffff !important;
}

[data-theme="dark"] .table th {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
}

[data-theme="dark"] .table td {
  border-color: #333333 !important;
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(255, 255, 255, 0.05) !important;
}

/* Lists */
[data-theme="dark"] .list-group-item {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

/* Breadcrumbs */
[data-theme="dark"] .breadcrumb {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
}

[data-theme="dark"] .breadcrumb-item {
  color: #cccccc !important;
}

[data-theme="dark"] .breadcrumb-item.active {
  color: #ffffff !important;
}

/* Pagination */
[data-theme="dark"] .pagination .page-link {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .pagination .page-item.active .page-link {
  background-color: #0077ff !important;
  border-color: #0077ff !important;
}

/* Tabs */
[data-theme="dark"] .nav-tabs {
  border-bottom: 1px solid #333333 !important;
}

[data-theme="dark"] .nav-tabs .nav-link {
  color: #cccccc !important;
}

[data-theme="dark"] .nav-tabs .nav-link.active {
  background-color: #1a1a1a !important;
  border-color: #333333 #333333 #1a1a1a !important;
  color: #ffffff !important;
}

/* Pills */
[data-theme="dark"] .nav-pills .nav-link {
  color: #cccccc !important;
}

[data-theme="dark"] .nav-pills .nav-link.active {
  background-color: #0077ff !important;
  color: #ffffff !important;
}

/* Cards */
[data-theme="dark"] .card {
  background: linear-gradient(145deg, #1e1e1e, #252525) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
}

/* Buttons */
[data-theme="dark"] .btn-light {
  background-color: #1a1a1a !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

/* Dropdowns */
[data-theme="dark"] .dropdown-menu {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .dropdown-item {
  color: #ffffff !important;
}

[data-theme="dark"] .dropdown-item:hover {
  background-color: #252525 !important;
}

/* Tooltips */
[data-theme="dark"] .tooltip .tooltip-inner {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

/* Popovers */
[data-theme="dark"] .popover {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .popover-header {
  background-color: #252525 !important;
  border-bottom: 1px solid #333333 !important;
}

/* Toasts */
[data-theme="dark"] .toast {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .toast-header {
  background-color: #252525 !important;
  border-bottom: 1px solid #333333 !important;
}

/* Progress bars */
[data-theme="dark"] .progress {
  background-color: #1a1a1a !important;
}

/* Badges */
[data-theme="dark"] .badge {
  background-color: #333333 !important;
  color: #ffffff !important;
}

/* Inputs */
[data-theme="dark"] .form-control {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .form-control:focus {
  border-color: #0077ff !important;
  box-shadow: 0 0 0 3px rgba(0, 119, 255, 0.25) !important;
}

/* Input groups */
[data-theme="dark"] .input-group-text {
  background-color: #252525 !important;
  border: 1px solid #333333 !important;
  color: #cccccc !important;
}

/* Checkboxes and radios */
[data-theme="dark"] .form-check-input {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
}

[data-theme="dark"] .form-check-input:checked {
  background-color: #0077ff !important;
  border-color: #0077ff !important;
}

/* Select */
[data-theme="dark"] .form-select {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

/* File input */
[data-theme="dark"] .form-file-label {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

/* Range input */
[data-theme="dark"] .form-range::-webkit-slider-thumb {
  background-color: #0077ff !important;
}

[data-theme="dark"] .form-range::-moz-range-thumb {
  background-color: #0077ff !important;
}

/* Accordion */
[data-theme="dark"] .accordion-item {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .accordion-button {
  background-color: #252525 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .accordion-button:not(.collapsed) {
  background-color: #0077ff !important;
  color: #ffffff !important;
}

/* Carousel */
[data-theme="dark"] .carousel-caption {
  background-color: rgba(0, 0, 0, 0.7) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .carousel-indicators button {
  background-color: #333333 !important;
}

[data-theme="dark"] .carousel-indicators button.active {
  background-color: #0077ff !important;
}

/* Spinners */
[data-theme="dark"] .spinner-border,
[data-theme="dark"] .spinner-grow {
  color: #0077ff !important;
}

/* Toasts */
[data-theme="dark"] .toast {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
}

/* Offcanvas */
[data-theme="dark"] .offcanvas {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
}

/* Placeholders */
[data-theme="dark"] .placeholder {
  background-color: #252525 !important;
}

/* Code */
[data-theme="dark"] code {
  background-color: #252525 !important;
  color: #cccccc !important;
}

/* Pre */
[data-theme="dark"] pre {
  background-color: #1a1a1a !important;
  color: #cccccc !important;
  border: 1px solid #333333 !important;
}

/* Blockquote */
[data-theme="dark"] blockquote {
  border-left: 4px solid #0077ff !important;
  background-color: #1a1a1a !important;
  color: #cccccc !important;
}

/* Horizontal rule */
[data-theme="dark"] hr {
  border-color: #333333 !important;
}

/* Images */
[data-theme="dark"] img {
  filter: brightness(0.9) contrast(1.1) !important;
}

/* SVG */
[data-theme="dark"] svg {
  filter: brightness(0.9) contrast(1.1) !important;
}

/* Icons */
[data-theme="dark"] .bi {
  color: inherit !important;
}

/* Links */
[data-theme="dark"] a:not(.btn) {
  color: #0088ff !important;
}

[data-theme="dark"] a:not(.btn):hover {
  color: #00aaff !important;
}

/* Text colors */
[data-theme="dark"] .text-dark {
  color: #ffffff !important;
}

[data-theme="dark"] .text-muted {
  color: #999999 !important;
}

/* Background colors */
[data-theme="dark"] .bg-light {
  background-color: #1a1a1a !important;
}

[data-theme="dark"] .bg-dark {
  background-color: #121212 !important;
}

/* Border colors */
[data-theme="dark"] .border {
  border-color: #333333 !important;
}

/* Shadow */
[data-theme="dark"] .shadow {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

/* Opacity */
[data-theme="dark"] .opacity-75 {
  opacity: 0.75 !important;
}

/* Backdrop */
[data-theme="dark"] .modal-backdrop {
  background-color: #000000 !important;
}

/* Scrollbar */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 12px !important;
  height: 12px !important;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: #1a1a1a !important;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background-color: #333333 !important;
  border-radius: 6px !important;
  border: 3px solid #1a1a1a !important;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background-color: #444444 !important;
}
