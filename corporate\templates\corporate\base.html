<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Corporate Prompt Master{% endblock %}</title>
    {% load static %}

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'corporate/css/corporate_styles.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/dark-mode-improvements.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/enhanced-dark-mode.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/leaderboard-dark.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/avatar-styles.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/dark-table-override.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/user-text-brightness.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/user-progress-brightness.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/user-detail-dark-bg.css' %}">
    <link rel="stylesheet" href="{% static 'corporate/css/leaderboard-time-period-fix.css' %}">

    <!-- Theme color meta tags -->
    <meta name="theme-color" content="#4a86e8" media="(prefers-color-scheme: light)">
    <meta name="theme-color" content="#1e1e1e" media="(prefers-color-scheme: dark)">
    <meta name="color-scheme" content="light dark">

    <!-- Always use dark mode -->
    <script>
        (function() {
            // Always apply dark mode
            document.documentElement.classList.add('dark-mode');
            document.documentElement.setAttribute('data-theme', 'dark');

            // We'll add the class to body once it's available
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('dark-mode');
                document.body.setAttribute('data-theme', 'dark');

                // Store theme preference
                localStorage.setItem('theme', 'dark');
                document.cookie = 'theme=dark; path=/; max-age=31536000';
            });
        })();
    </script>

    {% block extra_css %}{% endblock %}
</head>
<body class="dark-mode" data-theme="dark">
    <!-- Unified Navigation Header -->
    {% include 'unified_header.html' %}

    <!-- Messages -->
    {% if messages %}
    <div class="container mt-3">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Content -->
    <main class="container py-4">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="footer mt-auto py-4" style="background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%); border-top: 1px solid rgba(255, 255, 255, 0.05); box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.2);">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                    <span class="text-light" style="font-weight: 500;">© {% now "Y" %} Corporate Prompt Master. All rights reserved.</span>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <a href="#" class="text-decoration-none mx-2" style="color: rgba(255, 255, 255, 0.7); transition: color 0.2s ease;">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-decoration-none mx-2" style="color: rgba(255, 255, 255, 0.7); transition: color 0.2s ease;">
                        <i class="fab fa-linkedin"></i>
                    </a>
                    <a href="#" class="text-decoration-none mx-2" style="color: rgba(255, 255, 255, 0.7); transition: color 0.2s ease;">
                        <i class="fab fa-github"></i>
                    </a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Theme Toggle Script moved to unified_header.html -->

    {% block extra_js %}{% endblock %}
</body>
</html>
