@echo off
REM Script to run the Django application with Gunicorn on Windows
REM This replaces the Passenger deployment method

REM Activate virtual environment if it exists
if exist venv\Scripts\activate.bat (
    call venv\Scripts\activate.bat
)

REM Install or update dependencies
pip install -r requirements.txt

REM Collect static files
python manage.py collectstatic --noinput

REM Apply migrations
python manage.py migrate

REM Start Gunicorn with the configuration file
gunicorn prompt_game.wsgi:application -c gunicorn_config.py
