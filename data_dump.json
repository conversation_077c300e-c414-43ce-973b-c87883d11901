
-----

WeasyPrint could not import some external libraries. Please carefully follow the installation steps before reporting an issue:
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#installation
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#troubleshooting 

-----

WeasyPrint not available: cannot load library 'gobject-2.0-0': error 0x780.  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0'
[{"model": "auth.group", "pk": 1, "fields": {"name": "Company Administrators", "permissions": [142, 139, 140, 141, 143, 114]}}, {"model": "auth.group", "pk": 2, "fields": {"name": "Company Members", "permissions": [140, 141, 114]}}, {"model": "auth.group", "pk": 3, "fields": {"name": "Company Guests", "permissions": [141]}}, {"model": "auth.user", "pk": 1, "fields": {"password": "!Hn19s5S7WFVtfwuV4hvVSF03JA38j7ZmHgs9c4Cb", "last_login": null, "is_superuser": false, "username": "AnonymousUser", "first_name": "", "last_name": "", "email": "", "is_staff": false, "is_active": true, "date_joined": "2025-05-12T07:08:18.618Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 2, "fields": {"password": "pbkdf2_sha256$1000000$2gZEugwQmszCbdjLRuUqg3$Q4xwzz2CUGnQUtipmEfRwO3bcv46v6IDeqeOY5+YXNA=", "last_login": "2025-05-15T05:41:53.007Z", "is_superuser": false, "username": "allan", "first_name": "allan", "last_name": "scof", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-12T07:41:09.003Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 3, "fields": {"password": "pbkdf2_sha256$1000000$sNB80Fu4zoPjAJicfZfn4d$h6PBCPsxdKhyubEcqHo0LhvCMTJlSNYsgwCZv7W1lAw=", "last_login": null, "is_superuser": false, "username": "testuser", "first_name": "Test", "last_name": "User", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-12T07:58:41.186Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 4, "fields": {"password": "pbkdf2_sha256$1000000$aINQEdwDZ2PQpGmAIOCdUC$zqJz8vzGJWrfzZv828GwjenX3CyEjlHGa89CJ8l0fL4=", "last_login": "2025-05-13T16:41:02.480Z", "is_superuser": true, "username": "admin", "first_name": "", "last_name": "", "email": "<EMAIL>", "is_staff": true, "is_active": true, "date_joined": "2025-05-12T08:01:58.422Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 5, "fields": {"password": "pbkdf2_sha256$1000000$FX8TtTGpYb0BI3spCs3w7N$99prDc21D7uYoetsPcOuFjaBK6WXaiXUKfGa/y53GAs=", "last_login": null, "is_superuser": false, "username": "testuser2", "first_name": "", "last_name": "", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-12T08:02:37.172Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 6, "fields": {"password": "pbkdf2_sha256$1000000$IOhhbQynWA1shN57q0OxgL$R6qFDlCPea7y3dtP00i3mYasB/qYRiKgxOi+zhderm4=", "last_login": "2025-05-12T09:58:06.644Z", "is_superuser": false, "username": "testuser1", "first_name": "", "last_name": "", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-12T09:49:00.381Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 7, "fields": {"password": "pbkdf2_sha256$1000000$zRuukM7xYpsI9Ult7WHOSt$LJ3M1m2fLYM5AzzRmhVGufIZrfm0Xxmj7fa5y7j52RY=", "last_login": null, "is_superuser": false, "username": "testuser3", "first_name": "", "last_name": "", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-12T09:49:02.283Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 8, "fields": {"password": "pbkdf2_sha256$1000000$5Cm8N6m0d5V55H6hWKi1Ny$PV2LHWY3xLwCvS/twtVGCHeZ1GecutpQaAbpwuS04pI=", "last_login": "2025-05-13T11:48:10.530Z", "is_superuser": false, "username": "jeff", "first_name": "Wagaba", "last_name": "Jeff", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-12T18:51:00.602Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 9, "fields": {"password": "pbkdf2_sha256$1000000$dBLBFXulLkn34H3qCyFiX5$6LHaRxZWusnXvzPuQl06/gyPT7kwQVL/rQltr3eIVrA=", "last_login": "2025-05-15T05:41:07.010Z", "is_superuser": false, "username": "ten", "first_name": "ten", "last_name": "kumi", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-12T21:11:27.084Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 10, "fields": {"password": "pbkdf2_sha256$1000000$KpVk6PVOhzouaFUFWL6pkk$0DkTWP0Z+vAh3Nzoccbyd7UU+4yEOCw/pfJWbcPevmc=", "last_login": "2025-05-13T09:08:50.056Z", "is_superuser": false, "username": "two", "first_name": "two", "last_name": "bili", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-12T21:45:30.526Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 11, "fields": {"password": "pbkdf2_sha256$1000000$urcIsy73KZHRsqfmruzwRd$jKEnRydOVRC41gUlccTmx9K93bdrq5PZGzn0lzW6LYA=", "last_login": "2025-05-13T06:06:46.724Z", "is_superuser": false, "username": "three", "first_name": "three", "last_name": "satu", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-13T02:18:50.414Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 12, "fields": {"password": "pbkdf2_sha256$1000000$H6MJfaNqUBXsE0faEsM7Tf$ScA51AYWAXp2XFR7qO460Duy+PctnQ6a7/G+X8QcTPM=", "last_login": "2025-05-13T08:48:22.250Z", "is_superuser": false, "username": "dajuice", "first_name": "da", "last_name": "juice", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-13T08:08:04.486Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 13, "fields": {"password": "pbkdf2_sha256$1000000$Zn3R2fxW0pFRXaMGW1Novr$Y94jnhFLoqqr1RXledeEM2f/K7EaFZNB6MlSW7Qd0Ns=", "last_login": null, "is_superuser": false, "username": "jane", "first_name": "jane", "last_name": "for", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-14T16:13:15.201Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 14, "fields": {"password": "pbkdf2_sha256$1000000$UbpjDvJulBDONqBpOgFB5s$4rHjADFVmcwZkLzTaVATef0GhNaz0ZcRqqUdH5m8B7w=", "last_login": "2025-05-15T04:59:43.448Z", "is_superuser": false, "username": "victor", "first_name": "victor", "last_name": "victor", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-14T19:38:28.453Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 15, "fields": {"password": "pbkdf2_sha256$1000000$r3OqhoCtPghrdSa2SsXSOd$OeylUIpMyTXXXDr4sFrpBcQiXR+pJTFmcbxKdoHgqEU=", "last_login": null, "is_superuser": false, "username": "testuser_9a29d80f", "first_name": "Test", "last_name": "User", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-14T20:41:10.399Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 16, "fields": {"password": "pbkdf2_sha256$1000000$HUcwe6kbkHaCXw8ANlsKo5$up5jmZyc6fdqDq3wMGVEvpPHE/jRky8oryQ+MnPICAQ=", "last_login": null, "is_superuser": false, "username": "testuser_eba18347", "first_name": "Test", "last_name": "User", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-14T20:41:29.430Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 17, "fields": {"password": "pbkdf2_sha256$1000000$U1L0FJob30ve339lGJ59gL$DV+Xg9eaYNMe2Gao3FIvEfj+DlfnNN8yGkCJsNwuYBY=", "last_login": null, "is_superuser": false, "username": "testuser_59fcb854", "first_name": "Test", "last_name": "User", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-14T20:42:40.496Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 18, "fields": {"password": "pbkdf2_sha256$1000000$PH5iFsrzqBeVrTrx0pPY1L$LE+uwsCWnC7mZG4QE19A4tiwvL7K4os9JCYGmDsKN3w=", "last_login": null, "is_superuser": false, "username": "testuser_6394397d", "first_name": "Test", "last_name": "User", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-14T20:43:29.073Z", "groups": [], "user_permissions": []}}, {"model": "auth.user", "pk": 19, "fields": {"password": "pbkdf2_sha256$1000000$1eTMdcTszy7oJ8yjD83yXE$pdpskyfYwhwmxxrqdPYdNcGBXI7pIiCmWC6zwaI8fFc=", "last_login": null, "is_superuser": false, "username": "jimmy", "first_name": "jimmy", "last_name": "new", "email": "<EMAIL>", "is_staff": false, "is_active": true, "date_joined": "2025-05-14T20:50:38.083Z", "groups": [], "user_permissions": []}}, {"model": "sessions.session", "pk": "02q0p0ckl05qf1w60p1v3m39k6gep5ay", "fields": {"session_data": "e30:1uG7Du:VNjjjWFo2OFmOT_b_f1X63-btEu-kXvzZiK6XEhOBic", "expire_date": "2025-05-18T02:24:26.816Z"}}, {"model": "sessions.session", "pk": "06vrty0gix529e48v2raw9dl1mb55ed2", "fields": {"session_data": "e30:1uFSqS:Psc9pvkdpkTMrOeSOCCG-SurvDw5qsMpAfOEt3utkn0", "expire_date": "2025-05-16T07:17:32.327Z"}}, {"model": "sessions.session", "pk": "0dumof6rxrowk82cnbs48cg7295i02wm", "fields": {"session_data": "e30:1uFQxW:Ms9IUbdhprlc1HUcWkgybM7Rda1zij_iJrEV1gKsHbs", "expire_date": "2025-05-16T05:16:42.661Z"}}, {"model": "sessions.session", "pk": "15i3y9e3wylkhaxiqzbiovlmsketws0w", "fields": {"session_data": "e30:1uFTL2:vtN8EdQC5wmHx7CCtWoaKkLVcYMR5o4AeZfsKUyNAsQ", "expire_date": "2025-05-16T07:49:08.251Z"}}, {"model": "sessions.session", "pk": "1rchiyfx9y2k9r84rv0tzijcojpvhtgn", "fields": {"session_data": "e30:1uGVGr:85DG7TIon61o6QprVRBa2W-rm2blbtVyo0-gdqFUdeg", "expire_date": "2025-05-19T04:05:05.343Z"}}, {"model": "sessions.session", "pk": "1wnoy2h235s98zuzhm2q0q61oc0jt22b", "fields": {"session_data": "e30:1uFWRa:86Revr_BLEVv1ocG8EYL-h_KdVbK21YC_mKBk_5Z6OI", "expire_date": "2025-05-16T11:08:06.516Z"}}, {"model": "sessions.session", "pk": "1zveupuexl9qvqse2xm9bukw9haqc3k7", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFJ2b:U2gaSoGDUhPQTVua4Q_t-2BisQ2vwxPVgVOHo1zNKVc", "expire_date": "2025-05-28T20:49:25.484Z"}}, {"model": "sessions.session", "pk": "2j9e2kj54zv935qo658mbor121qrebbl", "fields": {"session_data": "e30:1uFVNE:uVugxmC6jXPCqVwYIwig5GlJrCElhUppqTXG6pdveXA", "expire_date": "2025-05-16T09:59:32.763Z"}}, {"model": "sessions.session", "pk": "2oshf5oaebmtoit57mmugfhfvvsjnrb9", "fields": {"session_data": ".eJxVj1FrwyAUhf-LzyWYqNH0saMtDPKwwWBvcr1q4tZq0WQsjP33JdA99PWc831wfojGkv2UPl0ke3J868_PUoh0Ovvj0A8qvb5fFvd0yFdzepkl2REN8zTqubisg12R5jEzgKtqK-wHxCFVmOKUg6m2SXVvS9Un6y6H-_ZBMEIZV1pyaBEp8Lo1UjHEmtWihQYbyz1SqljtGO0k47RzHTXSK-uFBNN5FBz5Ji2ulJCidt-3kBeyVy2ndEcAp_DlNKbrDeLy_-L3D7akV2E:1uFOr1:0v1ik0c10xPzY62Xs0iv6ScAJQ0Pif5STJ33CXbe4ME", "expire_date": "2025-05-16T03:01:51.533Z"}}, {"model": "sessions.session", "pk": "2po5olhqzqgi146o971e8odv7sunsbz8", "fields": {"session_data": ".eJxVj8FKxDAURf8l66GkTdo0s1NUECqiCEI34fUlsXE0Gfra0iL-uy2Mi9nee8-B-8MM0uDHdHKRHVnb0DI_PzZ37v79NKW1IVxnattX3T4sbzcv7MAMTGNvJnKDCXZDiuusA9xUe2E_IX6kDFMch9Bl-yS7tJQ9Jeu-bi_bK0EP1G-0klAhcpB51alaIOYiLysosLDSI-e1yJ3gWgnJtdO8U762vlTQaY-lRLlLyRGFFI1bzmFY2bGuJOcHBjiG2RlM32eI6_-L3z_jylib:1uFJGH:Guv68OsVUUvehuuWLeP2WkD2eRkA_4KdGCmn4LZYwtU", "expire_date": "2025-05-15T21:03:33.200Z"}}, {"model": "sessions.session", "pk": "3l3a1caj3rvgz7uqq06zck44a8ffdr23", "fields": {"session_data": "e30:1uG7Dw:Ks8cNyJy99sBTvV_k99q9iS_hFSbXO1Q4C1E9ebdAEY", "expire_date": "2025-05-18T02:24:28.853Z"}}, {"model": "sessions.session", "pk": "3mb2t3f1vnmwilak3eun6a7qb2pvi2mh", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFITM:QuYhH1h2ZXeXkOtOzW7QuKNprs8E0emguESDl7WmLMA", "expire_date": "2025-05-28T20:13:00.184Z"}}, {"model": "sessions.session", "pk": "3nc6fn432365ehl1cadgen4wy0zv7q2x", "fields": {"session_data": "e30:1uFWRd:4rw42Cv8ninq1d-OR-iKfayYXWAXnvoNFnmznnGj7e8", "expire_date": "2025-05-16T11:08:09.016Z"}}, {"model": "sessions.session", "pk": "3ra23uc52ni3c7rqb6wwz57cxqdfqcz1", "fields": {"session_data": "e30:1uFWRK:L9zPQdWyfebLYLBL4s9CK3ONC9OQwaqhMlk-DswFJwI", "expire_date": "2025-05-16T11:07:50.920Z"}}, {"model": "sessions.session", "pk": "3we76vs89wdzsfms5kdarhhpfve5b618", "fields": {"session_data": "e30:1uFWwh:0c0n_oxob9rN1CsojRXSVTFOGX3ThnbeJLqtPk_nrf4", "expire_date": "2025-05-16T11:40:15.502Z"}}, {"model": "sessions.session", "pk": "420eunxk8btdc0uayzba57trxikzhlm4", "fields": {"session_data": "e30:1uDxI9:fgPa_pa3y3EkbBODIicVFGxfTMc1HaeCikXI3sHpI5E", "expire_date": "2025-05-25T03:23:53.233Z"}}, {"model": "sessions.session", "pk": "4584c83ml4xxmfuwn985fi5itkbqo2rv", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFJ1d:cjD45DIiWNn7XrWT1nbmgZiQtjQcgMITpMZblkJrLvg", "expire_date": "2025-05-28T20:48:25.961Z"}}, {"model": "sessions.session", "pk": "4dq46kips41ar8vbxlmnv3hklqan041i", "fields": {"session_data": "e30:1uGHdZ:lfXNm7OMct-EC942lpcNsparGDXQH3fwKEaEhm9yYYI", "expire_date": "2025-05-18T13:31:37.820Z"}}, {"model": "sessions.session", "pk": "4rrd719xa4nhyqsgh81p0asjdx0yff1s", "fields": {"session_data": "e30:1uFS23:2h6UZogicwQSbqmOrX2WkT3moENViLblVhr02RzGZ3Y", "expire_date": "2025-05-16T06:25:27.824Z"}}, {"model": "sessions.session", "pk": "4x792la69bkez5wl6zx5oosubsbde8vy", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiT0NLZU91cFc0cFoxMWgxSFE5M0FSM0M1aVBYTERNdmkifQ:1uFPIC:_eYRZzkhnk8tiDDUboJXiiVmp6sD_nLmXSE4TU9C5K8", "expire_date": "2025-05-16T03:29:56.983Z"}}, {"model": "sessions.session", "pk": "4z1pc1w5psq7v90ffbcl0eft7xf5hh9l", "fields": {"session_data": "e30:1uGHev:jns3as_Ad0ihgyUtOgVGjj_-R7PwXIbljgP86XQPaOY", "expire_date": "2025-05-18T13:33:01.175Z"}}, {"model": "sessions.session", "pk": "5q3riaettwa2qtgjy292gh0zbcqag4d3", "fields": {"session_data": "e30:1uGZ9l:1hiYHF3caSB0F59ONxLAyPm4SrzdbLMlgPMQ6zpVKSM", "expire_date": "2025-05-19T08:14:01.923Z"}}, {"model": "sessions.session", "pk": "5rphv164acj0xjwdszomsnh23pdu7qj5", "fields": {"session_data": "e30:1uFmDM:IyzOwr6nbEyCxkvhfeeX_HmugTL1rfMhX5l-uZsc7JU", "expire_date": "2025-05-17T03:58:28.256Z"}}, {"model": "sessions.session", "pk": "5wtgihjs2lgvs314pep5kbz8j43g50xx", "fields": {"session_data": ".eJxVj8FuwjAQRP_FZxQ5sRMn3AriyAGqVqIXa71eEwOxozhUQNV_byLRA9eZeU-aH6YxDW6MZwpsyd6t_eweG7_-eEub0-q2745fod8Cd5R24eDZgmm4jq2-Jhq0txNSvGYGcFLNhT1BOMYMYxgHb7J5kj3blG2jpcvquX0RtJDaiVYSKkQOMq-MqgViLvKyggILKx1yXoucBG-UkLyhhhvlautKBaZxWEqUszRRSj4GTbfeD3e2rCvJ-YIBjv6bNMauh3D_f_H7B9wzWJg:1uFQiA:oCymxFO2O0RjJVkqZ3VgRPomBm1yFKQQRfbsBWfyRTk", "expire_date": "2025-05-16T05:00:50.598Z"}}, {"model": "sessions.session", "pk": "63xy6kf4ggrsaime5nfy6ivseini8ei0", "fields": {"session_data": "e30:1uFVND:JQxsNMGlE3Yo39XnyNq8BNJJDt0OktjfbDr9ZZZC50I", "expire_date": "2025-05-16T09:59:31.693Z"}}, {"model": "sessions.session", "pk": "6fwag37n7osnccvi8xmbaua4jf538i77", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFHxH:Gv3EIfrrvYH-q9xFBAc58dK8W9DmdlJ-wwUPQQpgcPU", "expire_date": "2025-05-28T19:39:51.362Z"}}, {"model": "sessions.session", "pk": "6oskgjny97u3gbahodg5rtgqaw780qs1", "fields": {"session_data": "e30:1uENox:m55xL8JxLuqXge7pd84j3H5tWW1UVn6oImJZ9YZ233E", "expire_date": "2025-05-26T07:43:31.139Z"}}, {"model": "sessions.session", "pk": "6ruv6dsutmev4dr48rus45lp187bt579", "fields": {"session_data": "e30:1uGZ9n:R3nZyzRm_rDpTccg-31EiidrL63owGZHs6cf5HifLU0", "expire_date": "2025-05-19T08:14:03.164Z"}}, {"model": "sessions.session", "pk": "6sf1yzjlhfhy8ppxd9gkfkymis2dmes1", "fields": {"session_data": "e30:1uENq2:bB72LpHHoJVrBMZA9fbrS-XOHOZwHT4yEC6fLPB-NGE", "expire_date": "2025-05-26T07:44:38.160Z"}}, {"model": "sessions.session", "pk": "6tyz6da4zp5ildu4efgh3pr2chesl2s7", "fields": {"session_data": "e30:1uFEZJ:uXsdv7HYH5XHTTwRA6iFO8_XDppcqa-nsAFKbXavoqY", "expire_date": "2025-05-28T16:02:53.150Z"}}, {"model": "sessions.session", "pk": "7fys10ckp4bnlux9c2idmr6knxtxkz6u", "fields": {"session_data": "e30:1uFW14:XovhvX6miLN99bViywSNsyWX3VtgDX8PXiBrphq8nbk", "expire_date": "2025-05-16T10:40:42.342Z"}}, {"model": "sessions.session", "pk": "7h1340a0lxg427exqz6s6h3w4rtwktq6", "fields": {"session_data": "e30:1uFVZ0:gjT4TA8Tc_5W1J-504d2ZV_16Nnmu2oZzAiYoKJ3LuQ", "expire_date": "2025-05-16T10:11:42.923Z"}}, {"model": "sessions.session", "pk": "7r12b8umt166xr1qpokk9nwqoee1bpbz", "fields": {"session_data": "e30:1uFY6q:wn0j4wbR875_4pdLZGhUg_KykRL6oYadJsagLsJcC1U", "expire_date": "2025-05-16T12:54:48.490Z"}}, {"model": "sessions.session", "pk": "7ygb961od3w6cwgqvsf86li3hy3w0w8u", "fields": {"session_data": "e30:1uFSt9:VjympMrfWFYZX5R_CRpecJFxLUkdqm08f_Bx3hwWYNU", "expire_date": "2025-05-16T07:20:19.608Z"}}, {"model": "sessions.session", "pk": "7z10zd9qno5ec42uozcozdg4vmugj5m2", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoieFJiZkQ4a0E2VVR3MzMxRVVsYkRuT0szQmlJek5tZzgifQ:1uFOzI:8c09DxWuelRo60lATB8NPG00oCbRcr3bI2XJC8XbZlc", "expire_date": "2025-05-16T03:10:24.081Z"}}, {"model": "sessions.session", "pk": "860ziucg5dn9qg9gtdzp4hha1z8ocha4", "fields": {"session_data": "e30:1uFWRa:86Revr_BLEVv1ocG8EYL-h_KdVbK21YC_mKBk_5Z6OI", "expire_date": "2025-05-16T11:08:06.790Z"}}, {"model": "sessions.session", "pk": "868nwwtst5p64hogm7k0fgn3z4eh4ghm", "fields": {"session_data": "e30:1uFTNV:-N_EYfCRuhKJ3bgiHoOTaLDv_Ozhg6kEyzoerHvpzes", "expire_date": "2025-05-16T07:51:41.136Z"}}, {"model": "sessions.session", "pk": "87h2p3kyt7th4r39l2fsflru6yzbysrt", "fields": {"session_data": "e30:1uFSuA:Z2VXzV_RPAqTf0FRl562wt5bWdc8ixUW62_Yf72rbLM", "expire_date": "2025-05-16T07:21:22.187Z"}}, {"model": "sessions.session", "pk": "8bejfamten3c7wuzll8n5dsva8ardr7k", "fields": {"session_data": "e30:1uFS9d:uar8OeY7S6oZUIDVeVCS1W5LcFdQ8Sq7eafmHnN6GNA", "expire_date": "2025-05-16T06:33:17.987Z"}}, {"model": "sessions.session", "pk": "8o68skjabaq2jbv1hsg31n0gzbfahyf4", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFJ2a:43NDR8nlkzkrG3TodoQvaKrGfH0VFTRpMZjPhFjrYjM", "expire_date": "2025-05-28T20:49:24.383Z"}}, {"model": "sessions.session", "pk": "8yzfhracq2yjxj245stu7k7gf0rzhkag", "fields": {"session_data": "e30:1uFVNC:fZOlW0Z-4FNEho0MBFjAqQpqNGpuajXm-90EvXrHCEM", "expire_date": "2025-05-16T09:59:30.830Z"}}, {"model": "sessions.session", "pk": "94dy8urljswp9739m823l5u3b6l1wyew", "fields": {"session_data": "e30:1uGUzX:Bb_bqmMNHd26fY_qYs2V4hmFo3Eb89vQuSD8HoByZAc", "expire_date": "2025-05-19T03:47:11.643Z"}}, {"model": "sessions.session", "pk": "9iq4ffxp5rgkc6uhslxvy5c2lciz7qyj", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiRmNEU0pwb2pGV2Y4YVJ1eWRGOUVMU1NKYk1SS2oxc0oifQ:1uFS1z:_3pnlm7L-m7pg4AuwaBKplNBLeuViLoKWlM5cj8kzsc", "expire_date": "2025-05-16T06:25:23.529Z"}}, {"model": "sessions.session", "pk": "9swtgf3g69jj32m1nu8a46s49kecyk2k", "fields": {"session_data": "e30:1uGUzW:9tk4Fk4iOEpY10pDzNB5l1g2hq6i_lE9rQ6v7gLL9a4", "expire_date": "2025-05-19T03:47:10.977Z"}}, {"model": "sessions.session", "pk": "9xq362znkse3e4q8kyrdrmpqo1dorwcw", "fields": {"session_data": "e30:1uGVGt:sn4Y6hAgjsXZ9s2rMdFuwOz9UWyFwN6mvcCHmmtnJTM", "expire_date": "2025-05-19T04:05:07.310Z"}}, {"model": "sessions.session", "pk": "9yie13v1ln4fvgyolesqs2fb62nb656y", "fields": {"session_data": "e30:1uG5Pv:cTcxDQgf4Lb8FIHCA9yOdtjfiG5NbVm9lEfDiuevCV0", "expire_date": "2025-05-18T00:28:43.012Z"}}, {"model": "sessions.session", "pk": "a1p0h7vbhk0qkto8wf7gu0meveiii4ow", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiNEMzWjllcTlRNXI1NlhaN1RiREVvUnI5S2o3TFdjUmYifQ:1uFS9k:2cAM1zDjrsJubNMMj9AfZWWSerpCHajU5dAfhSLzH7M", "expire_date": "2025-05-16T06:33:24.364Z"}}, {"model": "sessions.session", "pk": "a4vdqq2k7ulqa2zkzx1fpb1fbl5lk01d", "fields": {"session_data": "e30:1uFEZJ:uXsdv7HYH5XHTTwRA6iFO8_XDppcqa-nsAFKbXavoqY", "expire_date": "2025-05-28T16:02:53.068Z"}}, {"model": "sessions.session", "pk": "a5q8abx7vgtsosi7eo9gbd7o7u3qzhp2", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiSmxoWjcwaWE1Q3BRUmc3NDdEcVlRa1o2ZmtVVnNQUVkifQ:1uFJBN:2AZbk1dDS6QyRGhY5JPETagOoFDtIfF67ZgFN5H1N6I", "expire_date": "2025-05-15T20:58:29.932Z"}}, {"model": "sessions.session", "pk": "a6x1ubrfjqne1okvq0cri394j6kdekdt", "fields": {"session_data": "e30:1uFT20:XDDyyw4qWrUlDqgA4VD3TCsiiuaB1IYPXtSzFi0FEKs", "expire_date": "2025-05-16T07:29:28.752Z"}}, {"model": "sessions.session", "pk": "acuvwjctgpcaz7d4ujejc2m6nxf9752x", "fields": {"session_data": "e30:1uFWRL:0vju7zFb-_v2ISC-uvW_pC1gdtF4BOncUBsb7QbU0eI", "expire_date": "2025-05-16T11:07:51.433Z"}}, {"model": "sessions.session", "pk": "ad0rhsj1r0hkcg83wp351jegun8sue85", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiT2daTHBzZXdsODF5QU9yOUd5Q2dDQnpaZ2VUQTZEckEifQ:1uFR0u:3a0vkWR2VexksP_P-O-_rFoXvzKtSsYAZ0IQPP8gXkc", "expire_date": "2025-05-16T05:20:12.664Z"}}, {"model": "sessions.session", "pk": "aff3se103ct30wc26d3rocv3dkflbn5a", "fields": {"session_data": "e30:1uFrlF:udOO62keQKST9pgnmgjmnux-HDh-VEgzWClJMlIJ6J0", "expire_date": "2025-05-17T09:53:49.318Z"}}, {"model": "sessions.session", "pk": "aqz29lav5ypc7hhsg14zl8fush0b6dvz", "fields": {"session_data": "e30:1uGUzV:KrCPSFFr6HX8dVVjos4ynMNDxRNRa6qd6jQNI69pD3w", "expire_date": "2025-05-19T03:47:09.310Z"}}, {"model": "sessions.session", "pk": "awprr79f29mjtf6w84wgy74mce3w2831", "fields": {"session_data": ".eJxVj8FOwzAQRP_F5yraxE7s9AgqEpW4IDhwstZru3FT7GCniIL4dxKpHHqdmfek-WGaSvZzGl1kW4bw_ZDzCz7vxzHcfxxOebfbl9djeFMjqseJbZjG8zzoc3FZB7sgzW1mkBbVWtgjxkOqKMU5B1Otk-raluopWXe6u25vBAOWYaGlwI4IUNSdkYoT1bxuO2yoscITgOK149BLLqB3PRjplfWtRNN7agWJVVpcKSFF7b6mkC9sqzoBsGFIc_h0mtL7hPHy_-L3D6lsWF0:1uFNd8:JtYGKp-apGz7MPyU0mui7Z_RI9iB2UkCl88R_qiXZcc", "expire_date": "2025-05-16T01:43:26.596Z"}}, {"model": "sessions.session", "pk": "ay550lnzwljjsvc1ivz9pja9byc1myg6", "fields": {"session_data": "e30:1uFlwN:1zjBAb6DpF95RaIdvPVBQVoUDpFdfBuAgoGswvZ8fII", "expire_date": "2025-05-17T03:40:55.129Z"}}, {"model": "sessions.session", "pk": "ayhulzzr2knjk5hcm8d8x86faua9cz7m", "fields": {"session_data": "e30:1uFVNF:ACvFSQToZ164vBYkyAN--0F-qtdiF26DTxDDTsSmFF0", "expire_date": "2025-05-16T09:59:33.797Z"}}, {"model": "sessions.session", "pk": "b0ert59g3n01zpi78k9s5j8bclmsezfw", "fields": {"session_data": "e30:1uGUzU:CpwE8Q3_zmXoQDGzTinUwn4XXEU2NZjSx29XNcFEjOk", "expire_date": "2025-05-19T03:47:08.639Z"}}, {"model": "sessions.session", "pk": "behn86qlpu9ht5ajeuviawbt2pgtozmv", "fields": {"session_data": "e30:1uFPZv:c0PlvbELkgowXdWkS_ys7s-19zI010JZecy8o7MH5V4", "expire_date": "2025-05-16T03:48:15.399Z"}}, {"model": "sessions.session", "pk": "bfo20v513a7qi3985yk9j69otj2bqa41", "fields": {"session_data": "e30:1uFlwL:OSO6LJ_J1TP_S0EDDZIazVGQpxR_PbE8PN_0GUhifGk", "expire_date": "2025-05-17T03:40:53.352Z"}}, {"model": "sessions.session", "pk": "c7zimqif35g0q0jlyh32c907kyi63idf", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uEtzk:CyrlDn12VF8h0H44D-LP04Koj_hs5xd8uUnTwSCfcn8", "expire_date": "2025-05-27T18:04:48.006Z"}}, {"model": "sessions.session", "pk": "c9ksl26zx86kkeq8wv9v0gescwxlu6vp", "fields": {"session_data": "e30:1uFVND:JQxsNMGlE3Yo39XnyNq8BNJJDt0OktjfbDr9ZZZC50I", "expire_date": "2025-05-16T09:59:31.596Z"}}, {"model": "sessions.session", "pk": "cdb98qbhqrsnppckcvz53ho4dhn9wl0l", "fields": {"session_data": "e30:1uFWRH:IfxO37Zvf-MmjIEqgPtNbTTlNaBCV-6kuMA5uR3Dqio", "expire_date": "2025-05-16T11:07:47.584Z"}}, {"model": "sessions.session", "pk": "ch7oz13b7se4e3960iwdvkcrtk32t6lm", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiOThGMnZkOUZjY0l4cTdJUGNaejk0Q09SY0R5UmJOYk0ifQ:1uFPiS:mRsahoJuRSwb2LFwB5HeoPnqJCdT788aBxuZgo1spZQ", "expire_date": "2025-05-16T03:57:04.801Z"}}, {"model": "sessions.session", "pk": "ck0hy8huogi8gb1p7aotu9o79w5akkdk", "fields": {"session_data": "e30:1uFVNG:_qxFhmbBqGm68QUcPp5hAdFd8QgGlOdCTgMKEMdQDsQ", "expire_date": "2025-05-16T09:59:34.347Z"}}, {"model": "sessions.session", "pk": "czfv48ak69ws4kfgf7mzk154665m8ebp", "fields": {"session_data": "e30:1uFS9o:43rIYSXSwtfXiRMNpctM6tTGHPgvsAiUFuMmwrjmOp0", "expire_date": "2025-05-16T06:33:28.503Z"}}, {"model": "sessions.session", "pk": "d33nien9y5g0yuraqjciafc93n2t6nnp", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFJ2Y:Hn986vgSb1qukAOKtfhxf0s6NATjfgdfZL7UWVmvbGE", "expire_date": "2025-05-28T20:49:22.152Z"}}, {"model": "sessions.session", "pk": "d5ae51xdqovlzrrl1cv7mdl7htbobu18", "fields": {"session_data": "e30:1uFQN9:_txTZ4ekciL8qgqRZAvhWEmLDKVjA2fqw9rx_mEVWDM", "expire_date": "2025-05-16T04:39:07.625Z"}}, {"model": "sessions.session", "pk": "d5evrz20ze4g0i7vk3n7muwy9eivqr29", "fields": {"session_data": "e30:1uFPVj:b1FtwG7ANzf9-3Zydv99bXOtzfn3-tldqwKyg146JR4", "expire_date": "2025-05-16T03:43:55.330Z"}}, {"model": "sessions.session", "pk": "d9t3iqxn1osjhgxrt11la0chy7q7tu5u", "fields": {"session_data": "e30:1uFV1n:eJ8iMDYuq0lYsTDPBREg_U7egv2d_hdANDYM5R_B2m0", "expire_date": "2025-05-16T09:37:23.392Z"}}, {"model": "sessions.session", "pk": "daus2fx5kp1tink0tvb7paum68jbsa4l", "fields": {"session_data": ".eJxVjMEOgjAQRP-lZ9OU0m2LR-98Q7PsbgU1JaFwMv67kHDQ22Tem3mrhNs6pq3KkiZWV-XV5bcbkJ5SDsAPLPdZ01zWZRr0oeiTVt3PLK_b6f4djFjHfW2iDcDiAhiIZIF87iLl4GIWRoDWCJLdAzSddZ1ji40n3wIwSjN49fkC0RY3gA:1uEPvC:zBkqsGqlw73yhxWrS_SdAnwWqJFliow4xdA8S579G2Q", "expire_date": "2025-05-26T09:58:06.655Z"}}, {"model": "sessions.session", "pk": "ddopbuqd9l493p0o1x3shqic445lv2l2", "fields": {"session_data": "e30:1uGEF3:0f86spN4PRTwE6eVhyu8_o8RwIm6me9UbFW-H30vJZM", "expire_date": "2025-05-18T09:54:05.403Z"}}, {"model": "sessions.session", "pk": "dj1tbrj0zzo8n8gwer4gup8xxfx84wn9", "fields": {"session_data": "e30:1uFVNC:fZOlW0Z-4FNEho0MBFjAqQpqNGpuajXm-90EvXrHCEM", "expire_date": "2025-05-16T09:59:30.578Z"}}, {"model": "sessions.session", "pk": "do6b19w5feez2kwwu8265f91pgqqfpxb", "fields": {"session_data": "e30:1uFSgs:1C-vZIEqEXjbfjflHhHJARxFfzK33p2Pc30wmMsH1fw", "expire_date": "2025-05-16T07:07:38.546Z"}}, {"model": "sessions.session", "pk": "dpajbke455cwscw6ijoqnd3haq6exgkd", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFIqh:f0ZeXocakDzugqckoEBLkpF48haBipXEMiDTZlIWsvE", "expire_date": "2025-05-28T20:37:07.581Z"}}, {"model": "sessions.session", "pk": "dwcg4en4b2n27l9dsyhf7yq23ykqxym3", "fields": {"session_data": "e30:1uFXMf:7y6f3-jlFuGLz-y7w6dE_F4nWCf4hAw9em-mNAeOZPM", "expire_date": "2025-05-16T12:07:05.992Z"}}, {"model": "sessions.session", "pk": "dxf3xjfxx3zi65n5yx5f736p3p9z2l6p", "fields": {"session_data": ".eJxVj8FOwzAQRP_F5ypKYjt2eytSKUhQhPgAa71rJ26oXcUppEL8O4nopdeZeU-aH2YwD35MvYtsw3ZBb910eD891VOr3kr_-HF47vtvtfPtftu_sBUzcBk7c8luMIFmpBL3oQWcXUtDR4htKjDFcQi2WCbFrc3FayL3-XDb3gk6yN1MAxBZD67W5K2wJFBaT1Iir7TEilcN1UJzTRVX2MBaS62QNCksa-6tX6TZ5RxSNG46h-HKNroRZbligGP4cgbT6Qzx-n9jzX7_AJp-WQI:1uFQhU:k7_uGWU_oZw7ps5guz2cDa5v8N5lJu03HsJBSIikq-M", "expire_date": "2025-05-16T05:00:08.643Z"}}, {"model": "sessions.session", "pk": "dyl9nbqvuo0nyvww653o3r29dk04ws01", "fields": {"session_data": ".eJxVj8FrwyAchf8XzyWYaNT0mENZDxlssLLtIvpTp1urRe1oGPvfl0B36PW9933wfpCEkl1NXzaiLZoOT_N-__5yeHzYPU--vo3X14xrsBiCYKNHGyTVpXp5KTbLYBaku8-0gkW1FuZTxY_UQIo1B92sk-bWlmZKxh7H2_ZO4FXxC82pYgBY0ZZpLghAS9qeqQ46Qx1gLEhrCR44oXiwA9bcCeN6rvTgoKdAV2mxpYQUpb2eQ57RVjCK8QYpqOHbSkins4rz_4vfP_lpV6A:1uFQga:FZbPJ-A39wP4GYwfbInxMfcZEK3AoH6ZPMdwTHv6vuA", "expire_date": "2025-05-16T04:59:12.831Z"}}, {"model": "sessions.session", "pk": "eeoqo95fbhsofcddcozh7vriyk0j5qvk", "fields": {"session_data": "e30:1uFxgf:ibQ04TZIRr-mR2TipB42MGnRLlg4GIvByeTWUp_V3VY", "expire_date": "2025-05-17T16:13:29.897Z"}}, {"model": "sessions.session", "pk": "ek82i9gvl9knc0kf9tkebshkcs0zhmqx", "fields": {"session_data": "e30:1uFS1v:BL8QI8pKYr9EnzHWrNW1wjt9K7b34pewVcDHOkuMMvo", "expire_date": "2025-05-16T06:25:19.374Z"}}, {"model": "sessions.session", "pk": "ekmkv47c6dvuiwpqo7hks7ugavqbup2v", "fields": {"session_data": ".eJxVjskKAjEQRP8lZwnZOkaP3v2G0JN0O-OSyCyCiP_uDIyg13pVj3qJiNPYxmmgPnZZ7IUTm9-swXShsoB8xnKqMtUy9l0jl4pc6SCPNdP1sHb_BC0O7bxGCM44tUvoleZAhqmhAMEqQM_eeGJnQyJk6zl4kxUwO53AbUGDtrMU09g9KKZ6u2N5ft--P3vcP58:1uElVg:uNPXLMtpQWRBS4Tjcee2iki9PHpP2O8L-5lz62PaX94", "expire_date": "2025-05-27T09:01:12.430Z"}}, {"model": "sessions.session", "pk": "el7qrfu8oil3rjyt4w09u9rio0u2on15", "fields": {"session_data": "e30:1uFVND:JQxsNMGlE3Yo39XnyNq8BNJJDt0OktjfbDr9ZZZC50I", "expire_date": "2025-05-16T09:59:31.597Z"}}, {"model": "sessions.session", "pk": "evb71lmcnnp23hqynvubfak57esame2m", "fields": {"session_data": "e30:1uFxgd:bU3M1AI_MRRmeFmvZ5-gzDRKjw6FKDwDytRVal7iDNM", "expire_date": "2025-05-17T16:13:27.318Z"}}, {"model": "sessions.session", "pk": "exgvszi0rbcb9mqvp2d24kd07cthvlqp", "fields": {"session_data": "e30:1uFWRL:0vju7zFb-_v2ISC-uvW_pC1gdtF4BOncUBsb7QbU0eI", "expire_date": "2025-05-16T11:07:51.643Z"}}, {"model": "sessions.session", "pk": "f4ebyhvhhr8gbctvr2ihy8b40h3zogeq", "fields": {"session_data": "e30:1uGxz9:_HIpbfiCaRSHC8YUpGLzVk06rs2AEWH_1eEj35yqhjM", "expire_date": "2025-05-20T10:44:43.933Z"}}, {"model": "sessions.session", "pk": "fcnrh8jcn0j4k60esly2kgf0nclmxdhn", "fields": {"session_data": "e30:1uFxrq:sLYIDZ6biAUYumiYSLigE8MziqKhUJEbOeBqNeyMQiQ", "expire_date": "2025-05-17T16:25:02.753Z"}}, {"model": "sessions.session", "pk": "flchxucn5vnjhig48w51bgtx549plx8j", "fields": {"session_data": ".eJxVjDsOwjAQBe_iGln-rH-U9JzBWq9tHECOFCcV4u4QKQW0b2bei0Xc1ha3UZY4ZXZmip1-t4T0KH0H-Y79NnOa-7pMie8KP-jg1zmX5-Vw_w4ajvatHaAlEgjSJuc1kdTSWFSkMlQSwmtZtAhOgwgliOSqz9U4TKGSAQL2_gDRozeZ:1uEQcB:4T_0M8nMBduDJka_hQunXohGD2pne2L36MqYScrEIzE", "expire_date": "2025-05-26T10:42:31.019Z"}}, {"model": "sessions.session", "pk": "fney8k26j22daztt6v8hxx48wvm54bqw", "fields": {"session_data": "e30:1uFVNC:fZOlW0Z-4FNEho0MBFjAqQpqNGpuajXm-90EvXrHCEM", "expire_date": "2025-05-16T09:59:30.718Z"}}, {"model": "sessions.session", "pk": "g1pcy9gtd7ho1qoew32k0rex9jtt1hi7", "fields": {"session_data": ".eJxVjDsOwjAQBe_iGln-rH-U9JzBWq9tHECOFCcV4u4QKQW0b2bei0Xc1ha3UZY4ZXZmip1-t4T0KH0H-Y79NnOa-7pMie8KP-jg1zmX5-Vw_w4ajvatHaAlEgjSJuc1kdTSWFSkMlQSwmtZtAhOgwgliOSqz9U4TKGSAQL2_gDRozeZ:1uEQc9:NiB-9cErq14-nDbuK23zFf85FTVtMI83jW5sf1zawoA", "expire_date": "2025-05-26T10:42:29.773Z"}}, {"model": "sessions.session", "pk": "g3v5he0dto9u5whcaie6yychpiaa1epw", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiNk1vaTNFcEFXNmROWlVPeHdQcHI4SGloZDNzWlRudTYifQ:1uFSAd:lOQmX1YNWxE53jWKcUfzLz7hREI9dOanfLzMK4hOcKY", "expire_date": "2025-05-16T06:34:19.515Z"}}, {"model": "sessions.session", "pk": "gdsvl1q8140x3qv2wpg43ye8rwotaxz4", "fields": {"session_data": ".eJxVj7FuwyAURf-FObIwYGNnq9VkSFW1UrtkQo8HxDQJRICjRlX_vbaUDlnvvedI94cozMmVeLSBrMkxvelh-Bjd5148bcswMbZ53r1vdi_lfJVsT1ZEwVRGNWWblDczwh4zDTirlsJ8QTjECmMoyetqmVT3Nlev0djTcN8-CEbI40xLAS0iBVG3WnYcseZ10wJDZoRDSjteW057yQXtbU-1dJ1xjQTdO2wEikWabc4-BmW_Lz7dyLprBaUrAlj81SqM5wuE2_-L3z-IP1ci:1uFSkN:Jn4CzveXaceJhPJj9rPRl5FM0UyPmNPKUlj00-YmttQ", "expire_date": "2025-05-16T07:11:15.419Z"}}, {"model": "sessions.session", "pk": "gt7eghqzyeg2eqpla21bhrong77nynwb", "fields": {"session_data": "e30:1uFlwM:byRz9YY4uJSLUmRBoRAdPrYWLMIGzKGZO-_rb0-ufGo", "expire_date": "2025-05-17T03:40:54.750Z"}}, {"model": "sessions.session", "pk": "gv5ea8rvznob1ldvxduey2risw31tl65", "fields": {"session_data": "e30:1uFS25:TZYz-bAgJXHqiKFspoabbcG5oNYl8zoAHlyHh8xaE6w", "expire_date": "2025-05-16T06:25:29.894Z"}}, {"model": "sessions.session", "pk": "h0xzlqcnz1omgevlf4u2kmiwxjzd1eqq", "fields": {"session_data": "e30:1uFYfy:snFp-YusgfQbPH5QzMGUug2rH8ZOTiXNBUAyJg8JmvQ", "expire_date": "2025-05-16T13:31:06.937Z"}}, {"model": "sessions.session", "pk": "h0ziv7mvxqbhmbwfwekxfp9uu4kj6lla", "fields": {"session_data": ".eJxVj8tqwzAURP9F62Ak62Vn2WbZBroKXYmrK8lWm0jBsoNC6b_XhnSR7cycA_NDDJYpzPnbJ7In6qMOFznW1yr8wupbPqbhLJcbO3yejic8kB0xsMyjWYqfTHQr0j5nFnBVbYX7gjTkBnOap2ibbdI82tK8Z-fPL4_tk2CEMq60FqAQKQimrO44IuNMKmixdSIgpR1nntNec0F731OrQ-eC1GD7gFKg2KTFlxJzMr5e43Qn-04JSncEcI43bzBfrpDu_y9-_wBpTVgX:1uFJD3:fTfhkQX4N7PpXaHVWrVQuJeACVwsvgjxH9QfVyCiQik", "expire_date": "2025-05-15T21:00:13.858Z"}}, {"model": "sessions.session", "pk": "hc61m8lfl26axvp3au01xb95vvdvoa6a", "fields": {"session_data": "e30:1uFsro:LN8uql9sX91N0RrNC-X2WUqxGfiJ2jlXiAffF1vUPCE", "expire_date": "2025-05-17T11:04:40.447Z"}}, {"model": "sessions.session", "pk": "hn3nu1npciz56n3ucrpcrch5232fhfzl", "fields": {"session_data": "e30:1uGrlI:5JeFiGtHd4N3J8AdhSlXMdqkWliMpoxPpEtZcRKOO0M", "expire_date": "2025-05-20T04:06:00.823Z"}}, {"model": "sessions.session", "pk": "htqdx50ckqkfuf45zgxigwjot25gihvr", "fields": {"session_data": "e30:1uFW14:XovhvX6miLN99bViywSNsyWX3VtgDX8PXiBrphq8nbk", "expire_date": "2025-05-16T10:40:42.363Z"}}, {"model": "sessions.session", "pk": "i5ok3wpal5urorc5w8t6200w11padsz5", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiZzlOWWtnSUh6S3E4WVBNNnFLVkV2OXJ6elVSM05NUjkifQ:1uFpf9:1gPPccCMtaKRYPGkeyJK9xgnlfFISlEPaPpZZdL-L_c", "expire_date": "2025-05-17T07:39:23.955Z"}}, {"model": "sessions.session", "pk": "ixmn4bdmhw5yxfink09px7zuz0v9na2p", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoicGRraUkwczZzdHk0cHNqM2F3MXJka3BVYU03R3RMcjEifQ:1uFS1x:lXGIMEnxPRa8gVAnsEYI7jLyIKS1pFHQX5TSsERyJlA", "expire_date": "2025-05-16T06:25:21.462Z"}}, {"model": "sessions.session", "pk": "jwyt7qn84fsvmvc530cukz8lnn6amj0m", "fields": {"session_data": "e30:1uFVNC:fZOlW0Z-4FNEho0MBFjAqQpqNGpuajXm-90EvXrHCEM", "expire_date": "2025-05-16T09:59:30.821Z"}}, {"model": "sessions.session", "pk": "kd196nmj7tjccu2bhm05sxg9w2pve9cu", "fields": {"session_data": "e30:1uFsMU:Mmk6zhDI64RXae5E3HMe4OO8CihMa18AS0vD6pqN1lc", "expire_date": "2025-05-17T10:32:18.853Z"}}, {"model": "sessions.session", "pk": "ksdtm45v3ailjh8h5vew427v28hdul5g", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiSWZXQ1M4aDNaNXVLNTRDYTBNUUVHNU93RHByM2lsOTEifQ:1uFSAh:RW1czSUuY57DskXaioABSpV5wv6ztK50AQ0BiNxENV0", "expire_date": "2025-05-16T06:34:23.784Z"}}, {"model": "sessions.session", "pk": "l211jckslyw8tey9nere88h44bhhzutw", "fields": {"session_data": ".eJxVj1FrwjAUhf9LnqWkTZq0PipjTJEijLK3cHOT2Mwt0SYO3dh_XwvuwddzzvfB-SEK0-hyPNpAluR53Q_9q_C5E8fDOb-sKuz66_f-bZO23Q6eyIIouORBXZIdlTcTUj1mGnBSzYV5h3CIBcaQR6-LeVLc21TsorEfq_v2QTBAGiZachCIFHgptGwYYsnKWkCFleEOKW1YaRltJeO0tS3V0jXG1RJ067DmyGdpsin5GJS9nvx4I8tGcEoXBDD7L6swfp4g3P5f_P4BIVdXzQ:1uFOmA:zsMVfxh9idZCahPc7gsrQfg8Kfw9rou2hhp-yQMpYDc", "expire_date": "2025-05-16T02:56:50.570Z"}}, {"model": "sessions.session", "pk": "l9h0zdq8qk33n36g4exikxopvc7ky3vw", "fields": {"session_data": "e30:1uFxgd:bU3M1AI_MRRmeFmvZ5-gzDRKjw6FKDwDytRVal7iDNM", "expire_date": "2025-05-17T16:13:27.310Z"}}, {"model": "sessions.session", "pk": "le3a78i7ozii6pkb5vultt73srkrj5zh", "fields": {"session_data": "e30:1uFYdq:j4ngGOYxmUkVSY0qbkKcxOMRq9AfBvan64jisrpACyU", "expire_date": "2025-05-16T13:28:54.427Z"}}, {"model": "sessions.session", "pk": "licavi76nfrkzn83hezf0j2zkqj6xtyw", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoibUs5c3ZqR1RnMkhJM3BhTmxMa25seHFFN2dkSHB5cFAifQ:1uFpf9:WAdHWpSRasIE6XzxN-eJnroyrNgXZUpVJCm5Dt-FMD4", "expire_date": "2025-05-17T07:39:23.134Z"}}, {"model": "sessions.session", "pk": "liqunk0f571lp4j2staa2ujr6qxlk6al", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFJ4B:OXvgozf18kRSxRDjoeqsS5aIDuuAB6qCyu8FmhALDYc", "expire_date": "2025-05-28T20:51:03.307Z"}}, {"model": "sessions.session", "pk": "liyjcqcmakdh90qt4eblg4i1bi9m9scb", "fields": {"session_data": "e30:1uENq2:bB72LpHHoJVrBMZA9fbrS-XOHOZwHT4yEC6fLPB-NGE", "expire_date": "2025-05-26T07:44:38.075Z"}}, {"model": "sessions.session", "pk": "ll4nfn0vt5qxrvh1p1yhif0pafcebjcn", "fields": {"session_data": "e30:1uFcOE:jCYTV-EDY3o3SeYdFnnrrkpVu4uTHytJ8lpa51a0cRU", "expire_date": "2025-05-16T17:29:02.757Z"}}, {"model": "sessions.session", "pk": "lljy8vdbvkt7wfir7ldtkxp9228kyv65", "fields": {"session_data": "eyJhY3RpdmVfY29tcGFueV9pZCI6MX0:1uENrg:0q7WpyN30cNDlDTyRYl1Gzx7DUePnQmos8iS4gTJtwM", "expire_date": "2025-05-26T07:46:20.561Z"}}, {"model": "sessions.session", "pk": "lro6fc0v8a5ezxrtsr8r8xqs810lvhfo", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiRHd6YWxaSFBSNU1vdlZOR3I1ZHpXUFJWVlBoTjFPUTUifQ:1uFS9g:B7PWZlNncKoR6XsbWJWNJ5H_KXOVCLGDgNBcdm8BEZA", "expire_date": "2025-05-16T06:33:20.074Z"}}, {"model": "sessions.session", "pk": "luuotznljqfisfy8379wuqygm9dvxxtx", "fields": {"session_data": "e30:1uFlwM:byRz9YY4uJSLUmRBoRAdPrYWLMIGzKGZO-_rb0-ufGo", "expire_date": "2025-05-17T03:40:54.237Z"}}, {"model": "sessions.session", "pk": "ly3sk56nu8otuw7r1gwews4kt5hbjuup", "fields": {"session_data": "e30:1uGVGs:TOuNWsSsXVSHBVocgbTHYKnSMb0yvWcm2AGCldLOrWk", "expire_date": "2025-05-19T04:05:06.247Z"}}, {"model": "sessions.session", "pk": "m3an23d00xxrarln9iesop5mjch3b3y5", "fields": {"session_data": "eyJhY3RpdmVfY29tcGFueV9pZCI6MX0:1uENmh:qjCtoxeVuQ6IW1sy7_8jEyxw-J8zXSP3lk_xOpGIbEs", "expire_date": "2025-05-26T07:41:11.040Z"}}, {"model": "sessions.session", "pk": "m6omhw6jn6x0flc4695mbcx2nmol5c1f", "fields": {"session_data": "e30:1uGZ9f:-jfjojHNtFIh_DVqDKnqdbHsE1aNSOZjyvrHZPutOwY", "expire_date": "2025-05-19T08:13:55.178Z"}}, {"model": "sessions.session", "pk": "m9lxp9xh267g6zmraoftsbs4um73ciih", "fields": {"session_data": "e30:1uFrk4:gA4fLxwG3_wjQPGCLTLkl3AeWa3gyAhvUU4V2Ft332c", "expire_date": "2025-05-17T09:52:36.494Z"}}, {"model": "sessions.session", "pk": "mgvqgpedcm9oy9u0p51xdke43ntomce7", "fields": {"session_data": "e30:1uFmDK:JiIOmasPqgskq7D4CtruQ6Rk1Pz9CeFc9QapWJhjKok", "expire_date": "2025-05-17T03:58:26.788Z"}}, {"model": "sessions.session", "pk": "mna1jwfckaij720uz88yip34aujklifb", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiVUliYWwxTzZRbGwzUms0R29VVXZqVWxhTHBOcm5tMVoifQ:1uFPPs:Havmau_CLJAf2EvxVs7kaCZuvFaEQZC68sEFH0YkV2I", "expire_date": "2025-05-16T03:37:52.800Z"}}, {"model": "sessions.session", "pk": "moqfuu0z7vtig1qv9d8njqs42xbfb22x", "fields": {"session_data": "eyJyZWdpc3RyYXRpb25fbGlua190b2tlbiI6IjAxYjdhZWVmLTJkNmYtNDcwZi1iYmQ1LTQ4NDZmOGM1MGViNyIsInBlbmRpbmdfY29tcGFueV9pZCI6IjIifQ:1uFJ3s:Fsg4MEd_pGfOHdKR-cShAp2sbz_WsRYUvHK_jxHIOcQ", "expire_date": "2025-05-28T20:50:44.615Z"}}, {"model": "sessions.session", "pk": "mste0d0s3crgn19l117p7waotokhn5il", "fields": {"session_data": "e30:1uG7GT:JwygJIpRKylWqdgRCcwfnmbES6uLaRNxL97Htr4L6YI", "expire_date": "2025-05-18T02:27:05.322Z"}}, {"model": "sessions.session", "pk": "mvc0s6q0l173m2zbp4v6vjqusgm1lxfr", "fields": {"session_data": "e30:1uFVYj:ovodYmgeAX8vuz71Nhswlt7-8pDjEs8HAvMqYAmdPao", "expire_date": "2025-05-16T10:11:25.922Z"}}, {"model": "sessions.session", "pk": "mvldp7cewba1pg69lj9n0svc4h0z8mjh", "fields": {"session_data": "e30:1uFVND:JQxsNMGlE3Yo39XnyNq8BNJJDt0OktjfbDr9ZZZC50I", "expire_date": "2025-05-16T09:59:31.520Z"}}, {"model": "sessions.session", "pk": "mz6fmjhsme6o666y6w5x9et693wwtweg", "fields": {"session_data": "e30:1uGYwZ:Y7v1ZTbDC2QaRc7YXR0hFpKNt0v9TIl0FJnI6rmwoUU", "expire_date": "2025-05-19T08:00:23.318Z"}}, {"model": "sessions.session", "pk": "n9pxs8czepbokzjdbb45gp3hbrhbsl1m", "fields": {"session_data": "e30:1uEKkX:6yF6zd9GwMZe0cpJLuKCJuDM2UsbGvjilAeHF8O1LCQ", "expire_date": "2025-05-26T04:26:45.211Z"}}, {"model": "sessions.session", "pk": "nejtqdskjegkdysef82lnac4jxljaf7w", "fields": {"session_data": "e30:1uEKkf:eVPeUP_8c4o0Yl6sofnDmOQ3UYYgq7wyYWojDI6rj8E", "expire_date": "2025-05-26T04:26:53.381Z"}}, {"model": "sessions.session", "pk": "o18s6wgg7vocwgjitprdh70ogsw1r0pb", "fields": {"session_data": "e30:1uG5IG:cubwRakWtTBtBu5MzXSxX1icH57jw1kdAgMPnM45_RA", "expire_date": "2025-05-18T00:20:48.327Z"}}, {"model": "sessions.session", "pk": "o6apzs3mn47k3pw772050a3xf5x6iz6l", "fields": {"session_data": "e30:1uFxgh:zWRgEQvDvl5ulB6-jGJrsGiZNrmdIVdLqG8fE2j0JGw", "expire_date": "2025-05-17T16:13:31.035Z"}}, {"model": "sessions.session", "pk": "ofauzbi5tvrnbuifwb4w2mhg5bjxnsh3", "fields": {"session_data": "e30:1uGxzu:oQkd6IEs-BIP2E_usrajnijOQsA5VlmjRgBUHMXRYVA", "expire_date": "2025-05-20T10:45:30.550Z"}}, {"model": "sessions.session", "pk": "ohdqg8y1hm8mh5xi84414bnal32a5g11", "fields": {"session_data": "e30:1uFPgX:oNgX9hb1XDDynC949A3ff2qUoKtLgSCvKkk3_xiBTpI", "expire_date": "2025-05-16T03:55:05.880Z"}}, {"model": "sessions.session", "pk": "oxyvhrdqjcl8bh86sm3ffc1pw27nqv1p", "fields": {"session_data": "e30:1uENuZ:mUHTD2-F1mjzXcVDQoETxc9UMOgkvSCTXgYavAwX7hU", "expire_date": "2025-05-26T07:49:19.788Z"}}, {"model": "sessions.session", "pk": "p8jr51u197fx2btoogw7qkctniippw5a", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFIy8:3l6dLxV5FvMLIW0MtLAAMt43LcMiaWM6nQcaTkPZgPc", "expire_date": "2025-05-28T20:44:48.824Z"}}, {"model": "sessions.session", "pk": "p9wd8dm14hpq256emfo57tnsgddqyut1", "fields": {"session_data": "e30:1uFmDL:ebVsiLVkSi7fB65nrUValJ3ClM-JkSPOTFVHHNWQYk0", "expire_date": "2025-05-17T03:58:27.648Z"}}, {"model": "sessions.session", "pk": "piu62llvwjvtig5qu9i63gd8lgqycbz2", "fields": {"session_data": "e30:1uGfqF:wl0l2Xv0H56Du_LJgQQc89Gz4cG8xLvp7U5PTovf25k", "expire_date": "2025-05-19T15:22:19.125Z"}}, {"model": "sessions.session", "pk": "pv4l2md2a81ektrs8wy714o49jv6kt3r", "fields": {"session_data": "e30:1uGxAS:FpAZ7NO301GrmrN0fj5hvsYBP--GQ_v6Cx_Rvg09_bE", "expire_date": "2025-05-20T09:52:20.131Z"}}, {"model": "sessions.session", "pk": "q1u5dx4ewd1j8qwibel2tzl2017fda01", "fields": {"session_data": "e30:1uFYes:QvQHmz1OJB95Ce_ynOODY_JvS7-RwwgOdhH2W8izxJ0", "expire_date": "2025-05-16T13:29:58.317Z"}}, {"model": "sessions.session", "pk": "q72l9xs7w0bl7amuzyic5scr1wtrw816", "fields": {"session_data": ".eJxVj8tuwyAURP-FdWSBwTyya_rwqquqSpMNur5ATB8QGVIlqvrvtaV0ke3MnCPND7FYplDzh09kTWRi48N-2--K0U-HR3He9PX1pd_d3QOKt-2erIiFUx3tqfjJRjcj7W02AM6qpXDvkA65wZzqFIdmmTTXtjTP2fnPzXV7IxihjDOtBEhECoLJQWmOyDjrJLTYOhGQUs2Z59QoLqjxhg4qaBc6BYMJ2AkUi7T4UmJO1p-PcbqQtZaC0hUBrPHbW8xfR0iX_xe_f2N4VwA:1uFQLq:t_YTutfK23G_xymSp0qYQSps6aTLmYG7X4V35G--v_E", "expire_date": "2025-05-16T04:37:46.332Z"}}, {"model": "sessions.session", "pk": "q9pascrpi9ayv3g0y703jn4tojmqg159", "fields": {"session_data": "e30:1uEKgp:Rr8qb2qd2oqQJkYrzj9e5tnb8WIrU7ZYL3obofZ9aQQ", "expire_date": "2025-05-26T04:22:55.157Z"}}, {"model": "sessions.session", "pk": "qom7do8hyur9jq9pvoik7fvgc1kz5w63", "fields": {"session_data": "e30:1uFSAb:ef9O-rNI0DWIoneRgjIP-ZYGcdZct7rZ_kjNH9IMeJU", "expire_date": "2025-05-16T06:34:17.435Z"}}, {"model": "sessions.session", "pk": "qpa81rjg18iodlikiykcxfy1q42j7u41", "fields": {"session_data": "e30:1uFWRY:e-3w6GXjsQZAFZ6AFPE5HB0tBWvfv1vtbWDVlkuyQIg", "expire_date": "2025-05-16T11:08:04.726Z"}}, {"model": "sessions.session", "pk": "quoujnwvk5gy957o2u8qe6igw6gihepd", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiQzBmYWhNVWNGeGlZOUViN1FTU051Z3ZjZlZMQnNLRFgifQ:1uFJzU:GYuLlTLSguZRDamt4xqiEL6j-NSNVVO6rDbLquhH6FU", "expire_date": "2025-05-15T21:50:16.565Z"}}, {"model": "sessions.session", "pk": "qzmbsry2gn35e3j1t3wso2g1vkvuy0xq", "fields": {"session_data": "e30:1uFQZM:Uttb4ck9DjBy1cOsgzrmA47_j4ZndgQ0ArlhqxYGRnM", "expire_date": "2025-05-16T04:51:44.660Z"}}, {"model": "sessions.session", "pk": "r6o73pvcoe6g63kk4sj50znp4fveg3hv", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiZHJlYXVYS1VXWUxRTXhBNDY0THpCdDR4eEFZeU13NG8ifQ:1uFS21:U1T3QqImAFPZA_YVqznkJkJzMij9Lfw5k-H0LzXdivU", "expire_date": "2025-05-16T06:25:25.755Z"}}, {"model": "sessions.session", "pk": "rn6bouxrz8vxpdvarxg6jgiwdvpusx84", "fields": {"session_data": ".eJxVjEsOAiEQBe_C2hB-jejSvWcgDXTLqIFkmFkZ766TzEK3r6reS0RclxrXQXOcijgLJw6_W8L8oLaBcsd26zL3tsxTkpsidzrktRd6Xnb376DiqN8aITjj1CmjV5oDGaZEAYJVgJ698cTOhkzI1nPwpihgdjqDO4IGbcX7A9-yN38:1uEsgg:W7QvbpoQaLExZqmcxpq2JoAkHbqUVzRX26XJ0ueMHko", "expire_date": "2025-05-27T16:41:02.491Z"}}, {"model": "sessions.session", "pk": "sbhqmfv9jcy6etz79lqai7zivk1zmp5q", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFEeu:hKCcxdU9d8KsCgT0OoOgr4viMyh8LeP9Zul1YahUjnc", "expire_date": "2025-05-28T16:08:40.286Z"}}, {"model": "sessions.session", "pk": "sdp1bmijbm8jdl5hk4x6ksw51pyylw55", "fields": {"session_data": ".eJxVj1FrwyAUhf-LzyVoolH7OLpCYStjD2Vvcr1q49ZpiMm6MPbfl0D30NdzzvfB-SEGyxDG_OET2RItdnsxXZ8Uq0_Xl8Nh_ybm_sgur6f-8QjnjmyIgWnszFT8YKJbkPo-s4CLai3cO6RzrjCncYi2WifVrS3Vc3b-8nDb3gk6KN1CSw4tIgXOWitVg8gaJlqosXY8IKWqYb6hWjacaq-plUG5ICRYHVBw5Ku0-FJiTsZ_93GYyVa1nNINARzjlzeYP3tI8_-L3z9tBlcT:1uFR7W:SxXl1NQwrAVBhRXwJ9jeX_36o39zqfFgXAnA8-AzD94", "expire_date": "2025-05-16T05:27:02.033Z"}}, {"model": "sessions.session", "pk": "shwcdpjkktfx42wjszoyqclh823et2iw", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiRzBDN0NzSGNSNEhGbVU5NDh3a1hoRko1dUNSN1hkS3MifQ:1uFRiB:TpdCPp1VnNKGSTp7YKSPmNBqGiy_AIuYPaqsysgfCuM", "expire_date": "2025-05-16T06:04:55.179Z"}}, {"model": "sessions.session", "pk": "sigmj6ba6epgljgqv7xqo86n6vo05v86", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiOWFsZ3kyODZHWjNGRk1yVktXbjJwR1UxU3d2YWpNRVcifQ:1uFUCw:LOiBivn7_Lu3jDxdio3TY6ncsdir-a3hiFT7qG9YE6s", "expire_date": "2025-05-16T08:44:50.689Z"}}, {"model": "sessions.session", "pk": "so2awis2epiotnp3qeh0j43p5t4piy0v", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiVWFkd3dGM25uZnBaSmxtNlRIS0d2dG5ORG1vYnVXM3gifQ:1uFPaA:DYHQv8pNrrW1IPdfVcMsSN-2kfxXRTWtgwg3Bt0mjPQ", "expire_date": "2025-05-16T03:48:30.028Z"}}, {"model": "sessions.session", "pk": "ss2nut2n39o22rkrxelas9d4cjpm0to4", "fields": {"session_data": "e30:1uFVND:JQxsNMGlE3Yo39XnyNq8BNJJDt0OktjfbDr9ZZZC50I", "expire_date": "2025-05-16T09:59:31.597Z"}}, {"model": "sessions.session", "pk": "ssjcgb66633ckftg34nwyore23jnmivf", "fields": {"session_data": "e30:1uFV6X:Puyjm6N_weesPUBB6wpTlJibvYVJy31aF3mAFMqAuqg", "expire_date": "2025-05-16T09:42:17.842Z"}}, {"model": "sessions.session", "pk": "su80q6rqzb3untl3fvzc1jx07izfxi14", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiSFlxbjhOb05tdGRnZnZrWnJVVmF6akVBeHI0a3pGVEYifQ:1uFpiM:LBOo9XXXuoB7Zyvt1EIl3Pk2p4-MI4FYKs7Z0DK1I5M", "expire_date": "2025-05-17T07:42:42.205Z"}}, {"model": "sessions.session", "pk": "tugphn57xj4uasxxm7xe6mxx3iwd0fl4", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiVWJuUjhwNVM2dTRrQ3I4UDN4WUxmQ081UEpmRW92MVUifQ:1uFPLo:lmUvJabumwpcWhvLNUo5b2IODil6Ezcqmj4-fbwnvW0", "expire_date": "2025-05-16T03:33:40.761Z"}}, {"model": "sessions.session", "pk": "tybup9sl2ixcihn856x7ikiqw1ztygnm", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiZm5ENWdiUjBnd2ZIc3hWdEZEdEhVMklTWFlMZWJvQ1YifQ:1uFpfG:Dui0qddkd7YbSlo84mhC5CldIA5sk2qPTEcDhtgr8Ks", "expire_date": "2025-05-17T07:39:30.940Z"}}, {"model": "sessions.session", "pk": "u0gmc4u7iriiagg9m7rr58iy83d1qdgm", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFHgX:M-Wwb2hgId1C57KZumbPP6hdk27io-jsX7XBE43r8sw", "expire_date": "2025-05-28T19:22:33.714Z"}}, {"model": "sessions.session", "pk": "u2v990clszi5cbb1taapokptbwlgt0ys", "fields": {"session_data": "e30:1uGF4V:8CrJfR5sYjdK0C4PFPqBjv9mKBGPD94QHSgpsmxFclE", "expire_date": "2025-05-18T10:47:15.408Z"}}, {"model": "sessions.session", "pk": "u861xo4cng7jabywjxrn0xvw2g2awtk9", "fields": {"session_data": "e30:1uGF45:AR26TMCbbhOoxC1ZdEcHrns79Mt18IGFUBicK176RMw", "expire_date": "2025-05-18T10:46:49.187Z"}}, {"model": "sessions.session", "pk": "uf4q95kazzy9qw3hraa6jsfdwrsxatan", "fields": {"session_data": "e30:1uGZ9f:-jfjojHNtFIh_DVqDKnqdbHsE1aNSOZjyvrHZPutOwY", "expire_date": "2025-05-19T08:13:55.176Z"}}, {"model": "sessions.session", "pk": "ukaa4lnpcwo5mzx8tfuoec5p6s2r6ld4", "fields": {"session_data": "e30:1uFT2S:WYkxiUstTp3JFwQPRIPoaimfJq8Zc-Rw13tTfTXabKg", "expire_date": "2025-05-16T07:29:56.139Z"}}, {"model": "sessions.session", "pk": "vjxuhqd10vceu5rvjeluhxbp6s92vblw", "fields": {"session_data": "e30:1uFR6s:lEEwkMhI06zc8K6hITEYAepmC5oVSye_QVUYXtbYR68", "expire_date": "2025-05-16T05:26:22.966Z"}}, {"model": "sessions.session", "pk": "w11e1m7hwtuqoj37e5494hzamev35sh0", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFIx5:ybMh8BUFVbojljXW2kOMLGBiR8exI-Eb7gv_DToTPUY", "expire_date": "2025-05-28T20:43:43.932Z"}}, {"model": "sessions.session", "pk": "w6qi9nhoh5mrttjd0kmjqhy611aud4an", "fields": {"session_data": "e30:1uFsDJ:XwO9t_z6wIoYKyGHUQI9gm392aqh8lWxLvlnr1BbU-U", "expire_date": "2025-05-17T10:22:49.894Z"}}, {"model": "sessions.session", "pk": "wb6pop9zyxmugv78zv6d44p8zaytyyee", "fields": {"session_data": "e30:1uFS9m:SBsMRlBK7s04CHet3jfVRdASqn8Q6BceJBhn1Cr4IA8", "expire_date": "2025-05-16T06:33:26.429Z"}}, {"model": "sessions.session", "pk": "welugaanpu2hvb3mncarc2isjsks55i8", "fields": {"session_data": "e30:1uGxBE:PMXJ8SKfVCw-pnYpxq9RzUnIdFpesv-m_Iu7R_O-xeo", "expire_date": "2025-05-20T09:53:08.457Z"}}, {"model": "sessions.session", "pk": "whiw6klfbv8a74bxr0uayjedkpoud7h4", "fields": {"session_data": "e30:1uFJCV:cqj0RUoZX1W-ZpR_iIbCCvA4cAKtTi6ixw2HtOuU2zs", "expire_date": "2025-05-15T20:59:39.593Z"}}, {"model": "sessions.session", "pk": "widz1nitvdy5gur0lb11bzs4bwedfx9m", "fields": {"session_data": "e30:1uEKgo:M-tobhNif5dyE1vHf3ntXLJsHtUa57RYQHJWpWldE74", "expire_date": "2025-05-26T04:22:54.766Z"}}, {"model": "sessions.session", "pk": "wjzugg7g6oan611ooycdgl4vjzrw6gpt", "fields": {"session_data": ".eJxVj8FOwzAQRP_F5yraJE7s9AYSEgUVhHoAerHWa5u4gB3ZKSVC_DuJVA69zsx70vwwRTm5Mb7bwNaMn5pXebpNU3_lwm5_v7sb4ObpcQ8vdvPwvI1sxRQex14ds03KmxmpLjONNKuWwhwwvMWCYhiT18UyKc5tLrbR2I_r8_ZC0GPuZ1pwbIkAedlqIWuisi6bFiuqDHcEIOvS1tCJmkNnO9DCSeMagbpz1HDiizTbnH0Myn4PPk1sLVsOsGJIo_-yiuLngGH6f_H7B-FmV4U:1uFJFP:G4BP7QWoO3Q0_U7niXsx8SD-2xAl1ZaklZHbK_MqyAE", "expire_date": "2025-05-15T21:02:39.296Z"}}, {"model": "sessions.session", "pk": "wkelf75038pkgy3vqnx1lelmtqorvz33", "fields": {"session_data": "e30:1uEKgr:_ndKnfJ2DP_rGfnn4lz2ZRA7XyL1gHJukhohsxM0WDE", "expire_date": "2025-05-26T04:22:57.524Z"}}, {"model": "sessions.session", "pk": "wknzd2qqlf20x55tbsc1m3rzrtiu70y4", "fields": {"session_data": "e30:1uFYft:lCWVw9NpN5fj8p8wk8_fbuymfw438nNLKcRdbsquh_c", "expire_date": "2025-05-16T13:31:01.422Z"}}, {"model": "sessions.session", "pk": "wli2dobslmxdlx5btzq712ajy5c4foiw", "fields": {"session_data": "e30:1uFRJB:ilAtwg5F-05XWE7kdW7DjWiCRw9UiSEEO01kFY1udLQ", "expire_date": "2025-05-16T05:39:05.733Z"}}, {"model": "sessions.session", "pk": "wz6066qhd0iabblwgdqn0zatwjckvwc7", "fields": {"session_data": "e30:1uHTZi:vjuHWktzxH40x0MC6GlqabVEe9idTFFqE7gc8MQTP7Q", "expire_date": "2025-05-21T20:28:34.156Z"}}, {"model": "sessions.session", "pk": "x484japb1p9rjhmznv0hlqym5pdud2wd", "fields": {"session_data": "e30:1uFYdk:uv5VlKuvxUMBDSCWPfbaMSxB3GX2E2NfurKavXDbE1c", "expire_date": "2025-05-16T13:28:48.030Z"}}, {"model": "sessions.session", "pk": "x87yioph0c85o87abrih0e2qvj2u36wz", "fields": {"session_data": "e30:1uGEGT:iVl_RudEogHeeFZyN5PCf1k1jkoUmmVKGbU3tCJZk54", "expire_date": "2025-05-18T09:55:33.606Z"}}, {"model": "sessions.session", "pk": "xb5bpaj4egyhkd37kq56ened44813psq", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiRVptb2NrSXBwUERyOUZuOUtIdlNRSmZoTGZvM2owY0wifQ:1uFS9i:wC39oMwLt9S4pTxt1adNrPey0cCi2_B7vyllJd7dgNU", "expire_date": "2025-05-16T06:33:22.183Z"}}, {"model": "sessions.session", "pk": "xqaz04mk91v3a3gqtbuz53n9hb5070lk", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFHfm:Jot-TGVodfrxknCJT_FxQGAkSuCfGlpkGYNaR1nVDsU", "expire_date": "2025-05-28T19:21:46.378Z"}}, {"model": "sessions.session", "pk": "xs77uebqrhfz87kk2m0p7s3syknmhjre", "fields": {"session_data": "e30:1uDxI9:fgPa_pa3y3EkbBODIicVFGxfTMc1HaeCikXI3sHpI5E", "expire_date": "2025-05-25T03:23:53.152Z"}}, {"model": "sessions.session", "pk": "xtrbkx2dvg6p9v03cke86j7ocg3rrglz", "fields": {"session_data": "e30:1uGwXD:V927kaD8JEVoH0NJB1YvbusTk6yRlHR2deXLhCQ6Ri8", "expire_date": "2025-05-20T09:11:47.958Z"}}, {"model": "sessions.session", "pk": "xtrjvvrh6o674nxkno24dc776hqmccvy", "fields": {"session_data": "e30:1uFYel:vFW2hzasiKNL1z7HV6BuU5Kfi4g1uP9UJYCq72XBqKc", "expire_date": "2025-05-16T13:29:51.886Z"}}, {"model": "sessions.session", "pk": "xybafbae2620lq460e4z4aka2l3zc5z9", "fields": {"session_data": "e30:1uFRj1:m8Tg9-rnphaav0TXJPKHtRDNs6-3RdXGaNAwHHsG6sU", "expire_date": "2025-05-16T06:05:47.924Z"}}, {"model": "sessions.session", "pk": "xyxl0l3pc8o79w8nx6kmyxk9wlxozbj5", "fields": {"session_data": "e30:1uFSur:mC_YJOT564b4zKnUklOeCotvT9lvhsauAuoGC15N9IM", "expire_date": "2025-05-16T07:22:05.578Z"}}, {"model": "sessions.session", "pk": "y9ooe6dlig6wri430hdlcxcpriwivdm4", "fields": {"session_data": "e30:1uFU2m:x6s252RkTzXO_tptqAJr4ggCBq5o5Z8CuPTawMDs_B4", "expire_date": "2025-05-16T08:34:20.955Z"}}, {"model": "sessions.session", "pk": "yipxjhf37srusqwad9tq7ihrgl2v8643", "fields": {"session_data": ".eJxVjssOwiAURP-FtSG8WsCle7-BXC5g6wNMoSbG-O-2SU10O2fmZF7EwdwGN9c4uTGQPRFk95t5wEvMKwhnyKdCseQ2jZ6uFbrRSo8lxOth6_4JBqjDstYKekQGivdeG4nIJe96ECiCSsiYkTxKZrVUzEbLvE4mpE6Dtwk7hWqRArbxER2W2x3y8_v2_QFwNz-3:1uFHwX:1bbQ7fmholjvEvQZIKGhZcB_GvFS1PGongUzdwX3dUk", "expire_date": "2025-05-28T19:39:05.393Z"}}, {"model": "sessions.session", "pk": "yomez96whnc2ood5mk0ussx9zf5xsmg8", "fields": {"session_data": "e30:1uDxI9:fgPa_pa3y3EkbBODIicVFGxfTMc1HaeCikXI3sHpI5E", "expire_date": "2025-05-25T03:23:53.510Z"}}, {"model": "sessions.session", "pk": "zaetosm1scukubr8wg6h9cvtr629p1we", "fields": {"session_data": "e30:1uFV83:17SdH0KygnJihtevtmUlcjHdeJx9tBUcmOAPzMnjVBc", "expire_date": "2025-05-16T09:43:51.245Z"}}, {"model": "sessions.session", "pk": "zigjc7kqp3ef38jyyhfozsx2vrcz7jiz", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiSVVaUnhQSWlZSnI2bjREd2RuTzNGVEF3SXU1V1VzcDUifQ:1uFSAf:Im-i1O2laApex1FjG7aQQ7YZwL1vKipmRC9pAuYAxVU", "expire_date": "2025-05-16T06:34:21.666Z"}}, {"model": "sessions.session", "pk": "ztntrvorxpqmbyl4z1bca69zjg91vvgz", "fields": {"session_data": "e30:1uFXMf:7y6f3-jlFuGLz-y7w6dE_F4nWCf4hAw9em-mNAeOZPM", "expire_date": "2025-05-16T12:07:05.542Z"}}, {"model": "sessions.session", "pk": "zw2352ydhy1uowt8hka9v7wrrb9jlk1u", "fields": {"session_data": "eyJfY3NyZnRva2VuIjoiV1lnMU03MkJEakM5SFVUZFFWWGZ4b2w1MFI5akxTcmQifQ:1uFPIf:8pHHBaEIwn_GzBZUTOU68C2FhtKSJTKr2GzC97noc4w", "expire_date": "2025-05-16T03:30:25.774Z"}}, {"model": "sessions.session", "pk": "zyuf6rz6bdqkm6gt59ar1n157ljq9pm5", "fields": {"session_data": "e30:1uFS27:Og-5zunEIfwXmDeB_I_0p7-pN6-CTssL7v5xbIlyR1g", "expire_date": "2025-05-16T06:25:31.956Z"}}, {"model": "sites.site", "pk": 1, "fields": {"domain": "example.com", "name": "example.com"}}, {"model": "impersonate.impersonationlog", "pk": 1, "fields": {"impersonator": 4, "impersonating": 12, "session_key": "e44dfb5320b829f8f6e62c5f8db5ae29272d2f8c", "session_started_at": "2025-05-13T09:01:00.500Z", "session_ended_at": "2025-05-13T09:01:05.824Z"}}, {"model": "impersonate.impersonationlog", "pk": 2, "fields": {"impersonator": 4, "impersonating": 12, "session_key": "d859742b079a84326f212469c81ebb951660e310", "session_started_at": "2025-05-13T09:01:09.462Z", "session_ended_at": "2025-05-13T09:01:12.295Z"}}, {"model": "game.gamesession", "pk": 11, "fields": {"user": 1, "session_id": null, "created_at": "2025-05-12T07:22:52.943Z", "updated_at": "2025-05-12T07:22:52.943Z", "company": 1, "team": "Engineering", "current_role": "applicant", "performance_score": 100, "challenges_completed": 1, "role_challenges_completed": 1, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 12, "fields": {"user": 2, "session_id": null, "created_at": "2025-05-12T07:44:38.010Z", "updated_at": "2025-05-15T05:42:35.445Z", "company": 2, "team": "", "current_role": "applicant", "performance_score": 0, "challenges_completed": 0, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 20, "fields": {"user": 6, "session_id": null, "created_at": "2025-05-12T09:49:06.272Z", "updated_at": "2025-05-13T03:38:31.120Z", "company": 1, "team": "", "current_role": "accounts_receivable_associate", "performance_score": 190, "challenges_completed": 19, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[\"applicant\", \"junior_assistant\", \"sales_associate\", \"marketing_associate\", \"service_associate\", \"production_associate\", \"facilities_associate\"]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 21, "fields": {"user": 5, "session_id": null, "created_at": "2025-05-12T09:49:06.389Z", "updated_at": "2025-05-13T03:38:31.412Z", "company": 1, "team": "", "current_role": "coo", "performance_score": 610, "challenges_completed": 61, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[\"applicant\", \"junior_assistant\", \"sales_associate\", \"marketing_associate\", \"service_associate\", \"production_associate\", \"facilities_associate\", \"accounts_receivable_associate\", \"accounts_payable_associate\", \"hr_coordinator\", \"sales_manager\", \"advertising_manager\", \"service_manager\", \"production_manager\", \"facilities_manager\", \"accounts_receivable_manager\", \"accounts_payable_manager\", \"hr_manager\", \"vp_marketing\", \"vp_operations\", \"vp_finance\"]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 22, "fields": {"user": 7, "session_id": null, "created_at": "2025-05-12T09:49:06.660Z", "updated_at": "2025-05-13T03:38:31.434Z", "company": 1, "team": "", "current_role": "shareholders", "performance_score": 670, "challenges_completed": 67, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[\"applicant\", \"junior_assistant\", \"sales_associate\", \"marketing_associate\", \"service_associate\", \"production_associate\", \"facilities_associate\", \"accounts_receivable_associate\", \"accounts_payable_associate\", \"hr_coordinator\", \"sales_manager\", \"advertising_manager\", \"service_manager\", \"production_manager\", \"facilities_manager\", \"accounts_receivable_manager\", \"accounts_payable_manager\", \"hr_manager\", \"vp_marketing\", \"vp_operations\", \"vp_finance\", \"coo\", \"ceo\"]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 23, "fields": {"user": 8, "session_id": null, "created_at": "2025-05-12T19:32:28.640Z", "updated_at": "2025-05-12T21:07:01.666Z", "company": null, "team": "", "current_role": "applicant", "performance_score": 0, "challenges_completed": 0, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": false, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 24, "fields": {"user": 4, "session_id": null, "created_at": "2025-05-12T19:38:19.454Z", "updated_at": "2025-05-13T07:53:13.933Z", "company": null, "team": "", "current_role": "junior_assistant", "performance_score": 20, "challenges_completed": 1, "role_challenges_completed": 0, "game_completed": false, "current_manager": "manager", "current_task": "job_analysis", "completed_roles": "[\"applicant\"]", "first_task_pending": false, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 25, "fields": {"user": 9, "session_id": null, "created_at": "2025-05-12T21:11:49.441Z", "updated_at": "2025-05-13T06:16:02.006Z", "company": 2, "team": "", "current_role": "junior_assistant", "performance_score": 20, "challenges_completed": 1, "role_challenges_completed": 0, "game_completed": false, "current_manager": "manager", "current_task": "job_analysis", "completed_roles": "[\"applicant\"]", "first_task_pending": false, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 26, "fields": {"user": 10, "session_id": null, "created_at": "2025-05-12T21:45:51.955Z", "updated_at": "2025-05-13T08:06:42.956Z", "company": 2, "team": "", "current_role": "applicant", "performance_score": 0, "challenges_completed": 0, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 27, "fields": {"user": 11, "session_id": null, "created_at": "2025-05-13T02:19:41.360Z", "updated_at": "2025-05-13T02:20:34.806Z", "company": 2, "team": "", "current_role": "junior_assistant", "performance_score": 20, "challenges_completed": 1, "role_challenges_completed": 0, "game_completed": false, "current_manager": "manager", "current_task": "job_analysis", "completed_roles": "[\"applicant\"]", "first_task_pending": false, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 29, "fields": {"user": null, "session_id": "a4vdqq2k7ulqa2zkzx1fpb1fbl5lk01d", "created_at": "2025-05-14T16:02:52.716Z", "updated_at": "2025-05-14T16:02:52.716Z", "company": null, "team": "", "current_role": "applicant", "performance_score": 0, "challenges_completed": 0, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 30, "fields": {"user": null, "session_id": "6tyz6da4zp5ildu4efgh3pr2chesl2s7", "created_at": "2025-05-14T16:02:53.124Z", "updated_at": "2025-05-14T16:02:53.124Z", "company": null, "team": "", "current_role": "applicant", "performance_score": 0, "challenges_completed": 0, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 31, "fields": {"user": 1, "session_id": null, "created_at": "2025-05-15T03:27:02.536Z", "updated_at": "2025-05-15T03:27:02.536Z", "company": null, "team": "", "current_role": "senior_manager", "performance_score": 95, "challenges_completed": 10, "role_challenges_completed": 3, "game_completed": true, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[\"applicant\", \"junior_assistant\", \"assistant\", \"senior_assistant\", \"manager\", \"senior_manager\"]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 32, "fields": {"user": 1, "session_id": null, "created_at": "2025-05-15T03:29:04.064Z", "updated_at": "2025-05-15T03:29:04.064Z", "company": null, "team": "", "current_role": "senior_manager", "performance_score": 95, "challenges_completed": 10, "role_challenges_completed": 3, "game_completed": true, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[\"applicant\", \"junior_assistant\", \"assistant\", \"senior_assistant\", \"manager\", \"senior_manager\"]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 34, "fields": {"user": null, "session_id": "207lxtojmjcx786ktjhj7eg6osjcfqr1", "created_at": "2025-05-15T05:38:03.592Z", "updated_at": "2025-05-15T05:38:03.809Z", "company": null, "team": "", "current_role": "applicant", "performance_score": 0, "challenges_completed": 0, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 35, "fields": {"user": null, "session_id": "sigmj6ba6epgljgqv7xqo86n6vo05v86", "created_at": "2025-05-15T08:39:36.500Z", "updated_at": "2025-05-15T08:39:38.709Z", "company": null, "team": "", "current_role": "applicant", "performance_score": 0, "challenges_completed": 0, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 36, "fields": {"user": null, "session_id": "su80q6rqzb3untl3fvzc1jx07izfxi14", "created_at": "2025-05-16T07:39:21.760Z", "updated_at": "2025-05-16T07:41:49.217Z", "company": null, "team": "", "current_role": "applicant", "performance_score": 0, "challenges_completed": 0, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.gamesession", "pk": 37, "fields": {"user": null, "session_id": "tybup9sl2ixcihn856x7ikiqw1ztygnm", "created_at": "2025-05-16T07:39:28.252Z", "updated_at": "2025-05-16T07:39:30.769Z", "company": null, "team": "", "current_role": "applicant", "performance_score": 0, "challenges_completed": 0, "role_challenges_completed": 0, "game_completed": false, "current_manager": "hr", "current_task": "cover_letter", "completed_roles": "[]", "first_task_pending": true, "next_task_pending": false}}, {"model": "game.message", "pk": 935, "fields": {"game_session": 20, "message_id": "683adc85-f9fc-46fc-9211-a4c4bb11b93b", "sender": "hr", "text": "For your first challenge, I need you to respond to this job advertisement for our Junior Assistant role:\n\n**JOB ADVERTISEMENT**\n\n**Junior Assistant - Rwenzori Innovations Ltd.**\n\n**About Us:**\nRwenzori Innovations Ltd. is a leading innovator in the solar solutions industry, creating cutting-edge renewable energy products for businesses throughout Uganda and East Africa. With a team of dedicated professionals, we pride ourselves on quality, innovation, and exceptional customer service.\n\n**Role Overview:**\nWe are seeking a detail-oriented Junior Assistant to join our dynamic team. The ideal candidate will provide administrative support to various departments while developing professional skills in a fast-paced environment.\n\n**Key Responsibilities:**\n- Assist with daily administrative tasks and office operations\n- Coordinate meetings and manage calendars\n- Prepare and organize documents and presentations\n- Respond to emails and phone inquiries\n- Support team projects and initiatives\n- Maintain organized filing systems\n\n**Requirements:**\n- Excellent communication skills (written and verbal)\n- Strong attention to detail and organizational abilities\n- Proficiency in Microsoft Office Suite\n- Ability to multitask and prioritize effectively\n- Problem-solving mindset and proactive attitude\n- Bachelor's degree preferred but not required\n\n**Benefits:**\n- Competitive salary\n- Health and dental insurance\n- Paid time off\n- Professional development opportunities\n- Collaborative work environment\n\n**Location:** Kampala Business District\n\n**Type:** Full-time\n\nPlease draft a cover letter addressing your qualifications for this position. This is important because it demonstrates your communication skills which are essential for this role. If your application is strong, you'll move forward in our hiring process!", "html": "<p>For your first challenge, I need you to respond to this job advertisement for our Junior Assistant role:\n\n**JOB ADVERTISEMENT**\n\n**Junior Assistant - Rwenzori Innovations Ltd.**\n\n**About Us:**\nRwenzori Innovations Ltd. is a leading innovator in the solar solutions industry, creating cutting-edge renewable energy products for businesses throughout Uganda and East Africa. With a team of dedicated professionals, we pride ourselves on quality, innovation, and exceptional customer service.\n\n**Role Overview:**\nWe are seeking a detail-oriented Junior Assistant to join our dynamic team. The ideal candidate will provide administrative support to various departments while developing professional skills in a fast-paced environment.\n\n**Key Responsibilities:**\n- Assist with daily administrative tasks and office operations\n- Coordinate meetings and manage calendars\n- Prepare and organize documents and presentations\n- Respond to emails and phone inquiries\n- Support team projects and initiatives\n- Maintain organized filing systems\n\n**Requirements:**\n- Excellent communication skills (written and verbal)\n- Strong attention to detail and organizational abilities\n- Proficiency in Microsoft Office Suite\n- Ability to multitask and prioritize effectively\n- Problem-solving mindset and proactive attitude\n- Bachelor's degree preferred but not required\n\n**Benefits:**\n- Competitive salary\n- Health and dental insurance\n- Paid time off\n- Professional development opportunities\n- Collaborative work environment\n\n**Location:** Kampala Business District\n\n**Type:** Full-time\n\nPlease draft a cover letter addressing your qualifications for this position. This is important because it demonstrates your communication skills which are essential for this role. If your application is strong, you'll move forward in our hiring process!</p>", "timestamp": "2025-05-12T09:49:06.320Z", "is_challenge": true, "is_markdown": true, "is_promotion": false, "task_id": "cover_letter"}}, {"model": "game.message", "pk": 936, "fields": {"game_session": 20, "message_id": "2d077b32-e56b-4802-8e2c-c889fc7fdfb6", "sender": "user", "text": "User response to cover_letter", "html": "<p>User response to cover_letter</p>", "timestamp": "2025-05-12T09:49:06.339Z", "is_challenge": false, "is_markdown": true, "is_promotion": false, "task_id": null}}, {"model": "game.message", "pk": 937, "fields": {"game_session": 20, "message_id": "bad9da0e-3d3b-4688-b4bb-b633b106fa21", "sender": "hr", "text": "Good job on cover_letter!", "html": "<p>Good job on cover_letter!</p>", "timestamp": "2025-05-12T09:49:06.350Z", "is_challenge": false, "is_markdown": true, "is_promotion": false, "task_id": null}}, {"model": "game.message", "pk": 938, "fields": {"game_session": 21, "message_id": "34661c0b-8ecc-4d70-aca0-63b4ee21b5ec", "sender": "hr", "text": "For your first challenge, I need you to respond to this job advertisement for our Junior Assistant role:\n\n**JOB ADVERTISEMENT**\n\n**Junior Assistant - Rwenzori Innovations Ltd.**\n\n**About Us:**\nRwenzori Innovations Ltd. is a leading innovator in the solar solutions industry, creating cutting-edge renewable energy products for businesses throughout Uganda and East Africa. With a team of dedicated professionals, we pride ourselves on quality, innovation, and exceptional customer service.\n\n**Role Overview:**\nWe are seeking a detail-oriented Junior Assistant to join our dynamic team. The ideal candidate will provide administrative support to various departments while developing professional skills in a fast-paced environment.\n\n**Key Responsibilities:**\n- Assist with daily administrative tasks and office operations\n- Coordinate meetings and manage calendars\n- Prepare and organize documents and presentations\n- Respond to emails and phone inquiries\n- Support team projects and initiatives\n- Maintain organized filing systems\n\n**Requirements:**\n- Excellent communication skills (written and verbal)\n- Strong attention to detail and organizational abilities\n- Proficiency in Microsoft Office Suite\n- Ability to multitask and prioritize effectively\n- Problem-solving mindset and proactive attitude\n- Bachelor's degree preferred but not required\n\n**Benefits:**\n- Competitive salary\n- Health and dental insurance\n- Paid time off\n- Professional development opportunities\n- Collaborative work environment\n\n**Location:** Kampala Business District\n\n**Type:** Full-time\n\nPlease draft a cover letter addressing your qualifications for this position. This is important because it demonstrates your communication skills which are essential for this role. If your application is strong, you'll move forward in our hiring process!", "html": "<p>For your first challenge, I need you to respond to this job advertisement for our Junior Assistant role:\n\n**JOB ADVERTISEMENT**\n\n**Junior Assistant - Rwenzori Innovations Ltd.**\n\n**About Us:**\nRwenzori Innovations Ltd. is a leading innovator in the solar solutions industry, creating cutting-edge renewable energy products for businesses throughout Uganda and East Africa. With a team of dedicated professionals, we pride ourselves on quality, innovation, and exceptional customer service.\n\n**Role Overview:**\nWe are seeking a detail-oriented Junior Assistant to join our dynamic team. The ideal candidate will provide administrative support to various departments while developing professional skills in a fast-paced environment.\n\n**Key Responsibilities:**\n- Assist with daily administrative tasks and office operations\n- Coordinate meetings and manage calendars\n- Prepare and organize documents and presentations\n- Respond to emails and phone inquiries\n- Support team projects and initiatives\n- Maintain organized filing systems\n\n**Requirements:**\n- Excellent communication skills (written and verbal)\n- Strong attention to detail and organizational abilities\n- Proficiency in Microsoft Office Suite\n- Ability to multitask and prioritize effectively\n- Problem-solving mindset and proactive attitude\n- Bachelor's degree preferred but not required\n\n**Benefits:**\n- Competitive salary\n- Health and dental insurance\n- Paid time off\n- Professional development opportunities\n- Collaborative work environment\n\n**Location:** Kampala Business District\n\n**Type:** Full-time\n\nPlease draft a cover letter addressing your qualifications for this position. This is important because it demonstrates your communication skills which are essential for this role. If your application is strong, you'll move forward in our hiring process!</p>", "timestamp": "2025-05-12T09:49:06.417Z", "is_challenge": true, "is_markdown": true, "is_promotion": false, "task_id": "cover_letter"}}, {"model": "game.message", "pk": 939, "fields": {"game_session": 21, "message_id": "3d854486-a273-4f8e-8a69-2858fa1eb1ec", "sender": "user", "text": "User response to cover_letter", "html": "<p>User response to cover_letter</p>", "timestamp": "2025-05-12T09:49:06.429Z", "is_challenge": false, "is_markdown": true, "is_promotion": false, "task_id": null}}, {"model": "game.message", "pk": 940, "fields": {"game_session": 21, "message_id": "da2f9e70-e247-4f8f-adfd-68b69d90a0f3", "sender": "hr", "text": "Good job on cover_letter!", "html": "<p>Good job on cover_letter!</p>", "timestamp": "2025-05-12T09:49:06.439Z", "is_challenge": false, "is_markdown": true, "is_promotion": false, "task_id": null}}, {"model": "game.message", "pk": 941, "fields": {"game_session": 21, "message_id": "b37860d0-ac94-4f6c-b77d-a06746edadb6", "sender": "manager", "text": 