#!/usr/bin/env python
"""
Test script to check LLM functionality.
"""
import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

def test_llm_response_generator():
    """Test the LLM response generator."""
    print("Testing LLM response generator...")

    try:
        from game.llm_response_generator_openai_format import generate_response_with_llm

        # Test with a simple prompt
        prompt = "Please write a cover letter for a junior assistant position"
        task_id = "cover_letter"

        print(f"Calling generate_response_with_llm with prompt: {prompt[:50]}...")

        response = generate_response_with_llm(prompt, task_id=task_id, purpose="preview")

        print(f"Response received: {response[:100]}...")
        print("LLM response generator test PASSED")

    except Exception as e:
        print(f"LLM response generator test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()

def test_guideline_evaluation():
    """Test the guideline evaluation."""
    print("\nTesting guideline evaluation...")

    try:
        from game.guideline_evaluation import call_llm_api

        # Test with a simple prompt
        prompt = "Evaluate this text: Hello world"

        print(f"Calling call_llm_api with prompt: {prompt}")

        response = call_llm_api(prompt)

        if isinstance(response, dict):
            print(f"Response received: {str(response)[:100]}...")
        else:
            print(f"Response received: {response[:100] if response else 'None'}...")
        print("Guideline evaluation test PASSED")

    except Exception as e:
        print(f"Guideline evaluation test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()

def test_llm_feedback_generator():
    """Test the LLM feedback generator."""
    print("\nTesting LLM feedback generator...")

    try:
        from game.llm_feedback_generator import generate_manager_feedback

        # Test with simple parameters
        response = "This is a test response"
        task_id = "cover_letter"
        manager_name = "Test Manager"
        performance_metrics = {
            'overall_score': 85,
            'grade': 'good',
            'feedback_details': ['Good structure', 'Clear communication']
        }

        print(f"Calling generate_manager_feedback...")

        feedback = generate_manager_feedback(response, task_id, manager_name, performance_metrics)

        print(f"Feedback received: {feedback[:100] if feedback else 'None'}...")
        print("LLM feedback generator test PASSED")

    except Exception as e:
        print(f"LLM feedback generator test FAILED: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting LLM functionality tests...\n")

    test_llm_response_generator()
    test_guideline_evaluation()
    test_llm_feedback_generator()

    print("\nAll tests completed!")
