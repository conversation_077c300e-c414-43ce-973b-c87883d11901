<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Logo Upload</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body>
    <div class="container py-4">
        <h1 class="h2 mb-4">Test Logo Upload</h1>
<div class="card">
    <div class="card-body">
        <h5>Current Logo</h5>
        <div class="mb-3">
            {% with logo_url=assistant.get_logo_url %}
                {% if logo_url %}
                    <div class="mb-2">
                        <img src="{{ logo_url }}?t={{ assistant.id }}" alt="{{ assistant.name }} logo" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                    </div>
                    <div class="small text-muted mb-2">
                        Logo URL: {{ logo_url }}
                    </div>
                {% else %}
                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="width: 200px; height: 200px;">
                        <i class="bi bi-robot text-muted fs-1"></i>
                    </div>
                    <div class="small text-muted mt-2">
                        No logo available
                    </div>
                {% endif %}
            {% endwith %}

            <div class="mt-3 small">
                <strong>Debug Info:</strong>
                <ul class="list-unstyled">
                    <li>Assistant ID: {{ assistant.id }}</li>
                    <li>Logo field: {% if assistant.logo %}{{ assistant.logo.name }}{% else %}None{% endif %}</li>
                    <li>Logo URL method: {% if assistant.logo_url %}{{ assistant.logo_url }}{% else %}None{% endif %}</li>
                    <li>Get Logo URL method: {% if assistant.get_logo_url %}{{ assistant.get_logo_url }}{% else %}None{% endif %}</li>
                    <li>Company: {{ assistant.company.name }}</li>
                    <li>Company Logo: {% if assistant.company.info.logo %}{{ assistant.company.info.logo.name }}{% else %}None{% endif %}</li>
                </ul>
            </div>

            <div class="mt-3">
                <a href="{% url 'assistants:check_media_dirs' %}" class="btn btn-sm btn-outline-secondary" target="_blank">Check Media Directories</a>
            </div>
        </div>

        <h5>Upload New Logo</h5>
        <form method="post" enctype="multipart/form-data" id="logo-form">
            {% csrf_token %}
            <div class="mb-3">
                <label for="logo" class="form-label">Logo</label>
                <input type="file" name="logo" id="logo" class="form-control" accept="image/*">
            </div>
            <button type="submit" class="btn btn-primary">Upload Logo</button>
        </form>

        <div id="result" class="mt-3"></div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('logo-form');
    const resultDiv = document.getElementById('result');

    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Log form data for debugging
        console.log('Form is being submitted');
        console.log('Form contains logo file:', formData.has('logo'));

        fetch(window.location.href, {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);

            if (data.status === 'success') {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        ${data.message}
                    </div>
                    <div class="mt-3">
                        <img src="${data.logo_url}?t=${new Date().getTime()}" alt="Uploaded logo" class="img-thumbnail" style="max-width: 200px; max-height: 200px;">
                    </div>
                    <div class="small text-muted mt-2">
                        <strong>Debug Info:</strong><br>
                        Logo URL: ${data.logo_url}<br>
                        Timestamp: ${new Date().toISOString()}
                    </div>
                `;

                // Reload the page after 3 seconds to show the new logo
                setTimeout(() => {
                    window.location.href = window.location.pathname + '?t=' + new Date().getTime();
                }, 3000);
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        ${data.message}
                    </div>
                    <div class="small text-muted mt-2">
                        <strong>Debug Info:</strong><br>
                        Error: ${data.message}<br>
                        Timestamp: ${new Date().toISOString()}
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    An error occurred while uploading the logo.
                </div>
                <div class="small text-muted mt-2">
                    <strong>Debug Info:</strong><br>
                    Error: ${error.message || 'Unknown error'}<br>
                    Timestamp: ${new Date().toISOString()}
                </div>
            `;
        });
    });
});
</script>
    </div>
</body>
</html>
