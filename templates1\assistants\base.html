{% extends 'base/layout.html' %}
{% load static %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-lg-3 col-xl-2 d-none d-lg-block sidebar">
            <div class="position-sticky" style="top: 5rem;">
                <!-- Company Info -->
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-body text-center">
                        {# Use company.info.logo and apply consistent styling #}
                        {% if company.info.logo and company.info.logo.url %}
                            <img src="{{ company.info.logo.url }}"
                                 alt="{{ company.name }} Logo"
                                 class="img-thumbnail rounded-circle mb-3" {# Use thumbnail and circle #}
                                 style="width: 80px; height: 80px; object-fit: cover;"> {# Fixed size #}
                        {% else %}
                            {# Fallback icon #}
                            <div class="mb-3"> {# Add margin bottom like the image case #}
                                <i class="bi bi-building display-4 text-muted"></i> {# Adjusted size #}
                            </div>
                        {% endif %}
                        <h5 class="card-title mb-0">{{ company.name }}</h5>
                    </div>
                </div>

                <!-- Navigation -->
                <div class="nav flex-column">
                    <a href="{% url 'assistants:list' company.id %}"
                       class="nav-link {% if request.resolver_match.url_name == 'list' %}active{% endif %}">
                        <i class="bi bi-grid me-2"></i>
                        All Assistants
                    </a>
                    <a href="{% url 'assistants:create' company.id %}"
                       class="nav-link {% if request.resolver_match.url_name == 'create' %}active{% endif %}">
                        <i class="bi bi-plus-circle me-2"></i>
                        {% if company.entity_type == 'community' %}
                        Create Community Assistant
                        {% else %}
                        Create Assistant
                        {% endif %}
                    </a>
                    {% if assistant %}
                        <div class="dropdown-divider my-3"></div>
                        <h6 class="sidebar-heading px-3 mt-4 mb-1 text-muted text-uppercase">
                            Current Assistant
                        </h6>
                        {# Add Chat link here #}
                        <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}"
                           class="nav-link {% if request.resolver_match.url_name == 'assistant_chat' %}active{% endif %}">
                           <i class="bi bi-chat-dots me-2"></i>
                           Chat
                        </a>
                        {% if assistant.is_public %}
                        <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}"
                           class="nav-link" target="_blank"> {# Open in new tab #}
                           <i class="bi bi-box-arrow-up-right me-2"></i>
                           Public Chat View
                        </a>
                        {% endif %}
                        <a href="{% url 'assistants:history' company.id assistant.id %}"
                           class="nav-link {% if request.resolver_match.url_name == 'history' %}active{% endif %}">
                            <i class="bi bi-clock-history me-2"></i>
                            Chat History
                        </a>
                        <a href="{% url 'assistants:train' company.id assistant.id %}"
                           class="nav-link {% if request.resolver_match.url_name == 'train' %}active{% endif %}">
                            <i class="bi bi-arrow-up-circle me-2"></i>
                            Train Assistant
                        </a>
                        <a href="{% url 'assistants:analytics' company.id assistant.id %}"
                           class="nav-link {% if request.resolver_match.url_name == 'analytics' %}active{% endif %}">
                            <i class="bi bi-graph-up me-2"></i>
                            Analytics
                        </a>
                        <a href="{% url 'assistants:usage' company.id assistant.id %}"
                           class="nav-link {% if request.resolver_match.url_name == 'usage' %}active{% endif %}">
                            <i class="bi bi-speedometer2 me-2"></i>
                            Usage Stats
                        </a>
                        {% if user == company.owner or user.is_staff %}
                            <a href="{% url 'assistants:update' company.id assistant.id %}"
                               class="nav-link {% if request.resolver_match.url_name == 'update' %}active{% endif %}">
                                <i class="bi bi-gear me-2"></i>
                                Settings
                            </a>
                        {% endif %}
                    {% endif %}
                </div>

                <!-- Quick Stats -->
                {% if assistant %}
                    <div class="card border-0 shadow-sm mt-4">
                        <div class="card-body">
                            <h6 class="card-subtitle mb-3 text-muted">Quick Stats</h6>
                            <div class="d-flex align-items-center mb-2">
                                <div class="flex-grow-1">Total Interactions</div>
                                <div class="badge bg-light text-dark">{{ assistant.total_interactions }}</div>
                            </div>
                            {% if assistant.average_rating %}
                                <div class="d-flex align-items-center mb-2">
                                    <div class="flex-grow-1">Average Rating</div>
                                    <div class="badge bg-light text-dark">
                                        <i class="bi bi-star-fill text-warning me-1"></i>
                                        {{ assistant.average_rating|floatformat:1 }}
                                    </div>
                                </div>
                            {% endif %}
                            {% if assistant.last_trained %}
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">Last Trained</div>
                                    <div class="badge bg-light text-dark">
                                        {{ assistant.last_trained|timesince }} ago
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Main Content -->
        <main class="col-lg-9 col-xl-10 ms-sm-auto px-md-4 py-4">
            <!-- Header -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                <div>
                    {% block page_header %}{% endblock %}
                </div>
                <div class="btn-toolbar">
                    {% block page_actions %}{% endblock %}
                </div>
            </div>

            <!-- Content -->
            {% block main_content %}{% endblock %}
        </main>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: .5rem 1rem;
}

.sidebar .nav-link.active {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, .1);
}

.sidebar .nav-link:hover {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, .05);
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

.dropdown-divider {
    border-color: rgba(0, 0, 0, .1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip);
    });

    // Handle responsive sidebar
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        const toggleSidebar = document.createElement('button');
        toggleSidebar.className = 'btn btn-link d-lg-none position-fixed';
        toggleSidebar.style.top = '1rem';
        toggleSidebar.style.left = '1rem';
        toggleSidebar.style.zIndex = '1031';
        toggleSidebar.innerHTML = '<i class="bi bi-list fs-4"></i>';

        document.body.appendChild(toggleSidebar);

        toggleSidebar.addEventListener('click', () => {
            sidebar.classList.toggle('d-none');
        });
    }
});
</script>
{# TinyMCE init moved to assistant_form.html template #}
{% endblock %}
