from io import StringIO
from django.test import TestCase
from django.core.management import call_command
from django.utils import timezone
from django.contrib.auth.models import User
from accounts.models import Company, TeamInvitation
from datetime import timedelta

class ManageInvitationsCommandTest(TestCase):
    def setUp(self):
        # Create test user and company
        self.user = User.objects.create_user('testuser', '<EMAIL>', 'password')
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )
        
        # Create invitations with different states
        now = timezone.now()
        
        # Active invitation
        self.active_invitation = TeamInvitation.objects.create(
            company=self.company,
            invited_by=self.user,
            email='<EMAIL>',
            expires_at=now + timedelta(days=7)
        )
        
        # Expired pending invitation
        self.expired_pending = TeamInvitation.objects.create(
            company=self.company,
            invited_by=self.user,
            email='<EMAIL>',
            expires_at=now - timedelta(days=1),
            status='pending'
        )
        
        # Old declined invitation
        self.old_declined = TeamInvitation.objects.create(
            company=self.company,
            invited_by=self.user,
            email='<EMAIL>',
            expires_at=now - timedelta(days=40),
            status='declined',
            created_at=now - timedelta(days=40)
        )
        
        # About to expire invitation
        self.expiring_soon = TeamInvitation.objects.create(
            company=self.company,
            invited_by=self.user,
            email='<EMAIL>',
            expires_at=now + timedelta(days=3)
        )

    def test_clean_expired_invitations(self):
        """Test cleaning up expired invitations"""
        out = StringIO()
        call_command('manage_invitations', '--clean', stdout=out)
        
        # Check that expired pending was marked as expired
        self.expired_pending.refresh_from_db()
        self.assertEqual(self.expired_pending.status, 'expired')
        
        # Check that old declined was deleted
        with self.assertRaises(TeamInvitation.DoesNotExist):
            self.old_declined.refresh_from_db()
        
        # Check that active invitation wasn't affected
        self.active_invitation.refresh_from_db()
        self.assertEqual(self.active_invitation.status, 'pending')
        
        # Check output
        output = out.getvalue()
        self.assertIn('Found 2 invitations to clean up', output)

    def test_send_reminders(self):
        """Test sending reminders for expiring invitations"""
        out = StringIO()
        call_command('manage_invitations', '--remind', '--days=3', stdout=out)
        
        # Check that reminder was recorded in metadata
        self.expiring_soon.refresh_from_db()
        self.assertIn('reminder_sent', self.expiring_soon.metadata)
        
        # Check output
        output = out.getvalue()
        self.assertIn('Found 1 invitations needing reminders', output)
        self.assertIn('Sent 1 reminders', output)

    def test_dry_run(self):
        """Test dry run mode"""
        out = StringIO()
        call_command('manage_invitations', '--clean', '--remind', '--dry-run', stdout=out)
        
        # Check that no changes were made
        self.expired_pending.refresh_from_db()
        self.assertEqual(self.expired_pending.status, 'pending')
        
        self.old_declined.refresh_from_db()  # Should still exist
        
        # Check output
        output = out.getvalue()
        self.assertIn('Dry run - no changes made', output)
        self.assertIn('Dry run - no reminders sent', output)

    def test_no_actions_error(self):
        """Test error when no actions specified"""
        with self.assertRaisesMessage(
            CommandError,
            "Please specify at least one action: --clean or --remind"
        ):
            call_command('manage_invitations')

    def test_reminder_cooldown(self):
        """Test that reminders respect the 24-hour cooldown"""
        # Send first reminder
        call_command('manage_invitations', '--remind', '--days=3')
        
        # Try to send another reminder immediately
        out = StringIO()
        call_command('manage_invitations', '--remind', '--days=3', stdout=out)
        
        # Check that no new reminders were sent
        output = out.getvalue()
        self.assertIn('Found 0 invitations needing reminders', output)

    def test_summary_output(self):
        """Test the summary output"""
        out = StringIO()
        call_command('manage_invitations', '--clean', stdout=out)
        
        output = out.getvalue()
        self.assertIn('Total pending invitations:', output)
        self.assertIn('Invitations expiring in next week:', output)

from django.core import mail
from django.test.utils import override_settings

class TestInvitationEmails(TestCase):
    def setUp(self):
        self.user = User.objects.create_user('testuser', '<EMAIL>', 'password')
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_invitation_email(self):
        """Test that invitation emails are sent correctly"""
        invitation = TeamInvitation.objects.create(
            company=self.company,
            invited_by=self.user,
            email='<EMAIL>',
            expires_at=timezone.now() + timedelta(days=7)
        )
        
        # Check that one email was sent
        self.assertEqual(len(mail.outbox), 1)
        
        # Check email content
        email = mail.outbox[0]
        self.assertEqual(email.to, ['<EMAIL>'])
        self.assertIn(self.company.name, email.subject)
        self.assertIn(self.user.get_full_name(), email.body)
        
        # Check that both HTML and text versions exist
        self.assertTrue(email.alternatives)  # HTML version exists
        self.assertTrue(email.body)  # Text version exists

    @override_settings(EMAIL_BACKEND='django.core.mail.backends.locmem.EmailBackend')
    def test_reminder_email(self):
        """Test that reminder emails are sent correctly"""
        invitation = TeamInvitation.objects.create(
            company=self.company,
            invited_by=self.user,
            email='<EMAIL>',
            expires_at=timezone.now() + timedelta(days=3)
        )
        
        mail.outbox = []  # Clear the outbox
        
        # Send reminder
        call_command('manage_invitations', '--remind', '--days=3')
        
        # Check that reminder was sent
        self.assertEqual(len(mail.outbox), 1)
        email = mail.outbox[0]
        self.assertIn('reminder', email.body.lower())
