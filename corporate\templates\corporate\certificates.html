{% extends 'corporate/base.html' %}

{% block title %}Certificates - Corporate Prompt Master{% endblock %}

{% block extra_css %}
<style>
    .certificate-preview {
        border: 1px solid #ddd;
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 5px;
    }

    .certificate-inner {
        background-color: white;
        border: 10px solid #4a86e8;
        padding: 20px;
        text-align: center;
    }

    .certificate-header h3 {
        color: #4a86e8;
        font-size: 24px;
        margin-bottom: 20px;
        text-transform: uppercase;
    }

    .certificate-name {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 15px;
        border-bottom: 1px solid #4a86e8;
        display: inline-block;
        padding-bottom: 5px;
    }

    .certificate-text, .certificate-role, .certificate-score, .certificate-date, .certificate-verification {
        margin-bottom: 10px;
    }

    .certificate-role, .certificate-score {
        font-weight: bold;
        color: #4a86e8;
    }

    .certificate-games {
        margin: 10px auto;
        padding: 8px;
        border: 2px solid #4a86e8;
        border-radius: 8px;
        background-color: rgba(74, 134, 232, 0.1);
        max-width: 90%;
    }

    .certificate-games-title {
        font-weight: bold;
        color: #4a86e8;
        margin-bottom: 5px;
        text-align: center;
        font-size: 0.9em;
    }

    .certificate-games-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
        text-align: center;
    }

    .certificate-games-list li {
        font-weight: bold;
        padding: 2px 0;
        font-size: 0.9em;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-3">Your Certificates</h2>

        {% if certificates %}
        <div class="row">
            {% for certificate in certificates %}
            <div class="col-md-6 mb-4">
                <div class="card shadow h-100">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">{{ certificate.title }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="certificate-preview mb-3">
                            <div class="certificate-inner">
                                <div class="certificate-header">
                                    <h3>Certificate of Completion</h3>
                                </div>
                                <div class="certificate-content">
                                    <p class="certificate-name">{{ user.get_full_name|default:user.username }}</p>
                                    <p class="certificate-text">{{ certificate.description }}</p>
                                    <p class="certificate-role">Highest Role: {{ certificate.highest_role|title }}</p>
                                    <p class="certificate-score">Final Score: {{ certificate.final_score }}</p>

                                    {% if certificate.get_completed_games %}
                                    <div class="certificate-games">
                                        <p class="certificate-games-title">Completed Games:</p>
                                        <ul class="certificate-games-list">
                                            {% for game in certificate.get_completed_games %}
                                            <li>{{ game }}</li>
                                            {% endfor %}
                                        </ul>
                                    </div>
                                    {% endif %}
                                    <p class="certificate-date">Issued on {{ certificate.issue_date|date:"F d, Y" }}</p>
                                    <p class="certificate-verification">Verification Code: {{ certificate.verification_code }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="certificate-details">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Issue Date:</strong> {{ certificate.issue_date|date:"M d, Y" }}</p>
                                    <p><strong>Final Score:</strong> {{ certificate.final_score }}</p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Highest Role:</strong> {{ certificate.highest_role|title }}</p>
                                    <p><strong>Verification Code:</strong> {{ certificate.verification_code }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'corporate:download_certificate' certificate.id %}" class="btn btn-primary">
                                <i class="fas fa-download me-2"></i>Download PDF
                            </a>
                            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#shareModal{{ certificate.id }}">
                                <i class="fas fa-share-alt me-2"></i>Share
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Share Modal -->
            <div class="modal fade" id="shareModal{{ certificate.id }}" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Share Certificate</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Share your certificate with others using the verification code or link below:</p>

                            <div class="mb-3">
                                <label class="form-label">Verification Code</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{{ certificate.verification_code }}" readonly>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" data-copy="{{ certificate.verification_code }}">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Verification Link</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{% url 'corporate:verify_certificate' %}?code={{ certificate.verification_code }}" readonly>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" data-copy="{% url 'corporate:verify_certificate' %}?code={{ certificate.verification_code }}">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <a href="mailto:?subject=My%20Prompt%20Engineering%20Certificate&body=I've%20completed%20the%20Corporate%20Prompt%20Master%20training!%0A%0AVerify%20my%20certificate%20using%20this%20code:%20{{ certificate.verification_code }}%0A%0AOr%20click%20this%20link:%20{% url 'corporate:verify_certificate' %}?code={{ certificate.verification_code }}" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i>Share via Email
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url={% url 'corporate:verify_certificate' %}?code={{ certificate.verification_code }}" target="_blank" class="btn btn-primary">
                                    <i class="fab fa-linkedin me-2"></i>Share on LinkedIn
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>You haven't earned any certificates yet. Complete a game to earn your first certificate!
        </div>

        <div class="text-center mt-4">
            <a href="{% url 'corporate:game_list' %}" class="btn btn-primary btn-lg">
                <i class="fas fa-gamepad me-2"></i>Start Playing
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Copy button functionality
        document.querySelectorAll('.copy-btn').forEach(button => {
            button.addEventListener('click', function() {
                const textToCopy = this.getAttribute('data-copy');
                navigator.clipboard.writeText(textToCopy).then(() => {
                    // Change button text temporarily
                    const originalHTML = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check"></i>';
                    setTimeout(() => {
                        this.innerHTML = originalHTML;
                    }, 2000);
                });
            });
        });
    });
</script>
{% endblock %}
