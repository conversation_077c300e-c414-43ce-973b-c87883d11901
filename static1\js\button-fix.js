/**
 * Button Fix JavaScript
 * This script fixes issues with buttons not working in the assistant interface
 * by using event delegation and ensuring proper event handling.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Button Fix: Initializing button fixes');
    
    // Use event delegation for all button clicks
    document.body.addEventListener('click', function(event) {
        // Find the closest button or element with a button role
        const button = event.target.closest('button, [role="button"], .btn, .nav-button, .list-group-item');
        
        if (!button) return; // Not a button click
        
        console.log('Button Fix: Button clicked:', button.id || button.className);
        
        // Handle different button types
        if (button.id === 'test-sidebar-toggle' || 
            button.id === 'test-sidebar-toggle-mobile' || 
            button.id === 'sidebar-toggle-inside') {
            
            console.log('Button Fix: Sidebar toggle button clicked');
            // Call the toggleSidebar function if it exists
            if (typeof toggleSidebar === 'function') {
                toggleSidebar(event);
                event.preventDefault();
                event.stopPropagation();
            } else if (typeof toggleSidebarDirect === 'function') {
                toggleSidebarDirect(button);
                event.preventDefault();
                event.stopPropagation();
            }
        }
        
        // Handle navigation buttons
        if (button.classList.contains('nav-button') || 
            (button.classList.contains('list-group-item') && button.hasAttribute('data-section-id'))) {
            
            const sectionId = button.getAttribute('data-section-id');
            if (!sectionId) return;
            
            console.log('Button Fix: Navigation button clicked for section:', sectionId);
            
            // Call the showContentById function if it exists
            if (typeof showContentById === 'function') {
                showContentById(sectionId);
                event.preventDefault();
                event.stopPropagation();
            }
            
            // Close sidebar on mobile after clicking a navigation item
            if (window.innerWidth < 768) {
                const sidebar = document.getElementById('chat-sidebar');
                const sidebarOverlay = document.getElementById('sidebar-overlay');
                
                if (sidebar && sidebar.classList.contains('active')) {
                    sidebar.classList.remove('active');
                    sidebar.style.left = '-350px';
                    
                    // Update toggle text
                    const toggleTexts = document.querySelectorAll('.toggle-text');
                    toggleTexts.forEach(el => { el.textContent = 'Menu'; });
                    
                    // Hide overlay
                    if (sidebarOverlay) {
                        sidebarOverlay.style.opacity = '0';
                        sidebarOverlay.style.pointerEvents = 'none';
                        setTimeout(() => { sidebarOverlay.style.display = 'none'; }, 300);
                    }
                }
            }
        }
    });
    
    // Fix z-index issues that might be making buttons unclickable
    fixZIndexIssues();
    
    // Ensure buttons are properly styled and visible
    fixButtonStyling();
    
    // Set up a MutationObserver to fix buttons that are added dynamically
    const observer = new MutationObserver(function(mutations) {
        fixZIndexIssues();
        fixButtonStyling();
    });
    
    // Start observing the document
    observer.observe(document.body, { 
        childList: true, 
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    });
    
    console.log('Button Fix: Initialization complete');
});

/**
 * Fix z-index issues that might be making buttons unclickable
 */
function fixZIndexIssues() {
    // Fix z-index for buttons in the assistant header
    const assistantHeader = document.querySelector('.general-assistant-header, .assistant-header');
    if (assistantHeader) {
        // Ensure the header has a reasonable z-index
        assistantHeader.style.zIndex = '1050';
        
        // Fix z-index for buttons in the header
        const headerButtons = assistantHeader.querySelectorAll('button, .btn');
        headerButtons.forEach(button => {
            button.style.position = 'relative';
            button.style.zIndex = '1051';
        });
    }
    
    // Fix z-index for sidebar
    const sidebar = document.getElementById('chat-sidebar');
    if (sidebar) {
        sidebar.style.zIndex = '1060';
        
        // Fix z-index for buttons in the sidebar
        const sidebarButtons = sidebar.querySelectorAll('button, .btn, .nav-button, .list-group-item');
        sidebarButtons.forEach(button => {
            button.style.position = 'relative';
            button.style.zIndex = '1061';
        });
    }
}

/**
 * Fix button styling to ensure they are properly visible and clickable
 */
function fixButtonStyling() {
    // Fix styling for all buttons
    const allButtons = document.querySelectorAll('button, .btn, .nav-button, .list-group-item[data-section-id]');
    allButtons.forEach(button => {
        // Ensure buttons have proper pointer cursor
        button.style.cursor = 'pointer';
        
        // Ensure buttons have proper pointer events
        button.style.pointerEvents = 'auto';
        
        // Ensure buttons are not hidden
        button.style.visibility = 'visible';
        button.style.display = button.style.display || 'inline-block';
        button.style.opacity = '1';
    });
    
    // Fix styling for navigation buttons specifically
    const navButtons = document.querySelectorAll('.nav-button, .list-group-item[data-section-id]');
    navButtons.forEach(button => {
        // Add a subtle hover effect
        button.addEventListener('mouseenter', function() {
            this.style.backgroundColor = this.style.backgroundColor || 'rgba(0, 123, 255, 0.1)';
            this.style.transform = 'translateY(-1px)';
            this.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });
}
