"""
Tests for the permission system.
"""
from django.test import TestCase
from django.contrib.auth.models import User, Group
from guardian.shortcuts import get_perms
from accounts.models import Company, Membership
from assistants.models import Assistant

class PermissionTestCase(TestCase):
    """Test case for permissions."""

    def setUp(self):
        """Set up test data."""
        # Create test users
        self.owner = User.objects.create_user(username='owner', password='password')
        self.admin = User.objects.create_user(username='admin', password='password')
        self.member = User.objects.create_user(username='member', password='password')
        self.viewer = User.objects.create_user(username='viewer', password='password')
        
        # Create test company
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.owner
        )
        
        # Create company information
        from accounts.models import CompanyInformation
        CompanyInformation.objects.create(
            company=self.company,
            mission="Test mission",
            description="Test description",
            website="https://example.com",
            contact_email="<EMAIL>"
        )
        
        # Add users to company with different roles
        # The owner is automatically added as a member by the signal
        
        # Add admin to company
        admin_membership = Membership.objects.create(user=self.admin, company=self.company)
        admin_group = Group.objects.get(name='Company Administrators')
        self.admin.groups.add(admin_group)
        
        # Add member to company
        member_membership = Membership.objects.create(user=self.member, company=self.company)
        member_group = Group.objects.get(name='Company Members')
        self.member.groups.add(member_group)
        
        # Add viewer to company
        viewer_membership = Membership.objects.create(user=self.viewer, company=self.company)
        viewer_group = Group.objects.get(name='Company Guests')
        self.viewer.groups.add(viewer_group)
        
        # Create test assistant
        self.assistant = Assistant.objects.create(
            name='Test Assistant',
            company=self.company,
            assistant_type=Assistant.TYPE_GENERAL,
            system_prompt='You are a helpful assistant.',
            is_public=False,
            is_active=True
        )

    def test_owner_company_permissions(self):
        """Test that the owner has the correct permissions on the company."""
        # Get all permissions for the owner on the company
        perms = get_perms(self.owner, self.company)
        
        # Check specific permissions
        self.assertTrue('change_company_settings' in perms)
        self.assertTrue('manage_billing' in perms)
        self.assertTrue('manage_directory_listing' in perms)
        self.assertTrue('manage_members' in perms)
        self.assertTrue('manage_invites_links' in perms)
        self.assertTrue('view_company_activity' in perms)
        self.assertTrue('delete_company_object' in perms)
        self.assertTrue('manage_company_assistants' in perms)
        
        # Check using has_perm
        self.assertTrue(self.owner.has_perm('accounts.change_company_settings', self.company))
        self.assertTrue(self.owner.has_perm('accounts.manage_billing', self.company))
        self.assertTrue(self.owner.has_perm('accounts.manage_directory_listing', self.company))
        self.assertTrue(self.owner.has_perm('accounts.manage_members', self.company))
        self.assertTrue(self.owner.has_perm('accounts.manage_invites_links', self.company))
        self.assertTrue(self.owner.has_perm('accounts.view_company_activity', self.company))
        self.assertTrue(self.owner.has_perm('accounts.delete_company_object', self.company))
        self.assertTrue(self.owner.has_perm('accounts.manage_company_assistants', self.company))

    def test_owner_assistant_permissions(self):
        """Test that the owner has the correct permissions on assistants."""
        # Get all permissions for the owner on the assistant
        perms = get_perms(self.owner, self.assistant)
        
        # Check specific permissions
        self.assertTrue('view_assistant' in perms)
        self.assertTrue('change_assistant' in perms)
        self.assertTrue('delete_assistant' in perms)
        self.assertTrue('view_assistant_usage' in perms)
        self.assertTrue('view_assistant_analytics' in perms)
        
        # Check using has_perm
        self.assertTrue(self.owner.has_perm('assistants.view_assistant', self.assistant))
        self.assertTrue(self.owner.has_perm('assistants.change_assistant', self.assistant))
        self.assertTrue(self.owner.has_perm('assistants.delete_assistant', self.assistant))
        self.assertTrue(self.owner.has_perm('assistants.view_assistant_usage', self.assistant))
        self.assertTrue(self.owner.has_perm('assistants.view_assistant_analytics', self.assistant))

    def test_admin_permissions(self):
        """Test that the admin has the correct permissions."""
        # Check company permissions
        self.assertFalse(self.admin.has_perm('accounts.change_company_settings', self.company))
        self.assertFalse(self.admin.has_perm('accounts.delete_company_object', self.company))
        self.assertTrue(self.admin.has_perm('accounts.manage_company_assistants', self.company))
        
        # Check assistant permissions
        self.assertTrue(self.admin.has_perm('assistants.view_assistant', self.assistant))
        self.assertTrue(self.admin.has_perm('assistants.change_assistant', self.assistant))
        self.assertTrue(self.admin.has_perm('assistants.delete_assistant', self.assistant))
        self.assertTrue(self.admin.has_perm('assistants.view_assistant_usage', self.assistant))
        self.assertTrue(self.admin.has_perm('assistants.view_assistant_analytics', self.assistant))

    def test_member_permissions(self):
        """Test that the member has the correct permissions."""
        # Check company permissions
        self.assertFalse(self.member.has_perm('accounts.change_company_settings', self.company))
        self.assertFalse(self.member.has_perm('accounts.delete_company_object', self.company))
        self.assertTrue(self.member.has_perm('accounts.manage_company_assistants', self.company))
        
        # Check assistant permissions
        self.assertTrue(self.member.has_perm('assistants.view_assistant', self.assistant))
        self.assertTrue(self.member.has_perm('assistants.change_assistant', self.assistant))
        self.assertTrue(self.member.has_perm('assistants.delete_assistant', self.assistant))
        self.assertTrue(self.member.has_perm('assistants.view_assistant_usage', self.assistant))
        self.assertTrue(self.member.has_perm('assistants.view_assistant_analytics', self.assistant))

    def test_viewer_permissions(self):
        """Test that the viewer has the correct permissions."""
        # Check company permissions
        self.assertFalse(self.viewer.has_perm('accounts.change_company_settings', self.company))
        self.assertFalse(self.viewer.has_perm('accounts.delete_company_object', self.company))
        self.assertFalse(self.viewer.has_perm('accounts.manage_company_assistants', self.company))
        
        # Check assistant permissions
        self.assertTrue(self.viewer.has_perm('assistants.view_assistant', self.assistant))
        self.assertFalse(self.viewer.has_perm('assistants.change_assistant', self.assistant))
        self.assertFalse(self.viewer.has_perm('assistants.delete_assistant', self.assistant))
        self.assertFalse(self.viewer.has_perm('assistants.view_assistant_usage', self.assistant))
        self.assertFalse(self.viewer.has_perm('assistants.view_assistant_analytics', self.assistant))

    def test_public_assistant_access(self):
        """Test that public assistants are accessible to all authenticated users."""
        # Create a public assistant
        public_assistant = Assistant.objects.create(
            name='Public Assistant',
            company=self.company,
            assistant_type=Assistant.TYPE_GENERAL,
            system_prompt='You are a helpful assistant.',
            is_public=True,
            is_active=True
        )
        
        # Create a user who is not part of the company
        outside_user = User.objects.create_user(username='outside', password='password')
        
        # Check that the outside user can view the public assistant
        # This requires checking the view logic, not just permissions
        from accounts.templatetags.permission_tags import check_assistant_view
        self.assertTrue(check_assistant_view(outside_user, public_assistant))
        
        # But cannot modify it
        self.assertFalse(outside_user.has_perm('assistants.change_assistant', public_assistant))
        self.assertFalse(outside_user.has_perm('assistants.delete_assistant', public_assistant))
