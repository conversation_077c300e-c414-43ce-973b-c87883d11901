/* 
 * User Detail Dark Background CSS
 * Makes user information backgrounds dark while keeping everything else as is
 */

/* Make user information table rows dark */
.table tr {
    background-color: #1e1e1e !important;
}

/* Make user information table cells dark */
.table th,
.table td {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
}

/* Make progress summary cards dark */
.card.bg-light {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
}

/* Make progress summary card bodies dark */
.card.bg-light .card-body {
    background-color: #1e1e1e !important;
}

/* Make progress summary text visible on dark background */
.card.bg-light .card-body h3 {
    color: #ffffff !important;
}

.card.bg-light .card-body p.text-muted {
    color: #cccccc !important;
}

/* Make sure table borders are visible */
.table {
    border-color: #333333 !important;
}

/* Make sure table headers stand out */
.table thead th {
    background-color: #0d6efd !important;
    color: #ffffff !important;
    border-color: #0a58ca !important;
}

/* Make sure badges are visible */
.badge {
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}
