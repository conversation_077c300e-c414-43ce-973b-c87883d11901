/**
 * Mobile Chat Interface Improvements
 * Enhanced mobile experience for the chat interface
 */

/* Base chat improvements for all screen sizes */
.chat-container {
  max-width: 1200px !important;
  margin: 0 auto !important;
  border-radius: 1rem !important;
  overflow: hidden !important;
}

.chat-box {
  min-height: 400px !important;
  padding-bottom: 80px !important; /* Space for the fixed input form */
}

#chat-form {
  border-radius: 1.5rem !important;
  transition: all 0.2s ease !important;
}

#message-input {
  border-radius: 1.5rem !important;
  padding: 0.75rem 1rem !important;
  font-size: 1rem !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  background-color: #ffffff !important;
}

#send-button {
  border-radius: 1.5rem !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
}

/* Mobile optimizations */
@media (max-width: 768px) {
  /* Full-width chat container on mobile */
  .chat-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
    border-left: none !important;
    border-right: none !important;
    box-shadow: none !important;
  }

  /* Optimize chat box */
  .chat-box {
    padding: 0.75rem !important;
    padding-bottom: 80px !important; /* Space for the fixed input form */
  }

  /* Fixed chat form at the bottom */
  #chat-form {
    position: fixed !important;
    bottom: 0 !important;
    left: 0 !important;
    right: 0 !important;
    padding: 0.75rem 1rem !important;
    background-color: #ffffff !important;
    border-top: 1px solid rgba(0, 0, 0, 0.1) !important;
    border-radius: 0 !important;
    margin: 0 !important;
    z-index: 1000 !important;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1) !important;
    display: flex !important;
    align-items: center !important;
  }

  /* Optimize input field */
  #message-input {
    flex-grow: 1 !important;
    font-size: 16px !important; /* Prevents iOS zoom on focus */
    padding: 0.5rem 0.75rem !important;
    margin-right: 0.5rem !important;
    border-radius: 1.25rem !important;
  }

  /* Optimize send button */
  #send-button {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.9rem !important;
    border-radius: 1.25rem !important;
    min-width: 60px !important;
  }

  /* Optimize chat messages */
  .message {
    margin: 0.5rem 0 !important;
    max-width: 100% !important;
  }

  .message-content {
    max-width: 85% !important;
    padding: 0.75rem 1rem !important;
    font-size: 0.95rem !important;
    border-radius: 1.25rem !important;
    line-height: 1.5 !important;
  }

  /* Optimize user message */
  .user-message {
    justify-content: flex-end !important;
    padding-left: 15% !important;
  }

  /* Optimize assistant message */
  .assistant-message {
    justify-content: flex-start !important;
    padding-right: 15% !important;
  }

  /* Optimize chat avatars */
  .chat-avatar-container {
    display: none !important; /* Hide avatars on mobile to save space */
  }

  /* Optimize initial display area */
  #initial-display-area {
    padding: 1rem !important;
  }

  .initial-greeting {
    font-size: 1.25rem !important;
    margin-bottom: 1rem !important;
  }

  /* Optimize sidebar */
  #chat-sidebar {
    width: 85% !important;
    max-width: 300px !important;
    z-index: 1080 !important;
  }

  /* Optimize sidebar toggle button */
  #test-sidebar-toggle-mobile {
    padding: 0.4rem 0.75rem !important;
    font-size: 0.9rem !important;
  }

  /* Optimize sidebar overlay */
  #sidebar-overlay {
    background-color: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(3px) !important;
    -webkit-backdrop-filter: blur(3px) !important;
  }
}

/* Dark mode optimizations for mobile */
@media (max-width: 768px) {
  [data-theme="dark"] #chat-form {
    background-color: #1a1a1a !important;
    border-top: 1px solid #333333 !important;
  }

  [data-theme="dark"] #message-input {
    background-color: #252525 !important;
    border-color: #444444 !important;
    color: #ffffff !important;
  }

  [data-theme="dark"] #chat-sidebar {
    background-color: #1a1a1a !important;
    border-color: #333333 !important;
  }
}
