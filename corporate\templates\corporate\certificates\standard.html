<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate of Completion</title>
    <style>
        @page {
            size: letter landscape;
            margin: 0;
        }
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f9f9f9;
            color: #333;
        }
        .certificate-container {
            width: 1056px;
            height: 816px;
            position: relative;
            margin: 0 auto;
            background: linear-gradient(to bottom, #f9f6e9, #f5e7a3, #f9f6e9);
            border: 20px solid #d4af37;
            box-sizing: border-box;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        /* Add text shadow to all text in the certificate for better readability */
        .certificate-container * {
            text-shadow: 0 0 2px #fff, 0 0 2px #fff, 0 0 2px #fff;
        }
        .certificate-header {
            text-align: center;
            padding-top: 50px;
            margin-bottom: 20px;
        }
        .certificate-title {
            font-size: 48px;
            color: #5D2906;
            margin: 0;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        .certificate-subtitle {
            font-size: 24px;
            color: #5D2906;
            margin: 10px 0 0;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        .certificate-content {
            padding: 0 100px;
            text-align: center;
        }
        .recipient-name {
            font-size: 36px;
            font-weight: bold;
            color: #000;
            margin: 40px 0 20px;
            border-bottom: 2px solid #d4af37;
            display: inline-block;
            padding: 0 20px 10px;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        .certificate-text {
            font-size: 20px;
            line-height: 1.5;
            margin: 20px 0;
            color: #000;
        }
        .achievement {
            font-size: 24px;
            font-weight: bold;
            color: #5D2906;
            margin: 30px 0 20px;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        .completed-games {
            margin: 20px 0 30px;
            padding: 15px;
            border: 2px solid #d4af37;
            border-radius: 10px;
            background-color: rgba(255, 255, 255, 0.7);
            display: inline-block;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .completed-games h3 {
            font-size: 20px;
            color: #5D2906;
            margin: 0 0 10px;
            text-align: center;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        .completed-games ul {
            list-style-type: none;
            padding: 0;
            margin: 0;
            text-align: center;
        }
        .completed-games li {
            font-size: 18px;
            font-weight: bold;
            padding: 5px 0;
            color: #000;
        }
        .certificate-footer {
            position: absolute;
            bottom: 80px;
            left: 0;
            right: 0;
            text-align: center;
        }
        .signature-container {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
        }
        .signature {
            text-align: center;
        }
        .signature-line {
            width: 200px;
            border-bottom: 1px solid #333;
            margin: 0 auto 10px;
        }
        .signature-name {
            font-weight: bold;
            font-size: 18px;
        }
        .signature-title {
            font-size: 14px;
            color: #666;
        }
        .certificate-date {
            font-size: 18px;
            margin-bottom: 20px;
        }
        .certificate-seal {
            position: absolute;
            bottom: 100px;
            right: 100px;
            width: 120px;
            height: 120px;
            background: radial-gradient(circle, #d4af37, #b8860b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 16px;
            transform: rotate(-15deg);
            border: 2px solid #8B4513;
            box-shadow: 0 2px 10px rgba(139, 69, 19, 0.3);
        }
        .certificate-id {
            position: absolute;
            bottom: 30px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 12px;
            color: #999;
        }
        .verification-code {
            font-weight: bold;
            color: #5D2906;
        }
        .company-logo {
            position: absolute;
            top: 50px;
            left: 50px;
            max-width: 150px;
            max-height: 80px;
            color: #5D2906;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.8);
        }
        .score-box {
            position: absolute;
            top: 50px;
            right: 50px;
            background: linear-gradient(to right, #d4af37, #b8860b);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="certificate-container">
        {% if company_name %}
        <div class="company-logo">
            <strong>{{ company_name }}</strong>
        </div>
        {% endif %}

        <div class="score-box">
            Score: {{ score }}
        </div>

        <div class="certificate-header">
            <h1 class="certificate-title">Certificate of Completion</h1>
            <p class="certificate-subtitle">Prompt Engineering Excellence</p>
        </div>

        <div class="certificate-content">
            <div class="recipient-name">{{ full_name }}</div>

            <p class="certificate-text">
                {{ description }}
            </p>

            <p class="achievement">
                Highest Role Achieved: {{ highest_role }}
            </p>

            {% if completed_games %}
            <div class="completed-games">
                <h3>Completed Games:</h3>
                <ul>
                    {% for game in completed_games %}
                    <li>{{ game }}</li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}
        </div>

        <div class="certificate-footer">
            <div class="signature-container">
                <div class="signature">
                    <div class="signature-line"></div>
                    <div class="signature-name">Joy Nantogo</div>
                    <div class="signature-title">CEO, Rwenzori Innovations Ltd.</div>
                </div>

                <div class="signature">
                    <div class="signature-line"></div>
                    <div class="signature-name">Michael Rodriguez</div>
                    <div class="signature-title">Director of Training</div>
                </div>
            </div>

            <div class="certificate-date">
                Issued on {{ issue_date }}
            </div>
        </div>

        <div class="certificate-seal">
            CERTIFIED
        </div>

        <div class="certificate-id">
            Certificate ID: {{ certificate_id }} | Verification Code: <span class="verification-code">{{ verification_code }}</span>
        </div>
    </div>
</body>
</html>
