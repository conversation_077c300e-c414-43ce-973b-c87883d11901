/**
 * TinyMCE simplified version for local use with enhanced toolbar and image resizing
 */
(function() {
    window.tinymce = {
        init: function(settings) {
            console.log("TinyMCE local version initialized with settings:", settings);

            // Process each editor
            const editors = document.querySelectorAll(settings.selector);
            const editorInstances = [];

            // Clean up any existing editor containers
            const existingContainers = document.querySelectorAll('.tinymce-editor-container');
            if (existingContainers.length > 0) {
                console.log(`Found ${existingContainers.length} existing editor containers, cleaning up`);
                existingContainers.forEach(container => {
                    // Only remove containers that don't have the data-preserved attribute
                    if (!container.getAttribute('data-preserved')) {
                        container.parentNode.removeChild(container);
                    }
                });
            }

            editors.forEach(function(textarea) {
                // Skip if this textarea already has an editor
                if (textarea.getAttribute('data-tinymce-initialized') === 'true') {
                    console.log("Textarea already has an editor, skipping:", textarea.id);
                    return;
                }

                // Mark this textarea as initialized
                textarea.setAttribute('data-tinymce-initialized', 'true');

                // Create editor container
                const container = document.createElement('div');
                container.className = 'tinymce-editor-container';
                container.style.border = '1px solid #ccc';
                container.style.borderRadius = '4px';
                container.style.marginBottom = '10px';

                // Mark this container as preserved so it won't be removed by cleanup
                container.setAttribute('data-preserved', 'true');

                // Create toolbar
                const toolbar = document.createElement('div');
                toolbar.className = 'tinymce-toolbar';
                toolbar.style.padding = '5px';
                toolbar.style.backgroundColor = '#f5f5f5';
                toolbar.style.borderBottom = '1px solid #ccc';
                toolbar.style.display = 'flex';
                toolbar.style.flexWrap = 'wrap';

                // Create toolbar groups
                const formatGroup = document.createElement('div');
                formatGroup.className = 'toolbar-group';
                formatGroup.style.marginRight = '10px';
                formatGroup.style.borderRight = '1px solid #ddd';
                formatGroup.style.paddingRight = '10px';

                const alignGroup = document.createElement('div');
                alignGroup.className = 'toolbar-group';
                alignGroup.style.marginRight = '10px';
                alignGroup.style.borderRight = '1px solid #ddd';
                alignGroup.style.paddingRight = '10px';

                const insertGroup = document.createElement('div');
                insertGroup.className = 'toolbar-group';
                insertGroup.style.marginRight = '10px';

                // Add basic formatting buttons
                const boldBtn = createButton('B', 'Bold');
                const italicBtn = createButton('I', 'Italic');
                const underlineBtn = createButton('U', 'Underline');
                const strikeBtn = createButton('S', 'Strikethrough');

                // Add alignment buttons
                const alignLeftBtn = createButton('⫷', 'Align Left');
                const alignCenterBtn = createButton('≡', 'Align Center');
                const alignRightBtn = createButton('⫸', 'Align Right');
                const alignJustifyBtn = createButton('☰', 'Justify');

                // Add list buttons
                const bulletListBtn = createButton('•', 'Bullet List');
                const numberedListBtn = createButton('1.', 'Numbered List');

                // Add link and image buttons
                const linkBtn = createButton('🔗', 'Insert Link');
                const imageBtn = createButton('🖼️', 'Insert Image');
                const tableBtn = createButton('⊞', 'Insert Table');

                // Add buttons to groups
                formatGroup.appendChild(boldBtn);
                formatGroup.appendChild(italicBtn);
                formatGroup.appendChild(underlineBtn);
                formatGroup.appendChild(strikeBtn);

                alignGroup.appendChild(alignLeftBtn);
                alignGroup.appendChild(alignCenterBtn);
                alignGroup.appendChild(alignRightBtn);
                alignGroup.appendChild(alignJustifyBtn);
                alignGroup.appendChild(bulletListBtn);
                alignGroup.appendChild(numberedListBtn);

                insertGroup.appendChild(linkBtn);
                insertGroup.appendChild(imageBtn);
                insertGroup.appendChild(tableBtn);

                // Add groups to toolbar
                toolbar.appendChild(formatGroup);
                toolbar.appendChild(alignGroup);
                toolbar.appendChild(insertGroup);

                // Create editor content area
                const editorArea = document.createElement('div');
                editorArea.className = 'tinymce-editor-area';
                editorArea.style.padding = '10px';
                editorArea.style.minHeight = (settings.height || 300) + 'px';
                editorArea.style.maxHeight = (settings.height || 300) + 'px';
                editorArea.style.overflowY = 'auto';
                editorArea.contentEditable = true;
                editorArea.innerHTML = textarea.value;

                // Add components to container
                container.appendChild(toolbar);
                container.appendChild(editorArea);

                // Insert container before textarea
                textarea.parentNode.insertBefore(container, textarea);

                // Hide the original textarea
                textarea.style.display = 'none';

                // Create editor instance
                const editor = {
                    id: textarea.id,
                    container: container,
                    textarea: textarea,
                    editorArea: editorArea,
                    getContent: function() {
                        return this.editorArea.innerHTML;
                    },
                    setContent: function(content) {
                        this.editorArea.innerHTML = content;
                        return this;
                    },
                    save: function() {
                        this.textarea.value = this.getContent();
                        return this;
                    },
                    getContainer: function() {
                        return this.container;
                    }
                };

                // Add event listeners
                editorArea.addEventListener('input', function() {
                    editor.save();
                    if (settings.setup && typeof settings.setup === 'function') {
                        const evt = { target: editor };
                        settings.setup(editor);
                        editor.dispatchEvent('change', evt);
                    }
                });

                // Bold button functionality
                boldBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('bold', false, null);
                    editor.save();
                });

                // Italic button functionality
                italicBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('italic', false, null);
                    editor.save();
                });

                // Underline button functionality
                underlineBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('underline', false, null);
                    editor.save();
                });

                // Strikethrough button functionality
                strikeBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('strikeThrough', false, null);
                    editor.save();
                });

                // Alignment buttons functionality
                alignLeftBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('justifyLeft', false, null);
                    editor.save();
                });

                alignCenterBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('justifyCenter', false, null);
                    editor.save();
                });

                alignRightBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('justifyRight', false, null);
                    editor.save();
                });

                alignJustifyBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('justifyFull', false, null);
                    editor.save();
                });

                // List buttons functionality
                bulletListBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('insertUnorderedList', false, null);
                    editor.save();
                });

                numberedListBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.execCommand('insertOrderedList', false, null);
                    editor.save();
                });

                // Link button functionality
                linkBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const url = prompt('Enter URL:', 'http://');
                    if (url) {
                        document.execCommand('createLink', false, url);
                        editor.save();
                    }
                });

                // Table button functionality
                tableBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    const rows = prompt('Number of rows:', '3');
                    const cols = prompt('Number of columns:', '3');

                    if (rows && cols) {
                        let tableHtml = '<table border="1" style="width:100%;">';
                        for (let i = 0; i < parseInt(rows); i++) {
                            tableHtml += '<tr>';
                            for (let j = 0; j < parseInt(cols); j++) {
                                tableHtml += '<td style="padding:8px;">Cell</td>';
                            }
                            tableHtml += '</tr>';
                        }
                        tableHtml += '</table>';

                        document.execCommand('insertHTML', false, tableHtml);
                        editor.save();
                    }
                });

                // Image button functionality
                imageBtn.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Create file input
                    const input = document.createElement('input');
                    input.type = 'file';
                    input.accept = 'image/*';

                    input.addEventListener('change', function() {
                        if (this.files && this.files[0]) {
                            const file = this.files[0];

                            // Create FormData
                            const formData = new FormData();
                            formData.append('file', file);

                            // Get CSRF token
                            function getCookie(name) {
                                let cookieValue = null;
                                if (document.cookie && document.cookie !== '') {
                                    const cookies = document.cookie.split(';');
                                    for (let i = 0; i < cookies.length; i++) {
                                        const cookie = cookies[i].trim();
                                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                                            break;
                                        }
                                    }
                                }
                                return cookieValue;
                            }
                            const csrftoken = getCookie('csrftoken');

                            // Show upload indicator
                            const uploadIndicator = document.createElement('div');
                            uploadIndicator.className = 'upload-indicator';
                            uploadIndicator.innerHTML = '<span class="spinner-border spinner-border-sm" role="status"></span> Uploading image...';
                            uploadIndicator.style.position = 'fixed';
                            uploadIndicator.style.top = '50%';
                            uploadIndicator.style.left = '50%';
                            uploadIndicator.style.transform = 'translate(-50%, -50%)';
                            uploadIndicator.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
                            uploadIndicator.style.color = 'white';
                            uploadIndicator.style.padding = '15px 20px';
                            uploadIndicator.style.borderRadius = '5px';
                            uploadIndicator.style.zIndex = '9999';
                            document.body.appendChild(uploadIndicator);

                            // Upload image
                            fetch('/assistant/tinymce/upload/', {
                                method: 'POST',
                                body: formData,
                                credentials: 'same-origin',
                                headers: {
                                    'X-CSRFToken': csrftoken
                                }
                            })
                            .then(response => {
                                if (!response.ok) {
                                    throw new Error(`HTTP error! Status: ${response.status}`);
                                }
                                return response.json();
                            })
                            .then(data => {
                                if (data.location) {
                                    console.log('Image uploaded successfully:', data.location);

                                    // Insert image
                                    const img = document.createElement('img');
                                    img.src = data.location;
                                    img.alt = file.name;
                                    img.style.maxWidth = '100%';
                                    img.style.height = 'auto';
                                    img.style.display = 'block';
                                    img.style.margin = '10px 0';
                                    img.style.borderRadius = '4px';

                                    editorArea.focus();
                                    const selection = window.getSelection();
                                    const range = selection.getRangeAt(0);
                                    range.insertNode(img);

                                    // Make the image resizable
                                    makeImageResizable(img, editorArea, editor);

                                    editor.save();
                                } else {
                                    console.error('Image upload failed:', data.error || 'Unknown error');
                                    alert('Image upload failed: ' + (data.error || 'Unknown error'));
                                }
                            })
                            .catch(error => {
                                console.error('Image upload error:', error);
                                alert('Image upload failed: ' + error.message);
                            })
                            .finally(() => {
                                // Remove upload indicator
                                if (uploadIndicator && uploadIndicator.parentNode) {
                                    uploadIndicator.parentNode.removeChild(uploadIndicator);
                                }
                            });
                        }
                    });

                    input.click();
                });

                // Add event dispatcher
                editor.dispatchEvent = function(eventName, event) {
                    if (settings.setup && typeof settings.setup === 'function') {
                        const handler = editor['on' + eventName];
                        if (handler && typeof handler === 'function') {
                            handler.call(editor, event);
                        }
                    }
                };

                // Add event handlers
                editor.on = function(eventName, callback) {
                    editor['on' + eventName] = callback;
                    return editor;
                };

                // Initialize editor
                if (settings.setup && typeof settings.setup === 'function') {
                    settings.setup(editor);
                }

                // Trigger init event
                editor.dispatchEvent('init', { target: editor });

                // Make all existing images resizable
                makeAllImagesResizable(editorArea, editor);

                editorInstances.push(editor);
            });

            // Add editors to tinymce object
            tinymce.editors = editorInstances;

            // Add triggerSave method
            tinymce.triggerSave = function() {
                editorInstances.forEach(function(editor) {
                    editor.save();
                });
            };

            // Return a promise that resolves with the editor instances
            return Promise.resolve(editorInstances);
        },
        get: function(id) {
            if (!tinymce.editors) return null;

            // Check if we have an editor with this ID
            const editor = tinymce.editors.find(function(editor) {
                return editor.id === id;
            });

            if (editor) return editor;

            // If no editor found, check if the textarea exists and has been initialized
            const textarea = document.getElementById(id);
            if (textarea && textarea.getAttribute('data-tinymce-initialized') === 'true') {
                // Return a dummy editor object to prevent multiple initializations
                return {
                    id: id,
                    getContent: function() { return textarea.value; },
                    setContent: function(content) { textarea.value = content; return this; },
                    save: function() { return this; },
                    getContainer: function() { return textarea.parentNode; }
                };
            }

            return null;
        },
        editors: []
    };

    // Helper function to create a button
    function createButton(text, title) {
        const button = document.createElement('button');
        button.innerHTML = text;
        button.title = title;
        button.style.marginRight = '5px';
        button.style.padding = '3px 8px';
        button.style.border = '1px solid #ccc';
        button.style.borderRadius = '3px';
        button.style.backgroundColor = '#fff';
        button.style.cursor = 'pointer';
        button.style.marginBottom = '5px';

        // Add hover effect
        button.addEventListener('mouseover', function() {
            this.style.backgroundColor = '#f0f0f0';
        });

        button.addEventListener('mouseout', function() {
            this.style.backgroundColor = '#fff';
        });

        return button;
    }

    // Helper function to make images resizable
    function makeImageResizable(img, editorArea, editor) {
        // Skip if already resizable
        if (img.getAttribute('data-resizable') === 'true') {
            return;
        }

        // Mark as resizable
        img.setAttribute('data-resizable', 'true');

        // Make sure the image is draggable
        img.draggable = true;

        // Add resize styles
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
        img.style.cursor = 'move';

        // Create a wrapper for the image
        const wrapper = document.createElement('div');
        wrapper.className = 'resizable-image-wrapper';
        wrapper.style.position = 'relative';
        wrapper.style.display = 'inline-block';
        wrapper.style.maxWidth = '100%';

        // Create toolbar for alignment
        const toolbar = document.createElement('div');
        toolbar.className = 'image-toolbar';
        toolbar.style.position = 'absolute';
        toolbar.style.top = '-30px';
        toolbar.style.left = '0';
        toolbar.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
        toolbar.style.borderRadius = '3px';
        toolbar.style.padding = '3px';
        toolbar.style.display = 'none';
        toolbar.style.zIndex = '101';

        // Create alignment buttons
        const alignLeftBtn = createAlignButton('⫷', 'Align Left');
        const alignCenterBtn = createAlignButton('≡', 'Align Center');
        const alignRightBtn = createAlignButton('⫸', 'Align Right');

        // Add buttons to toolbar
        toolbar.appendChild(alignLeftBtn);
        toolbar.appendChild(alignCenterBtn);
        toolbar.appendChild(alignRightBtn);

        // Add resize handle
        const handle = document.createElement('div');
        handle.className = 'resize-handle';
        handle.style.position = 'absolute';
        handle.style.width = '10px';
        handle.style.height = '10px';
        handle.style.right = '0';
        handle.style.bottom = '0';
        handle.style.backgroundColor = '#4285f4';
        handle.style.border = '1px solid white';
        handle.style.borderRadius = '50%';
        handle.style.cursor = 'se-resize';
        handle.style.zIndex = '100';

        // Replace the image with the wrapper containing the image
        img.parentNode.insertBefore(wrapper, img);
        wrapper.appendChild(img);
        wrapper.appendChild(handle);
        wrapper.appendChild(toolbar);

        // Add alignment functionality
        alignLeftBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Apply left alignment
            wrapper.style.display = 'block';
            wrapper.style.textAlign = 'left';
            wrapper.style.marginRight = 'auto';
            wrapper.style.marginLeft = '0';
            img.style.display = 'inline-block';

            // Save changes
            editor.save();
        });

        alignCenterBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Apply center alignment
            wrapper.style.display = 'block';
            wrapper.style.textAlign = 'center';
            wrapper.style.marginLeft = 'auto';
            wrapper.style.marginRight = 'auto';
            img.style.display = 'inline-block';

            // Save changes
            editor.save();
        });

        alignRightBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Apply right alignment
            wrapper.style.display = 'block';
            wrapper.style.textAlign = 'right';
            wrapper.style.marginLeft = 'auto';
            wrapper.style.marginRight = '0';
            img.style.display = 'inline-block';

            // Save changes
            editor.save();
        });

        // Add resize functionality
        handle.addEventListener('mousedown', function(e) {
            e.preventDefault();
            e.stopPropagation();

            // Get initial dimensions
            const startX = e.clientX;
            const startY = e.clientY;
            const startWidth = img.offsetWidth;
            const startHeight = img.offsetHeight;
            const aspectRatio = startWidth / startHeight;

            // Add mousemove and mouseup event listeners to document
            function onMouseMove(moveEvent) {
                moveEvent.preventDefault();

                // Calculate new dimensions
                const widthChange = moveEvent.clientX - startX;
                const newWidth = startWidth + widthChange;
                const newHeight = newWidth / aspectRatio;

                // Ensure minimum dimensions
                if (newWidth >= 50 && newHeight >= 50) {
                    // Apply new dimensions
                    img.style.width = newWidth + 'px';
                    img.style.height = newHeight + 'px';

                    // Save changes
                    editor.save();
                }
            }

            function onMouseUp() {
                document.removeEventListener('mousemove', onMouseMove);
                document.removeEventListener('mouseup', onMouseUp);
            }

            document.addEventListener('mousemove', onMouseMove);
            document.addEventListener('mouseup', onMouseUp);
        });

        // Add drag functionality for moving the image
        img.addEventListener('mousedown', function(e) {
            if (e.target !== handle) {
                // Allow default behavior for selecting the image
                e.stopPropagation();
            }
        });

        // Show/hide toolbar on hover
        wrapper.addEventListener('mouseenter', function() {
            toolbar.style.display = 'block';
            handle.style.display = 'block';
        });

        wrapper.addEventListener('mouseleave', function() {
            toolbar.style.display = 'none';
            handle.style.display = 'none';
        });

        // Initially hide the handle
        handle.style.display = 'none';
    }

    // Helper function to create alignment buttons
    function createAlignButton(text, title) {
        const button = document.createElement('button');
        button.innerHTML = text;
        button.title = title;
        button.style.backgroundColor = 'transparent';
        button.style.border = 'none';
        button.style.color = 'white';
        button.style.cursor = 'pointer';
        button.style.padding = '2px 5px';
        button.style.fontSize = '14px';

        // Add hover effect
        button.addEventListener('mouseover', function() {
            this.style.backgroundColor = 'rgba(255, 255, 255, 0.2)';
        });

        button.addEventListener('mouseout', function() {
            this.style.backgroundColor = 'transparent';
        });

        return button;
    }

    // Make all existing images in the editor resizable
    function makeAllImagesResizable(editorArea, editor) {
        const images = editorArea.querySelectorAll('img:not([data-resizable="true"])');
        images.forEach(function(img) {
            makeImageResizable(img, editorArea, editor);
        });
    }

    // Add a function to clean up editor containers
    window.cleanupTinyMCEContainers = function() {
        const containers = document.querySelectorAll('.tinymce-editor-container');
        console.log(`Cleaning up ${containers.length} TinyMCE containers`);

        // Keep track of preserved containers
        let preservedCount = 0;

        containers.forEach((container, index) => {
            // If this is the first container, preserve it
            if (index === 0) {
                container.setAttribute('data-preserved', 'true');
                preservedCount++;
            }

            // Remove all non-preserved containers
            if (!container.getAttribute('data-preserved')) {
                container.parentNode.removeChild(container);
            }
        });

        console.log(`Preserved ${preservedCount} containers, removed ${containers.length - preservedCount} containers`);
    };

    // Clean up containers on page load
    document.addEventListener('DOMContentLoaded', function() {
        setTimeout(window.cleanupTinyMCEContainers, 500);
    });
})();
