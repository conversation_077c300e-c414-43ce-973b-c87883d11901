/* TinyMCE Dark Theme Styling */

/* Main editor container */
.tox-tinymce {
    border: 1px solid #333 !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    box-shadow: 0 0 15px rgba(0, 0, 255, 0.1) !important;
    background-color: #1a1a1a !important;
}

/* Editor content area */
.tox-edit-area__iframe {
    background-color: #1a1a1a !important;
}

/* Toolbar styling */
.tox-toolbar__primary {
    background-color: #252525 !important;
    border-bottom: 1px solid #333 !important;
    padding: 4px !important;
}

/* Toolbar buttons */
.tox-tbtn {
    color: #e0e0e0 !important;
    margin: 2px !important;
    border-radius: 6px !important;
    background-color: #2a2a2a !important;
    border: 1px solid #3d3d3d !important;
    transition: all 0.2s ease-in-out !important;
}

/* Toolbar button hover state */
.tox-tbtn:hover {
    background-color: #3a3a3a !important;
    border-color: #4d81d6 !important;
    box-shadow: 0 0 5px rgba(77, 129, 214, 0.5) !important;
}

/* Active toolbar button */
.tox-tbtn--enabled, .tox-tbtn--active {
    background-color: #2d4b7a !important;
    border-color: #4d81d6 !important;
    color: #ffffff !important;
}

/* Toolbar button icons */
.tox-icon {
    color: #e0e0e0 !important;
}

/* Toolbar button icons on hover */
.tox-tbtn:hover .tox-icon {
    color: #ffffff !important;
}

/* Toolbar dropdown */
.tox-tbtn__select-label {
    color: #e0e0e0 !important;
}

/* Toolbar separator */
.tox-toolbar__group:not(:last-of-type) {
    border-right: 1px solid #3d3d3d !important;
}

/* Dropdown menus */
.tox-collection--list, .tox-menu {
    background-color: #252525 !important;
    border: 1px solid #333 !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

/* Dropdown menu items */
.tox-collection__item {
    color: #e0e0e0 !important;
}

/* Dropdown menu items on hover */
.tox-collection__item--active {
    background-color: #3a3a3a !important;
}

/* Dialog styling */
.tox-dialog {
    background-color: #252525 !important;
    border: 1px solid #333 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4) !important;
}

/* Dialog header */
.tox-dialog__header {
    background-color: #2a2a2a !important;
    border-bottom: 1px solid #333 !important;
}

/* Dialog title */
.tox-dialog__title {
    color: #e0e0e0 !important;
}

/* Dialog body */
.tox-dialog__body {
    color: #e0e0e0 !important;
}

/* Dialog footer */
.tox-dialog__footer {
    background-color: #2a2a2a !important;
    border-top: 1px solid #333 !important;
}

/* Dialog input fields */
.tox-textfield, .tox-textarea, .tox-selectfield select {
    background-color: #1a1a1a !important;
    border: 1px solid #333 !important;
    color: #e0e0e0 !important;
    border-radius: 4px !important;
}

/* Dialog input fields on focus */
.tox-textfield:focus, .tox-textarea:focus, .tox-selectfield select:focus {
    border-color: #4d81d6 !important;
    box-shadow: 0 0 0 2px rgba(77, 129, 214, 0.25) !important;
}

/* Dialog buttons */
.tox-button {
    background-color: #2d4b7a !important;
    border-color: #4d81d6 !important;
    color: #ffffff !important;
    border-radius: 6px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
    transition: all 0.2s ease-in-out !important;
}

/* Dialog buttons on hover */
.tox-button:hover:not(:disabled) {
    background-color: #3a5d94 !important;
    border-color: #5e8fe3 !important;
    box-shadow: 0 2px 8px rgba(77, 129, 214, 0.4) !important;
}

/* Secondary dialog buttons */
.tox-button--secondary {
    background-color: #3a3a3a !important;
    border-color: #4d4d4d !important;
    color: #e0e0e0 !important;
}

/* Secondary dialog buttons on hover */
.tox-button--secondary:hover:not(:disabled) {
    background-color: #4a4a4a !important;
    border-color: #5d5d5d !important;
}

/* Custom styling for file input */
input[type="file"].form-control {
    background-color: #1a1a1a !important;
    border-color: #333 !important;
    color: #e0e0e0 !important;
    box-shadow: none !important;
    transition: border-color 0.3s, box-shadow 0.3s !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
}

input[type="file"].form-control:hover {
    border-color: #4d81d6 !important;
}

input[type="file"].form-control:focus {
    border-color: #4d81d6 !important;
    box-shadow: 0 0 0 3px rgba(77, 129, 214, 0.25) !important;
}

/* Custom toolbar styling for the simplified toolbar */
.tinymce-toolbar {
    background-color: #252525 !important;
    border-bottom: 1px solid #333 !important;
    padding: 8px !important;
    display: flex !important;
    flex-wrap: wrap !important;
    border-top-left-radius: 6px !important;
    border-top-right-radius: 6px !important;
}

.toolbar-group {
    margin-right: 10px !important;
    border-right: 1px solid #3d3d3d !important;
    padding-right: 10px !important;
}

.toolbar-group:last-child {
    border-right: none !important;
}

.tinymce-toolbar button {
    margin-right: 5px !important;
    padding: 5px 10px !important;
    border: 1px solid #3d3d3d !important;
    border-radius: 6px !important;
    background-color: #2a2a2a !important;
    color: #e0e0e0 !important;
    cursor: pointer !important;
    margin-bottom: 5px !important;
    transition: all 0.2s ease-in-out !important;
}

.tinymce-toolbar button:hover {
    background-color: #3a3a3a !important;
    border-color: #4d81d6 !important;
    box-shadow: 0 0 5px rgba(77, 129, 214, 0.5) !important;
    color: #ffffff !important;
}

.tinymce-toolbar button.active {
    background-color: #2d4b7a !important;
    border-color: #4d81d6 !important;
    color: #ffffff !important;
}
