{% extends 'corporate/base.html' %}

{% block title %}User Progress - {{ company.name }}{% endblock %}

{% block extra_css %}
{% include 'corporate/user_progress_extra.html' %}
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-3">User Progress</h2>
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>{{ company.name }} User Progress</h5>
            </div>
            <div class="card-body">
                {% if user_progress %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead style="background-color: #0d6efd !important;">
                            <tr>
                                <th style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">User</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Job Title</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Role</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Highest Score</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Total Sessions</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Latest Certificate</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for progress in user_progress %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-2">
                                            {% if progress.user.profile.avatar %}
                                            <img src="{{ progress.user.profile.avatar.url }}" alt="Avatar" class="rounded-circle" width="40">
                                            {% else %}
                                            <div class="avatar-placeholder rounded-circle text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                {{ progress.user.username|first|upper }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="user-name" style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">{{ progress.user.get_full_name|default:progress.user.username }}</h6>
                                            <small class="user-handle" style="color: #ffffff !important;">@{{ progress.user.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td style="color: #ffffff !important;">{{ progress.corporate_profile.job_title|default:"Not specified" }}</td>
                                <td>
                                    {% if progress.corporate_profile.is_company_admin %}
                                    <span class="badge bg-danger" style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important; border: 1px solid rgba(255, 255, 255, 0.3) !important;">Admin</span>
                                    {% else %}
                                    <span class="badge bg-secondary" style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important; border: 1px solid rgba(255, 255, 255, 0.3) !important;">Member</span>
                                    {% endif %}
                                </td>
                                <td style="color: #ffffff !important;">{{ progress.highest_score }}</td>
                                <td style="color: #ffffff !important;">{{ progress.total_sessions }}</td>
                                <td>
                                    {% if progress.latest_certificate %}
                                    <a href="{% url 'corporate:download_certificate' certificate_id=progress.latest_certificate.id %}" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-certificate me-1"></i>{{ progress.latest_certificate.issue_date|date:"M d, Y" }}
                                    </a>
                                    {% else %}
                                    <span class="text-muted" style="color: #ffffff !important;">No certificates</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'corporate:user_progress_detail' user_id=progress.user.id %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-chart-line me-1"></i>Details
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No user progress data available.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
