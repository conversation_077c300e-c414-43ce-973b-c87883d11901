{% extends 'corporate/auth_base.html' %}

{% block title %}Register - {{ invitation.company.name }}{% endblock %}

{% block auth_content %}
<div class="card shadow-lg border-0 rounded-lg mt-5">
    <div class="card-header">
        <h3 class="text-center font-weight-light my-4">Create Account</h3>
        <div class="text-center mb-3">
            <p class="lead">
                You've been invited to join <strong>{{ invitation.company.name }}</strong>
            </p>
            {% if invitation.job_title or invitation.department %}
            <p>
                {% if invitation.job_title %}
                <span class="badge bg-primary">{{ invitation.job_title }}</span>
                {% endif %}
                {% if invitation.department %}
                <span class="badge bg-secondary">{{ invitation.department }}</span>
                {% endif %}
            </p>
            {% endif %}
            {% if invitation.is_admin %}
            <p>
                <span class="badge bg-danger">Administrator</span>
            </p>
            {% endif %}
            {% if invitation.message %}
            <div class="alert alert-info">
                <p class="mb-0"><em>{{ invitation.message }}</em></p>
            </div>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <form method="post">
            {% csrf_token %}
            
            {% if form.non_field_errors %}
            <div class="alert alert-danger">
                {% for error in form.non_field_errors %}
                {{ error }}
                {% endfor %}
            </div>
            {% endif %}
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="form-floating mb-3 mb-md-0">
                        {{ form.first_name }}
                        <label for="{{ form.first_name.id_for_label }}">First Name</label>
                        {% if form.first_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.first_name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating">
                        {{ form.last_name }}
                        <label for="{{ form.last_name.id_for_label }}">Last Name</label>
                        {% if form.last_name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.last_name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="form-floating mb-3">
                {{ form.username }}
                <label for="{{ form.username.id_for_label }}">Username</label>
                {% if form.username.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.username.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="form-floating mb-3">
                {{ form.email }}
                <label for="{{ form.email.id_for_label }}">Email Address</label>
                {% if form.email.errors %}
                <div class="invalid-feedback d-block">
                    {% for error in form.email.errors %}
                    {{ error }}
                    {% endfor %}
                </div>
                {% endif %}
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="form-floating mb-3 mb-md-0">
                        {{ form.password1 }}
                        <label for="{{ form.password1.id_for_label }}">Password</label>
                        {% if form.password1.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password1.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating mb-3 mb-md-0">
                        {{ form.password2 }}
                        <label for="{{ form.password2.id_for_label }}">Confirm Password</label>
                        {% if form.password2.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.password2.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="mt-4 mb-0">
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-block">Create Account</button>
                </div>
            </div>
        </form>
    </div>
    <div class="card-footer text-center py-3">
        <div class="small">
            <a href="{% url 'corporate:login' %}">Already have an account? Sign in</a>
        </div>
    </div>
</div>
{% endblock %}
