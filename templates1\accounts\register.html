{% extends 'base/layout.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Create Account - 24seven{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <img src="{% static 'img/24seven-logo.svg' %}" alt="24seven" class="mb-4" height="48">
                        <h2 class="h4">Create Your Account</h2>
                        <p class="text-muted">Join 24seven to manage your team</p>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    {{ error }}
                                {% endfor %}
                            </div>
                        {% endif %}

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {% render_field form.first_name class="form-control" placeholder="First Name" %}
                                    <label for="{{ form.first_name.id_for_label }}">First Name</label>
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.first_name.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {% render_field form.last_name class="form-control" placeholder="Last Name" %}
                                    <label for="{{ form.last_name.id_for_label }}">Last Name</label>
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.last_name.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-3 mt-3">
                            {% render_field form.username class="form-control" placeholder="Username" %}
                            <label for="{{ form.username.id_for_label }}">Username</label>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.username.errors|join:", " }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Letters, digits, and @/./+/-/_ only.
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            {% render_field form.email class="form-control" placeholder="Email" %}
                            <label for="{{ form.email.id_for_label }}">Email Address</label>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.email.errors|join:", " }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-floating mb-3">
                            {% render_field form.password1 class="form-control" placeholder="Password" %}
                            {# Label removed, handled by form-floating/placeholder #}
                            {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password1.errors|join:", " }}
                                </div>
                            {% endif %}
                            <div class="form-text small">
                                {{ form.password1.help_text|safe }}
                            </div>
                        </div>

                        <div class="form-floating mb-4"> {# Added password2 block back #}
                            {% render_field form.password2 class="form-control" placeholder="Confirm Password" %}
                            {# Label removed, handled by form-floating/placeholder #}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password2.errors|join:", " }}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.captcha %}
                            <div class="mb-3">
                                {{ form.captcha }}
                            </div>
                        {% endif %}

                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="agree_terms" required>
                            <label class="form-check-label small" for="agree_terms">
                                I agree to the <a href="{% url 'terms' %}" target="_blank">Terms of Service</a>
                                and <a href="{% url 'privacy' %}" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-person-plus me-2"></i>
                                Create Account
                            </button>
                        </div>

                        {% if invitation_token %}
                            <input type="hidden" name="invitation_token" value="{{ invitation_token }}">
                        {% endif %}
                    </form>

                    <div class="text-center">
                        <p class="text-muted">
                            Already have an account?
                            <a href="{% url 'accounts:login' %}" class="text-decoration-none">
                                Log in
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- SSO Options -->
            {% if social_auth_providers %}
                <div class="card shadow-sm mt-4">
                    <div class="card-body p-4">
                        <p class="text-center text-muted small mb-3">Or sign up with</p>
                        <div class="d-grid gap-2">
                            {% for provider in social_auth_providers %}
                                <a href="{% url 'social:begin' provider.id %}"
                                   class="btn btn-outline-secondary">
                                    <i class="bi bi-{{ provider.icon }} me-2"></i>
                                    {{ provider.name }}
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    const addPasswordToggle = function(fieldId) {
        const toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = 'btn btn-outline-secondary';
        toggleBtn.innerHTML = '<i class="bi bi-eye"></i>';
        toggleBtn.onclick = function() {
            const field = document.getElementById(fieldId);
            const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
            field.setAttribute('type', type);
            this.innerHTML = type === 'password' ? '<i class="bi bi-eye"></i>' : '<i class="bi bi-eye-slash"></i>';
        };

        const field = document.getElementById(fieldId);
        const wrapper = document.createElement('div');
        wrapper.className = 'input-group';
        field.parentNode.insertBefore(wrapper, field);
        wrapper.appendChild(field);
        wrapper.appendChild(toggleBtn);
    };

    // Add toggle buttons for both password fields
    addPasswordToggle('{{ form.password1.id_for_label }}');
    addPasswordToggle('{{ form.password2.id_for_label }}'); // Added back toggle for password2

    // Form validation
    const form = document.querySelector('form');
    const termsCheckbox = document.getElementById('agree_terms');

    form.addEventListener('submit', function(e) {
        if (!termsCheckbox.checked) {
            e.preventDefault();
            alert('Please agree to the Terms of Service and Privacy Policy');
            termsCheckbox.focus();
        }
    });
});
</script>
{% endblock %}
