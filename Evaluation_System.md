# Evaluation System

This document explains the evaluation system used in the Corporate Prompt Master game to assess player responses and provide feedback.

## Overview

The Corporate Prompt Master game uses a sophisticated evaluation system to assess player responses to tasks. This system combines multiple approaches:

1. **LLM-Based Evaluation**: Uses large language models to analyze responses against requirements
2. **Guideline-Based Assessment**: Evaluates responses against structured guidelines
3. **Similarity Comparison**: Compares responses to reference solutions
4. **Fallback Mechanisms**: Ensures evaluation continues even if primary methods fail

## Evaluation Components

### Enhanced Evaluation (`enhanced_evaluation.py`)

The primary evaluation module that uses LLMs to assess responses:

```python
def evaluate_response_with_enhanced_llm(user_response, task_id, task_description):
    """
    Evaluate a user's response using an LLM.
    
    Args:
        user_response (str): The user's response text
        task_id (str): The ID of the current task
        task_description (str): The description of the task
        
    Returns:
        dict: Evaluation results
    """
    # Construct evaluation prompt
    evaluation_prompt = f"""
    You are evaluating a response to the following task:
    
    TASK: {task_description}
    
    USER RESPONSE:
    {user_response}
    
    Please evaluate this response on the following criteria:
    1. Completeness (1-10): Does it address all requirements?
    2. Quality (1-10): Is it well-written and structured?
    3. Professionalism (1-10): Is the tone appropriate?
    4. Relevance (1-10): Does it directly address the task?
    
    Also provide an overall score (1-10) and indicate if it meets minimum requirements.
    Include specific feedback and improvement suggestions.
    """
    
    # Call the LLM with the evaluation prompt
    evaluation_response = generate_response_with_llm(evaluation_prompt, "evaluation")
    
    # Parse the LLM response to extract scores and feedback
    parsed_results = parse_evaluation_response(evaluation_response)
    return parsed_results
```

### Guideline Evaluation (`guideline_evaluation.py`)

A complementary evaluation system that checks responses against predefined guidelines:

```python
def evaluate_response_with_guidelines(user_response, task_id):
    """
    Evaluate a user's response against structured guidelines using an LLM.
    
    Args:
        user_response (str): The user's response text
        task_id (str): The ID of the current task
        
    Returns:
        tuple: (grade, score, detailed_feedback, section_scores)
    """
    # Get guidelines for the task
    guidelines = get_guidelines_for_task(task_id)
    
    # Format guidelines for the prompt
    formatted_guidelines = format_guidelines_for_prompt(guidelines)
    
    # Construct the evaluation prompt
    prompt = f"""
    You are evaluating a response to a task. The response should follow these guidelines:
    
    {formatted_guidelines}
    
    USER RESPONSE:
    {user_response}
    
    Evaluate how well the response meets each guideline criterion.
    """
    
    # Call the LLM API
    evaluation_result = call_llm_api(prompt)
    
    # Parse the evaluation result
    # ...
    
    return grade, overall_score, feedback_details, section_scores
```

### Score Calculation (`enhanced_evaluation.py`)

Converts evaluation metrics into a numerical score:

```python
def calculate_score(evaluation_results):
    """
    Calculate the final score based on evaluation results.
    
    Args:
        evaluation_results (dict): The evaluation results
        
    Returns:
        int: The calculated score
    """
    base_score = 0
    bonus_score = 0

    # Base score from meeting requirements
    if evaluation_results["meets_requirements"]:
        base_score = 10
    else:
        # Partial credit based on completeness score
        base_score = evaluation_results["completeness_score"]

    # Bonus points for exceptional quality
    if evaluation_results["overall_score"] >= 9:
        bonus_score += 5
    elif evaluation_results["overall_score"] >= 7:
        bonus_score += 2

    # Additional bonuses for specific achievements
    if evaluation_results["creativity_noted"]:
        bonus_score += 3

    # Calculate final score (max 20)
    final_score = min(base_score + bonus_score, 20)
    
    return final_score
```

## Evaluation Process

The evaluation process follows these steps:

1. **Task Submission**: Player submits a response to a task
2. **Initial Processing**: The response is cleaned and prepared for evaluation
3. **LLM Evaluation**: The response is sent to an LLM for evaluation
4. **Guideline Check**: The response is checked against task-specific guidelines
5. **Score Calculation**: A numerical score is calculated based on the evaluation
6. **Feedback Generation**: Personalized feedback is generated based on the evaluation
7. **Result Presentation**: The evaluation results and feedback are presented to the player

This process is implemented in the `submit_prompt` function in `context_aware_game.py`:

```python
@app.route('/submit_prompt', methods=['POST'])
def submit_prompt():
    global game_state
    
    # Get the prompt from the request
    data = request.get_json()
    prompt = data.get('prompt', '')
    
    # Get current task and role information
    current_task_id = game_state.get("current_task")
    current_role = game_state.get("current_role")
    
    # Get tasks for the current role
    role_tasks = get_all_role_tasks(current_role)
    
    # Find the current task
    current_task = None
    for task in role_tasks:
        if task["id"] == current_task_id:
            current_task = task
            break
    
    # Evaluate the response
    if ENHANCED_EVALUATION_AVAILABLE:
        evaluation_results = evaluate_response_with_enhanced_llm(
            prompt, 
            current_task_id, 
            current_task["description"]
        )
        
        # Calculate score
        points_earned = calculate_score(evaluation_results)
        
        # Generate feedback
        feedback = generate_improvement_feedback(evaluation_results)
    else:
        # Fallback to basic evaluation
        # ...
    
    # Process task completion
    game_state = process_task_completion(game_state, evaluation_results, points_earned)
    
    # Check for promotion
    promoted, promotion_message = check_for_promotion(game_state, ROLE_PROGRESSION)
    
    # Generate manager feedback
    manager_feedback = generate_manager_feedback(
        prompt,
        current_task_id,
        manager_name,
        {
            "grade": grade,
            "overall_score": overall_performance_score
        }
    )
    
    # Return results to the client
    # ...
```

## Scoring System

The scoring system is designed to be fair and motivating:

### Score Components

1. **Base Score (0-10)**:
   - Determined by whether the response meets basic requirements
   - If requirements are met: 10 points
   - If requirements are not met: Points based on completeness (0-10)

2. **Bonus Points (0-10)**:
   - Quality bonus: Up to 5 points for exceptional quality
   - Creativity bonus: 3 points for creative or innovative responses
   - Additional bonuses for specific achievements

3. **Final Score (0-20)**:
   - Sum of base score and bonus points, capped at 20

### Performance Grades

Scores are translated into grades for feedback:

- **Good (80-100%)**: Excellent work that meets or exceeds expectations
- **Okay (50-79%)**: Acceptable work that meets basic requirements but has room for improvement
- **Bad (0-49%)**: Work that does not meet basic requirements and needs significant improvement

## Feedback Generation

The game generates personalized feedback from managers:

```python
def generate_manager_feedback(player_response, task_id, manager_name, performance_metrics):
    """
    Generate focused performance feedback from a manager.
    
    Args:
        player_response (str): The player's response to the task
        task_id (str): The ID of the current task
        manager_name (str): The name of the manager providing feedback
        performance_metrics (dict): Dictionary containing performance metrics
        
    Returns:
        str: Generated feedback
    """
    # Extract performance data
    grade = performance_metrics.get("grade", "okay")
    overall_score = performance_metrics.get("overall_score", 70)
    
    # Construct feedback prompt
    feedback_prompt = f"""
    As {manager_name}, provide feedback on this employee's work.
    
    TASK: [Task description]
    RESPONSE: [Player's response]
    PERFORMANCE: {grade} (Score: {overall_score}/100)
    
    Your feedback should:
    1. Be in your authentic voice as {manager_name}
    2. Acknowledge specific strengths
    3. Provide constructive criticism
    4. Give 1-2 specific suggestions for improvement
    5. End with clear next steps
    
    Keep your feedback concise (150-200 words).
    """
    
    # Call the LLM API and return formatted feedback
    # ...
```

The feedback includes:
- Personalized comments from the manager character
- Recognition of strengths in the response
- Constructive criticism for areas of improvement
- Specific suggestions for enhancement
- Clear next steps for the player

## Response Parsing

The evaluation system parses LLM responses to extract structured data:

```python
def parse_evaluation_response(evaluation_text):
    """
    Parse the evaluation response from the LLM.
    
    Args:
        evaluation_text (str): The LLM's evaluation response
        
    Returns:
        dict: Parsed evaluation results
    """
    try:
        # Try to parse as JSON first
        # This is the preferred format if the LLM follows instructions
        import json
        import re
        
        # Find JSON block in the response
        json_match = re.search(r'```json\s*(.*?)\s*```', evaluation_text, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
        else:
            # Try to find a JSON object without markdown formatting
            json_match = re.search(r'(\{.*\})', evaluation_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
            else:
                # If no JSON found, use the entire text
                json_str = evaluation_text
        
        # Parse the JSON
        evaluation_data = json.loads(json_str)
        
        # Extract and return the evaluation data
        # ...
        
    except Exception as e:
        # If JSON parsing fails, fall back to regex-based extraction
        return extract_evaluation_with_regex(evaluation_text)
```

## Fallback Mechanisms

The evaluation system includes several fallback mechanisms:

### 1. Default Evaluation

If the LLM evaluation fails completely:

```python
def create_default_evaluation():
    """
    Create a default evaluation when LLM evaluation fails.
    
    Returns:
        dict: Default evaluation results
    """
    return {
        "completeness_score": 5,
        "quality_score": 5,
        "professionalism_score": 5,
        "relevance_score": 5,
        "overall_score": 5,
        "meets_requirements": True,
        "feedback": ["The system was unable to evaluate your response in detail."],
        "improvement_suggestions": ["Try to address all aspects of the task."],
        "creativity_noted": False
    }
```

### 2. Length-Based Evaluation

A simple evaluation based on response length:

```python
def fallback_evaluation(user_response):
    """
    Fallback evaluation method when LLM evaluation fails.
    
    Args:
        user_response (str): The user's response text
        
    Returns:
        tuple: (grade, score, feedback_details, section_scores)
    """
    # Simple length-based evaluation
    response_length = len(user_response)
    
    if response_length > 300:
        grade = "good"
        score = 80
        feedback = "Your response is comprehensive and detailed."
    elif response_length > 150:
        grade = "okay"
        score = 60
        feedback = "Your response is adequate but could be more detailed."
    else:
        grade = "bad"
        score = 30
        feedback = "Your response is too brief and lacks detail."
    
    return grade, score, [feedback], []
```

### 3. Minimum Point Guarantee

Ensures players receive at least a minimum score for completed tasks:

```python
# Ensure minimum points for any completed task
if points_earned < 5:
    points_earned = 5
```

This comprehensive evaluation system provides players with meaningful feedback on their responses, helping them improve their skills while maintaining an engaging game experience.
