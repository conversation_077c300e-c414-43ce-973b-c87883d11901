/* Eye-Friendly Theme CSS
 * Designed to reduce eye strain from bright white backgrounds
 * while maintaining readability and aesthetics
 */

:root {
  /* Base Colors - Softer, warmer tones to reduce eye strain */
  --ef-background: #f0f0e8; /* Slightly warmer off-white */
  --ef-background-alt: #e8e8e0; /* Warmer alternative background */
  --ef-surface: #f8f8f0; /* Warm white surface */
  --ef-surface-alt: #f5f5ed; /* Alternative surface */

  /* Text Colors - Reduced contrast for better readability */
  --ef-text-primary: #444444; /* Softer than pure black */
  --ef-text-secondary: #666666; /* Medium gray */
  --ef-text-muted: #888888; /* Light gray */

  /* Accent Colors - Softer blue tones */
  --ef-primary: #5b8cb8; /* Softer blue */
  --ef-primary-light: #e0ebf5; /* Very light blue */
  --ef-primary-dark: #3a6ea5; /* Darker blue but not too dark */

  /* UI Elements */
  --ef-border: #e0e0d8; /* Warmer border color */
  --ef-shadow: rgba(0, 0, 0, 0.03); /* Very subtle shadow */
  --ef-shadow-stronger: rgba(0, 0, 0, 0.08); /* Stronger but still subtle */

  /* Card and Container Colors */
  --ef-card-bg: #f8f8f0; /* Warm white */
  --ef-card-border: #e8e8e0; /* Subtle border */

  /* Sidebar Colors */
  --ef-sidebar-bg: #e8e8e0; /* Slightly darker than main background */
  --ef-sidebar-border: #d8d8d0; /* Subtle border */

  /* Message Colors */
  --ef-user-message-bg: #6c7a86; /* Softer gray-blue */
  --ef-user-message-text: #ffffff; /* White text */
  --ef-assistant-message-bg: #e8f0f5; /* Very light blue */
  --ef-assistant-message-text: #3a6ea5; /* Matching blue */

  /* Footer Colors */
  --ef-footer-bg: #e8e8e0; /* Match sidebar */
  --ef-footer-text: #777777; /* Medium gray */

  /* Navbar Colors */
  --ef-navbar-bg: #e8e8e0; /* Match sidebar */
  --ef-navbar-text: #555555; /* Medium-dark gray */
  --ef-navbar-shadow: rgba(0, 0, 0, 0.03); /* Very subtle shadow */
}

/* Dark Mode Colors - Softer dark theme to reduce eye strain */
[data-theme="dark"] {
  /* Base Colors - Not pure black, but softer dark tones */
  --ef-background: #282c34; /* Dark blue-gray, not pure black */
  --ef-background-alt: #2e3440; /* Slightly lighter alternative */
  --ef-surface: #323842; /* Surface color */
  --ef-surface-alt: #3b4252; /* Alternative surface */

  /* Text Colors - Not pure white, reduced contrast */
  --ef-text-primary: #e5e9f0; /* Off-white, not harsh white */
  --ef-text-secondary: #d8dee9; /* Slightly dimmer */
  --ef-text-muted: #aaaaaa; /* Medium gray */

  /* Accent Colors - Softer blues */
  --ef-primary: #81a1c1; /* Nordic blue */
  --ef-primary-light: #4c566a; /* Darker blue-gray */
  --ef-primary-dark: #88c0d0; /* Lighter blue */

  /* UI Elements */
  --ef-border: #4c566a; /* Dark blue-gray border */
  --ef-shadow: rgba(0, 0, 0, 0.15); /* Subtle shadow */
  --ef-shadow-stronger: rgba(0, 0, 0, 0.25); /* Stronger but still subtle */

  /* Card and Container Colors */
  --ef-card-bg: #3b4252; /* Slightly lighter than background */
  --ef-card-border: #4c566a; /* Subtle border */

  /* Sidebar Colors */
  --ef-sidebar-bg: #2e3440; /* Match background-alt */
  --ef-sidebar-border: #434c5e; /* Subtle border */

  /* Message Colors */
  --ef-user-message-bg: #4c566a; /* Dark blue-gray */
  --ef-user-message-text: #eceff4; /* Off-white */
  --ef-assistant-message-bg: #3b4252; /* Slightly lighter than background */
  --ef-assistant-message-text: #d8dee9; /* Off-white */

  /* Footer Colors */
  --ef-footer-bg: #2e3440; /* Match background-alt */
  --ef-footer-text: #d8dee9; /* Off-white */

  /* Navbar Colors */
  --ef-navbar-bg: #2e3440; /* Match background-alt */
  --ef-navbar-text: #e5e9f0; /* Off-white */
  --ef-navbar-shadow: rgba(0, 0, 0, 0.15); /* Subtle shadow */
}

/* Base Styles */
body {
  background-color: var(--ef-background);
  color: var(--ef-text-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  font-size: 16px; /* Base font size */
  line-height: 1.6; /* Improved line height for readability */
  letter-spacing: 0.01em; /* Slightly increased letter spacing */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  text-rendering: optimizeLegibility; /* Better text rendering */
  -webkit-font-smoothing: antialiased; /* Smoother fonts on WebKit */
  -moz-osx-font-smoothing: grayscale; /* Smoother fonts on Firefox */
}

/* Add a subtle texture to break up large areas */
body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.3; /* Reduced opacity for subtlety */
  background-image: url("data:image/png;base64,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");
  z-index: -1;
  filter: opacity(0.5) contrast(0.8); /* Reduce contrast of the texture */
}

/* Add a subtle blue light filter to reduce eye strain */
body::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(to bottom, rgba(255, 253, 242, 0.03), rgba(255, 253, 242, 0.03));
  z-index: -1;
  mix-blend-mode: overlay;
}

/* Main Content */
main {
  background-color: var(--ef-background);
}

/* Navigation */
.navbar {
  background-color: var(--ef-navbar-bg);
  box-shadow: 0 2px 8px var(--ef-navbar-shadow);
  border-bottom: 1px solid var(--ef-border);
}

.navbar .nav-link {
  color: var(--ef-navbar-text);
}

.navbar .nav-link:hover {
  color: var(--ef-primary);
}

.navbar-brand {
  color: var(--ef-text-primary);
}

/* Dropdown Menus */
.dropdown-menu {
  background-color: var(--ef-surface);
  border-color: var(--ef-border);
  box-shadow: 0 4px 12px var(--ef-shadow);
}

.dropdown-item {
  color: var(--ef-text-primary);
}

.dropdown-item:hover {
  background-color: var(--ef-background-alt);
  color: var(--ef-primary);
}

/* Cards */
.card {
  background-color: var(--ef-card-bg);
  border-color: var(--ef-card-border);
  box-shadow: 0 2px 8px var(--ef-shadow);
}

.card-header {
  background-color: var(--ef-background-alt);
  border-bottom-color: var(--ef-card-border);
}

.card-footer {
  background-color: var(--ef-background-alt);
  border-top-color: var(--ef-card-border);
}

/* Buttons */
.btn-primary {
  background-color: var(--ef-primary);
  border-color: var(--ef-primary-dark);
}

.btn-primary:hover {
  background-color: var(--ef-primary-dark);
  border-color: var(--ef-primary-dark);
}

.btn-outline-primary {
  color: var(--ef-primary);
  border-color: var(--ef-primary);
}

.btn-outline-primary:hover {
  background-color: var(--ef-primary);
  color: white;
}

/* Forms */
.form-control {
  background-color: var(--ef-surface);
  border-color: var(--ef-border);
  color: var(--ef-text-primary);
}

.form-control:focus {
  background-color: var(--ef-surface);
  border-color: var(--ef-primary);
  color: var(--ef-text-primary);
  box-shadow: 0 0 0 0.2rem rgba(58, 110, 165, 0.15);
}

/* Sidebar */
.sidebar {
  background-color: var(--ef-sidebar-bg);
  border-right-color: var(--ef-sidebar-border);
  box-shadow: 0 4px 20px var(--ef-shadow);
}

/* Chat Messages */
.user-message {
  background: var(--ef-user-message-bg);
  color: var(--ef-user-message-text);
}

.assistant-message {
  background: var(--ef-assistant-message-bg);
  color: var(--ef-assistant-message-text);
}

.message-content {
  background-color: var(--ef-surface);
  border-color: var(--ef-border);
}

.user-message .message-content {
  background: var(--ef-user-message-bg);
  color: var(--ef-user-message-text);
}

.assistant-message .message-content {
  background: var(--ef-assistant-message-bg);
  color: var(--ef-assistant-message-text);
}

/* Chat Input */
#chat-form.input-group {
  background-color: var(--ef-surface);
  border-color: var(--ef-border);
  box-shadow: 0 4px 12px var(--ef-shadow);
}

#message-input.form-control {
  background-color: var(--ef-surface);
  border-color: var(--ef-border);
  color: var(--ef-text-primary);
}

/* Footer */
footer.footer {
  background-color: var(--ef-footer-bg);
  border-top-color: var(--ef-border);
  color: var(--ef-footer-text);
}

footer.footer h5, footer.footer h6 {
  color: var(--ef-text-primary);
}

footer.footer .list-unstyled a {
  color: var(--ef-text-secondary);
}

footer.footer .list-unstyled a:hover {
  color: var(--ef-primary);
}

/* Alerts */
.alert {
  border-color: var(--ef-border);
  box-shadow: 0 2px 8px var(--ef-shadow);
}

/* Tables */
.table {
  color: var(--ef-text-primary);
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: var(--ef-background-alt);
}

/* Modal */
.modal-content {
  background-color: var(--ef-surface);
  border-color: var(--ef-border);
}

.modal-header {
  border-bottom-color: var(--ef-border);
}

.modal-footer {
  border-top-color: var(--ef-border);
}

/* Theme Toggle Button */
.theme-toggle-btn {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--ef-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px var(--ef-shadow-stronger), 0 0 0 3px rgba(255, 255, 255, 0.5);
  z-index: 1000;
  border: none;
  transition: all 0.3s ease;
  font-size: 1.5rem; /* Larger icon */
}

.theme-toggle-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 16px var(--ef-shadow-stronger), 0 0 0 4px rgba(255, 255, 255, 0.6);
}

.theme-toggle-btn:active {
  transform: scale(0.95);
}

/* Add a tooltip/label to the theme toggle button */
.theme-toggle-btn::before {
  content: attr(title);
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--ef-surface);
  color: var(--ef-text-primary);
  padding: 5px 10px;
  border-radius: 5px;
  font-size: 14px;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
  box-shadow: 0 2px 8px var(--ef-shadow);
  border: 1px solid var(--ef-border);
}

.theme-toggle-btn:hover::before {
  opacity: 1;
  visibility: visible;
}

/* Reduce eye strain for code blocks and pre elements */
pre, code {
  background-color: var(--ef-background-alt);
  color: var(--ef-text-primary);
  border-color: var(--ef-border);
}

/* TinyMCE Editor */
.tox-tinymce {
  border-color: var(--ef-border) !important;
  background-color: var(--ef-surface) !important;
}

.tox-editor-container {
  background-color: var(--ef-surface) !important;
}

/* List Group Items */
.list-group-item {
  background-color: var(--ef-surface);
  border-color: var(--ef-border);
}

/* Pagination */
.pagination .page-link {
  background-color: var(--ef-surface);
  border-color: var(--ef-border);
  color: var(--ef-text-primary);
}

.pagination .page-item.active .page-link {
  background-color: var(--ef-primary);
  border-color: var(--ef-primary);
  color: white;
}

/* Badges */
.badge {
  background-color: var(--ef-primary-light);
  color: var(--ef-primary-dark);
}

/* Progress Bars */
.progress {
  background-color: var(--ef-background-alt);
}

.progress-bar {
  background-color: var(--ef-primary);
}

/* Tooltips */
.tooltip .tooltip-inner {
  background-color: var(--ef-surface);
  color: var(--ef-text-primary);
  border: 1px solid var(--ef-border);
  box-shadow: 0 2px 8px var(--ef-shadow);
}

/* Popovers */
.popover {
  background-color: var(--ef-surface);
  border-color: var(--ef-border);
}

.popover-header {
  background-color: var(--ef-background-alt);
  border-bottom-color: var(--ef-border);
}

.popover-body {
  color: var(--ef-text-primary);
}

/* Tabs */
.nav-tabs .nav-link {
  color: var(--ef-text-secondary);
  border-color: transparent;
}

.nav-tabs .nav-link.active {
  color: var(--ef-primary);
  background-color: var(--ef-surface);
  border-color: var(--ef-border) var(--ef-border) var(--ef-surface);
}

.nav-tabs .nav-link:hover {
  border-color: var(--ef-background-alt) var(--ef-background-alt) var(--ef-border);
}

/* Accordion */
.accordion-button {
  background-color: var(--ef-surface);
  color: var(--ef-text-primary);
}

.accordion-button:not(.collapsed) {
  background-color: var(--ef-primary-light);
  color: var(--ef-primary-dark);
}

.accordion-body {
  background-color: var(--ef-surface);
}

/* Breadcrumbs */
.breadcrumb {
  background-color: var(--ef-background-alt);
}

.breadcrumb-item a {
  color: var(--ef-primary);
}

/* Utility classes for text colors */
.text-primary {
  color: var(--ef-primary) !important;
}

.text-secondary {
  color: var(--ef-text-secondary) !important;
}

.text-muted {
  color: var(--ef-text-muted) !important;
}

/* Background utility classes */
.bg-primary {
  background-color: var(--ef-primary) !important;
}

.bg-light {
  background-color: var(--ef-background-alt) !important;
}

.bg-dark {
  background-color: var(--ef-text-primary) !important;
}

/* Border utility classes */
.border {
  border-color: var(--ef-border) !important;
}

/* Scrollbar styling for WebKit browsers */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: var(--ef-background-alt);
}

::-webkit-scrollbar-thumb {
  background: var(--ef-border);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--ef-text-muted);
}
