{% extends 'base/layout.html' %}
{% load static %}
{% load widget_tweaks %}
{% load account_tags %} {# Explicitly load here #}

{% block title %}Account Settings - 24seven{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="d-sm-flex align-items-center justify-content-between mb-4">
                <div>
                    <h1 class="h3 mb-0">Account Settings</h1>
                    <p class="text-muted mb-0">
                        Manage your personal account settings
                    </p>
                </div>
                <div class="mt-3 mt-sm-0">
                    <a href="{% url 'accounts:dashboard' %}" class="btn btn-light">
                        <i class="bi bi-arrow-left me-2"></i>
                        Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Profile Settings -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-transparent">
                    <h5 class="card-title mb-0">Profile Information</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" class="profile-form">
                        {% csrf_token %}
                        <input type="hidden" name="form_type" value="profile"> {# Ensure hidden input exists #}

                        <div class="row mb-4">
                            <!-- Avatar Section -->
                            <div class="col-md-4 col-lg-3 mb-3 mb-md-0 text-center text-md-start">
                                <div class="position-relative d-inline-block">
                                    {% if user.profile.avatar %}
                                        <img id="avatarPreview" src="{{ user.profile.avatar.url }}"
                                             alt="{{ user.get_full_name|default:'User' }} Avatar"
                                             class="rounded-circle border"
                                             width="120" height="120">
                                    {% else %}
                                        <img id="avatarPreview" src="{{ user.email|gravatar_url:120 }}"
                                             alt="{{ user.get_full_name|default:'User' }} Avatar"
                                             width="120" height="120">
                                    {% endif %}
                                    {# Manually render only the file input, hidden #}
                                    <input type="file" name="{{ form.avatar.name }}" accept="image/*" class="visually-hidden" id="{{ form.avatar.id_for_label }}">
                                    {# Label triggers the hidden input #}
                                    <label for="{{ form.avatar.id_for_label }}"
                                           class="btn btn-light btn-sm rounded-circle position-absolute bottom-0 end-0 p-1 lh-1 border" {# Added border #}
                                           style="transform: translate(15%, 15%);" {# Adjusted position slightly #}
                                           title="Change avatar">
                                        <i class="bi bi-camera fs-6"></i>
                                    </label>
                                </div>
                                {% if form.avatar.errors %}
                                    <div class="invalid-feedback d-block mt-2">{{ form.avatar.errors|join:", " }}</div>
                                {% endif %}
                                {# Display selected filename via JS #}
                                <div id="avatarFilename" class="form-text mt-1 small text-muted"></div> {# Added text-muted #}
                            </div>

                            <!-- Profile Fields -->
                            <div class="col-md-8 col-lg-9">
                                <div class="row g-3">
                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                        {% render_field form.first_name class+="form-control" %}
                                        {% if form.first_name.errors %}
                                            <div class="invalid-feedback d-block">{{ form.first_name.errors|join:", " }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                        {% render_field form.last_name class+="form-control" %}
                                        {% if form.last_name.errors %}
                                            <div class="invalid-feedback d-block">{{ form.last_name.errors|join:", " }}</div>
                                        {% endif %}
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                                        {% render_field form.email class+="form-control" %}
                                        {% if form.email.errors %}
                                            <div class="invalid-feedback d-block">{{ form.email.errors|join:", " }}</div>
                                        {% endif %}
                                    </div>

                                    {# Bio field moved inside this grid column #}
                                    <div class="col-12 mb-3">
                                        <label for="{{ form.bio.id_for_label }}" class="form-label">Bio</label>
                                        {% render_field form.bio class+="form-control" rows="3" %}
                                        {% if form.bio.errors %}
                                            <div class="invalid-feedback d-block">{{ form.bio.errors|join:", " }}</div>
                                        {% endif %}
                                        {# Removed orphaned endif tag that was here #}
                                        <div class="form-text">
                                            A brief description about yourself that will be visible to your team members.
                                        </div>
                                    </div>
                                </div>
                                {# Save button moved inside this column #}
                                <div class="d-flex justify-content-end mt-3">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-save me-2"></i>
                                        Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                        {# Removed Bio section from here as it's moved above #}
                        {# Removed Save button from here as it's moved above #}
                    </form>
                </div>
            </div>

            <!-- Preferences -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-transparent">
                    <h5 class="card-title mb-0">Preferences</h5>
                </div>
                <div class="card-body">
                    <form method="post" class="preferences-form">
                        {% csrf_token %}
                        <input type="hidden" name="form_type" value="preferences">

                        <div class="row g-3">
                            <div class="col-md-6 mb-3"> {# Added mb-3 #}
                                <label for="{{ form.language.id_for_label }}" class="form-label"> {# TODO: Update ID when using preferences_form #}
                                    Language
                                </label>
                                {# {% render_field preferences_form.language class="form-select" %} #} {# Commented out - Use preferences_form later #}
                                <select class="form-select" disabled><option>English (Default)</option></select> {# Placeholder #}
                            </div>

                            <div class="col-md-6 mb-3"> {# Added mb-3 #}
                                <label for="{{ form.theme.id_for_label }}" class="form-label"> {# TODO: Update ID when using preferences_form #}
                                    Theme
                                </label>
                                {# {% render_field preferences_form.theme class="form-select" %} #} {# Commented out - Use preferences_form later #}
                                <select class="form-select" disabled><option>System Default</option></select> {# Placeholder #}
                            </div>
                        </div>

                        <hr class="my-4">

                        <h6 class="mb-3">Notifications</h6>

                        <div class="form-check form-switch mb-3">
                            {# {% render_field preferences_form.email_notifications class="form-check-input" %} #} {# Commented out - Use preferences_form later #}
                            <input type="checkbox" class="form-check-input" id="emailNotifications" disabled> {# Placeholder #}
                            <label class="form-check-label" for="emailNotifications"> {# TODO: Update ID when using preferences_form #}
                                Email Notifications
                            </label>
                            <div class="form-text">
                                Receive important updates and notifications via email.
                            </div>
                        </div>

                        <div class="form-check form-switch mb-3">
                            {# {% render_field preferences_form.desktop_notifications class="form-check-input" %} #} {# Commented out - Use preferences_form later #}
                            <input type="checkbox" class="form-check-input" id="desktopNotifications" disabled> {# Placeholder #}
                            <label class="form-check-label" for="desktopNotifications"> {# TODO: Update ID when using preferences_form #}
                                Desktop Notifications
                            </label>
                            <div class="form-text">
                                Show notifications on your desktop when browser is open.
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary" disabled> {# Disabled placeholder #}
                                <i class="bi bi-save me-2"></i>
                                Save Preferences
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-transparent">
                    <h5 class="card-title mb-0">Security</h5>
                </div>
                <div class="card-body">
                    <!-- Password Change -->
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div>
                            <h6 class="mb-1">Password</h6>
                            <p class="text-muted small mb-0">
                                Change your account password
                            </p>
                        </div>
                        <a href="{% url 'accounts:password_change' %}" class="btn btn-light">
                            Change Password
                        </a>
                    </div>

                    <!-- Two Factor Auth -->
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div>
                            <h6 class="mb-1">Two-Factor Authentication</h6>
                            <p class="text-muted small mb-0">
                                Add an extra layer of security to your account
                            </p>
                        </div>
                        <button type="button" class="btn btn-light" disabled>
                            Coming Soon
                        </button>
                    </div>

                    <!-- Active Sessions -->
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="mb-1">Active Sessions</h6>
                            <p class="text-muted small mb-0">
                                You have {{ active_sessions }} active session{{ active_sessions|pluralize }}
                            </p>
                        </div>
                        <button type="button"
                                class="btn btn-outline-danger"
                                data-bs-toggle="modal"
                                data-bs-target="#logoutAllModal">
                            Sign Out All
                        </button>
                    </div>
                </div>
            </div>

            <!-- Danger Zone -->
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Danger Zone
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h6 class="text-danger mb-1">Delete Account</h6>
                            <p class="text-muted small mb-0">
                                Permanently delete your account and all associated data
                            </p>
                        </div>
                        <button type="button"
                                class="btn btn-outline-danger"
                                data-bs-toggle="modal"
                                data-bs-target="#deleteAccountModal">
                            Delete Account
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sign Out All Modal -->
<div class="modal fade" id="logoutAllModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sign Out All Sessions</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>This will sign you out of all devices except this one. Are you sure?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="{% url 'accounts:logout_all' %}">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Sign Out All</button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Account Modal -->
<div class="modal fade" id="deleteAccountModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Account</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-danger">
                    <h6 class="alert-heading">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Warning: This action cannot be undone
                    </h6>
                    <p class="mb-0">This will permanently delete:</p>
                    <ul class="mb-0">
                        <li>Your personal account and profile</li>
                        <li>All companies you own</li>
                        <li>All content you've created</li>
                        <li>All your settings and preferences</li>
                    </ul>
                </div>
                <form method="post" action="{% url 'accounts:delete_account' %}" id="deleteAccountForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="confirmDelete" class="form-label">
                            Type your email address to confirm
                        </label>
                        <input type="email"
                               class="form-control"
                               id="confirmDelete"
                               name="confirm_email"
                               required
                               pattern="{{ user.email|escapejs }}"
                               placeholder="{{ user.email }}">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="deleteAccountForm" class="btn btn-danger" disabled>
                    Delete Account
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Avatar preview and filename display
    const avatarInput = document.getElementById('{{ form.avatar.id_for_label }}'); // Use Django form ID
    const avatarPreview = document.getElementById('avatarPreview');
    const avatarFilename = document.getElementById('avatarFilename');

    if (avatarInput && avatarPreview && avatarFilename) { // Check all elements exist
        avatarInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const file = this.files[0];
                // Update preview
                const reader = new FileReader();
                reader.onload = function(e) {
                    avatarPreview.src = e.target.result;
                }
                reader.readAsDataURL(file);
                // Update filename display
                avatarFilename.textContent = file.name;
            } else {
                 // Clear filename if no file selected
                 avatarFilename.textContent = '';
                 // Reset preview to original or Gravatar if needed (optional)
                 avatarPreview.src = '{% if user.profile.avatar %}{{ user.profile.avatar.url }}{% else %}{{ user.email|gravatar_url:120 }}{% endif %}';
            }
        });
    }

    // Delete account confirmation
    const deleteForm = document.getElementById('deleteAccountForm');
    const deleteInput = document.getElementById('confirmDelete');
    const deleteButton = deleteForm.closest('.modal-content').querySelector('.modal-footer button[type="submit"]'); // Adjusted selector

    if (deleteInput && deleteButton) {
        deleteInput.addEventListener('input', function() {
            deleteButton.disabled = this.value !== '{{ user.email|escapejs }}';
        });
    }

    // Form dirty check (unchanged)
    const forms = document.querySelectorAll('form:not([id="deleteAccountForm"])');
    forms.forEach(form => {
        let formDirty = false;

        form.addEventListener('change', function() {
            formDirty = true;
        });

        form.addEventListener('submit', function() {
            formDirty = false;
        });

        window.addEventListener('beforeunload', function(e) {
            if (formDirty) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
    });
});
</script>
{% endblock %}
