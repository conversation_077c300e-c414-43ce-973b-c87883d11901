/* 
 * User Text Brightness Enhancement
 * This CSS file makes user-related text brighter on the company users page
 * while leaving everything else as is.
 */

/* Make user names brighter and more visible */
.table .user-name,
.table .d-flex .user-name,
.table tbody tr td .user-name {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5) !important;
}

/* Make user email addresses brighter */
.table tbody tr td a[href^="mailto:"],
.table tbody tr td span[style*="color: #333333"],
.table tbody tr td span.text-muted,
.table tbody tr td small.text-muted {
    color: #ffffff !important;
    font-weight: normal !important;
}

/* Make "Not specified" text brighter */
.table tbody tr td span[style*="color: #333333; font-style: italic;"] {
    color: #ffffff !important;
    font-style: italic !important;
}

/* Make joined date brighter */
.table tbody tr td[style*="color: #000000"] {
    color: #ffffff !important;
}

/* Ensure user avatars have good contrast */
.avatar-placeholder {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Make badge text brighter while keeping the background color */
.badge.bg-danger,
.badge.bg-secondary {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Ensure any user-related text in modals is also bright */
.modal-body .user-name,
.modal-content .user-email,
.modal-content span[style*="color: #333333"] {
    color: #ffffff !important;
}
