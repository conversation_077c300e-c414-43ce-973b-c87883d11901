{% extends "assistants/base.html" %}

{% block title %}Assistant Analytics{% endblock %}

{% block page_header %}
    <div class="d-flex justify-content-between align-items-center">
        <h1 class="h2 mb-0">Assistant Analytics for {{ assistant.name }}</h1>
        <div class="btn-group btn-group-sm" role="group" aria-label="Timeframe selection">
            {# Construct URL with existing query params if any, adding/replacing period #}
            <a href="?period=day" class="btn {% if selected_period == 'day' %}btn-primary{% else %}btn-outline-secondary{% endif %}">Day</a>
            <a href="?period=week" class="btn {% if selected_period == 'week' %}btn-primary{% else %}btn-outline-secondary{% endif %}">Week</a>
            <a href="?period=month" class="btn {% if selected_period == 'month' %}btn-primary{% else %}btn-outline-secondary{% endif %}">Month</a>
            <a href="?period=all" class="btn {% if selected_period == 'all' or not selected_period %}btn-primary{% else %}btn-outline-secondary{% endif %}">All Time</a>
        </div>
    </div>
{% endblock %}

{% block main_content %}
<div class="row g-4 mb-4">
    <!-- Total Interactions -->
    <div class="col-md-6 col-xl-3">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title text-muted text-uppercase small">Total Interactions</h5>
                <p class="card-text display-6">{{ analytics.total_interactions|default:"0" }}</p>
            </div>
        </div>
    </div>

    <!-- Average Rating -->
    <div class="col-md-6 col-xl-3">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title text-muted text-uppercase small">Average Rating</h5>
                <p class="card-text display-6">
                    {% if analytics.average_rating %}
                        {{ analytics.average_rating|floatformat:1 }} <span class="fs-5 text-muted">/ 5</span>
                    {% else %}
                        N/A
                    {% endif %}
                </p>
            </div>
        </div>
    </div>

    <!-- Total Tokens Used -->
    <div class="col-md-6 col-xl-3">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title text-muted text-uppercase small">Total Tokens Used</h5>
                <p class="card-text display-6">{{ analytics.total_tokens|default:"0" }}</p>
            </div>
        </div>
    </div>

    <!-- Average Response Time -->
    <div class="col-md-6 col-xl-3">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title text-muted text-uppercase small">Avg. Response Time</h5>
                <p class="card-text display-6">
                    {% if analytics.average_response_time %}
                        {{ analytics.average_response_time|floatformat:2 }}s
                    {% else %}
                        N/A
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Charts Section -->
<div class="row g-4 mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Key Metrics Overview</h5>
            </div>
            <div class="card-body">
                <canvas id="keyMetricsChart" style="min-height: 250px;"></canvas>
            </div>
        </div>
    </div>
    {# Add placeholders for more charts later #}
    <div class="col-md-6">
        <div class="card">
            <div class="card-body text-center text-muted">
                <i class="bi bi-graph-up h3"></i>
                <p>Interactions Over Time (Coming Soon)</p>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-body text-center text-muted">
                 <i class="bi bi-star-fill h3"></i>
                <p>Rating Distribution (Coming Soon)</p>
            </div>
        </div>
    </div>
</div>

{# Placeholder for Detailed Interaction Table #}
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">Recent Interactions</h5>
    </div>
    <div class="card-body p-0"> {# Remove padding for full-width table #}
        {% if recent_interactions %}
            <div class="table-responsive">
                <table class="table table-hover table-striped mb-0">
                    <thead>
                        <tr>
                            <th scope="col">User</th>
                            <th scope="col">Prompt</th>
                            <th scope="col">Response (Start)</th>
                            <th scope="col" class="text-center">Rating</th>
                            <th scope="col" class="text-center">Tokens</th>
                            <th scope="col">Timestamp</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for interaction in recent_interactions %}
                        <tr>
                            <td>{{ interaction.user.username|default:"-" }}</td>
                            <td title="{{ interaction.prompt }}">
                                {{ interaction.prompt|truncatechars:50 }}
                            </td>
                            <td title="{{ interaction.response }}">
                                {{ interaction.response|truncatechars:50 }}
                            </td>
                            <td class="text-center">
                                {% if interaction.rating %}
                                    {{ interaction.rating }}/5 <i class="bi bi-star-fill text-warning"></i>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                             <td class="text-center">{{ interaction.token_count|default:"-" }}</td>
                            <td>{{ interaction.created_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
             <div class="card-footer text-center">
                 {# Link to full history page #}
                 <a href="{% url 'assistants:history' company_id=company.id assistant_id=assistant.id %}" class="btn btn-sm btn-outline-secondary">
                     View Full History
                 </a>
             </div>
        {% else %}
            <div class="card-body text-center">
                <p class="text-muted mb-0">No interactions recorded yet.</p>
            </div>
        {% endif %}
    </div>
</div>

{% endblock %}

{% block extra_js %}
{{ block.super }} {# Include JS from base template if any #}
<script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const ctx = document.getElementById('keyMetricsChart');
    if (ctx) {
        // Prepare data - Use 0 for missing values for visualization
        const totalInteractions = {{ analytics.total_interactions|default:"0" }};
        // Note: Chart.js doesn't directly plot average rating or time well in a simple bar with interactions/tokens.
        // This chart is a basic example; more specific charts are better.
        // Let's plot Interactions and Tokens for now.
        const totalTokens = {{ analytics.total_tokens|default:"0" }};
        // Avg Rating and Response Time are better shown separately or as KPIs.

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ['Total Interactions', 'Total Tokens Used'],
                datasets: [{
                    label: 'Count',
                    data: [totalInteractions, totalTokens],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.6)', // Blue for interactions
                        'rgba(255, 159, 64, 0.6)' // Orange for tokens
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 159, 64, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                             precision: 0 // Ensure whole numbers on y-axis
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false // Hide legend for simple chart
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                let label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += context.parsed.y;
                                }
                                return label;
                            }
                        }
                    }
                },
                responsive: true,
                maintainAspectRatio: false
            }
        });
    } else {
        console.error("Canvas element #keyMetricsChart not found.");
    }
});
</script>
{% endblock %}
