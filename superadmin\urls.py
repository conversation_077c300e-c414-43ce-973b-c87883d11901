from django.urls import path
from . import views

app_name = 'superadmin'

urlpatterns = [
    path('', views.DashboardView.as_view(), name='dashboard'),

    # Company Management
    path('companies/', views.CompanyListView.as_view(), name='company_list'),
    path('companies/<int:pk>/', views.CompanyDetailView.as_view(), name='company_detail'),
    path('companies/<int:pk>/toggle-active/', views.CompanyActivateToggleView.as_view(), name='company_activate_toggle'),
    path('users/<int:user_pk>/impersonate/', views.CompanyImpersonateView.as_view(), name='company_impersonate'),

    # Game Session Management
    path('game-sessions/', views.GameSessionListView.as_view(), name='game_session_list'),
    path('game-sessions/<int:pk>/', views.GameSessionDetailView.as_view(), name='game_session_detail'),

    # Leaderboard Management
    path('leaderboards/', views.LeaderboardListView.as_view(), name='leaderboard_list'),
    path('leaderboards/<int:pk>/', views.LeaderboardDetailView.as_view(), name='leaderboard_detail'),

    # User Management
    path('users/', views.UserListView.as_view(), name='user_list'),
    path('users/<int:pk>/', views.UserDetailView.as_view(), name='user_detail'),
    path('users/<int:pk>/toggle-active/', views.UserToggleActiveView.as_view(), name='user_toggle_active'),
    path('users/<int:pk>/toggle-staff/', views.UserToggleStaffView.as_view(), name='user_toggle_staff'),
    path('users/<int:pk>/toggle-superuser/', views.UserToggleSuperuserView.as_view(), name='user_toggle_superuser'),
]
