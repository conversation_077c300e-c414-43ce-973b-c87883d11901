/* Directory Theme CSS based on Community Assistants page */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --light-bg: #f8f9fa;
    --border-color: #dee2e6;
    --card-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --card-hover-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    --border-radius: 0.375rem;
    --border-radius-lg: 0.5rem;
    --transition-speed: 0.3s;
}

/* Card Styling */
.directory-card {
    background-color: #fff;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--card-shadow);
    transition: all var(--transition-speed) ease;
    border: 1px solid var(--border-color);
    overflow: hidden;
    height: 100%;
    position: relative;
}

.directory-card:hover {
    box-shadow: var(--card-hover-shadow);
    transform: translateY(-2px);
}

/* Section Styling */
.directory-section {
    background-color: rgba(240, 242, 245, 0.5);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.directory-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #212529;
    display: flex;
    align-items: center;
}

.directory-section-title i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

/* Button Styling */
.directory-btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    transition: all var(--transition-speed) ease;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    border: 1px solid transparent;
}

.directory-btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.directory-btn-primary:hover {
    background-color: #0b5ed7;
    color: white;
}

.directory-btn-outline {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--secondary-color);
}

.directory-btn-outline:hover {
    background-color: #f8f9fa;
    color: #212529;
}

/* Badge Styling */
.directory-badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.directory-badge-featured {
    background-color: #198754;
    color: white;
}

.directory-badge-gold {
    background-color: #ffc107;
    color: #212529;
}

.directory-badge-standard {
    background-color: #6c757d;
    color: white;
}

/* List Styling */
.directory-list {
    list-style: none;
    padding-left: 0;
    margin-bottom: 0;
}

.directory-list-item {
    padding: 0.75rem 1.25rem;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.125);
    margin-bottom: -1px;
}

.directory-list-item:first-child {
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.directory-list-item:last-child {
    margin-bottom: 0;
    border-bottom-left-radius: var(--border-radius);
    border-bottom-right-radius: var(--border-radius);
}

/* Search Box Styling */
.directory-search {
    position: relative;
    margin-bottom: 1.5rem;
}

.directory-search input {
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    width: 100%;
    transition: all var(--transition-speed) ease;
}

.directory-search input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    outline: 0;
}

.directory-search button {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    background: transparent;
    border: none;
    padding: 0 1rem;
    color: var(--secondary-color);
}

/* Rating Stars */
.directory-rating {
    color: #ffc107;
    font-size: 0.875rem;
}

/* Avatar/Logo Styling */
.directory-avatar {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    object-fit: cover;
    border: 1px solid var(--border-color);
}

.directory-avatar-lg {
    width: 96px;
    height: 96px;
}

.directory-avatar-sm {
    width: 48px;
    height: 48px;
}

/* Pagination Styling */
.directory-pagination {
    display: flex;
    justify-content: center;
    padding-left: 0;
    list-style: none;
    margin: 1.5rem 0;
}

.directory-pagination-item {
    margin: 0 0.25rem;
}

.directory-pagination-link {
    display: block;
    padding: 0.375rem 0.75rem;
    color: var(--primary-color);
    background-color: #fff;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
}

.directory-pagination-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #0a58ca;
}

.directory-pagination-link.active {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* Header Styling */
.directory-header {
    background-color: #fff;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
}

.directory-header-title {
    font-size: 1.75rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #212529;
}

.directory-header-subtitle {
    font-size: 1rem;
    color: var(--secondary-color);
    margin-bottom: 0;
}

/* Footer Styling */
.directory-footer {
    background-color: #f8f9fa;
    padding: 2rem 0;
    margin-top: 3rem;
    border-top: 1px solid var(--border-color);
}

.directory-footer-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #212529;
}

.directory-footer-link {
    color: var(--secondary-color);
    text-decoration: none;
    transition: all var(--transition-speed) ease;
}

.directory-footer-link:hover {
    color: #212529;
    text-decoration: underline;
}

/* Tabs Styling */
.directory-tabs {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 1rem;
    list-style: none;
    border-bottom: 1px solid var(--border-color);
}

.directory-tab-item {
    margin-bottom: -1px;
}

.directory-tab-link {
    display: block;
    padding: 0.5rem 1rem;
    border: 1px solid transparent;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    text-decoration: none;
    color: var(--secondary-color);
    transition: all var(--transition-speed) ease;
}

.directory-tab-link:hover {
    border-color: #e9ecef #e9ecef var(--border-color);
    color: #495057;
}

.directory-tab-link.active {
    color: #495057;
    background-color: #fff;
    border-color: var(--border-color) var(--border-color) #fff;
}

/* Like Button Styling */
.directory-like-btn {
    background: transparent;
    border: none;
    color: #6c757d;
    transition: all var(--transition-speed) ease;
    cursor: pointer;
}

.directory-like-btn:hover {
    color: #dc3545;
}

.directory-like-btn.active {
    color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .directory-section {
        padding: 1rem;
    }
    
    .directory-card {
        margin-bottom: 1rem;
    }
    
    .directory-header {
        padding: 1rem 0;
    }
    
    .directory-header-title {
        font-size: 1.5rem;
    }
}
