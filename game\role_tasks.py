# Role-specific tasks for the game
# This file contains the tasks for each role in the game

def get_tasks_for_role(role, tasks):
    """Get the tasks for a specific role.

    Args:
        role: The role to get tasks for
        tasks: The list of all tasks

    Returns:
        A list of tasks for the specified role
    """
    # Define tasks for each role
    role_tasks = {
        "applicant": [
            # Applicant role has 1 task (cover letter) as it's the entry point
            # This is an exception to the 3-task rule
            tasks[0]
        ],
        "junior_assistant": [
            # Junior Assistant role has 3 tasks
            tasks[1], tasks[2], tasks[3]
        ],
        "sales_associate": [
            # Sales Associate role has 3 tasks
            tasks[4], tasks[5], tasks[6]
        ],
        "marketing_associate": [
            # Create new tasks for marketing_associate
            {
                "id": "marketing_campaign",
                "manager": "vp",
                "description": "Design a marketing campaign for our new SolarMax Pro model targeting enterprise clients in the finance, healthcare, and manufacturing sectors.",
                "response_template": """# SolarMax Pro: Enterprise Excellence Campaign

## Campaign Overview
This integrated marketing campaign will position the SolarMax Pro as the essential enterprise solution for organizations seeking enhanced operational efficiency, security, and performance. By targeting key decision-makers in finance, healthcare, and manufacturing sectors, we will establish the SolarMax Pro as the premium choice for mission-critical operations.

## Target Audience Profiles

### Finance Sector
**Primary Decision-Makers:** CIOs, CTOs, Heads of Operations
**Key Pain Points:** Security vulnerabilities, system downtime costs, regulatory compliance
**Value Proposition:** Military-grade security protocols and 45% reduction in system downtime

### Healthcare Sector
**Primary Decision-Makers:** IT Directors, Operations Managers, Medical Technology Officers
**Key Pain Points:** Data protection, system reliability, integration with existing systems
**Value Proposition:** Seamless integration capabilities and 72-hour battery life for critical care settings

### Manufacturing Sector
**Primary Decision-Makers:** Production Managers, Plant Directors, Automation Engineers
**Key Pain Points:** Operational inefficiency, equipment downtime, complex workflows
**Value Proposition:** 27% increase in operational efficiency and AI-powered optimization

## Campaign Strategy

### Phase 1: Awareness (Months 1-2)
- **Executive Roundtables:** Industry-specific events featuring current clients sharing ROI success stories
- **Targeted Digital Advertising:** LinkedIn and industry publication campaigns with sector-specific messaging
- **Analyst Relations:** Briefings with Gartner and Forrester to position X7 in upcoming reports
- **Content Marketing:** White papers on "The Future of Enterprise Operations" tailored to each vertical

### Phase 2: Consideration (Months 3-4)
- **Solution Webinars:** Technical deep-dives showcasing X7 capabilities for each industry
- **Case Study Campaign:** Sector-specific success stories distributed through email and social channels
- **Product Comparison Tools:** Interactive online tools comparing X7 to competitors on key metrics
- **Free Assessment Offer:** Operational efficiency assessment with personalized ROI projection

### Phase 3: Decision (Months 5-6)
- **Executive Demo Program:** Personalized on-site demonstrations for qualified prospects
- **Implementation Workshops:** Planning sessions showing seamless integration process
- **Pilot Program:** 30-day trial with dedicated support for qualified enterprise prospects
- **Incentive Offers:** Limited-time pricing and premium support packages for early adopters

## Creative Approach

### Campaign Tagline
"Transform Your Enterprise Operations: Faster. Smarter. More Secure."

### Key Messaging Pillars
1. **Performance Excellence:** "35% faster processing for real-time decision making"
2. **Intelligent Adaptation:** "AI-powered optimization that learns your business"
3. **Uncompromising Security:** "Military-grade protection for mission-critical data"
4. **Operational Continuity:** "72-hour battery life ensures business never stops"

### Visual Identity
- Clean, sophisticated design language emphasizing precision and performance
- Industry-specific imagery showing X7 in relevant environments
- Data visualization elements highlighting performance metrics
- Blue and silver color palette conveying trust, technology, and premium positioning

## Channel Strategy

### Digital Channels
- **LinkedIn Campaign:** Targeted ads to senior decision-makers with industry-specific messaging
- **Industry Publications:** Digital advertising in top finance, healthcare, and manufacturing publications
- **Programmatic Display:** Retargeting campaign focusing on visitors to product pages
- **Email Marketing:** Segmented nurture streams by industry and buying stage

### Traditional Channels
- **Print Advertising:** Full-page ads in Harvard Business Review, Healthcare Informatics, and Manufacturing Today
- **Direct Mail:** Premium executive mailers with personalized ROI calculators
- **Industry Events:** Sponsorship of key conferences with X7 demonstration stations
- **Sales Enablement:** Sector-specific presentation decks and conversation guides

## Budget Allocation
- Digital Marketing: 40% ($400,000)
- Events & Sponsorships: 25% ($250,000)
- Content Development: 15% ($150,000)
- Sales Enablement: 10% ($100,000)
- Traditional Advertising: 10% ($100,000)

## Success Metrics

### Marketing KPIs
- Generate 500+ qualified enterprise leads across target sectors
- Achieve 25% increase in website traffic from target industries
- Secure 15+ media placements in industry publications
- Attain 30% email open rates and 5% click-through rates

### Business Outcomes
- Close $5M in new X7 sales within 6 months
- Achieve 20% conversion rate from pilot program to purchase
- Increase market share in target industries by 3-5 percentage points
- Establish 15+ reference customers across target sectors

## Implementation Timeline

**Month 1:** Campaign preparation, content development, sales training
**Month 2:** Launch awareness phase, executive roundtables begin
**Month 3:** Roll out consideration phase, activate webinar series
**Month 4:** Deploy case studies, launch comparison tools
**Month 5:** Initiate decision phase, begin pilot program
**Month 6:** Full campaign deployment, ROI analysis, planning for phase 2

## Conclusion
The SolarMax Pro Enterprise Excellence Campaign leverages a strategic, multi-channel approach to position our premium product as the essential solution for enterprise operations. By addressing specific pain points in our target industries and clearly communicating our unique value proposition, we will drive significant market share growth in the enterprise segment.""",
                "feedback": {
                    "good": "Excellent marketing campaign! Your strategy is comprehensive and well-targeted to our key enterprise segments. The phased approach and industry-specific messaging will resonate strongly with decision-makers. I particularly appreciate the detailed budget allocation and clear success metrics. This is exactly the kind of strategic thinking we need in our marketing department.",
                    "okay": "Good campaign outline with solid targeting. The overall structure is sound, but it could use more specific messaging for each industry vertical and clearer differentiation from competitors. The budget allocation seems reasonable, though I'd like to see more detail on the expected ROI. With some refinement, this could be a very effective campaign.",
                    "bad": "This campaign needs significant improvement. It lacks clear targeting and differentiated messaging for our enterprise segments. The budget allocation is too vague, and the success metrics aren't specific enough to measure effectiveness. Please revise with more industry-specific content and a clearer value proposition that addresses the unique needs of each target sector."
                }
            },
            {
                "id": "social_media_strategy",
                "manager": "vp",
                "description": "Develop a B2B social media strategy to promote our enterprise solutions across LinkedIn, Twitter, and industry forums.",
                "response_template": """# B2B Social Media Strategy: Enterprise Solutions

## Executive Summary
This comprehensive social media strategy will position Rwenzori Innovations as a thought leader in enterprise technology solutions while driving qualified leads and supporting our sales cycle. By leveraging LinkedIn, Twitter, and industry forums with targeted, value-driven content, we will build meaningful relationships with decision-makers and influencers in our key market segments.

## Strategic Objectives
1. Establish Rwenzori Innovations as a trusted authority in enterprise technology solutions
2. Generate and nurture qualified leads for our enterprise product portfolio
3. Support the sales cycle with targeted content for each buyer journey stage
4. Build a community of advocates and influencers around our brand
5. Increase share of voice in conversations about enterprise technology solutions

## Target Audience Analysis

### Primary Audience: Decision Makers
- **Titles:** CIOs, CTOs, IT Directors, Operations Executives
- **Demographics:** 35-55 years old, primarily male (65%), enterprise organizations
- **Platforms:** LinkedIn (primary), Industry Forums, Limited Twitter usage
- **Content Preferences:** Data-driven insights, ROI analysis, peer case studies
- **Pain Points:** Operational efficiency, security vulnerabilities, integration challenges

### Secondary Audience: Influencers
- **Titles:** IT Managers, Department Heads, Technical Evaluators
- **Demographics:** 30-45 years old, mixed gender, mid to large organizations
- **Platforms:** LinkedIn, Twitter, Active in Industry Forums
- **Content Preferences:** Technical specifications, comparison guides, how-to content
- **Pain Points:** Implementation complexity, user adoption, technical support

## Platform Strategy

### LinkedIn Strategy
**Objective:** Lead generation and thought leadership

**Content Mix:**
- Thought leadership articles (30%)
- Case studies and success stories (25%)
- Industry insights and trend analysis (20%)
- Product announcements and updates (15%)
- Company culture and behind-the-scenes (10%)

**Tactical Approach:**
- Executive thought leadership program with weekly posts from leadership team
- LinkedIn Live sessions featuring customer success stories and product demos
- Sponsored content targeting decision-makers at companies with 1000+ employees
- LinkedIn Groups participation in enterprise technology communities
- Advanced audience targeting using intent data and account-based marketing

**Posting Cadence:**
- Company page: 5-7 posts per week
- Executive profiles: 2-3 posts per week each
- LinkedIn Articles: 2 per month
- LinkedIn Live: Bi-weekly

### Twitter Strategy
**Objective:** Industry engagement and real-time connection

**Content Mix:**
- Industry news and commentary (35%)
- Quick tips and insights (25%)
- Event coverage and announcements (20%)
- Engagement with industry influencers (15%)
- Product highlights (5%)

**Tactical Approach:**
- Real-time engagement with industry conversations using hashtag monitoring
- Twitter chats with industry experts on enterprise technology topics
- Live-tweeting from major industry events and conferences
- Rapid response to industry news with expert commentary
- Multimedia content optimized for Twitter (short videos, infographics)

**Posting Cadence:**
- 3-5 original tweets per day
- 5-7 engagement actions (replies, retweets) per day
- Weekly scheduled Twitter chat participation

### Industry Forums Strategy
**Objective:** Deep expertise demonstration and community building

**Target Forums:**
- Spiceworks Community
- Gartner Peer Insights
- Reddit r/sysadmin and r/ITManagement
- Industry-specific forums (HealthIT, FinTechTalk, etc.)

**Tactical Approach:**
- Dedicated subject matter experts assigned to each forum
- Regular contribution of high-value, non-promotional content
- Direct problem-solving and question answering
- Hosting of AMAs (Ask Me Anything) sessions with product experts
- Careful monitoring for product feedback and feature requests

**Engagement Cadence:**
- Daily monitoring and response
- 3-5 substantial contributions per week per forum
- Monthly featured expert content

## Content Strategy

### Content Pillars
1. **Operational Excellence:** Content focused on efficiency, automation, and optimization
2. **Security & Compliance:** Addressing enterprise security challenges and regulatory requirements
3. **Digital Transformation:** Insights on technology-driven business evolution
4. **Implementation Success:** Best practices, case studies, and implementation guides
5. **Future of Enterprise Tech:** Forward-looking content on emerging technologies and trends

### Content Types by Funnel Stage

**Awareness Stage:**
- Industry trend reports and analysis
- Educational infographics and statistics
- Thought leadership articles on business challenges

**Consideration Stage:**
- Product comparison guides
- Case studies and success stories
- Webinar recordings and expert interviews

**Decision Stage:**
- ROI calculators and value assessment tools
- Implementation roadmaps
- Technical specifications and compatibility guides

**Retention Stage:**
- Best practice guides
- Advanced feature tutorials
- User community spotlights

## Campaign Integration

### Product Launch Support
- Pre-launch teaser campaign (2 weeks)
- Launch day coordinated posting across all platforms
- Post-launch success stories and testimonials
- Ongoing feature highlight series

### Event Amplification
- Pre-event promotion and speaker highlights
- Live coverage and real-time engagement
- Post-event content repurposing and highlight reels
- Virtual event integration with social platforms

### Sales Enablement
- Social selling training for sales team
- Creation of shareable content for sales representatives
- Lead handoff process for social media inquiries
- Closed-loop reporting on social-generated leads

## Measurement Framework

### Awareness Metrics
- Follower growth rate by platform
- Impressions and reach
- Share of voice vs. competitors
- Sentiment analysis

### Engagement Metrics
- Engagement rate by post and platform
- Amplification rate (shares/retweets)
- Comment quality and sentiment
- Video view rates and completion percentages

### Conversion Metrics
- Click-through rates to website
- Social media lead generation
- Conversion path analysis
- Cost per lead by platform and campaign

### Business Impact Metrics
- Influenced pipeline
- Social-attributed revenue
- Customer acquisition cost
- Retention and upsell correlation

## Resource Requirements

### Team Structure
- Social Media Manager (1 FTE)
- Content Creator (1 FTE)
- Community Manager (1 FTE)
- Executive Program Coordinator (0.5 FTE)

### Technology Stack
- Social media management platform (Hootsuite Enterprise)
- Social listening tool (Brandwatch)
- Analytics platform (Sprout Social)
- Content calendar (Asana)
- Design resources (Canva Pro, Adobe Creative Suite)

### Budget Allocation
- Content creation: 40% ($80,000)
- Paid social promotion: 35% ($70,000)
- Tools and technology: 15% ($30,000)
- Training and development: 10% ($20,000)

## Implementation Timeline

**Month 1:** Platform audit, audience research, strategy refinement
**Month 2:** Content development, team training, baseline measurement
**Month 3:** Full strategy launch, initial campaign deployment
**Months 4-5:** Optimization based on performance data
**Month 6:** Comprehensive performance review and strategy adjustment

## Risk Management

### Potential Challenges
- Low initial engagement from target audience
- Negative comments or customer service issues
- Competitive social media activity
- Algorithm changes affecting organic reach

### Mitigation Strategies
- Paid promotion to jumpstart engagement
- Detailed crisis communication plan
- Competitive monitoring and agile response capability
- Platform diversification and owned media development

## Conclusion
This B2B social media strategy provides a comprehensive framework for establishing Rwenzori Innovations as a thought leader in enterprise technology solutions while driving measurable business results. By focusing on high-value content delivered through the right channels to precisely targeted audiences, we will build meaningful relationships with decision-makers and influencers in our key market segments.""",
                "feedback": {
                    "good": "Outstanding social media strategy! Your platform-specific approach and content calendar show excellent understanding of B2B marketing. The detailed audience analysis and content mapping to the buyer journey demonstrate strategic thinking. I particularly appreciate the measurement framework and how you've tied social activities to business outcomes. This is exactly the kind of comprehensive strategy we need.",
                    "okay": "Solid strategy with good platform selection. The content pillars are well-defined, but could use more specific KPIs and measurement approaches. The implementation timeline is realistic, though I'd like to see more detail on how we'll integrate this with our other marketing channels. With some refinement, this could be a very effective strategy.",
                    "bad": "This strategy needs significant improvement. It doesn't adequately address the unique challenges of B2B social media marketing. The platform selection lacks justification, and the content strategy is too generic for our enterprise audience. Please revise with more specific tactics, clearer KPIs, and a more detailed implementation plan."
                }
            },
            {
                "id": "content_marketing",
                "manager": "vp",
                "description": "Create a content marketing plan that establishes our company as a thought leader in enterprise technology solutions.",
                "response_template": """# Enterprise Technology Thought Leadership: Content Marketing Plan

## Strategic Overview
This content marketing plan will establish Rwenzori Innovations as the authoritative voice in enterprise technology solutions through a multi-channel approach that delivers high-value, educational content to decision-makers at every stage of their buyer journey. By focusing on solving real business challenges rather than product promotion, we will build trust, generate qualified leads, and support the entire sales cycle.

## Market Analysis

### Industry Landscape
- Enterprise technology market growing at 8.2% annually
- Increasing focus on digital transformation across industries
- Rising concerns about security, efficiency, and integration
- Information overload making it difficult to capture attention
- Buyers completing 70% of purchase journey before contacting sales

### Target Audience Personas

**Strategic Decision Makers**
- C-suite and VP-level executives
- Primary concerns: ROI, competitive advantage, strategic alignment
- Content preferences: Executive summaries, case studies, industry trends
- Channels: LinkedIn, executive events, email, industry publications

**Technical Evaluators**
- IT Directors, System Architects, Technical Managers
- Primary concerns: Implementation, integration, security, performance
- Content preferences: Technical white papers, webinars, comparison guides
- Channels: Industry forums, webinars, technical blogs, YouTube

**End Users**
- Department Managers, Operations Teams
- Primary concerns: Usability, training, efficiency, support
- Content preferences: How-to guides, video tutorials, user communities
- Channels: Knowledge base, user forums, email newsletters, YouTube

## Content Strategy

### Thought Leadership Pillars

1. **Future of Enterprise Operations**
   - Focus: How AI, automation, and connectivity are transforming business operations
   - Formats: Research reports, executive interviews, trend analysis
   - Example: "The Connected Enterprise: 2025 and Beyond" annual report

2. **Operational Excellence**
   - Focus: Best practices for optimizing enterprise processes and systems
   - Formats: Case studies, benchmarking tools, methodology guides
   - Example: "Operational Excellence Framework" interactive assessment

3. **Security & Compliance**
   - Focus: Emerging threats, regulatory changes, and protection strategies
   - Formats: Security bulletins, compliance guides, expert roundtables
   - Example: "Enterprise Security Quarterly Briefing" webinar series

4. **Digital Transformation Journey**
   - Focus: Real-world transformation stories and implementation roadmaps
   - Formats: Customer success stories, transformation playbooks, ROI models
   - Example: "Digital Transformation Heroes" video interview series

### Content Mapping to Buyer Journey

**Awareness Stage (40% of content)**
- Thought leadership articles and blog posts
- Industry research reports and trend analysis
- Educational infographics and data visualizations
- Podcast episodes with industry experts

**Consideration Stage (30% of content)**
- In-depth white papers and eBooks
- Webinars and virtual events
- Solution comparison guides
- Expert Q&A sessions and interviews

**Decision Stage (20% of content)**
- Detailed case studies with results
- ROI calculators and assessment tools
- Implementation guides and roadmaps
- Product demonstration videos

**Retention & Advocacy Stage (10% of content)**
- Customer success spotlights
- Advanced usage tutorials
- User community content
- Industry recognition and awards

## Channel Strategy

### Owned Media

**Corporate Blog**
- Publishing cadence: 3 posts per week
- Content mix: Thought leadership (40%), educational (40%), news (20%)
- SEO focus: Enterprise technology keywords, long-tail problem queries

**Resource Center**
- Comprehensive library of downloadable assets
- Gated premium content for lead generation
- Organized by topic, industry, and buyer stage

**Email Newsletter**
- Weekly digest of valuable content
- Segmented by persona and industry
- Progressive profiling to refine targeting

**Webinar Program**
- Monthly thought leadership webinars
- Quarterly technical deep-dives
- On-demand library with registration

### Earned Media

**Media Relations**
- Quarterly press releases on industry insights
- Executive bylines in top industry publications
- Data-driven story pitches to technology journalists

**Speaking Engagements**
- Target 10-15 key industry conferences annually
- Focus on educational, non-promotional content
- Amplification strategy for each presentation

**Analyst Relations**
- Regular briefings with Gartner, Forrester, IDC
- Participation in relevant research
- Amplification of favorable mentions and reports

### Paid Media

**Content Syndication**
- Targeted distribution through industry platforms
- Account-based marketing to priority prospects
- Retargeting campaigns for engaged audiences

**Sponsored Content**
- Native advertising in key industry publications
- Sponsored research reports with media partners
- Promoted social media content to extend reach

### Social Media

**LinkedIn**
- Primary platform for executive audience
- Daily posting schedule with thought leadership focus
- LinkedIn Live sessions with industry experts

**Twitter**
- Real-time industry engagement
- Event amplification and live coverage
- Thought leadership content distribution

**YouTube**
- Educational video series and webinar recordings
- Technical tutorials and demonstrations
- Customer success story videos

## Content Production Framework

### Content Development Process
1. Quarterly content planning aligned with business objectives
2. Subject matter expert interviews and research
3. Professional writing and design resources
4. Multi-stage review process for accuracy and quality
5. Optimization for search and user experience
6. Distribution and promotion planning
7. Performance measurement and optimization

### Content Repurposing Strategy
- Each major content piece will be repurposed into 5-7 derivative assets
- Example: Research report → Webinar → Blog series → Infographic → Social posts
- Modular content approach for efficient customization

### Editorial Calendar
Detailed 90-day rolling calendar with:
- Content themes aligned with business priorities
- Publishing dates and promotion schedules
- Assigned responsibilities and deadlines
- Content status tracking and approvals

## Lead Generation & Nurturing

### Lead Capture Strategy
- Progressive profiling across multiple content interactions
- Value-based gating for premium content
- Clear privacy policies and preference management
- Seamless integration with marketing automation

### Nurture Programs
- Industry-specific nurture tracks
- Persona-based content journeys
- Behavioral triggers based on content consumption
- Sales enablement content for later-stage prospects

### Lead Scoring Model
- Content engagement depth and frequency
- Topic affinity analysis
- Buying intent signals
- Account-level engagement scoring

## Measurement Framework

### Content Performance Metrics
- Traffic and engagement metrics by piece and channel
- Conversion rates at each stage of funnel
- Time to conversion and content path analysis
- Search ranking improvements for target keywords

### Business Impact Metrics
- Marketing qualified leads (MQLs) generated
- Sales qualified leads (SQLs) influenced
- Pipeline contribution and velocity impact
- Customer acquisition cost and lifetime value

### Reporting Cadence
- Weekly performance dashboards
- Monthly content effectiveness review
- Quarterly strategic assessment and planning
- Annual comprehensive program evaluation

## Resource Requirements

### Team Structure
- Content Marketing Manager (1 FTE)
- Content Writers (2 FTEs)
- Graphic Designer (1 FTE)
- Video Producer (1 FTE)
- SEO Specialist (0.5 FTE)

### External Resources
- Industry subject matter experts
- Specialized content agencies for major assets
- Video production services
- Translation services for global markets

### Technology Stack
- Content management system
- Marketing automation platform
- SEO and content optimization tools
- Analytics and attribution software
- Digital asset management system

### Budget Allocation
- Content creation: 50% ($250,000)
- Content promotion: 30% ($150,000)
- Technology and tools: 15% ($75,000)
- Training and development: 5% ($25,000)

## Implementation Roadmap

**Q1: Foundation Building**
- Audience research and persona refinement
- Content audit and gap analysis
- Editorial guidelines and process development
- Core asset development for each pillar

**Q2: Program Launch**
- Website resource center launch
- Thought leadership content series kickoff
- Lead nurture program implementation
- Measurement framework activation

**Q3: Optimization & Expansion**
- Performance analysis and content optimization
- Expansion of content types and channels
- Advanced lead scoring implementation
- Sales enablement content development

**Q4: Scale & Innovate**
- Account-based content marketing pilot
- Interactive content development
- Personalization implementation
- Annual planning for year two

## Conclusion
This comprehensive content marketing plan provides the strategic framework, tactical approach, and resource requirements to establish Rwenzori Innovations as the authoritative thought leader in enterprise technology solutions. By consistently delivering high-value content that addresses real business challenges, we will build trust with decision-makers, generate qualified leads, and support the entire sales cycle while positioning our brand as an innovative leader in the industry.""",
                "feedback": {
                    "good": "Exceptional content marketing plan! Your thought leadership strategy and content distribution approach are comprehensive and strategic. The detailed buyer personas and content mapping show deep understanding of our audience's needs at each stage. I particularly appreciate how you've tied content metrics to business outcomes and provided a clear implementation roadmap. This is exactly the kind of strategic thinking we need in our marketing team.",
                    "okay": "Good content plan with solid topic ideas. The overall structure is sound, but could use more specific distribution channels and performance metrics. The implementation timeline is realistic, though I'd like to see more detail on how we'll resource this plan effectively. With some refinement on measurement and execution details, this could be a very effective content strategy.",
                    "bad": "This content plan needs significant improvement. It lacks a cohesive strategy and doesn't adequately address our thought leadership goals. The topics are too generic, and there's insufficient detail on content production, distribution channels, and performance measurement. Please revise with more specific content themes aligned to our business objectives and a clearer execution plan."
                }
            }
        ],
        "sales_manager": [
            {
                "id": "sales_team_structure",
                "manager": "vp",
                "description": "Design an optimal sales team structure for our enterprise solutions division.",
                "response_template": """# Sales Team Structure\n\nThis structure maximizes market coverage and customer relationship management.""",
                "feedback": {
                    "good": "Excellent sales team structure! Your design balances territory coverage and specialization effectively.",
                    "okay": "Solid team structure with good role definitions. Could use more detail on compensation models.",
                    "bad": "This structure needs significant improvement. It doesn't address our enterprise sales model."
                }
            },
            {
                "id": "sales_training",
                "manager": "vp",
                "description": "Develop a training program for our enterprise sales team.",
                "response_template": """# Sales Training Program\n\nThis program equips our team with consultative selling skills and technical knowledge.""",
                "feedback": {
                    "good": "Outstanding training program! Your approach combines technical knowledge with consultative selling skills.",
                    "okay": "Good training outline with solid modules. Could use more specific role-playing scenarios.",
                    "bad": "This program needs significant improvement. It doesn't address enterprise solution selling."
                }
            },
            {
                "id": "sales_forecasting",
                "manager": "vp",
                "description": "Create a sales forecasting methodology for our enterprise division.",
                "response_template": """# Sales Forecasting Methodology\n\nThis approach combines quantitative analysis with qualitative input for accurate projections.""",
                "feedback": {
                    "good": "Excellent forecasting methodology! Your combination of inputs creates a balanced approach.",
                    "okay": "Solid forecasting approach with good pipeline stages. Could use more specific weightings.",
                    "bad": "This methodology needs significant improvement. It lacks the rigor needed for accurate forecasting."
                }
            }
        ],
        "advertising_manager": [
            {
                "id": "ad_campaign",
                "manager": "vp",
                "description": "Design an advertising campaign for our enterprise solutions.",
                "response_template": """# Advertising Campaign\n\nThis campaign will establish Rwenzori Innovations as a premier provider of enterprise solutions.""",
                "feedback": {
                    "good": "Excellent advertising campaign! Your approach is perfectly aligned with our enterprise audience.",
                    "okay": "Good campaign with solid media choices. Could use more specific creative direction.",
                    "bad": "This campaign needs significant improvement. It doesn't communicate our value proposition."
                }
            },
            {
                "id": "trade_show",
                "manager": "vp",
                "description": "Create a plan for our presence at the upcoming Enterprise Technology Expo.",
                "response_template": """# Trade Show Strategy\n\nThis plan maximizes our presence and lead generation at the industry's premier event.""",
                "feedback": {
                    "good": "Outstanding trade show plan! Your approach to booth design and lead generation is strategic.",
                    "okay": "Solid trade show plan with good elements. Could use more specific follow-up processes.",
                    "bad": "This plan needs significant improvement. It lacks the focus needed for this major event."
                }
            },
            {
                "id": "brand_guidelines",
                "manager": "vp",
                "description": "Update our brand guidelines for enterprise technology positioning.",
                "response_template": """# Brand Guidelines\n\nThese guidelines strengthen our enterprise positioning while maintaining our core brand equity.""",
                "feedback": {
                    "good": "Excellent brand guidelines! Your updates maintain our identity while elevating our positioning.",
                    "okay": "Good brand update with solid visual elements. Could use more specific application examples.",
                    "bad": "These guidelines need significant improvement. They don't evolve our brand for enterprise positioning."
                }
            }
        ],
        "vp_marketing": [
            {
                "id": "marketing_strategy",
                "manager": "coo",
                "description": "Develop a marketing strategy aligned with our company's growth objectives.",
                "response_template": """# Annual Marketing Strategy\n\nThis strategy aligns our marketing investments with the company's growth objectives.""",
                "feedback": {
                    "good": "Exceptional marketing strategy! Your approach balances brand building with demand generation.",
                    "okay": "Solid marketing strategy with good channel mix. Could use more specific budget allocation.",
                    "bad": "This strategy needs significant improvement. It lacks the cohesion needed for our objectives."
                }
            },
            {
                "id": "marketing_org",
                "manager": "coo",
                "description": "Propose an organizational structure for our marketing department.",
                "response_template": """# Marketing Department Structure\n\nThis structure balances specialization with integration for maximum effectiveness.""",
                "feedback": {
                    "good": "Excellent organizational design! Your structure balances specialization and collaboration.",
                    "okay": "Good organizational structure with clear roles. Could use more specific transition planning.",
                    "bad": "This structure needs significant improvement. It doesn't address our growth needs."
                }
            },
            {
                "id": "marketing_metrics",
                "manager": "coo",
                "description": "Develop a marketing measurement framework that demonstrates business contribution.",
                "response_template": """# Marketing Measurement Framework\n\nThis framework connects marketing activities to business outcomes through measurement.""",
                "feedback": {
                    "good": "Outstanding measurement framework! Your approach to connecting activities to outcomes is sophisticated.",
                    "okay": "Solid measurement approach with good metrics. Could use more specific attribution modeling.",
                    "bad": "This framework needs significant improvement. It doesn't demonstrate marketing's contribution."
                }
            }
        ]
    }

    # Return tasks for the requested role, or create appropriate generic tasks
    if role in role_tasks:
        return role_tasks[role]
    else:
        # BUGFIX: Ensure all roles (except applicant) have exactly 3 tasks
        # Determine the appropriate manager based on the role's position in the hierarchy
        manager = "vp"  # Default manager

        # Determine the appropriate manager based on role
        if role in ["junior_assistant"]:
            manager = "hr"
        elif role in ["sales_associate", "marketing_associate", "sales_manager", "advertising_manager"]:
            manager = "vp_marketing"
        elif role in ["service_associate", "production_associate", "facilities_associate",
                     "service_manager", "production_manager", "facilities_manager"]:
            manager = "vp_operations"
        elif role in ["accounts_receivable_associate", "accounts_payable_associate",
                     "accounts_receivable_manager", "accounts_payable_manager"]:
            manager = "vp_finance"
        elif role in ["vp_marketing", "vp_operations", "vp_finance"]:
            manager = "coo"
        elif role in ["coo"]:
            manager = "ceo"
        elif role in ["ceo", "shareholders"]:
            manager = "board"

        # Create 3 generic tasks for this role with the appropriate manager
        role_display = role.replace("_", " ").title()
        return [
            {
                "id": f"task_{i+1}_for_{role}",
                "manager": manager,
                "description": f"Complete task {i+1} for the {role_display} role. This task is designed to test your skills in this position.",
                "response_template": f"""# Response for {role_display} Task {i+1}

## Overview
This is a comprehensive response addressing all requirements for Task {i+1} in the {role_display} role.

## Key Points
- Strategic approach to solving the problem
- Detailed implementation plan
- Analysis of potential challenges and solutions
- Metrics for measuring success

## Conclusion
This solution will effectively address the requirements while providing measurable business value.""",
                "feedback": {
                    "good": f"Excellent work! Your response for this {role_display} task demonstrates strong understanding and strategic thinking. You've addressed all the key requirements and provided valuable insights. You're ready to take on more challenges in this role.",
                    "okay": f"Good effort on this {role_display} task. You've covered the basics, but there's room for more depth and strategic thinking. Continue developing your skills in this area, and you'll make great progress.",
                    "bad": f"This response for the {role_display} task needs significant improvement. It lacks the depth and strategic thinking required for this role. Please review the requirements carefully and provide a more comprehensive solution."
                }
            } for i in range(3)
        ]

# Function to generate role progression visualization
def generate_role_progression_html(current_role, completed_roles):
    """Generate HTML for role progression visualization.

    Args:
        current_role: The current role of the player
        completed_roles: List of roles the player has completed

    Returns:
        HTML string for the role progression visualization
    """
    # Define the role progression path organized by department
    role_path = [
        # Entry Level
        "applicant", "junior_assistant",
        # Marketing Department
        "sales_associate", "marketing_associate",
        "sales_manager", "advertising_manager", "vp_marketing",
        # Operations Department
        "service_associate", "production_associate", "facilities_associate",
        "service_manager", "production_manager", "facilities_manager", "vp_operations",
        # Finance Department
        "accounts_receivable_associate", "accounts_payable_associate",
        "accounts_receivable_manager", "accounts_payable_manager", "vp_finance",
        # HR Department
        "hr_coordinator", "hr_manager", "hr_director",
        # Executive Level
        "coo", "ceo", "shareholders"
    ]

    # Group roles by department for visualization
    departments = {
        "Entry": ["applicant", "junior_assistant"],
        "Marketing": ["sales_associate", "marketing_associate", "sales_manager", "advertising_manager", "vp_marketing"],
        "Operations": ["service_associate", "production_associate", "facilities_associate", "service_manager", "production_manager", "facilities_manager", "vp_operations"],
        "Finance": ["accounts_receivable_associate", "accounts_payable_associate", "accounts_receivable_manager", "accounts_payable_manager", "vp_finance"],
        "HR": ["hr_coordinator", "hr_manager", "hr_director"],
        "Executive": ["coo", "ceo", "shareholders"]
    }

    # Find the current department
    current_department = None
    for dept, roles in departments.items():
        if current_role in roles:
            current_department = dept
            break

    # Generate HTML for the visualization
    html = '<div class="role-progression">'
    html += '<h3>Career Progression</h3>'

    # Add department headers
    html += '<div class="department-headers">'
    for dept in departments.keys():
        active_class = 'active-department' if dept == current_department else ''
        html += f'<div class="department-header {active_class}">{dept}</div>'
    html += '</div>'

    # Add role path
    html += '<div class="role-path">'

    for i, role in enumerate(role_path):
        # Determine the status of this role
        if role == current_role:
            status = 'current'
        elif role in completed_roles:
            status = 'completed'
        else:
            status = 'future'

        # Add the role node
        html += f'<div class="role-node {status}" title="{role.replace("_", " ").title()}">{i+1}</div>'

        # Add connector if not the last role
        if i < len(role_path) - 1:
            connector_class = 'completed' if role in completed_roles else 'future'
            html += f'<div class="role-connector {connector_class}"></div>'

    html += '</div>'
    html += f'<div class="current-role">Current Role: <strong>{current_role.replace("_", " ").title()}</strong></div>'

    # Add progress indicator
    current_index = role_path.index(current_role) if current_role in role_path else 0
    progress_percent = int((current_index / len(role_path)) * 100)
    html += f'<div class="career-progress">Career Progress: <div class="progress-bar"><div class="progress-fill" style="width: {progress_percent}%"></div></div> {progress_percent}%</div>'

    html += '</div>'

    return html
