{% extends 'base/layout.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Create {% if form.entity_type.value == 'community' %}Community{% else %}Company{% endif %} - 24seven{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- Header -->
            <div class="text-center mb-5">
                <h1 class="display-5 fw-bold mb-3">Create Your {% if form.entity_type.value == 'community' %}Community{% else %}Workspace{% endif %}</h1>
                <p class="lead text-muted">
                    {% if form.entity_type.value == 'community' %}
                        Set up your community to start sharing knowledge
                    {% else %}
                        Set up your workspace to start collaborating
                    {% endif %}
                </p>
            </div>

            <!-- Progress Steps -->
            <div class="progress mb-4" style="height: 4px;">
                <div class="progress-bar" role="progressbar" style="width: 0%;"
                     data-current-step="1" data-total-steps="3"></div>
            </div>

            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <form method="post" id="companyForm" class="needs-validation" novalidate>
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Step 1: Basic Information -->
                        <div class="step" data-step="1">
                            <h3 class="h5 mb-4">Workspace Information</h3>

                            <div class="mb-3">
                                <label for="{{ form.entity_type.id_for_label }}" class="form-label">
                                    Workspace Type <span class="text-danger">*</span>
                                </label>
                                {% render_field form.entity_type class="form-select form-select-lg mb-3" %}
                                {% if form.entity_type.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.entity_type.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-text">
                                    <strong>Company:</strong> For businesses and organizations with teams<br>
                                    <strong>Community:</strong> For community-driven AI assistants with user contributions
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    <span id="entity-name-label">Name</span> <span class="text-danger">*</span>
                                </label>
                                {% render_field form.name class="form-control form-control-lg" placeholder="Enter name" %}
                                {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.name.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.mission.id_for_label }}" class="form-label">
                                    <span id="mission-label">Mission</span>
                                </label>
                                {% render_field form.mission class="form-control" placeholder="Brief description of the mission" rows="3" %}
                                <div class="form-text" id="mission-help">
                                    This helps define the purpose of your workspace.
                                </div>
                            </div>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="{{ form.industry.id_for_label }}" class="form-label">
                                        Industry
                                    </label>
                                    {% render_field form.industry class="form-select" %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.size.id_for_label }}" class="form-label">
                                        Company Size
                                    </label>
                                    {% render_field form.size class="form-select" %}
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Contact Details -->
                        <div class="step d-none" data-step="2">
                            <h3 class="h5 mb-4">Contact Information</h3>

                            <div class="mb-3">
                                <label for="{{ form.website.id_for_label }}" class="form-label">
                                    Company Website
                                </label>
                                {% render_field form.website class="form-control" placeholder="https://example.com" %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.contact_email.id_for_label }}" class="form-label">
                                    Contact Email
                                </label>
                                {% render_field form.contact_email class="form-control" placeholder="<EMAIL>" %}
                                <div class="form-text">
                                    This email will be visible to your team members.
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.contact_phone.id_for_label }}" class="form-label">
                                    Contact Phone
                                </label>
                                {% render_field form.contact_phone class="form-control" placeholder="+1234567890" %}
                            </div>
                        </div>

                        <!-- Step 3: Settings & Preferences -->
                        <div class="step d-none" data-step="3">
                            <h3 class="h5 mb-4">Preferences</h3>

                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <label for="{{ form.timezone.id_for_label }}" class="form-label">
                                        Timezone <span class="text-danger">*</span>
                                    </label>
                                    {% render_field form.timezone class="form-select" %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.language.id_for_label }}" class="form-label">
                                        Language <span class="text-danger">*</span>
                                    </label>
                                    {% render_field form.language class="form-select" %}
                                </div>
                            </div>

                            <div class="form-check form-switch mb-3">
                                {% render_field form.list_in_directory class="form-check-input" %}
                                <label class="form-check-label" for="{{ form.list_in_directory.id_for_label }}">
                                    List in Public Directory
                                </label>
                                <div class="form-text">
                                    Make your company visible in our public directory.
                                </div>
                            </div>

                            <div class="alert alert-info">
                                <h6 class="alert-heading">
                                    <i class="bi bi-info-circle me-2"></i>
                                    What's Next?
                                </h6>
                                <p class="small mb-0">
                                    After creating your company, you'll be able to:
                                </p>
                                <ul class="small mb-0">
                                    <li>Invite team members</li>
                                    <li>Set up AI assistants</li>
                                    <li>Upload company content</li>
                                    <li>Configure advanced settings</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Navigation Buttons -->
                        <div class="d-flex justify-content-between mt-4">
                            <button type="button" class="btn btn-light prev-step" style="display: none;">
                                <i class="bi bi-arrow-left me-2"></i>
                                Back
                            </button>
                            <div class="ms-auto">
                                <button type="button" class="btn btn-primary next-step">
                                    Next
                                    <i class="bi bi-arrow-right ms-2"></i>
                                </button>
                                <button type="submit" class="btn btn-success submit-step" style="display: none;">
                                    <i class="bi bi-check2 me-2"></i>
                                    Create {% if form.entity_type.value == 'community' %}Community{% else %}Company{% endif %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Help Text -->
            <div class="text-center mt-4">
                <p class="text-muted small">
                    Need help? <a href="{% url 'contact' %}">Contact our support team</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.step {
    transition: opacity 0.3s ease-in-out;
}
.progress-bar {
    transition: width 0.3s ease-in-out;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('companyForm');
    const steps = document.querySelectorAll('.step');
    const progressBar = document.querySelector('.progress-bar');
    const prevBtn = document.querySelector('.prev-step');
    const nextBtn = document.querySelector('.next-step');
    const submitBtn = document.querySelector('.submit-step');
    const totalSteps = steps.length;
    let currentStep = 1;

    // Entity type selector
    const entityTypeSelect = document.getElementById('{{ form.entity_type.id_for_label }}');
    const entityNameLabel = document.getElementById('entity-name-label');
    const missionLabel = document.getElementById('mission-label');
    const missionHelp = document.getElementById('mission-help');

    // Update labels based on entity type
    function updateLabels() {
        const entityType = entityTypeSelect.value;
        if (entityType === 'company') {
            entityNameLabel.textContent = 'Company Name';
            missionLabel.textContent = 'Company Mission';
            missionHelp.textContent = 'This helps your team understand the company\'s purpose.';
        } else if (entityType === 'community') {
            entityNameLabel.textContent = 'Community Name';
            missionLabel.textContent = 'Community Purpose';
            missionHelp.textContent = 'This helps users understand the community\'s focus.';
        }
    }

    // Initial update
    updateLabels();

    // Listen for changes
    entityTypeSelect.addEventListener('change', updateLabels);

    function updateProgress() {
        const progress = ((currentStep - 1) / (totalSteps - 1)) * 100;
        progressBar.style.width = `${progress}%`;
    }

    function showStep(stepNumber) {
        steps.forEach(step => {
            step.classList.add('d-none');
            if (step.dataset.step == stepNumber) {
                step.classList.remove('d-none');
            }
        });

        // Update buttons
        prevBtn.style.display = stepNumber > 1 ? 'block' : 'none';
        nextBtn.style.display = stepNumber < totalSteps ? 'block' : 'none';
        submitBtn.style.display = stepNumber == totalSteps ? 'block' : 'none';

        currentStep = stepNumber;
        updateProgress();
    }

    function validateStep(stepNumber) {
        const step = document.querySelector(`[data-step="${stepNumber}"]`);
        const inputs = step.querySelectorAll('input[required], select[required], textarea[required]');
        let isValid = true;

        inputs.forEach(input => {
            if (!input.value.trim()) {
                isValid = false;
                input.classList.add('is-invalid');
            } else {
                input.classList.remove('is-invalid');
            }
        });

        return isValid;
    }

    nextBtn.addEventListener('click', () => {
        if (validateStep(currentStep)) {
            showStep(currentStep + 1);
        }
    });

    prevBtn.addEventListener('click', () => {
        showStep(currentStep - 1);
    });

    // Form validation
    form.addEventListener('submit', function(e) {
        if (!validateStep(currentStep)) {
            e.preventDefault();
        }
    });

    // Initialize tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip);
    });

    // Initialize first step
    showStep(1);
});
</script>
{% endblock %}
