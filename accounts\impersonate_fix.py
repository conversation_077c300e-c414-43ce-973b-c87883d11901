"""
Fix for django-impersonate middleware.
"""
from django.utils.deprecation import MiddlewareMixin

class ImpersonateFixMiddleware(MiddlewareMixin):
    """
    Fix middleware for django-impersonate.
    This middleware should be placed after the django-impersonate middleware.
    It ensures the is_impersonate flag is properly set when real_user is present.
    """

    def process_request(self, request):
        """Fix impersonation detection."""
        # First, ensure we're not showing impersonation UI for anonymous users
        if not request.user.is_authenticated:
            # If user is not authenticated, ensure impersonation flags are removed
            if hasattr(request, 'is_impersonate'):
                request.is_impersonate = False
            if hasattr(request, 'real_user'):
                delattr(request, 'real_user')
            if hasattr(request, 'show_impersonation_ui'):
                request.show_impersonation_ui = False
            return None

        # For authenticated users, check if real_user exists
        if hasattr(request, 'real_user') and request.real_user is not None:
            # Check if real_user is different from current user
            if request.real_user.is_authenticated and request.real_user.id != request.user.id:
                # Force set the is_impersonate flag regardless of its current value
                request.is_impersonate = True
                # Also set the show_impersonation_ui flag
                request.show_impersonation_ui = True
            else:
                # If real_user exists but is the same as current user, this is not impersonation
                request.is_impersonate = False
                request.show_impersonation_ui = False
        elif getattr(request, 'is_impersonate', False):
            # If is_impersonate is set but real_user doesn't exist, this is an error state
            request.is_impersonate = False
            if hasattr(request, 'show_impersonation_ui'):
                request.show_impersonation_ui = False

        return None
