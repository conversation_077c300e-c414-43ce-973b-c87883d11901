/**
 * Navbar Dark Mode Handler
 * Ensures dark mode is properly applied to the navigation bar
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if dark mode is active
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    
    // Apply dark mode styles to navbar
    applyDarkModeToNavbar(isDarkMode);
    
    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        const isDarkMode = e.detail.theme === 'dark';
        applyDarkModeToNavbar(isDarkMode);
    });
    
    // Function to apply dark mode to navbar
    function applyDarkModeToNavbar(isDarkMode) {
        if (isDarkMode) {
            // Apply dark mode to navbar
            const navbar = document.querySelector('.navbar');
            if (navbar) {
                navbar.style.backgroundColor = '#121212';
                navbar.style.borderBottom = '1px solid #333333';
                navbar.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.3)';
            }
            
            // Apply dark mode to navbar brand
            const navbarBrand = document.querySelector('.navbar-brand');
            if (navbarBrand) {
                navbarBrand.style.color = '#ffffff';
            }
            
            // Apply dark mode to navbar links
            document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
                link.style.color = '#cccccc';
                
                // Add hover effect
                link.addEventListener('mouseenter', function() {
                    this.style.color = '#ffffff';
                });
                
                // Add leave effect
                link.addEventListener('mouseleave', function() {
                    if (!this.classList.contains('active')) {
                        this.style.color = '#cccccc';
                    }
                });
                
                // Style active links
                if (link.classList.contains('active')) {
                    link.style.color = '#ffffff';
                    link.style.fontWeight = '600';
                }
            });
            
            // Apply dark mode to navbar toggler
            const navbarToggler = document.querySelector('.navbar-toggler');
            if (navbarToggler) {
                navbarToggler.style.borderColor = '#333333';
                navbarToggler.style.color = '#ffffff';
            }
            
            // Apply dark mode to navbar collapse
            const navbarCollapse = document.querySelector('.navbar-collapse');
            if (navbarCollapse) {
                navbarCollapse.style.backgroundColor = '#121212';
            }
            
            // Apply dark mode to dropdown menus
            document.querySelectorAll('.dropdown-menu').forEach(menu => {
                menu.style.backgroundColor = '#1a1a1a';
                menu.style.border = '1px solid #333333';
                menu.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.3)';
                
                // Style dropdown items
                menu.querySelectorAll('.dropdown-item').forEach(item => {
                    item.style.color = '#cccccc';
                    
                    // Add hover effect
                    item.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#252525';
                        this.style.color = '#ffffff';
                    });
                    
                    // Add leave effect
                    item.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = '#cccccc';
                    });
                });
                
                // Style dropdown dividers
                menu.querySelectorAll('.dropdown-divider').forEach(divider => {
                    divider.style.borderTopColor = '#333333';
                });
            });
            
            // Apply dark mode to navbar search
            const navbarSearch = document.querySelector('.navbar .form-control');
            if (navbarSearch) {
                navbarSearch.style.backgroundColor = '#1a1a1a';
                navbarSearch.style.borderColor = '#333333';
                navbarSearch.style.color = '#ffffff';
                
                // Add focus effect
                navbarSearch.addEventListener('focus', function() {
                    this.style.borderColor = '#0077ff';
                    this.style.boxShadow = '0 0 0 3px rgba(0, 119, 255, 0.25)';
                });
                
                // Add blur effect
                navbarSearch.addEventListener('blur', function() {
                    this.style.borderColor = '#333333';
                    this.style.boxShadow = 'none';
                });
            }
            
            // Apply dark mode to navbar buttons
            document.querySelectorAll('.navbar .btn').forEach(btn => {
                if (btn.classList.contains('btn-primary')) {
                    btn.style.background = 'linear-gradient(to bottom, #0077ff, #0055cc)';
                    btn.style.borderColor = 'transparent';
                    btn.style.color = '#ffffff';
                    btn.style.boxShadow = '0 4px 10px rgba(0, 102, 255, 0.3)';
                } else if (btn.classList.contains('btn-outline-primary')) {
                    btn.style.color = '#0077ff';
                    btn.style.borderColor = '#0077ff';
                    btn.style.backgroundColor = 'transparent';
                } else {
                    btn.style.backgroundColor = '#1a1a1a';
                    btn.style.borderColor = '#333333';
                    btn.style.color = '#ffffff';
                }
                
                // Add hover effect
                btn.addEventListener('mouseenter', function() {
                    if (this.classList.contains('btn-primary')) {
                        this.style.background = 'linear-gradient(to bottom, #0088ff, #0066dd)';
                        this.style.boxShadow = '0 6px 15px rgba(0, 102, 255, 0.4)';
                        this.style.transform = 'translateY(-2px)';
                    } else if (this.classList.contains('btn-outline-primary')) {
                        this.style.backgroundColor = 'rgba(0, 119, 255, 0.1)';
                        this.style.color = '#0088ff';
                    } else {
                        this.style.backgroundColor = '#252525';
                    }
                });
                
                // Add leave effect
                btn.addEventListener('mouseleave', function() {
                    if (this.classList.contains('btn-primary')) {
                        this.style.background = 'linear-gradient(to bottom, #0077ff, #0055cc)';
                        this.style.boxShadow = '0 4px 10px rgba(0, 102, 255, 0.3)';
                        this.style.transform = 'translateY(0)';
                    } else if (this.classList.contains('btn-outline-primary')) {
                        this.style.backgroundColor = 'transparent';
                        this.style.color = '#0077ff';
                    } else {
                        this.style.backgroundColor = '#1a1a1a';
                    }
                });
            });
        } else {
            // Reset styles for light mode
            document.querySelectorAll('.navbar, .navbar-brand, .navbar-nav .nav-link, .navbar-toggler, .navbar-collapse, .dropdown-menu, .dropdown-item, .dropdown-divider, .navbar .form-control, .navbar .btn').forEach(el => {
                el.style.backgroundColor = '';
                el.style.background = '';
                el.style.borderColor = '';
                el.style.borderBottomColor = '';
                el.style.boxShadow = '';
                el.style.color = '';
                el.style.fontWeight = '';
                el.style.transform = '';
                
                // Remove event listeners by cloning and replacing
                const newEl = el.cloneNode(true);
                el.parentNode.replaceChild(newEl, el);
            });
        }
    }
    
    // Apply dark mode immediately if needed
    if (isDarkMode) {
        applyDarkModeToNavbar(true);
    }
});
