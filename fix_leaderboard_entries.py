#!/usr/bin/env python
"""
<PERSON><PERSON>t to fix leaderboard entries by standardizing game_id values and removing duplicate leaderboards.
"""
import os
import django
import logging

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from corporate.models import Company, Leaderboard, LeaderboardEntry, CorporateUser

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def standardize_game_ids():
    """
    Standardize all game_id values to 'corporate_prompt_master'
    """
    logger.info("Standardizing game_id values...")
    
    # Get all entries with empty or different game_id values
    non_standard_entries = LeaderboardEntry.objects.exclude(game_id='corporate_prompt_master')
    count = non_standard_entries.count()
    
    logger.info(f"Found {count} entries with non-standard game_id values")
    
    # Update all entries to use 'corporate_prompt_master'
    updated = non_standard_entries.update(
        game_id='corporate_prompt_master',
        game_name='Corporate Prompt Master'
    )
    
    logger.info(f"Updated {updated} entries to use standard game_id")
    
    return updated

def remove_duplicate_leaderboards():
    """
    Remove duplicate leaderboards for the same company and time period
    """
    logger.info("Checking for duplicate leaderboards...")
    
    # Get all companies
    companies = Company.objects.all()
    removed_count = 0
    
    for company in companies:
        logger.info(f"Checking leaderboards for company: {company.name}")
        
        # Check each time period
        for period in ['all_time', 'monthly', 'weekly', 'daily']:
            # Get all leaderboards for this company and time period
            leaderboards = Leaderboard.objects.filter(
                company=company,
                time_period=period,
                is_global=False
            ).order_by('id')
            
            if leaderboards.count() > 1:
                logger.info(f"Found {leaderboards.count()} leaderboards for {company.name} ({period})")
                
                # Keep the first one and move all entries to it
                primary_leaderboard = leaderboards.first()
                logger.info(f"Keeping leaderboard: {primary_leaderboard.name} (ID: {primary_leaderboard.id})")
                
                # Process other leaderboards
                for lb in leaderboards[1:]:
                    logger.info(f"Processing duplicate leaderboard: {lb.name} (ID: {lb.id})")
                    
                    # Get entries for this leaderboard
                    entries = LeaderboardEntry.objects.filter(leaderboard=lb)
                    entry_count = entries.count()
                    
                    if entry_count > 0:
                        logger.info(f"Moving {entry_count} entries to primary leaderboard")
                        
                        # For each entry, move it to the primary leaderboard or update existing
                        for entry in entries:
                            # Check if an entry already exists in the primary leaderboard
                            existing_entry = LeaderboardEntry.objects.filter(
                                leaderboard=primary_leaderboard,
                                user=entry.user,
                                game_id=entry.game_id
                            ).first()
                            
                            if existing_entry:
                                # Update the existing entry if the score is higher
                                if entry.score > existing_entry.score:
                                    existing_entry.score = entry.score
                                    existing_entry.highest_role = entry.highest_role
                                    existing_entry.save()
                                    logger.info(f"Updated entry for user {entry.user.username} with higher score {entry.score}")
                            else:
                                # Create a new entry in the primary leaderboard
                                new_entry = LeaderboardEntry(
                                    leaderboard=primary_leaderboard,
                                    user=entry.user,
                                    score=entry.score,
                                    highest_role=entry.highest_role,
                                    game_id=entry.game_id,
                                    game_name=entry.game_name
                                )
                                new_entry.save()
                                logger.info(f"Created new entry for user {entry.user.username} with score {entry.score}")
                    
                    # Delete the duplicate leaderboard
                    lb.delete()
                    removed_count += 1
                    logger.info(f"Deleted duplicate leaderboard: {lb.name}")
    
    logger.info(f"Removed {removed_count} duplicate leaderboards")
    return removed_count

def ensure_consistent_entries():
    """
    Ensure all users appear in all time periods with consistent scores
    """
    logger.info("Ensuring consistent entries across time periods...")
    
    # Get all companies
    companies = Company.objects.all()
    created_count = 0
    
    for company in companies:
        logger.info(f"Processing company: {company.name}")
        
        # Get all users for this company
        company_users = CorporateUser.objects.filter(company=company).values_list('user_id', flat=True)
        logger.info(f"Found {len(company_users)} users for company {company.name}")
        
        # Get all time periods for this company
        time_periods = Leaderboard.objects.filter(
            company=company,
            is_global=False
        ).values_list('time_period', flat=True).distinct()
        
        # For each user, ensure they have entries in all time periods
        for user_id in company_users:
            try:
                user = User.objects.get(id=user_id)
                logger.info(f"Processing user: {user.username}")
                
                # Find the user's best entry
                best_entry = LeaderboardEntry.objects.filter(
                    user=user,
                    leaderboard__company=company
                ).order_by('-score').first()
                
                if best_entry:
                    logger.info(f"Best entry for {user.username}: score={best_entry.score}, role={best_entry.highest_role}")
                    
                    # Ensure this user has an entry in each time period
                    for period in time_periods:
                        # Get or create the leaderboard for this period
                        leaderboard, created = Leaderboard.objects.get_or_create(
                            company=company,
                            time_period=period,
                            is_global=False,
                            defaults={'name': f"{company.name} Leaderboard ({period})"}
                        )
                        
                        # Check if an entry exists for this user in this leaderboard
                        entry_exists = LeaderboardEntry.objects.filter(
                            leaderboard=leaderboard,
                            user=user
                        ).exists()
                        
                        if not entry_exists:
                            # Create a new entry with the same score and role as the best entry
                            new_entry = LeaderboardEntry(
                                leaderboard=leaderboard,
                                user=user,
                                score=best_entry.score,
                                highest_role=best_entry.highest_role,
                                game_id='corporate_prompt_master',
                                game_name='Corporate Prompt Master'
                            )
                            new_entry.save()
                            created_count += 1
                            logger.info(f"Created new entry for {user.username} in {period} leaderboard")
            except User.DoesNotExist:
                logger.warning(f"User with ID {user_id} does not exist")
    
    logger.info(f"Created {created_count} new entries for consistency")
    return created_count

def main():
    """Main function to run all fixes"""
    logger.info("=== Starting leaderboard fixes ===")
    
    # Step 1: Standardize game_id values
    standardize_game_ids()
    
    # Step 2: Remove duplicate leaderboards
    remove_duplicate_leaderboards()
    
    # Step 3: Ensure consistent entries across time periods
    ensure_consistent_entries()
    
    logger.info("=== Leaderboard fixes completed ===")
    
    # Print final statistics
    leaderboards = Leaderboard.objects.all()
    entries = LeaderboardEntry.objects.all()
    
    logger.info(f"Final leaderboard count: {leaderboards.count()}")
    logger.info(f"Final entry count: {entries.count()}")
    
    # Print all leaderboards
    logger.info("=== Leaderboards ===")
    for lb in leaderboards:
        company_name = lb.company.name if lb.company else "Global"
        logger.info(f"Name: {lb.name}, Company: {company_name}, Time Period: {lb.time_period}, Is Global: {lb.is_global}")
    
    # Print all entries
    logger.info("=== Leaderboard Entries ===")
    for entry in entries:
        logger.info(f"User: {entry.user.username}, Score: {entry.score}, Game ID: {entry.game_id}, Leaderboard: {entry.leaderboard.name}, Time Period: {entry.leaderboard.time_period}")

if __name__ == "__main__":
    main()
