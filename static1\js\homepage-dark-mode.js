/**
 * Homepage Dark Mode Handler
 * Ensures dark mode is properly applied to the homepage with blue background
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if dark mode is active
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    
    // Apply dark mode styles to homepage elements
    applyDarkModeToHomepage(isDarkMode);
    
    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        const isDarkMode = e.detail.theme === 'dark';
        applyDarkModeToHomepage(isDarkMode);
    });
    
    // Function to apply dark mode to homepage elements
    function applyDarkModeToHomepage(isDarkMode) {
        if (isDarkMode) {
            // Apply dark mode to hero section
            const heroSection = document.querySelector('.vibrant-hero, .bg-primary, section.py-5.bg-primary');
            if (heroSection) {
                heroSection.style.backgroundColor = '#121212';
                heroSection.style.backgroundImage = 
                    'radial-gradient(circle at 25% 25%, rgba(0, 102, 255, 0.2) 0%, transparent 50%), ' +
                    'radial-gradient(circle at 75% 75%, rgba(0, 60, 180, 0.2) 0%, transparent 50%), ' +
                    'linear-gradient(to bottom, #121212, #0a0a0a)';
                heroSection.style.backgroundAttachment = 'fixed';
                heroSection.style.color = '#ffffff';
                
                // Apply to headings
                heroSection.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
                    heading.style.color = '#ffffff';
                    heading.style.textShadow = '0 2px 5px rgba(0, 0, 0, 0.3)';
                });
                
                // Apply to paragraphs
                heroSection.querySelectorAll('p').forEach(p => {
                    p.style.color = 'rgba(255, 255, 255, 0.9)';
                });
                
                // Apply to buttons
                heroSection.querySelectorAll('.btn-primary').forEach(btn => {
                    btn.style.background = 'linear-gradient(to bottom, #0077ff, #0055cc)';
                    btn.style.border = 'none';
                    btn.style.color = 'white';
                    btn.style.boxShadow = '0 4px 10px rgba(0, 102, 255, 0.3)';
                });
                
                heroSection.querySelectorAll('.btn-outline-primary, .btn-outline-light').forEach(btn => {
                    btn.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                    btn.style.borderColor = 'rgba(255, 255, 255, 0.3)';
                    btn.style.color = '#ffffff';
                    btn.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.2)';
                });
                
                // Apply to search box
                heroSection.querySelectorAll('.search-box, .form-control').forEach(input => {
                    input.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
                    input.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                    input.style.color = '#ffffff';
                    input.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.2)';
                });
                
                // Apply to search directory container
                const searchDirectory = heroSection.querySelector('.search-directory, .search-form-card');
                if (searchDirectory) {
                    searchDirectory.style.backgroundColor = 'rgba(0, 0, 0, 0.3)';
                    searchDirectory.style.borderColor = 'rgba(255, 255, 255, 0.1)';
                    searchDirectory.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.3)';
                    searchDirectory.style.backdropFilter = 'blur(10px)';
                    searchDirectory.style.webkitBackdropFilter = 'blur(10px)';
                    
                    // Apply to search input
                    searchDirectory.querySelectorAll('input[type="text"]').forEach(input => {
                        input.style.backgroundColor = 'rgba(0, 0, 0, 0.2)';
                        input.style.borderColor = 'rgba(255, 255, 255, 0.2)';
                        input.style.color = '#ffffff';
                    });
                    
                    // Apply to labels
                    searchDirectory.querySelectorAll('label').forEach(label => {
                        label.style.color = '#ffffff';
                    });
                    
                    // Apply to radio buttons
                    searchDirectory.querySelectorAll('.form-check-label').forEach(label => {
                        label.style.color = 'rgba(255, 255, 255, 0.9)';
                    });
                }
                
                // Apply to tabs
                heroSection.querySelectorAll('.nav-tabs').forEach(tabs => {
                    tabs.style.borderBottomColor = 'rgba(255, 255, 255, 0.1)';
                    
                    tabs.querySelectorAll('.nav-link').forEach(link => {
                        link.style.color = 'rgba(255, 255, 255, 0.7)';
                    });
                    
                    tabs.querySelectorAll('.nav-link.active').forEach(link => {
                        link.style.color = '#ffffff';
                        link.style.borderBottomColor = '#ffffff';
                        link.style.fontWeight = '600';
                    });
                });
            }
            
            // Apply to features section
            const featuresSection = document.querySelector('.features-section, section.py-5:not(.bg-primary)');
            if (featuresSection) {
                featuresSection.style.backgroundColor = '#121212';
                featuresSection.style.color = '#ffffff';
                
                // Apply to feature cards
                featuresSection.querySelectorAll('.card').forEach(card => {
                    card.style.background = 'linear-gradient(145deg, #1e1e1e, #252525)';
                    card.style.borderColor = '#333333';
                    card.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)';
                    card.style.color = '#ffffff';
                    
                    // Apply to card headings
                    card.querySelectorAll('h3, h4, h5').forEach(heading => {
                        heading.style.color = '#ffffff';
                    });
                    
                    // Apply to card paragraphs
                    card.querySelectorAll('p').forEach(p => {
                        p.style.color = 'rgba(255, 255, 255, 0.8)';
                    });
                });
                
                // Apply to section headings
                featuresSection.querySelectorAll('h2, h3, h4, h5, h6').forEach(heading => {
                    heading.style.color = '#ffffff';
                });
                
                // Apply to section paragraphs
                featuresSection.querySelectorAll('p.lead, p.text-muted').forEach(p => {
                    p.style.color = 'rgba(255, 255, 255, 0.8) !important';
                });
            }
            
            // Apply to CTA section
            const ctaSection = document.querySelector('.cta-section, section.py-5.bg-gradient');
            if (ctaSection) {
                ctaSection.style.background = 'linear-gradient(145deg, #1a1a1a, #121212)';
                ctaSection.style.borderColor = '#333333';
                ctaSection.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.4)';
                ctaSection.style.color = '#ffffff';
                
                // Apply to CTA content
                const ctaContent = ctaSection.querySelector('.bg-white, .rounded-3');
                if (ctaContent) {
                    ctaContent.style.backgroundColor = '#1e1e1e !important';
                    ctaContent.style.background = 'linear-gradient(145deg, #1e1e1e, #252525)';
                    ctaContent.style.borderColor = '#333333';
                    ctaContent.style.boxShadow = '0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)';
                    ctaContent.style.color = '#ffffff';
                    
                    // Apply to CTA headings
                    ctaContent.querySelectorAll('h2, h3, h4, h5, h6').forEach(heading => {
                        heading.style.color = '#ffffff';
                    });
                    
                    // Apply to CTA paragraphs
                    ctaContent.querySelectorAll('p').forEach(p => {
                        p.style.color = 'rgba(255, 255, 255, 0.8)';
                    });
                }
            }
            
            // Apply to company logo carousel
            const companyLogoCarousel = document.querySelector('.company-logo-carousel-container');
            if (companyLogoCarousel) {
                companyLogoCarousel.style.backgroundColor = '#1a1a1a';
                companyLogoCarousel.style.borderColor = '#333333';
                
                // Apply to company logos
                companyLogoCarousel.querySelectorAll('.company-logo-item a').forEach(item => {
                    item.style.color = '#ffffff';
                });
                
                // Apply to company names
                companyLogoCarousel.querySelectorAll('.company-name').forEach(name => {
                    name.style.color = '#ffffff';
                });
                
                // Apply to assistant counts
                companyLogoCarousel.querySelectorAll('.assistant-count').forEach(count => {
                    count.style.color = 'rgba(255, 255, 255, 0.7)';
                });
                
                // Apply to logo placeholders
                companyLogoCarousel.querySelectorAll('.company-logo-placeholder').forEach(placeholder => {
                    placeholder.style.backgroundColor = '#252525';
                    placeholder.style.borderColor = '#333333';
                    placeholder.style.color = '#ffffff';
                });
            }
        } else {
            // Reset styles for light mode
            document.querySelectorAll('.vibrant-hero, .bg-primary, section.py-5.bg-primary, .features-section, section.py-5:not(.bg-primary), .cta-section, section.py-5.bg-gradient, .company-logo-carousel-container').forEach(section => {
                section.style.backgroundColor = '';
                section.style.backgroundImage = '';
                section.style.backgroundAttachment = '';
                section.style.color = '';
                section.style.borderColor = '';
                section.style.boxShadow = '';
                
                // Reset headings
                section.querySelectorAll('h1, h2, h3, h4, h5, h6').forEach(heading => {
                    heading.style.color = '';
                    heading.style.textShadow = '';
                });
                
                // Reset paragraphs
                section.querySelectorAll('p').forEach(p => {
                    p.style.color = '';
                });
                
                // Reset buttons
                section.querySelectorAll('.btn').forEach(btn => {
                    btn.style.background = '';
                    btn.style.border = '';
                    btn.style.color = '';
                    btn.style.boxShadow = '';
                });
                
                // Reset inputs
                section.querySelectorAll('input, .form-control').forEach(input => {
                    input.style.backgroundColor = '';
                    input.style.borderColor = '';
                    input.style.color = '';
                    input.style.boxShadow = '';
                });
                
                // Reset cards
                section.querySelectorAll('.card, .rounded-3').forEach(card => {
                    card.style.background = '';
                    card.style.backgroundColor = '';
                    card.style.borderColor = '';
                    card.style.boxShadow = '';
                    card.style.color = '';
                });
                
                // Reset tabs
                section.querySelectorAll('.nav-tabs').forEach(tabs => {
                    tabs.style.borderBottomColor = '';
                    
                    tabs.querySelectorAll('.nav-link').forEach(link => {
                        link.style.color = '';
                        link.style.borderBottomColor = '';
                        link.style.fontWeight = '';
                    });
                });
                
                // Reset search directory
                const searchDirectory = section.querySelector('.search-directory, .search-form-card');
                if (searchDirectory) {
                    searchDirectory.style.backgroundColor = '';
                    searchDirectory.style.borderColor = '';
                    searchDirectory.style.boxShadow = '';
                    searchDirectory.style.backdropFilter = '';
                    searchDirectory.style.webkitBackdropFilter = '';
                    
                    // Reset labels
                    searchDirectory.querySelectorAll('label').forEach(label => {
                        label.style.color = '';
                    });
                }
            });
        }
    }
    
    // Set up a mutation observer to apply styles to new elements
    const observer = new MutationObserver(function(mutations) {
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        if (isDarkMode) {
            applyDarkModeToHomepage(true);
        }
    });
    
    observer.observe(document.body, { childList: true, subtree: true });
});
