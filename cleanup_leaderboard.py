import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from corporate.models import Company, Leaderboard, LeaderboardEntry

def cleanup_leaderboard_entries():
    """Clean up duplicate leaderboard entries"""
    # Get all companies
    companies = Company.objects.all()
    print(f"Total companies: {companies.count()}")

    for company in companies:
        print(f"\nCompany: {company.name}")

        # Get leaderboards for this company
        leaderboards = Leaderboard.objects.filter(company=company, is_global=False)
        print(f"  Leaderboards: {leaderboards.count()}")

        for leaderboard in leaderboards:
            print(f"  - {leaderboard.name} ({leaderboard.time_period})")

            # Get all users with entries in this leaderboard
            user_ids = LeaderboardEntry.objects.filter(
                leaderboard=leaderboard
            ).values_list('user_id', flat=True).distinct()

            print(f"    Users with entries: {len(user_ids)}")

            # For each user, keep only the entry with game_id='corporate_prompt_master'
            for user_id in user_ids:
                user = User.objects.get(id=user_id)

                # Get all entries for this user
                entries = LeaderboardEntry.objects.filter(
                    leaderboard=leaderboard,
                    user=user
                )

                if entries.count() > 1:
                    print(f"    - {user.username} has {entries.count()} entries")

                    # Keep only the entry with game_id='corporate_prompt_master'
                    corporate_entries = entries.filter(game_id='corporate_prompt_master')
                    if corporate_entries.exists():
                        # Delete all other entries
                        entries.exclude(game_id='corporate_prompt_master').delete()
                        print(f"      Kept corporate_prompt_master entry, deleted {entries.count() - corporate_entries.count()} others")
                    else:
                        # Keep the first entry and delete the rest
                        first_entry = entries.first()
                        entries.exclude(id=first_entry.id).delete()

                        # Update the first entry with game information
                        first_entry.game_id = 'corporate_prompt_master'
                        first_entry.game_name = 'Corporate Prompt Master'
                        first_entry.save()

                        print(f"      Kept first entry and updated it with game information")

            # Check entries after cleanup
            entries = LeaderboardEntry.objects.filter(leaderboard=leaderboard)
            print(f"    Entries after cleanup: {entries.count()}")

            for entry in entries:
                print(f"    - {entry.user.username}: {entry.score} points, {entry.highest_role}, Game: {entry.game_name}")

def main():
    print("=== Cleaning up Leaderboard Entries ===")
    cleanup_leaderboard_entries()

if __name__ == "__main__":
    main()
