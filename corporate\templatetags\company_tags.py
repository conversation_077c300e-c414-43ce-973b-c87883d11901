from django import template
from django.contrib.auth.models import User
from corporate.models import Company, CorporateUser

register = template.Library()

@register.filter
def get_company_role(user, company):
    """
    Determine a user's role in a company.
    Returns: 'owner', 'admin', 'member', or '' if no role
    """
    if not user or not company:
        return ''

    # Check if user is the owner
    if user == company.owner:
        return 'Owner'

    # Check if user has a corporate profile for this company
    try:
        corporate_user = CorporateUser.objects.get(user=user, company=company)
        if corporate_user.is_company_admin:
            return 'Admin'
        return 'Member'
    except CorporateUser.DoesNotExist:
        pass

    # No role found
    return ''
