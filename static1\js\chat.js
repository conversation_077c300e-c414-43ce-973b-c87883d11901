// Function to safely escape HTML
function escapeHtml(unsafe) {
    return unsafe
        .replace(/&/g, "&amp;")
        .replace(/</g, "&gt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;");
}

// Function to add context links to message with enhanced styling
function addContextLinks(message, contexts) {
    let messageWithContext = message;

    if (contexts && contexts.length > 0) {
        // Add context links section with improved styling
        messageWithContext += `
            <div class="context-links">
                <div class="context-links-header">
                    <i class="bi bi-info-circle me-1"></i>
                    <small>Sources used:</small>
                </div>
                <div class="context-links-container">`;

        contexts.forEach((context, index) => {
            const contextData = {
                id: context.id,
                title: escapeHtml(context.title),
                created_by: escapeHtml(context.created_by),
                created_at: escapeHtml(context.created_at)
            };

            messageWithContext += `
                <a href="#"
                   class="context-link"
                   data-bs-toggle="modal"
                   data-bs-target="#contextModal"
                   data-context-id="${contextData.id}"
                   data-context-title="${contextData.title}"
                   data-context-created-by="${contextData.created_by}"
                   data-context-created-at="${contextData.created_at}">
                    <i class="bi bi-file-text me-1"></i>${contextData.title}
                </a>${index < contexts.length - 1 ? ' • ' : ''}`;
        });

        messageWithContext += `
                </div>
            </div>`;
    }

    return messageWithContext;
}

// Initialize context modal handler when document is ready
document.addEventListener('DOMContentLoaded', function() {
    const contextModal = document.getElementById('contextModal');
    if (contextModal) {
        contextModal.addEventListener('show.bs.modal', function(event) {
            const button = event.relatedTarget;
            const contextId = button.getAttribute('data-context-id');
            const title = button.getAttribute('data-context-title');
            const createdBy = button.getAttribute('data-context-created-by');
            const createdAt = button.getAttribute('data-context-created-at');

            showContext(button, contextId, title, createdBy, createdAt);
        });
    }
});

// Function to show context in modal with enhanced presentation
function showContext(button, contextId, title, createdBy, createdAt) {
    const contextModal = document.getElementById('contextModal');
    if (!contextModal) return;

    // Update modal title and metadata
    const modalTitle = contextModal.querySelector('.modal-title');
    const modalMetadata = contextModal.querySelector('.context-metadata');
    if (modalTitle) modalTitle.textContent = title;
    if (modalMetadata) {
        modalMetadata.innerHTML = `
            <small class="text-muted">
                <i class="bi bi-person me-1"></i>${createdBy} •
                <i class="bi bi-calendar me-1"></i>${createdAt}
            </small>`;
    }

    // Show loading state with improved styling
    const modalBody = contextModal.querySelector('.modal-body');
    if (modalBody) {
        modalBody.innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status"></div>
                <p class="mt-3">Loading content...</p>
            </div>`;
    }

    // Get the company and assistant IDs from meta tags
    const companyId = document.querySelector('meta[name="company-id"]').content;
    const assistantId = document.querySelector('meta[name="assistant-id"]').content;

    // Fetch context content
    fetch(`/assistant/company/${companyId}/assistants/${assistantId}/context/${contextId}/`, {
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.content) {
            // Update the modal body with the context content
            modalBody.innerHTML = `<div class="context-content">${data.content}</div>`;

            // Update usage and upvote counts
            const usageCount = contextModal.querySelector('#context-usage');
            const upvoteCount = contextModal.querySelector('#context-upvotes');
            const answerUpvoteCount = contextModal.querySelector('#context-answer-upvotes');
            const answerUpvoteBadge = contextModal.querySelector('#context-answer-upvotes-badge');

            if (usageCount) usageCount.textContent = data.times_used || 0;
            if (upvoteCount) upvoteCount.textContent = data.direct_upvote_count || 0;

            // Handle answer upvotes
            if (answerUpvoteCount && data.answer_upvote_count > 0) {
                answerUpvoteCount.textContent = data.answer_upvote_count;
                if (answerUpvoteBadge) answerUpvoteBadge.style.display = 'inline-flex';
            } else if (answerUpvoteBadge) {
                answerUpvoteBadge.style.display = 'none';
            }

            // Set up the upvote button
            const upvoteBtn = contextModal.querySelector('#upvote-context-btn');
            if (upvoteBtn) {
                upvoteBtn.setAttribute('data-context-id', contextId);
                upvoteBtn.setAttribute('data-url', data.upvote_url);

                // Update button appearance based on upvote status
                if (data.is_upvoted) {
                    upvoteBtn.classList.remove('btn-outline-success');
                    upvoteBtn.classList.add('btn-success');
                    upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up-fill me-1"></i> Upvoted';
                } else {
                    upvoteBtn.classList.remove('btn-success');
                    upvoteBtn.classList.add('btn-outline-success');
                    upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up me-1"></i> Upvote This Knowledge';
                }

                // Add click event listener for upvoting
                upvoteBtn.onclick = function() {
                    const url = this.getAttribute('data-url');
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                    fetch(url, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': csrfToken,
                            'Content-Type': 'application/json'
                        }
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Update the upvote counts
                            if (upvoteCount) upvoteCount.textContent = data.upvote_count;

                            // If we have answer upvotes, make sure they're still displayed
                            const answerUpvoteCount = contextModal.querySelector('#context-answer-upvotes');
                            const answerUpvoteBadge = contextModal.querySelector('#context-answer-upvotes-badge');
                            if (answerUpvoteCount && answerUpvoteBadge && parseInt(answerUpvoteCount.textContent) > 0) {
                                answerUpvoteBadge.style.display = 'inline-flex';
                            }

                            // Update the button appearance
                            if (data.upvote_status === 'added') {
                                upvoteBtn.classList.remove('btn-outline-success');
                                upvoteBtn.classList.add('btn-success');
                                upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up-fill me-1"></i> Upvoted';
                            } else {
                                upvoteBtn.classList.remove('btn-success');
                                upvoteBtn.classList.add('btn-outline-success');
                                upvoteBtn.innerHTML = '<i class="bi bi-hand-thumbs-up me-1"></i> Upvote This Knowledge';
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error upvoting context:', error);
                    });
                };
            }
        } else {
            modalBody.innerHTML = '<p class="text-danger">Error loading content</p>';
        }
    })
    .catch(error => {
        modalBody.innerHTML = '<p class="text-danger">Error loading content</p>';
        console.error('Error:', error);
    });
}

// Main chat functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap modal events
    const contextModal = document.getElementById('contextModal');
    if (contextModal) {
        contextModal.addEventListener('show.bs.modal', function(event) {
            showContext(event.target);
        });
    }

    const chatForm = document.getElementById('chat-form');
    const messageInput = document.getElementById('message-input');
    const chatBox = document.getElementById('chat-box');
    const sendButton = document.getElementById('send-button');
    const resetButton = document.getElementById('reset-chat-btn');
    let messageHistory = [];

    function addMessage(message, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'assistant-message'}`;
        messageDiv.innerHTML = message;
        chatBox.appendChild(messageDiv);
        chatBox.scrollTop = chatBox.scrollHeight;
    }

    // Function to handle assistant messages (exported for external use)
    window.handleAssistantMessage = function(data) {
        // Process response content
        let processedContent = data.content;

        // Add context links if available
        if (data.used_contexts) {
            processedContent = addContextLinks(processedContent, data.used_contexts);
        }

        // Display the message
        addMessage(processedContent, false);
    };

    function sendMessage(message) {
        // Show user message
        addMessage(escapeHtml(message), true);

        // Clear input
        messageInput.value = '';

        // Add loading indicator
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'message assistant-message loading';
        loadingDiv.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
        chatBox.appendChild(loadingDiv);
        chatBox.scrollTop = chatBox.scrollHeight;

        // Get path components from current URL
        const pathParts = window.location.pathname.split('/');
        let companyId, assistantId, slug, useSlugUrl = false;

        if (pathParts.includes('company')) {
            // Format: /company/{company_id}/assistants/{assistant_id}/chat/
            companyId = pathParts[pathParts.indexOf('company') + 1];
            assistantId = pathParts[pathParts.indexOf('assistants') + 1];
            console.log('DEBUG: Using company/assistant ID URL format');
        } else if (pathParts.includes('assistant')) {
            // Format: /assistant/{slug}/chat/
            // Need to get IDs from the page for API calls
            companyId = document.querySelector('meta[name="company-id"]').content;
            assistantId = document.querySelector('meta[name="assistant-id"]').content;

            // Get the slug from the URL
            const assistantIndex = pathParts.indexOf('assistant');
            if (assistantIndex >= 0 && assistantIndex + 1 < pathParts.length) {
                slug = pathParts[assistantIndex + 1];
                useSlugUrl = true;
                console.log(`DEBUG: Using slug URL format, slug=${slug}`);
            }
        }

        // Prepare request data
        const data = {
            message: message,
            history: messageHistory,
            use_community_context: true // Always use community context for context links feature
        };

        // Determine which URL to use
        let apiUrl;
        if (useSlugUrl && slug) {
            apiUrl = `/assistant/interact/${slug}/`;
            console.log('DEBUG: Using slug-based URL for interaction:', apiUrl);
        } else {
            apiUrl = `/assistant/company/${companyId}/assistants/${assistantId}/interact/`;
            console.log('DEBUG: Using ID-based URL for interaction:', apiUrl);
        }

        // Log the current URL format for debugging
        console.log('DEBUG: Current page URL:', window.location.pathname);

        // Send message to server
        console.log('DEBUG: Sending message to server', {
            url: apiUrl,
            data: data,
            companyId: companyId,
            assistantId: assistantId,
            slug: slug,
            useSlugUrl: useSlugUrl
        });

        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            console.log('DEBUG: Response status:', response.status);
            if (!response.ok) {
                console.error('DEBUG: Response not OK:', response);
                return response.text().then(text => {
                    console.error('DEBUG: Error response text:', text);
                    throw new Error(`Server error: ${response.status} - ${text}`);
                });
            }
            return response.json();
        })
        .then(data => {
            // Remove loading indicator
            loadingDiv.remove();

            console.log('DEBUG: Received response data:', data);

            if (data.status === 'error') {
                throw new Error(data.error || 'Error processing message');
            }

            // Add response to history
            messageHistory.push({ role: 'user', content: message });
            messageHistory.push({ role: 'assistant', content: data.content });

            // Use handleAssistantMessage to process and display the response
            handleAssistantMessage(data);
        })
        .catch(error => {
            loadingDiv.remove();
            const errorMessage = error.message || 'Error sending message';
            addMessage(`<div class="text-danger">${errorMessage}</div>`, false);
            console.error('DEBUG: Error in fetch:', error);
        });
    }

    // Form submit handler
    chatForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const message = messageInput.value.trim();
        if (message) {
            sendMessage(message);
        }
    });

    // Send button click handler
    sendButton.addEventListener('click', function() {
        const message = messageInput.value.trim();
        if (message) {
            sendMessage(message);
        }
    });

    // Reset button handler is implemented in assistant_chat.html
    // to provide a more comprehensive reset experience

    // Enter key handler
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            const message = messageInput.value.trim();
            if (message) {
                sendMessage(message);
            }
        }
    });
});

// Export showContext for use in other scripts
window.showContext = showContext;
