{% extends 'base/layout.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block extra_head %}
<!-- Load TinyMCE -->
<script src="{% static 'tinymce/tinymce.min.js' %}"></script>
<!-- Note: tinymce-config.js is not loaded here as we are using a specific inline config -->

<!-- TinyMCE visibility fixes -->
<style>
/* Fix for TinyMCE visibility issue */
.tox-tinymce {
    visibility: visible !important;
    display: block !important;
    opacity: 1 !important;
    min-height: 300px !important;
}

.tox-editor-container {
    background-color: white !important;
    visibility: visible !important;
    width: 100% !important;
}

.tox-toolbar__primary {
    visibility: visible !important;
    display: flex !important;
}

.tox-edit-area {
    width: 100% !important;
}

.tox-edit-area__iframe {
    visibility: visible !important;
    display: block !important;
    width: 100% !important;
}

/* Ensure form controls have proper styling */
.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.25rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
</style>
{% endblock %}

{% block title %}Add Context - {{ assistant.name }}{% endblock %}

{% block extra_js %}
<script>
// Function to count words in TinyMCE content
function countWords(content) {
    // Strip HTML tags
    const textOnly = content.replace(/<[^>]*>/g, ' ');
    // Count words (non-empty strings after splitting by whitespace)
    const words = textOnly.split(/\s+/).filter(word => word.length > 0);
    return words.length;
}

// Function to update word count display
function updateWordCount(editor) {
    const wordCount = countWords(editor.getContent());
    const wordCountElement = document.getElementById('word-count');
    if (wordCountElement) {
        wordCountElement.textContent = wordCount;

        // Apply styling based on word count
        if (wordCount > 500) {
            wordCountElement.classList.add('text-danger');
            wordCountElement.classList.remove('text-warning', 'text-muted');

            // Disable submit button
            const submitButton = document.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.title = 'Content exceeds 500 word limit';
            }

            // Add warning message if not already present
            if (!document.querySelector('.word-limit-warning')) {
                const warningDiv = document.createElement('div');
                warningDiv.className = 'alert alert-danger mt-2 word-limit-warning';
                warningDiv.innerHTML = '<i class="bi bi-exclamation-triangle-fill me-2"></i>Content exceeds the 500 word limit. Please shorten your text to submit.';
                wordCountElement.closest('.word-counter').after(warningDiv);
            }
        }
        else if (wordCount > 400) {
            wordCountElement.classList.add('text-warning');
            wordCountElement.classList.remove('text-danger', 'text-muted');

            // Enable submit button
            const submitButton = document.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.title = '';
            }

            // Remove warning if present
            const warning = document.querySelector('.word-limit-warning');
            if (warning) warning.remove();
        }
        else {
            wordCountElement.classList.add('text-muted');
            wordCountElement.classList.remove('text-danger', 'text-warning');

            // Enable submit button
            const submitButton = document.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.title = '';
            }

            // Remove warning if present
            const warning = document.querySelector('.word-limit-warning');
            if (warning) warning.remove();
        }
    }
}

document.addEventListener('DOMContentLoaded', function() {
    if (typeof tinymce !== 'undefined') {
        tinymce.init({
            selector: '#id_text_content', // Target the specific textarea
            height: 300,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount', 'autoresize' // Added autoresize
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | link image media table | code | help',
            images_upload_url: '/assistants/tinymce/upload/', // Make sure this URL is correct for community context
            file_picker_types: 'image',
            file_picker_callback: function (callback, value, meta) {
                if (meta.filetype === 'image') {
                    var input = document.createElement('input');
                    input.setAttribute('type', 'file');
                    input.setAttribute('accept', 'image/*');
                    input.onchange = function () {
                        var file = this.files[0];
                        var formData = new FormData();
                        formData.append('file', file);
                        // Use the same upload URL as assistant_form
                        fetch('/assistants/tinymce/upload/', {
                            method: 'POST',
                            body: formData,
                            credentials: 'same-origin' // Ensure cookies are sent if needed
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.location) {
                                callback(data.location, { alt: file.name });
                            } else {
                                console.error('Image upload failed:', data.error || 'Unknown error');
                                alert('Image upload failed.');
                            }
                        })
                        .catch((error) => {
                            console.error('Image upload fetch error:', error);
                            alert('Image upload failed.');
                        });
                    };
                    input.click();
                }
            },
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px; }',
            statusbar: true,
            autoresize_bottom_margin: 20, // From assistant_form config
            setup: function (editor) {
                editor.on('change', function () {
                    editor.save(); // Important to save content back to the textarea
                    const textarea = document.getElementById(editor.id);
                    if (textarea) {
                        textarea.value = editor.getContent();
                    }

                    // Update word count on change
                    updateWordCount(editor);
                });

                editor.on('init', function(evt) {
                    console.log(`TinyMCE initialized for: ${evt.target.id}`);
                    // Ensure content loads if textarea already has value
                    const textarea = document.getElementById(editor.id);
                    if (textarea && textarea.value) {
                        editor.setContent(textarea.value);
                    }

                    // Initial word count
                    updateWordCount(editor);
                });

                editor.on('error', function(e) {
                    console.error(`TinyMCE Error for #${editor.id}:`, e);
                });

                // Handle paste events to prevent large spaces (copied from assistant_form)
                editor.on('PastePreProcess', function(e) {
                    let content = e.content;
                    content = content.replace(/(<br\s*\/?\s*>){2,}/gi, '<br>');
                    content = content.replace(/(<p>\s*&nbsp;\s*<\/p>){2,}/gi, '<p>&nbsp;</p>');
                    content = content.replace(/(<p>\s*&nbsp;\s*<\/p>)+$/gi, '');
                    content = content.replace(/(<br\s*\/?\s*>)+$/gi, '');
                    e.content = content;
                });

                // Update word count after paste
                editor.on('PastePostProcess', function() {
                    updateWordCount(editor);
                });

                // Handle content changes to resize editor properly (copied from assistant_form)
                editor.on('SetContent', function() {
                    if (editor.plugins.autoresize) {
                        editor.plugins.autoresize.resize();
                    }

                    // Update word count when content is set
                    updateWordCount(editor);
                });
            }
        }).then(editors => {
             if (editors && editors.length > 0) {
                 console.log(`TinyMCE init SUCCESS for selector '#id_text_content'.`);
             } else {
                 console.warn(`TinyMCE init completed for selector '#id_text_content', but no editor instances returned.`);
             }
         }).catch(error => {
             console.error(`TinyMCE init FAILED for selector '#id_text_content'. Error:`, error);
         });
    } else {
        console.error('TinyMCE object not found. Ensure TinyMCE script is loaded before this script.');
    }

    // Add form submit handler to prevent submission if over word limit
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (typeof tinymce !== 'undefined' && tinymce.get('id_text_content')) {
                const editor = tinymce.get('id_text_content');
                const wordCount = countWords(editor.getContent());

                if (wordCount > 500) {
                    e.preventDefault();
                    alert('Your content exceeds the 500 word limit. Please shorten your text to submit.');
                    return false;
                }

                // Save TinyMCE content to form field before submission
                editor.save();
            }
        });
    }

    // Function to ensure TinyMCE editor is visible
    function ensureTinyMCEVisible() {
        if (typeof tinymce !== 'undefined' && tinymce.get('id_text_content')) {
            const editor = tinymce.get('id_text_content');
            const editorContainer = editor.getContainer();

            if (editorContainer) {
                // Force visibility of the editor container
                editorContainer.style.visibility = 'visible';
                editorContainer.style.display = 'block';
                editorContainer.style.opacity = '1';

                // Force visibility of all TinyMCE elements
                const tinyMCEElements = document.querySelectorAll('.tox-tinymce, .tox-toolbar__primary, .tox-toolbar-overlord, .tox-edit-area, .tox-edit-area__iframe');
                tinyMCEElements.forEach(function(el) {
                    el.style.visibility = 'visible';
                    el.style.display = el.tagName === 'DIV' ? 'block' : 'flex';
                    el.style.opacity = '1';
                });

                console.log('TinyMCE visibility enforced');
            }
        }
    }

    // Call the function after a delay to ensure TinyMCE has initialized
    setTimeout(ensureTinyMCEVisible, 1000);

    // Also call it when the tab is clicked (if this is in a tabbed interface)
    const contributionsTab = document.getElementById('contributions-tab');
    if (contributionsTab) {
        contributionsTab.addEventListener('click', function() {
            setTimeout(ensureTinyMCEVisible, 500);
        });
    }
});
</script>
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Add Context to {{ assistant.name }}</h1>
            <p class="text-muted mb-0">
                Contribute knowledge to help the community assistant provide better answers
            </p>
        </div>
        <div class="col-auto">
            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-light">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Assistant
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="{{ form.title.id_for_label }}" class="form-label">{{ form.title.label }}</label>
                            {{ form.title }}
                            {% if form.title.help_text %}
                                <div class="form-text">{{ form.title.help_text }}</div>
                            {% endif %}
                            {% if form.title.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.title.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.text_content.id_for_label }}" class="form-label">{{ form.text_content.label }}</label>
                            {{ form.text_content }}
                            <div class="word-counter mt-1 text-end">
                                <small class="text-muted" id="word-count">0</small>
                                <small class="text-muted"> / 500 words</small>
                            </div>
                            {% if form.text_content.help_text %}
                                <div class="form-text">{{ form.text_content.help_text }}</div>
                            {% endif %}
                            {% if form.text_content.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.text_content.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="alert alert-info mt-2 mb-3">
                            <i class="bi bi-info-circle me-2"></i>
                            <small>Keep your contribution under 500 words. Shorter, focused knowledge is more effective.</small>
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.keywords.id_for_label }}" class="form-label">{{ form.keywords.label }}</label>
                            {{ form.keywords }}
                            {% if form.keywords.help_text %}
                                <div class="form-text">{{ form.keywords.help_text }}</div>
                            {% endif %}
                            {% if form.keywords.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.keywords.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ form.image.id_for_label }}" class="form-label">{{ form.image.label }}</label>
                            {{ form.image }}
                            {% if form.image.help_text %}
                                <div class="form-text">{{ form.image.help_text }}</div>
                            {% endif %}
                            {% if form.image.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.image.errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle me-2"></i>
                                Add Context
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">Tips for Good Context</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            Be specific and factual
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            Include relevant details
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            Use clear, concise language
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-info-circle-fill text-primary me-2"></i>
                            <strong>Keep content under 500 words</strong>
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            Add keywords to help categorize
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle-fill text-success me-2"></i>
                            Include images when helpful
                        </li>
                    </ul>
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-lightbulb me-2"></i>
                        <small>Shorter, focused contributions are more effective than lengthy ones. The 500-word limit helps ensure your knowledge is concise and to the point.</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

