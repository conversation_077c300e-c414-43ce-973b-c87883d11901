from .models import Company, Membership
from django.db.models import Q
from django.contrib.auth.models import Group # Import Group
import logging
import sys

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)

def company_context(request):
    """
    Adds company-related context to templates.
    - all_user_companies: QuerySet of all companies the user owns or is a member of.
    - active_company: The currently active company object from the session.
    """
    context = {
        'all_user_companies': Company.objects.none(), # Default to empty queryset
        'active_company': None,
        'can_view_active_dashboard': False, # Default permission to False
        'can_manage_active_assistants': False, # Default permission to False
        'is_company_member': False, # Default flag for team button visibility
    }
    user = request.user

    if user.is_authenticated:
        # Get companies owned by the user
        owned_companies = user.owned_companies.all()

        # Get companies the user is a member of
        member_companies = Company.objects.filter(memberships__user=user)

        # Combine owned and member companies, ensuring uniqueness and ordering
        # Using Q objects and distinct() is generally efficient
        all_companies_qs = Company.objects.filter(
            Q(owner=user) | Q(memberships__user=user)
        ).distinct().order_by('name')

        # DEBUG: Print the count of companies found for the user
        print(f"[DEBUG ContextProcessor] Found {all_companies_qs.count()} companies for user '{user}'") # DEBUG

        context['all_user_companies'] = all_companies_qs

        # Get the active company from session
        active_company_id = request.session.get('active_company_id')
        if active_company_id:
            try:
                # Ensure the active company is one the user actually has access to
                context['active_company'] = all_companies_qs.get(id=active_company_id)
            except Company.DoesNotExist:
                # Clear invalid ID from session if company not found or not accessible
                del request.session['active_company_id']
                context['active_company'] = None # Reset active_company context

        # If no active company found/set, try to set a default (e.g., first owned, then first member)
        # This logic might be better placed in middleware or specific views if complex
        if not context['active_company'] and all_companies_qs.exists():
             # Simple default: first from the combined list
             default_company = all_companies_qs.first()
             context['active_company'] = default_company
             # Optionally set it in the session too, but be careful about side effects
             # request.session['active_company_id'] = str(default_company.id)

        # Check dashboard permission for the determined active_company
        if context['active_company']:
            # --- START MODIFICATION ---
            if user.is_superuser:
                # Superusers always have access
                context['can_view_active_dashboard'] = True
                context['can_manage_active_assistants'] = True
                print(f"[DEBUG ContextProcessor] User '{user}' is superuser, granting dashboard/manage access.") # DEBUG
            else:
                # Original permission checks for non-superusers
                active_company_for_perm = context['active_company']
                perm_to_check = 'accounts.view_company_activity'
                print(f"[DEBUG ContextProcessor] Checking perm '{perm_to_check}' for user '{user}' on company '{active_company_for_perm}' (ID: {active_company_for_perm.id})") # DEBUG
                try:
                    has_permission = user.has_perm(perm_to_check, active_company_for_perm)
                    context['can_view_active_dashboard'] = has_permission
                    print(f"[DEBUG ContextProcessor] Result of has_perm: {has_permission}") # DEBUG
                except Exception as e:
                    print(f"[DEBUG ContextProcessor] Error checking dashboard permission: {e}") # DEBUG
                    context['can_view_active_dashboard'] = False

                perm_manage_assistants = 'accounts.view_company_activity' # Using same perm
                print(f"[DEBUG ContextProcessor] Checking perm '{perm_manage_assistants}' (same as dashboard) for user '{user}' on company '{active_company_for_perm}' (ID: {active_company_for_perm.id})") # DEBUG
                try:
                    has_manage_permission = user.has_perm(perm_manage_assistants, active_company_for_perm)
                    context['can_manage_active_assistants'] = has_manage_permission
                    print(f"[DEBUG ContextProcessor] Result of has_perm (manage assistants using dashboard perm): {has_manage_permission}") # DEBUG
                except Exception as e:
                    print(f"[DEBUG ContextProcessor] Error checking manage assistants permission: {e}") # DEBUG
                    context['can_manage_active_assistants'] = False

                # Check if user is in the 'Company Members' group
                try:
                    company_member_group = Group.objects.get(name="Company Members")
                    # Check group membership directly on the user object
                    if company_member_group in user.groups.all():
                         # Important: This checks GLOBAL group membership, not per-company.
                         # For per-company group checks, a different approach (like checking Membership model fields
                         # or using django-guardian's group permissions if set up that way) would be needed.
                         # Assuming for now that global group membership is sufficient for this UI element.
                         context['is_company_member'] = True
                         print(f"[DEBUG ContextProcessor] User '{user}' IS in 'Company Members' group (globally).") # DEBUG
                    else:
                         print(f"[DEBUG ContextProcessor] User '{user}' is NOT in 'Company Members' group (globally).") # DEBUG
                except Group.DoesNotExist:
                    print("[DEBUG ContextProcessor] 'Company Members' group not found.") # DEBUG
                except Exception as e:
                    print(f"[DEBUG ContextProcessor] Error checking 'Company Members' group: {e}") # DEBUG

            # --- END MODIFICATION ---
        elif user.is_superuser:
             # Grant access even if no company is active for superuser
             context['can_view_active_dashboard'] = True
             context['can_manage_active_assistants'] = True
             print(f"[DEBUG ContextProcessor] User '{user}' is superuser, granting dashboard/manage access (no active company).") # DEBUG


    return context

def impersonation_debug_context(request):
    """
    Add impersonation debug information to the context.
    """
    context = {
        'debug_is_impersonating': False,
        'debug_impersonation_info': {}
    }

    # Only consider impersonation for authenticated users
    if not request.user.is_authenticated:
        is_impersonating = False
    else:
        # Check if impersonation is active by is_impersonate flag
        is_impersonate_flag = hasattr(request, 'is_impersonate') and request.is_impersonate

        # Check if real_user exists and is different from current user
        has_real_user = hasattr(request, 'real_user') and request.real_user is not None
        different_users = has_real_user and request.real_user.is_authenticated and request.real_user.id != request.user.id

        # If we have a real_user that's different from the current user, this is impersonation
        # regardless of the is_impersonate flag
        if different_users:
            is_impersonating = True
            # Force set the is_impersonate flag
            if not is_impersonate_flag:
                request.is_impersonate = True
                request.show_impersonation_ui = True
        else:
            # Otherwise, use the is_impersonate flag
            is_impersonating = is_impersonate_flag

    if is_impersonating:
        context['debug_is_impersonating'] = True

        # Add minimal debug information
        debug_info = {
            'is_impersonate': getattr(request, 'is_impersonate', False),
            'real_user_username': getattr(request.real_user, 'username', None) if hasattr(request, 'real_user') else None,
            'impersonated_user_username': getattr(request.user, 'username', None),
        }

        context['debug_impersonation_info'] = debug_info

    return context
