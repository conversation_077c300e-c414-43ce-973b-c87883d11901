{% if page_obj.has_other_pages %}
<nav aria-label="Page navigation">
  <ul class="pagination justify-content-center mt-4">
    {% if page_obj.has_previous %}
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
          <span aria-hidden="true">&laquo;</span>
        </a>
      </li>
    {% else %}
      <li class="page-item disabled">
        <span class="page-link" aria-hidden="true">&laquo;</span>
      </li>
    {% endif %}

    {% for i in page_obj.paginator.page_range %}
      {% if page_obj.number == i %}
        <li class="page-item active" aria-current="page"><span class="page-link">{{ i }}</span></li>
      {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
        {# Show nearby page numbers #}
        <li class="page-item"><a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a></li>
      {% elif i == page_obj.number|add:'-3' or i == page_obj.number|add:'3' %}
         {# Add ellipsis for gaps #}
         <li class="page-item disabled"><span class="page-link">...</span></li>
      {% endif %}
    {% endfor %}

    {% if page_obj.has_next %}
      <li class="page-item">
        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
          <span aria-hidden="true">&raquo;</span>
        </a>
      </li>
    {% else %}
      <li class="page-item disabled">
        <span class="page-link" aria-hidden="true">&raquo;</span>
      </li>
    {% endif %}
  </ul>
</nav>
{% endif %}
