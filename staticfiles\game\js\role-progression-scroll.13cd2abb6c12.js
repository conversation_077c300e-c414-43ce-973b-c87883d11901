// Role Progression Scroll Indicator
document.addEventListener('DOMContentLoaded', function() {
    // Initialize after a short delay to ensure the DOM is fully loaded
    setTimeout(initRoleProgressionScroll, 1000);
});

// Initialize role progression scroll indicator
function initRoleProgressionScroll() {
    // Find all role progression containers
    const roleProgressionContainers = document.querySelectorAll('.role-progression-container');
    
    if (roleProgressionContainers.length === 0) {
        console.log('No role progression containers found');
        return;
    }
    
    console.log(`Found ${roleProgressionContainers.length} role progression containers`);
    
    // Add scroll indicators to each container
    roleProgressionContainers.forEach((container, index) => {
        addScrollIndicator(container, index);
    });
}

// Add scroll indicator to a role progression container
function addScrollIndicator(container, index) {
    // Check if the container already has a scroll indicator
    if (container.querySelector('.role-progression-scroll-indicator')) {
        return;
    }
    
    // Create the scroll indicator
    const indicator = document.createElement('div');
    indicator.className = 'role-progression-scroll-indicator';
    indicator.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M7 13l5 5 5-5M7 6l5 5 5-5"/></svg>';
    indicator.title = 'Scroll to see more';
    
    // Add the indicator to the container
    container.appendChild(indicator);
    
    // Find the role path element
    const rolePath = container.querySelector('.role-path');
    if (!rolePath) {
        console.log('No role path found in container', index);
        return;
    }
    
    // Add click event to scroll the role path
    indicator.addEventListener('click', function() {
        // Scroll the role path horizontally
        rolePath.scrollBy({
            left: 100,
            behavior: 'smooth'
        });
    });
    
    // Show/hide indicator based on scroll width
    function updateIndicatorVisibility() {
        if (rolePath.scrollWidth > rolePath.clientWidth) {
            indicator.style.display = 'flex';
        } else {
            indicator.style.display = 'none';
        }
    }
    
    // Update indicator visibility initially
    updateIndicatorVisibility();
    
    // Update indicator visibility on window resize
    window.addEventListener('resize', updateIndicatorVisibility);
}

// Re-initialize when game state changes
function reinitializeRoleProgressionScroll() {
    setTimeout(initRoleProgressionScroll, 500);
}

// Expose the reinitialization function to the global scope
window.reinitializeRoleProgressionScroll = reinitializeRoleProgressionScroll;
