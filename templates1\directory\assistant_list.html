{% extends "base/layout.html" %}
{% load static account_tags rating_tags %} {# Load new rating_tags #}

{% block title %}Assistant Directory{% endblock %}

{% block body_class %}bg-light company-directory-page{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/directory.css' %}">
<link rel="stylesheet" href="{% static 'css/featured-carousel.css' %}">
<link rel="stylesheet" href="{% static 'css/assistant-list-dark-mode.css' %}">
<style>
    /* Ensure animation is applied to carousel */
    .featured-carousel-items {
        display: flex;
        animation: scroll 60s linear infinite;
        width: max-content;
        min-height: 300px;
        animation-play-state: running;
        overflow: visible;
        transition: animation-play-state 0.3s ease;
    }

    /* Make carousel items visible */
    .featured-carousel-item {
        flex: 0 0 auto;
        margin: 0 30px;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        min-width: 300px;
        min-height: 300px;
        position: relative;
        z-index: 1;
        transition: transform 0.3s ease, filter 0.3s ease;
    }

    .featured-carousel-item:hover {
        transform: scale(1.05);
        z-index: 10; /* Bring hovered item to front */
        filter: brightness(1.05);
    }

    /* Style the featured item wrapper */
    .featured-item-wrapper {
        position: relative !important;
        width: 355px !important;
        min-height: 300px !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        background-color: rgba(255, 255, 255, 0.8) !important;
        border-radius: 10px !important;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
        padding: 20px !important;
        transition: all 0.3s ease !important;
    }

    .featured-item-wrapper:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15) !important;
    }

    /* Make logo container visible */
    .featured-carousel-item .logo-container {
        height: 180px !important;
        width: 180px !important;
        min-height: 180px !important;
        min-width: 180px !important;
        max-height: 180px !important;
        max-width: 180px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: white !important;
        border-radius: 0.25rem !important;
        overflow: hidden !important;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2), 0 8px 15px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        position: relative !important;
        margin: 0 auto 20px !important;
        z-index: 2 !important;
    }

    /* Make logo placeholder visible */
    .featured-carousel-item .logo-placeholder {
        color: #0d6efd !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 100% !important;
        height: 100% !important;
        background-color: rgba(240, 248, 255, 0.5) !important;
    }

    .featured-carousel-item .logo-placeholder i {
        font-size: 140px !important;
        line-height: 1 !important;
        display: block !important;
        text-align: center !important;
        margin: 0 !important;
        padding: 0 !important;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2) !important;
    }

    /* Make item info visible */
    .featured-carousel-item .item-info {
        text-align: center !important;
        width: 100% !important;
        padding: 10px !important;
    }

    .featured-carousel-item .item-info h5 {
        color: #0d6efd !important;
        font-size: 1.3rem !important;
        font-weight: 700 !important;
        line-height: 1.3 !important;
        margin-bottom: 0.5rem !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) !important;
    }

    .featured-carousel-item .item-info p {
        font-size: 0.9rem !important;
        line-height: 1.5 !important;
        color: #495057 !important;
        margin-bottom: 0.5rem !important;
    }

    /* Make like button visible */
    .featured-carousel-item .like-button {
        background-color: white !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
        border: 1px solid rgba(0, 0, 0, 0.08) !important;
        width: 36px !important;
        height: 36px !important;
        padding: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: all 0.2s ease !important;
        z-index: 10 !important;
        position: absolute !important;
        top: 10px !important;
        right: 10px !important;
    }

    /* Animation for continuous scrolling */
    @keyframes scroll {
        0% { transform: translateX(0); }
        100% { transform: translateX(-50%); }
    }

    /* Apply directory theme styling */
    .featured-section {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
        position: relative !important;
        z-index: 100 !important;
        overflow: visible !important;
    }

    /* Make featured carousel container visible */
    .featured-carousel-container {
        width: 100% !important;
        overflow: hidden !important;
        position: relative !important;
        padding: 30px 0 70px 0 !important;
        margin-bottom: 30px !important;
        background-color: rgba(255, 255, 255, 0.5) !important;
        border-radius: 10px !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05) !important;
    }

    .tier-section {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    .list-group-item {
        background-color: #fff;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
        overflow: hidden;
        height: auto;
        min-height: 160px;
        margin-bottom: 1rem;
    }

    .list-group-item:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }

    .filter-form {
        background-color: rgba(240, 242, 245, 0.5);
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        border: 1px solid #dee2e6;
    }

    /* Logo container styles are now in directory.css */

    .logo-placeholder {
        font-size: 2.5rem;
    }

    .tier-badge {
        position: absolute;
        top: 0;
        left: 0;
        font-size: 0.7em;
        padding: 0.25em 0.5em;
        margin: 0.5rem;
        z-index: 10;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .tier-gold {
        background-color: #ffc107;
        color: #212529;
    }

    .tier-silver {
        background-color: #adb5bd;
        color: #212529;
    }

    .tier-bronze {
        background-color: #cd7f32;
        color: #fff;
    }

    .featured-carousel .carousel-item {
        background-color: #fff;
        border-radius: 0.5rem;
        padding: 1.5rem;
    }

    .carousel-indicators {
        position: relative;
        margin-top: 1rem;
        margin-bottom: 0;
    }

    .carousel-indicators button {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #dee2e6;
    }

    .carousel-indicators button.active {
        background-color: #0d6efd;
    }
</style>
{% endblock %}

{% block head_extra %}
<style>
    /* Fix for carousel hover pause */
    .featured-carousel-items:hover {
        animation-play-state: paused !important;
    }

    /* Rating stars styling */
    .star-rating .stars .bi-star-fill {
        color: gold;
    }
    .star-rating .stars .bi-star {
        color: #ccc;
    }
    .modal-stars .modal-star-btn.active i {
        color: gold !important;
    }

    /* Make stars more clickable */
    .modal-stars .modal-star-btn {
        cursor: pointer;
        padding: 10px !important;
        margin: 0 5px;
        transition: transform 0.2s;
    }
    .modal-stars .modal-star-btn:hover {
        transform: scale(1.2);
    }
    .modal-stars .modal-star-btn i {
        font-size: 1.5em;
    }

    /* Featured carousel rating styling */
    .featured-carousel-item .rating-display-container {
        margin-top: 5px;
        text-align: center;
    }
    .featured-carousel-item .star-rating {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }
    .featured-carousel-item .star-rating .stars {
        display: inline-flex;
    }
    .featured-carousel-item .star-rating .stars i {
        font-size: 1rem;
        margin: 0 1px;
    }

    /* Logo container styles are now in directory.css */

    /* Ensure consistent card sizing */
    .list-group-item {
        height: 220px;
        overflow: hidden;
    }

    /* Interactive Stars */
    .interactive-star-rating { cursor: default; }
    .interactive-star-rating .star-btn {
        background: none;
        border: none;
        padding: 0 0.1em;
        margin: 0;
        cursor: pointer;
        color: #ddd;
        transition: color 0.2s ease-in-out, transform 0.1s ease;
        font-size: 1.1em;
    }
    .interactive-star-rating .star-btn:hover {
        color: #f8d96c;
        transform: scale(1.1);
    }
    .interactive-star-rating .star-btn.filled {
        color: #f5c518;
    }
    .interactive-star-rating .star-btn.user-rated {
        color: #f5c518;
    }
    .interactive-star-rating.rating-submitted .star-btn {
        cursor: not-allowed;
        opacity: 0.7;
    }
</style>
{% csrf_token %}
<meta name="csrf-token" content="{{ csrf_token }}">
{% endblock %}

{% block content %}
<div class="container mt-5 mb-5 company-directory-container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="display-5 mb-0 fw-bold">Assistant Directory</h1>
    </div>

    {# Filter Form #}
    <form method="get" action="{% url 'directory:assistant_list' %}" class="filter-form">
        <h5 class="mb-3 fw-bold"><i class="bi bi-funnel-fill me-2 text-primary"></i>Find Assistants</h5>

        <div class="row g-3 mb-3">
            <div class="col-md-6"> {# Name/Description Filter #}
                <label for="q_name_dir" class="form-label">Search</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                    <input type="text" name="q_name" id="q_name_dir" class="form-control" placeholder="Search name or description..." value="{{ q_name|default:'' }}">
                </div>
            </div>
            <div class="col-md-3"> {# Company Filter #}
                <label for="q_company_dir" class="form-label">Company</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-building"></i></span>
                    <input type="text" name="q_company" id="q_company_dir" class="form-control" placeholder="Company..." value="{{ q_company|default:'' }}">
                </div>
            </div>
            <div class="col-md-3"> {# Sort Dropdown #}
                <label for="sort_by" class="form-label">Sort By</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-sort-down"></i></span>
                    <select name="sort_by" id="sort_by" class="form-select">
                        <option value="tier" {% if sort_by == 'tier' or not sort_by %}selected{% endif %}>Sort by Tier</option>
                        <option value="latest" {% if sort_by == 'latest' %}selected{% endif %}>Sort by Latest</option>
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Sort by Name</option>
                        <option value="rating" {% if sort_by == 'rating' %}selected{% endif %}>Sort by Rating</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row g-3">
            <div class="col-md-4"> {# Category Filter #}
                <label for="q_category_dir" class="form-label">Category</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-tag"></i></span>
                    <input type="text" name="q_category" id="q_category_dir" class="form-control" placeholder="Category..." value="{{ q_category|default:'' }}">
                </div>
            </div>
            <div class="col-md-4"> {# Tag Filter #}
                <label for="q_tag_dir" class="form-label">Tag</label>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-tags"></i></span>
                    <input type="text" name="q_tag" id="q_tag_dir" class="form-control" placeholder="Tag..." value="{{ q_tag|default:'' }}">
                </div>
            </div>
            <div class="col-md-4"> {# Buttons #}
                <label class="form-label">&nbsp;</label>
                <div class="d-flex">
                    <button type="submit" class="btn btn-primary me-2 flex-grow-1">
                        <i class="bi bi-filter me-1"></i> Apply Filters
                    </button>
                    <a href="{% url 'directory:assistant_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle"></i>
                    </a>
                </div>
            </div>
        </div>
    </form>
    {# End Filter Form #}

    {# Display Active Filters #}
    {% if q_name or q_company or q_category or q_tag %}
    <div class="alert alert-light border mb-4 mt-2">
        <div class="d-flex align-items-center">
            <i class="bi bi-funnel me-2 text-primary"></i>
            <span class="fw-medium">Filtering by:</span>
            <div class="ms-2">
                {% if q_name %}<span class="badge bg-primary me-1">Name/Desc: {{ q_name }}</span>{% endif %}
                {% if q_company %}<span class="badge bg-info text-dark me-1">Company: {{ q_company }}</span>{% endif %}
                {% if q_category %}<span class="badge bg-secondary me-1">Category: {{ q_category }}</span>{% endif %}
                {% if q_tag %}<span class="badge bg-light text-dark border me-1">Tag: {{ q_tag }}</span>{% endif %}
            </div>
        </div>
    </div>
    {% endif %}
    {# End Display Active Filters #}

    {# --- Featured Assistants Section (Continuous Scrolling Carousel) --- #}
    <!-- DEBUG: featured_listings count: {{ featured_listings|length }} -->
    <div class="featured-section mt-5">
        <h2 class="mb-4 fw-bold"><i class="bi bi-star-fill me-2 text-warning"></i>Featured Assistants</h2>

        {% with settings=directory_settings_dict %}
        <div class="featured-carousel-container"
             data-visible-count="{{ settings.featured_visible_count|default:1 }}"
             data-animation-delay="{{ settings.featured_autoplay_delay|default:5000 }}">
            <div class="featured-carousel-items">
                {% if featured_listings %}
                    {# Display real featured assistants #}
                    {% for listing in featured_listings %}
                        {% with assistant=listing.assistant %}
                        <div class="featured-carousel-item">
                            <div class="featured-item-wrapper">
                                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" title="{{ assistant.name }}" class="text-center">
                                    {% with logo_url=assistant.get_logo_url %}
                                        <div class="logo-container">
                                            {% if logo_url %}
                                                <img src="{{ logo_url }}" alt="{{ assistant.name }} Logo"
                                                     onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'logo-placeholder\'><i class=\'bi bi-robot\'></i></div>';">
                                            {% else %}
                                                <div class="logo-placeholder">
                                                    <i class="bi bi-robot"></i>
                                                </div>
                                            {% endif %}
                                        </div>
                                    {% endwith %}
                                    <div class="item-info">
                                        <h5>{{ assistant.name }}</h5>
                                        {% if listing.description %}
                                            <p>{{ listing.description|truncatechars:60 }}</p>
                                        {% endif %}
                                        <div class="rating-display-container" id="rating-display-{{ assistant.id }}">
                                            {% if listing.avg_rating and listing.avg_rating > 0 %}
                                                {% render_stars listing.avg_rating listing.total_ratings %}
                                            {% else %}
                                                <span class="text-muted fst-italic">(No ratings yet)</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </a>
                                {% if assistant.id %}
                                    <button class="like-button btn btn-icon {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                            data-item-id="{{ assistant.id }}"
                                            data-item-type="assistant"
                                            title="{% if assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}">
                                        <i class="bi bi-heart-fill"></i>
                                    </button>
                                {% endif %}
                            </div>
                        </div>
                        {% endwith %}
                    {% endfor %}

                    {# Duplicate the real featured assistants for continuous scrolling effect #}
                    {% if featured_listings|length >= 3 %}
                        {% for listing in featured_listings %}
                            {% with assistant=listing.assistant %}
                            <div class="featured-carousel-item">
                                <div class="featured-item-wrapper">
                                    <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" title="{{ assistant.name }}" class="text-center">
                                        {% with logo_url=assistant.get_logo_url %}
                                            <div class="logo-container">
                                                {% if logo_url %}
                                                    <img src="{{ logo_url }}" alt="{{ assistant.name }} Logo"
                                                         onerror="this.style.display='none'; this.parentNode.innerHTML='<div class=\'logo-placeholder\'><i class=\'bi bi-robot\'></i></div>';">
                                                {% else %}
                                                    <div class="logo-placeholder">
                                                        <i class="bi bi-robot"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        {% endwith %}
                                        <div class="item-info">
                                            <h5>{{ assistant.name }}</h5>
                                            {% if listing.description %}
                                                <p>{{ listing.description|truncatechars:60 }}</p>
                                            {% endif %}
                                            <div class="rating-display-container" id="rating-display-dup-{{ assistant.id }}">
                                                {% if listing.avg_rating and listing.avg_rating > 0 %}
                                                    {% render_stars listing.avg_rating listing.total_ratings %}
                                                {% else %}
                                                    <span class="text-muted fst-italic">(No ratings yet)</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </a>
                                    {% if assistant.id %}
                                        <button class="like-button btn btn-icon {% if assistant.id in saved_assistant_ids %}text-danger{% else %}text-secondary{% endif %}"
                                                data-item-id="{{ assistant.id }}"
                                                data-item-type="assistant"
                                                title="{% if assistant.id in saved_assistant_ids %}Unlike{% else %}Like{% endif %}">
                                            <i class="bi bi-heart-fill"></i>
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                            {% endwith %}
                        {% endfor %}
                    {% endif %}
                {% else %}
                    {# No featured assistants - JavaScript will create example items #}
                    <!-- No featured assistants found. Example items will be created by JavaScript. -->
                {% endif %}
            </div>
        </div>
        {% endwith %}
    </div>
    {# --- End Featured Assistants Section --- #}

    {# --- Main Assistant List Section --- #}
    <div class="mt-5">
        <h2 class="h2 mb-4 fw-bold">
            {% if sort_by == 'tier' %}
                <i class="bi bi-layers-fill me-2 text-primary"></i>Assistants by Tier
            {% else %}
                <i class="bi bi-robot me-2 text-primary"></i>All Assistants
            {% endif %}
        </h2>

        {% if page_obj.object_list %}
            <div class="list-group" id="assistant-list-container"> {# Container for event delegation, added ID #}
                {% if sort_by == 'tier' %}
                    {# Logic to display tier headers dynamically #}
                    {% with TIER_GOLD='gold' TIER_SILVER='silver' TIER_BRONZE='bronze' TIER_STANDARD='standard' %} {# Define constants #}
                    {% regroup page_obj.object_list by assistant.get_tier_display as tier_groups %}

                    {% for group in tier_groups %}
                        {# Determine Tier Heading based on grouper (which is the display name) #}
                        {% with tier_display_name=group.grouper %}
                            {# Display all tiers, including Standard #}
                            <div class="tier-section {% if tier_display_name == 'Gold' %}gold{% elif tier_display_name == 'Silver' %}silver{% elif tier_display_name == 'Bronze' %}bronze{% elif tier_display_name == 'Standard' %}standard{% endif %}">
                                <h3 class="d-flex align-items-center">
                                    {% if tier_display_name == 'Gold' %}
                                        <i class="bi bi-trophy-fill me-2 text-warning"></i>Gold Tier
                                        <span class="badge bg-warning text-dark ms-2 small">Premium</span>
                                    {% elif tier_display_name == 'Silver' %}
                                        <i class="bi bi-award-fill me-2 text-secondary"></i>Silver Tier
                                        <span class="badge bg-secondary text-white ms-2 small">Enhanced</span>
                                    {% elif tier_display_name == 'Bronze' %}
                                        <i class="bi bi-award me-2" style="color: #cd7f32;"></i>Bronze Tier
                                        <span class="badge" style="background-color: #cd7f32; color: white;" class="ms-2 small">Plus</span>
                                    {% elif tier_display_name == 'Standard' %}
                                        <i class="bi bi-robot me-2 text-primary"></i>Standard Assistants
                                    {% else %}
                                        <i class="bi bi-robot me-2"></i>Other Assistants {# Fallback #}
                                    {% endif %}
                                </h3>

                                {# Loop through items in this tier group #}
                                <div class="list-group company-cards-container">
                                    {% for listing in group.list %}
                                        {% include "directory/partials/assistant_card.html" with listing=listing saved_assistant_ids=saved_assistant_ids display_context='tier' %}
                                    {% endfor %}
                                </div>
                            </div>
                        {% endwith %}
                    {% endfor %}
                    {% endwith %}

                {% else %}
                    {# Default display if not sorting by tier #}
                    <div class="list-group company-cards-container">
                        {% for listing in page_obj.object_list %}
                            {% include "directory/partials/assistant_card.html" with listing=listing saved_assistant_ids=saved_assistant_ids display_context='tier' %}
                        {% endfor %}
                    </div>
                {% endif %}
            </div> {# /list-group #}

            <div class="mt-5">
                {% include "pagination_with_items_per_page.html" with page_obj=page_obj %} {# Use enhanced pagination with items per page #}
            </div>
            {% csrf_token %} {# Keep CSRF token if needed for other actions on the page #}

        {% else %}
            <div class="alert alert-info shadow-sm">
                <div class="d-flex align-items-center">
                    <i class="bi bi-info-circle-fill me-3 fs-4"></i>
                    <div>
                        <h5 class="mb-1">No Results Found</h5>
                        <p class="mb-0">No assistants found matching your criteria. Try adjusting your filters.</p>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
    {# --- End Main Assistant List Section --- #}

</div> {# /container #}

{# Rating Modal Structure (Enhanced) #}
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="ratingModalLabel">Rate Assistant</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-4">How would you rate <strong id="modalAssistantName" class="text-primary">this assistant</strong>?</p>
        <div class="modal-stars text-center mb-4" style="font-size: 2.5rem;">
            {% for i_int in "12345" %}
            <button class="modal-star-btn btn btn-link text-secondary p-1" data-rating-value="{{ i_int }}" title="Rate {{ i_int }} star{{ i_int|pluralize }}">
                <i class="bi bi-star"></i>
            </button>
            {% endfor %}
        </div>
        <div class="text-center text-muted small mb-3">Click on a star to select your rating</div>
        <div id="modalErrorMsg" class="alert alert-danger small mt-2" style="display: none;"></div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>
            <i class="bi bi-check-circle me-1"></i> Submit Rating
        </button>
      </div>
    </div>
  </div>
</div>
{# End Rating Modal #}

{# Folder Options Modal (Enhanced) #}
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header bg-light">
        <h5 class="modal-title" id="folderOptionsModalLabel">
            <i class="bi bi-heart-fill text-danger me-2"></i>Add to Favorites
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Add <strong id="modalFolderNameItemName" class="text-primary">this item</strong> to your favorites:</p>

        {# Option 1: Save without folder #}
        <div class="card mb-3 border-primary">
            <div class="card-body p-3">
                <h6 class="card-title mb-2"><i class="bi bi-bookmark-heart-fill me-2 text-primary"></i>Quick Save</h6>
                <p class="card-text small text-muted mb-2">Save without organizing into a folder</p>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-primary save-without-folder-btn" id="saveWithoutFolderBtn">
                        <i class="bi bi-bookmark-heart me-2"></i>Save to Favorites
                    </button>
                </div>
            </div>
        </div>

        {# Option 2: Add to existing folder #}
        <div id="existingFoldersSection" class="card mb-3" style="display: none;">
            <div class="card-body p-3">
                <h6 class="card-title mb-2"><i class="bi bi-folder-fill me-2 text-warning"></i>Add to Folder</h6>
                <p class="card-text small text-muted mb-2">Add to one of your existing folders</p>
                <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-folder-symlink"></i></span>
                    <select class="form-select" id="selectFolder">
                        <option selected disabled value="">Choose folder...</option>
                        {# Options will be populated by JS #}
                    </select>
                    <button class="btn btn-primary" type="button" id="addToFolderBtn" disabled>
                        <i class="bi bi-plus-lg"></i> Add
                    </button>
                </div>
            </div>
        </div>

        {# Option 3: Create new folder #}
        <div class="card">
            <div class="card-body p-3">
                <h6 class="card-title mb-2"><i class="bi bi-folder-plus me-2 text-success"></i>Create New Folder</h6>
                <p class="card-text small text-muted mb-2">Create a new folder and add this item to it</p>
                <button type="button" class="btn btn-outline-success w-100 mb-3 create-folder-toggle-btn" id="createFolderToggleBtn">
                    <i class="bi bi-folder-plus me-2"></i> Create New Folder
                </button>
                <div id="createFolderForm" style="display: none; margin-top: 15px;">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
                        <input type="text" class="form-control" id="newFolderName" placeholder="New folder name...">
                        <button class="btn btn-success create-and-save-btn" type="button" id="createAndSaveBtn" disabled>
                            <i class="bi bi-check-lg"></i> Create
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div id="folderModalErrorMsg" class="alert alert-danger small mt-3" style="display: none;">
          <!-- Error messages will be displayed here -->
        </div>
      </div>
    </div>
  </div>
</div>
{# End Folder Options Modal #}


{% endblock %}

{% block extra_js %}
<!-- Direct hover pause fix for assistants list carousel -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Direct approach to fix hover pause for assistants list
    const carousel = document.querySelector('.featured-carousel-items');
    const carouselContainer = document.querySelector('.featured-carousel-container');

    if (carousel && carouselContainer) {
        console.log('[DEBUG] Assistants List: Adding direct hover handlers');

        // Remove any inline animation-play-state
        carousel.style.removeProperty('animation-play-state');

        // Add hover handlers to container
        carouselContainer.addEventListener('mouseenter', function() {
            carousel.style.animationPlayState = 'paused';
            console.log('[DEBUG] Assistants List: Direct pause on container hover');
        });

        carouselContainer.addEventListener('mouseleave', function() {
            carousel.style.animationPlayState = 'running';
            console.log('[DEBUG] Assistants List: Direct resume on container leave');
        });

        // Add hover handlers to all items
        const items = carousel.querySelectorAll('.featured-carousel-item');
        items.forEach(function(item) {
            item.addEventListener('mouseenter', function() {
                carousel.style.animationPlayState = 'paused';
                console.log('[DEBUG] Assistants List: Direct pause on item hover');
            });

            item.addEventListener('mouseleave', function() {
                if (!carouselContainer.matches(':hover')) {
                    carousel.style.animationPlayState = 'running';
                    console.log('[DEBUG] Assistants List: Direct resume on item leave');
                }
            });
        });
    }
});
</script>

<!-- Debug info for featured carousel -->
<script>
console.log('[DEBUG] Assistant List: Page loaded');
document.addEventListener('DOMContentLoaded', function() {
    console.log('[DEBUG] Assistant List: DOMContentLoaded event fired');

    // Check for carousel container
    const carouselContainer = document.querySelector('.featured-carousel-container');
    console.log('[DEBUG] Assistant List: Featured carousel container exists?', !!carouselContainer);
    if (carouselContainer) {
        console.log('[DEBUG] Assistant List: Carousel container attributes:', {
            visibleCount: carouselContainer.dataset.visibleCount,
            animationDelay: carouselContainer.dataset.animationDelay,
            width: carouselContainer.offsetWidth,
            height: carouselContainer.offsetHeight
        });
    }

    // Check for carousel items container
    const carouselItemsContainer = document.querySelector('.featured-carousel-items');
    console.log('[DEBUG] Assistant List: Featured carousel items container exists?', !!carouselItemsContainer);
    if (carouselItemsContainer) {
        console.log('[DEBUG] Assistant List: Carousel items container dimensions:', {
            width: carouselItemsContainer.offsetWidth,
            height: carouselItemsContainer.offsetHeight,
            scrollWidth: carouselItemsContainer.scrollWidth
        });
    }

    // Check carousel items
    const carouselItems = document.querySelectorAll('.featured-carousel-item');
    console.log('[DEBUG] Assistant List: Found', carouselItems.length, 'carousel items');

    // Log details about each carousel item
    if (carouselItems.length > 0) {
        console.log('[DEBUG] Assistant List: First carousel item dimensions:', {
            width: carouselItems[0].offsetWidth,
            height: carouselItems[0].offsetHeight
        });

        // Check for images in carousel items
        const carouselImages = document.querySelectorAll('.featured-carousel-item img');
        console.log('[DEBUG] Assistant List: Found', carouselImages.length, 'images in carousel items');

        // Check for placeholders in carousel items
        const carouselPlaceholders = document.querySelectorAll('.featured-carousel-item .logo-placeholder');
        console.log('[DEBUG] Assistant List: Found', carouselPlaceholders.length, 'placeholders in carousel items');
    }

    // Check like buttons
    const likeButtons = document.querySelectorAll('.featured-carousel-item .like-button');
    console.log('[DEBUG] Assistant List: Found', likeButtons.length, 'like buttons in carousel items');

    // Log any errors with images
    const allImages = document.querySelectorAll('.featured-carousel-item img');
    allImages.forEach((img, index) => {
        img.addEventListener('error', function() {
            console.error(`[DEBUG] Image load error for carousel item ${index}:`, {
                src: img.src,
                alt: img.alt
            });
        });
    });
});
</script>
<script src="{% static 'js/featured-carousel.js' %}"></script>
<script>
// Backup script to ensure example items are created
document.addEventListener('DOMContentLoaded', function() {
    console.log('[DEBUG] Assistant List: Backup script for example items');

    // Wait a bit to ensure the main script has run
    setTimeout(function() {
        const carousel = document.querySelector('.featured-carousel-items');
        if (!carousel) {
            console.error('[DEBUG] Assistant List: No carousel items container found in backup script');
            return;
        }

        const items = carousel.querySelectorAll('.featured-carousel-item');
        console.log('[DEBUG] Assistant List: Backup script found', items.length, 'items');

        // Check if items are visible
        if (items.length > 0) {
            console.log('[DEBUG] Assistant List: Checking item visibility');

            items.forEach((item, index) => {
                const rect = item.getBoundingClientRect();
                console.log(`[DEBUG] Assistant List: Item ${index} position:`, {
                    top: rect.top,
                    left: rect.left,
                    width: rect.width,
                    height: rect.height,
                    visible: rect.width > 0 && rect.height > 0
                });

                // Force item to be visible
                item.style.display = 'flex';
                item.style.visibility = 'visible';
                item.style.opacity = '1';

                // Check wrapper
                const wrapper = item.querySelector('.featured-item-wrapper');
                if (wrapper) {
                    wrapper.style.display = 'flex';
                    wrapper.style.visibility = 'visible';
                    wrapper.style.opacity = '1';
                    wrapper.style.width = '355px';

                    // Check logo container
                    const logoContainer = wrapper.querySelector('.logo-container');
                    if (logoContainer) {
                        logoContainer.style.display = 'flex';
                        logoContainer.style.visibility = 'visible';
                        logoContainer.style.opacity = '1';
                    }

                    // Check item info
                    const itemInfo = wrapper.querySelector('.item-info');
                    if (itemInfo) {
                        itemInfo.style.display = 'block';
                        itemInfo.style.visibility = 'visible';
                        itemInfo.style.opacity = '1';
                    }
                }
            });

            // Force animation
            carousel.style.animation = 'none';
            setTimeout(() => {
                carousel.style.animation = 'scroll 60s linear infinite';
                console.log('[DEBUG] Assistant List: Reapplied animation');
            }, 100);
        }

        if (items.length === 0) {
            console.log('[DEBUG] Assistant List: Creating example items from backup script');

            // Create example items
            for (let i = 1; i <= 5; i++) {
                const item = document.createElement('div');
                item.className = 'featured-carousel-item';
                item.style.display = 'flex';
                item.style.visibility = 'visible';
                item.style.opacity = '1';

                item.innerHTML = `
                    <div class="featured-item-wrapper" style="width: 355px; display: flex; visibility: visible; opacity: 1;">
                        <a href="#" title="Example Assistant ${i}" class="text-center">
                            <div class="logo-container" style="display: flex; visibility: visible; opacity: 1;">
                                <div class="logo-placeholder" style="display: flex; visibility: visible; opacity: 1;">
                                    <i class="bi bi-robot" style="visibility: visible; opacity: 1;"></i>
                                </div>
                            </div>
                            <div class="item-info" style="display: block; visibility: visible; opacity: 1;">
                                <h5 style="visibility: visible; opacity: 1;">Example Assistant ${i}</h5>
                                <p style="visibility: visible; opacity: 1;">This is an example assistant.</p>
                            </div>
                        </a>
                        <button class="like-button btn btn-icon text-secondary"
                                data-item-id="example-${i}"
                                data-item-type="assistant"
                                title="Like">
                            <i class="bi bi-heart-fill"></i>
                        </button>
                    </div>
                `;

                carousel.appendChild(item);
            }

            // Duplicate for continuous scrolling
            for (let i = 1; i <= 5; i++) {
                const item = document.createElement('div');
                item.className = 'featured-carousel-item';
                item.style.display = 'flex';
                item.style.visibility = 'visible';
                item.style.opacity = '1';

                item.innerHTML = `
                    <div class="featured-item-wrapper" style="width: 355px; display: flex; visibility: visible; opacity: 1;">
                        <a href="#" title="Example Assistant ${i}" class="text-center">
                            <div class="logo-container" style="display: flex; visibility: visible; opacity: 1;">
                                <div class="logo-placeholder" style="display: flex; visibility: visible; opacity: 1;">
                                    <i class="bi bi-robot" style="visibility: visible; opacity: 1;"></i>
                                </div>
                            </div>
                            <div class="item-info" style="display: block; visibility: visible; opacity: 1;">
                                <h5 style="visibility: visible; opacity: 1;">Example Assistant ${i}</h5>
                                <p style="visibility: visible; opacity: 1;">This is an example assistant.</p>
                            </div>
                        </a>
                        <button class="like-button btn btn-icon text-secondary"
                                data-item-id="example-${i}"
                                data-item-type="assistant"
                                title="Like">
                            <i class="bi bi-heart-fill"></i>
                        </button>
                    </div>
                `;

                carousel.appendChild(item);
            }

            // Force animation
            carousel.style.animation = 'none';
            setTimeout(() => {
                carousel.style.animation = 'scroll 60s linear infinite';
                console.log('[DEBUG] Assistant List: Applied animation to example items');
            }, 100);
        }
    }, 1000); // Wait 1 second to ensure the main script has run
});

// Helper function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    if (!csrfInput) {
        const csrfMeta = document.querySelector('meta[name="csrf-token"]');
        if (csrfMeta) return csrfMeta.getAttribute('content');
    }
    return csrfInput ? csrfInput.value : null;
}

// --- Favorite Button & Folder Modal Logic ---
function handleFavoriteButtons() {
    const listContainer = document.getElementById('assistant-list-container'); // Main list container
    const featuredCarousel = document.getElementById('featuredAssistantsCarousel'); // Featured carousel container
    const folderModalElement = document.getElementById('folderOptionsModal');
    let folderModal = null;
    if (folderModalElement) {
        folderModal = new bootstrap.Modal(folderModalElement);
    } else {
        console.error("Folder options modal element not found.");
        return; // Cannot proceed without the modal
    }

    // Check if either container exists
    if (!listContainer && !featuredCarousel) {
        console.log("Neither assistant list container nor featured carousel found.");
        return;
    }

    const clickHandler = async (event) => {
        const button = event.target.closest('.like-button');
        if (!button) return; // Ignore clicks not on the like button

        event.preventDefault();
        event.stopPropagation();

        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            console.error("CSRF token not found!");
            alert("Action failed. Please refresh.");
            return;
        }

        const itemId = button.dataset.itemId;
        const itemType = button.dataset.itemType;
        const url = "{% url 'directory:toggle_saved_item' %}";

        // Store for potential modal use
        currentModalItemId = itemId;
        currentModalItemType = itemType;
        console.log('Set current modal item:', { currentModalItemId, currentModalItemType });

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({ 'item_id': itemId, 'item_type': itemType })
            });

            const data = await response.json();

            if (!response.ok) throw new Error(data.message || `HTTP error ${response.status}`);

            if (data.status === 'success' && data.action === 'unfavorited') {
                // Item was unfavorited successfully
                updateHeartIcon(button, false);
            } else if (data.status === 'options') {
                // Item is not saved, show folder options modal
                populateAndShowFolderModal(data);
            } else {
                // Unexpected success response
                console.error('Unexpected response from toggle_saved_item:', data);
                alert('An unexpected error occurred.');
            }

        } catch (error) {
            console.error('Error handling favorite click:', error);
            alert(`An error occurred: ${error.message}`);
        }
    };

    if (listContainer) {
        listContainer.addEventListener('click', clickHandler);
    } else {
        console.log("Main assistant list container not found.");
    }
    if (featuredCarousel) { // Changed from featuredList
         // Need to listen on the container
        featuredCarousel.addEventListener('click', clickHandler);
    } else {
        console.log("Featured assistants carousel container not found.");
    }


    // Store current item details for modal actions
    let currentModalItemId = null;
    let currentModalItemType = null;

    // --- Helper: Update Heart Icon ---
    function updateHeartIcon(button, isSaved) {
        if (!button) return;
        if (isSaved) {
            button.classList.remove('text-secondary');
            button.classList.add('text-danger');
            button.title = 'Unlike';
        } else {
            button.classList.remove('text-danger');
            button.classList.add('text-secondary');
            button.title = 'Like';
        }
    }

    // --- Helper: Populate and Show Folder Modal ---
    function populateAndShowFolderModal(data) {
        console.log('populateAndShowFolderModal called with data:', data);
        if (!folderModalElement || !folderModal) {
            console.error('Modal elements not found:', { folderModalElement: !!folderModalElement, folderModal: !!folderModal });
            return;
        }

        // Make sure we have the item ID and type
        if (data.item_id && data.item_type) {
            currentModalItemId = data.item_id;
            currentModalItemType = data.item_type;
            console.log('Updated current modal item from data:', { currentModalItemId, currentModalItemType });
        }

        // Set item name
        const itemNameElement = folderModalElement.querySelector('#modalFolderNameItemName');
        if (itemNameElement) itemNameElement.textContent = data.item_name || 'this item';

        // Also set the item name in the old-style element if it exists
        const oldStyleItemName = folderModalElement.querySelector('#modalItemName');
        if (oldStyleItemName) oldStyleItemName.textContent = data.item_name || 'this item';

        // Populate existing folders dropdown
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const existingFoldersSection = folderModalElement.querySelector('#existingFoldersSection');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');

        selectFolder.innerHTML = '<option selected disabled value="">Choose folder...</option>'; // Reset options
        if (data.folders && data.folders.length > 0) {
            data.folders.forEach(folder => {
                const option = document.createElement('option');
                option.value = folder.id;
                option.textContent = folder.name;
                selectFolder.appendChild(option);
            });
            existingFoldersSection.style.display = 'block';
            addToFolderBtn.disabled = true; // Disable until a folder is selected
        } else {
            existingFoldersSection.style.display = 'none';
        }

        // Reset other fields
        folderModalElement.querySelector('#newFolderName').value = '';
        folderModalElement.querySelector('#createFolderAndSaveBtn').disabled = true;
        folderModalElement.querySelector('#folderModalErrorMsg').style.display = 'none';
        folderModalElement.querySelector('#folderModalErrorMsg').textContent = '';

        // Show the modal
        folderModal.show();
    }

    // Use the existing currentModalItemId and currentModalItemType variables

    // --- Folder Modal Event Listeners ---
    if (folderModalElement) {
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');
        const newFolderNameInput = folderModalElement.querySelector('#newFolderName');
        const createFolderBtn = folderModalElement.querySelector('#createFolderAndSaveBtn');
        const saveWithoutFolderBtn = folderModalElement.querySelector('#saveWithoutFolderBtn');
        const errorMsgElement = folderModalElement.querySelector('#folderModalErrorMsg');

        // Enable/disable "Add" button based on selection
        selectFolder.addEventListener('change', () => {
            addToFolderBtn.disabled = !selectFolder.value;
        });

        // Enable/disable "Create & Save" button based on input
        newFolderNameInput.addEventListener('input', () => {
            createFolderBtn.disabled = !newFolderNameInput.value.trim();
        });

        // Action: Save without folder
        saveWithoutFolderBtn.addEventListener('click', async () => {
            console.log('Save without folder button clicked');
            await handleSaveFolderAction("{% url 'directory:save_item_no_folder' %}", {});
        });

        // Action: Add to existing folder
        addToFolderBtn.addEventListener('click', async () => {
            const folderId = selectFolder.value;
            if (!folderId) return;
            await handleSaveFolderAction("{% url 'directory:add_item_to_folder' %}", { folder_id: folderId });
        });

        // Action: Create folder and save
        createFolderBtn.addEventListener('click', async () => {
            console.log('Create folder button clicked');
            const folderName = newFolderNameInput.value.trim();
            if (!folderName) {
                console.log('No folder name entered');
                return;
            }
            console.log('Creating folder with name:', folderName);
            await handleSaveFolderAction("{% url 'directory:create_folder_and_save' %}", { folder_name: folderName });
        });

        // Generic function to handle the save actions
        async function handleSaveFolderAction(url, additionalParams) {
            console.log('handleSaveFolderAction called with:', { url, additionalParams });

            if (!currentModalItemId || !currentModalItemType) {
                console.error('Item details missing:', { currentModalItemId, currentModalItemType });
                showFolderModalError("Item details missing. Please close and retry.");
                return;
            }

            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                 console.error('CSRF token missing');
                 showFolderModalError("CSRF token missing. Please refresh.");
                 return;
            }

            console.log('Save action proceeding with:', {
                itemId: currentModalItemId,
                itemType: currentModalItemType,
                csrfToken: csrfToken.substring(0, 5) + '...'
            });

            showFolderModalError(''); // Clear previous errors

            const bodyParams = new URLSearchParams({
                'item_id': currentModalItemId,
                'item_type': currentModalItemType,
                ...additionalParams
            });

            try {
                console.log(`Sending request to ${url} with params:`, Object.fromEntries(bodyParams));

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: bodyParams
                });

                console.log(`Response status: ${response.status}`);
                const data = await response.json();
                console.log('Response data:', data);

                if (!response.ok || data.status !== 'success') {
                    const errorMsg = data.message || `HTTP error ${response.status}`;
                    console.error('Error response from server:', errorMsg);
                    throw new Error(errorMsg);
                }

                console.log('Save successful!');

                // Success! Update the heart icon on the page and close modal
                // Find the button in any of our containers
                let originalButton = null;

                // Check in the main list container
                if (listContainer) {
                    originalButton = listContainer.querySelector(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
                }

                // Check in the featured carousel if not found
                if (!originalButton && featuredCarousel) {
                    originalButton = featuredCarousel.querySelector(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
                }

                if (originalButton) {
                    updateHeartIcon(originalButton, true);
                } else {
                    console.warn(`Could not find original like button for item ${currentModalItemType} ${currentModalItemId}`);
                }

                folderModal.hide();

            } catch (error) {
                console.error(`Error saving favorite via modal (${url}):`, error);
                const errorMessage = `Error: ${error.message}`;
                console.log('Showing error message:', errorMessage);
                showFolderModalError(errorMessage);
                alert(errorMessage); // Also show an alert for better visibility
            }
        }

        function showFolderModalError(message) {
            console.log('showFolderModalError called with:', message);
            if (!errorMsgElement) {
                console.error('Error message element not found in the DOM');
                return;
            }
            errorMsgElement.textContent = message;
            errorMsgElement.style.display = message ? 'block' : 'none';
            console.log('Error message element updated:', {
                text: errorMsgElement.textContent,
                display: errorMsgElement.style.display
            });
        }
    }
}
// --- End Favorite Button & Folder Modal Logic ---


// --- Modal Rating Logic ---
function handleRatingModal() {
    const ratingModalElement = document.getElementById('ratingModal');
    if (!ratingModalElement) {
        console.error("Rating modal element not found.");
        return;
    }

    let ratingModal = bootstrap.Modal.getInstance(ratingModalElement);
    if (!ratingModal) {
        try {
            ratingModal = new bootstrap.Modal(ratingModalElement);
        } catch (e) {
            console.error("Failed to initialize Bootstrap Modal:", e);
            return;
        }
    }

    const modalTitle = ratingModalElement.querySelector('#ratingModalLabel');
    const modalAssistantName = ratingModalElement.querySelector('#modalAssistantName');
    const modalStarsContainer = ratingModalElement.querySelector('.modal-stars');
    const modalSubmitBtn = ratingModalElement.querySelector('#submitRatingBtn');
    const modalErrorMsg = ratingModalElement.querySelector('#modalErrorMsg');
    let currentAssistantId = null;
    let selectedRating = 0;

    // 1. Populate modal when triggered
    ratingModalElement.addEventListener('show.bs.modal', function (event) {
        const button = event.relatedTarget;
        if (!button || !button.classList.contains('rate-assistant-btn')) {
             // Clear state if triggered by something else
             currentAssistantId = null;
             ratingModalElement.dataset.itemType = ''; // Clear type marker
             return;
        }

        // Populate for Assistant
        currentItemType = 'assistant'; // Set type
        currentAssistantId = button.getAttribute('data-assistant-id');
        const assistantName = button.getAttribute('data-assistant-name');

        if (modalTitle) modalTitle.textContent = `Rate ${assistantName || 'Assistant'}`;
        if (modalAssistantName) modalAssistantName.textContent = assistantName || 'this assistant';
        ratingModalElement.dataset.itemId = currentAssistantId; // Store ID
        ratingModalElement.dataset.itemType = currentItemType; // Store type

        // Reset modal state
        selectedRating = 0;
        if (modalErrorMsg) {
            modalErrorMsg.textContent = '';
            modalErrorMsg.style.display = 'none';
        }
        if (modalSubmitBtn) modalSubmitBtn.disabled = true;

        // Reset stars visual state
        if (modalStarsContainer) {
            const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
            stars.forEach(star => {
                star.classList.remove('selected', 'text-warning');
                star.classList.add('text-secondary');
                const icon = star.querySelector('i');
                if (icon) {
                    icon.classList.remove('bi-star-fill');
                    icon.classList.add('bi-star');
                }
            });
        }
    });

    // 2. Handle star selection
    if (modalStarsContainer) {
        modalStarsContainer.addEventListener('click', function(event) {
            const starButton = event.target.closest('.modal-star-btn');
            if (!starButton) return;

            selectedRating = parseInt(starButton.dataset.ratingValue);
            if (modalSubmitBtn) modalSubmitBtn.disabled = false;
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';

            const stars = modalStarsContainer.querySelectorAll('.modal-star-btn');
            stars.forEach(star => {
                const starValue = parseInt(star.dataset.ratingValue);
                const icon = star.querySelector('i');
                if (icon) {
                    if (starValue <= selectedRating) {
                        star.classList.add('selected', 'text-warning');
                        star.classList.remove('text-secondary');
                        icon.classList.remove('bi-star');
                        icon.classList.add('bi-star-fill');
                    } else {
                        star.classList.remove('selected', 'text-warning');
                        star.classList.add('text-secondary');
                        icon.classList.remove('bi-star-fill');
                        icon.classList.add('bi-star');
                    }
                }
            });
        });
    } else {
         console.error("Modal stars container not found.");
    }


    // 3. Handle rating submission
    if (modalSubmitBtn) {
        modalSubmitBtn.addEventListener('click', async function() {
            const type = ratingModalElement.dataset.itemType;
            const id = ratingModalElement.dataset.itemId; // Use generic itemId

            // Only proceed if the modal was opened for an assistant
            if (type !== 'assistant' || selectedRating === 0 || !id) {
                 if (modalErrorMsg && type === 'assistant') {
                    modalErrorMsg.textContent = 'Please select a rating (1-5 stars).';
                    modalErrorMsg.style.display = 'block';
                 } else if (type !== 'assistant') {
                     console.log("Submit clicked, but modal not configured for assistant rating.");
                 }
                return;
            }

            const csrfTokenLocal = getCsrfToken();
            const url = `/directory/rate-assistant/${id}/`;

            modalSubmitBtn.disabled = true;
            modalSubmitBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Saving...';
            if (modalErrorMsg) modalErrorMsg.style.display = 'none';

            try {
                const bodyParams = new URLSearchParams();
                bodyParams.append('assistant_id', id); // Send correct param name
                bodyParams.append('rating', selectedRating);

                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfTokenLocal,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: bodyParams
                });

                const data = await response.json();

                if (!response.ok || data.status !== 'success') {
                    throw new Error(data.message || `HTTP error ${response.status}`);
                }

                ratingModal.hide();

                const starsHtml = data.rendered_stars_html;
                const displayContainer = document.getElementById(`rating-display-${id}`); // Use specific ID

                let updateSuccess = false;
                if (displayContainer && starsHtml) {
                    displayContainer.outerHTML = starsHtml;
                    updateSuccess = true;
                } else {
                     console.error("[Rating] Could not find container or received empty stars HTML for assistant.");
                }

                if (updateSuccess) {
                    const rateButton = document.querySelector(`.rate-assistant-btn[data-assistant-id="${id}"]`);
                    const msgSpan = document.getElementById(`rating-msg-${id}`); // Use specific ID
                    if (rateButton) {
                         rateButton.disabled = true;
                         rateButton.innerHTML = '<i class="bi bi-check-lg"></i> Rated';
                    }
                    if (msgSpan) {
                         msgSpan.textContent = 'Thanks!';
                         msgSpan.style.display = 'inline';
                    }
                } else {
                    const msgSpan = document.getElementById(`rating-msg-${id}`); // Use specific ID
                     if (msgSpan) {
                         msgSpan.textContent = 'Rated (refresh?)';
                         msgSpan.classList.remove('text-success');
                         msgSpan.classList.add('text-warning');
                         msgSpan.style.display = 'inline';
                     }
                }

            } catch (error) {
                console.error('Error submitting rating via modal:', error);
                 if (modalErrorMsg) {
                    modalErrorMsg.textContent = `Error: ${error.message}`;
                    modalErrorMsg.style.display = 'block';
                 }
            } finally {
                modalSubmitBtn.disabled = false;
                modalSubmitBtn.innerHTML = 'Submit Rating';
                ratingModalElement.dataset.itemType = ''; // Clear type
                ratingModalElement.dataset.itemId = ''; // Clear ID
                currentAssistantId = null; // Clear tracked ID
            }
        });
    } else {
         console.error("Modal submit button not found.");
    }
}
// --- End Modal Rating Logic ---

// Initialize handlers when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM loaded, initializing handlers.");
    handleFavoriteButtons(); // Initialize favorite button handler
    handleRatingModal();   // Initialize modal rating handler

    // Initialize featured carousel like buttons with enhanced debugging
    console.log('[DEBUG] Looking for like buttons in featured carousel items');
    const featuredCarouselContainer = document.querySelector('.featured-carousel-container');
    console.log('[DEBUG] Featured carousel container:', featuredCarouselContainer);

    const featuredCarouselItems = document.querySelectorAll('.featured-carousel-items .like-button');
    console.log(`[DEBUG] Found ${featuredCarouselItems.length} like buttons in featured carousel`);

    // Debug each like button found
    featuredCarouselItems.forEach((button, index) => {
        console.log(`[DEBUG] Featured carousel like button #${index + 1}:`, {
            element: button,
            itemId: button.getAttribute('data-item-id'),
            itemType: button.getAttribute('data-item-type'),
            hasCorrectAttributes: Boolean(button.getAttribute('data-item-id') && button.getAttribute('data-item-type')),
            classes: button.className
        });
    });

    // Also check for carousel items to ensure they're being rendered
    const carouselItems = document.querySelectorAll('.featured-carousel-items > *');
    console.log(`[DEBUG] Found ${carouselItems.length} items in featured carousel`);

    featuredCarouselItems.forEach(button => {
        button.addEventListener('click', async function(event) {
            console.log('[DEBUG] Featured carousel like button clicked');
            event.preventDefault();
            event.stopPropagation();

            const itemId = this.getAttribute('data-item-id');
            const itemType = this.getAttribute('data-item-type');
            console.log(`[DEBUG] Featured carousel like button clicked for ${itemType} with ID ${itemId}`);

            // Use the same handler as regular like buttons
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                console.error("[ERROR] CSRF token not found!");
                alert("Action failed. Please refresh.");
                return;
            }
            console.log('[DEBUG] CSRF token found:', csrfToken.substring(0, 5) + '...');

            currentModalItemId = itemId;
            currentModalItemType = itemType;
            console.log('Set current modal item from featured carousel:', { currentModalItemId, currentModalItemType });

            try {
                const url = "{% url 'directory:toggle_saved_item' %}";
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: new URLSearchParams({ 'item_id': itemId, 'item_type': itemType })
                });

                const data = await response.json();

                if (!response.ok) throw new Error(data.message || `HTTP error ${response.status}`);

                if (data.status === 'success' && data.action === 'unfavorited') {
                    // Item was unfavorited successfully
                    updateHeartIcon(this, false);
                } else if (data.status === 'options') {
                    // Item is not saved, show folder options modal
                    populateAndShowFolderModal(data);
                } else if (data.status === 'success' && data.action === 'favorited') {
                    // Handle case where toggle immediately favorites (no modal needed)
                    updateHeartIcon(this, true);
                }
            } catch (error) {
                console.error('Error handling featured carousel like button click:', error);
                alert(`An error occurred: ${error.message}`);
            }
        });
    });
});
</script>

{# Removed Swiper.js JS #}
{# <script src="https://cdn.jsdelivr.net/npm/swiper@11/swiper-bundle.min.js"></script> #}

{# Removed Swiper Initialization Script #}
{# {{ directory_settings_dict|json_script:"directory-settings-data" }} #}
{# Removed script tag #}

{# Removed duplicate Folder Options Modal #}

<!-- Dark mode script for assistant list -->
<script src="{% static 'js/assistant-list-dark-mode.js' %}"></script>
<script src="{% static 'js/favorites-functionality.js' %}"></script>

{% endblock %}

</final_file_content>

IMPORTANT: For any future changes to this file, use the final_file_content shown above as your reference. This content reflects the current state of the file, including any auto-formatting (e.g., if you used single quotes but the formatter converted them to double quotes). Always base your SEARCH/REPLACE operations on this final version to ensure accuracy.


<environment_details>
# VSCode Visible Files
templates/directory/assistant_list.html

# VSCode Open Tabs
directory/apps.py
content/apps.py
company_assistant/__init__.py
templates/accounts/password_reset.html
templates/accounts/password_reset_done.html
templates/accounts/password_reset_confirm.html
templates/accounts/password_reset_complete.html
templates/accounts/password_change.html
templates/accounts/password_change_done.html
accounts/tests/test_models.py
accounts/tests/test_views.py
accounts/tests/test_forms.py
accounts/tests/test_signals.py
accounts/tests/__init__.py
accounts/management/commands/README.md
accounts/management/commands/toggle_debug.py
accounts/management/commands/test_setup.py
accounts/templatetags/__init__.py
accounts/tests/test_templatetags.py
content/urls.py
README.md
.gitignore
accounts/apps.py
content/models.py
.env.example
content/views.py
content/admin.py
templates/contact.html
templates/demo.html
templates/features.html
templates/security.html
templates/careers.html
templates/cookies.html
templates/accounts/company_create.html
templates/accounts/user_settings.html
templates/accounts/email/team_invitation.html
templates/accounts/email/team_invitation.txt
accounts/management/commands/manage_invitations.py
accounts/tests/test_commands.py
templates/accounts/password_reset_subject.txt
templates/accounts/password_reset_email.html
templates/accounts/login.html
templates/accounts/register.html
templates/home.html
templates/errors/404.html
templates/errors/403.html
templates/errors/500.html
accounts/signals.py
assistants/apps.py
templates/search.html
test_openai.py
templates/assistants/assistant_train.html
templates/assistants/assistant_usage.html
assistants/urls.py
templates/assistants/assistant_history.html
templates/base/pagination.html
templates/pagination.html
templates/directory/assistant_directory_list.html
directory/management/commands/sync_listings.py
directory/templatetags/directory_tags.py
directory/templatetags/__init__.py
templates/directory/partials/render_stars_partial.html
directory/templatetags/rating_tags.py
accounts/utils.py
accounts/admin.py
accounts/migrations/0005_seed_initial_roles.py
accounts/migrations/0006_combined_rbac_setup.py
assistants/migrations/0003_assistantaccesstoken_assistantfolder_and_more.py
accounts/urls.py
company_assistant/views.py
company_assistant/urls.py
templates/accounts/company_team.html
accounts/templatetags/account_tags.py
templates/accounts/tags/activity_item.html
templates/accounts/company_switch.html
../cs/grweb4.py
assistants/signals.py
../../AppData/Local/Temp/temp_image_1743375134464.png
templates/assistants/partials/chat_response.html
assistants/llm_utils.py
templates/assistants/assistant_wizard_form.html
accounts/forms.py
templates/accounts/company_settings.html
templates/directory/company_list.html
templates/directory/partials/favorite_company_item.html
templates/directory/my_favorites.html
templates/directory/partials/favorite_assistant_item.html
templates/accounts/public_company_detail.html
templates/assistants/assistant_detail.html
templates/assistants/assistant_chat.html
templates/assistants/assistant_analytics.html
templates/accounts/company_detail.html
assistants/views.py
templates/base/layout.html
templates/assistants/assistant_list.html
templates/assistants/base.html
../../AppData/Local/Temp/temp_image_1743515691236.png
static/css/style.css
templates/directory/partials/assistant_card.html
assistants/migrations/0009_add_featured_settings.py
assistants/forms.py
assistants/admin.py
directory/admin.py
directory/forms.py
templates/directory/directory_settings.html
directory/urls.py
templates/assistants/assistant_form.html
accounts/views.py
directory/views.py
assistants/models.py
templates/accounts/dashboard.html
directory/models.py
templates/directory/assistant_list.html
accounts/templatetags/permission_tags.py
accounts/models.py
requirements.txt
company_assistant/settings.py
.env
manage.py
company_assistant/wsgi.py
company_assistant/asgi.py
templates/accounts/tags/company_member_card.html
accounts/management/commands/__init__.py
accounts/management/__init__.py
../../AppData/Local/Temp/temp_image_1742878610084.png
../../AppData/Local/Temp/temp_image_1742836842125.png

# Current Time
4/3/2025, 6:28:26 AM (Africa/Kampala, UTC+3:00)

# Current Mode
ACT MODE
</environment_details>
