from django.shortcuts import redirect, get_object_or_404
from django.urls import reverse_lazy
from django.views.generic import TemplateView, ListView, View
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from django.contrib import messages
from django.contrib.auth import get_user_model
import sys
import django
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from django.db import connection

from game.models import Company, GameSession, LeaderboardEntry

# Decorator for checking superuser status
superuser_required = method_decorator(user_passes_test(lambda u: u.is_superuser), name='dispatch')

@superuser_required
class DashboardView(TemplateView):
    """
    Displays the main superadmin dashboard with system information and key metrics.
    """
    template_name = 'superadmin/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Game Superadmin Dashboard")

        # System information
        context['django_version'] = django.get_version()
        context['python_version'] = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"

        # Database information
        db_engine = connection.vendor
        if db_engine == 'sqlite':
            db_name = 'SQLite'
        elif db_engine == 'postgresql':
            db_name = 'PostgreSQL'
        elif db_engine == 'mysql':
            db_name = 'MySQL'
        else:
            db_name = db_engine.capitalize()
        context['database_engine'] = db_name

        # Total counts
        User = get_user_model()
        
        context['total_companies'] = Company.objects.count()
        context['total_users'] = User.objects.count()
        context['total_game_sessions'] = GameSession.objects.count()
        context['total_leaderboard_entries'] = LeaderboardEntry.objects.count()

        return context

@superuser_required
class CompanyListView(ListView):
    """
    Displays a list of all companies with filtering options.
    """
    model = Company
    template_name = 'superadmin/company_list.html'
    context_object_name = 'companies'
    paginate_by = 25 # Optional: Add pagination

    def get_queryset(self):
        queryset = super().get_queryset().select_related('owner').order_by('-created_at')

        # Filtering & Searching
        status = self.request.GET.get('status')
        has_sessions = self.request.GET.get('has_sessions')
        query = self.request.GET.get('q') # Get search query

        if query:
            # Search by company name OR owner username
            queryset = queryset.filter(
                Q(name__icontains=query) | Q(owner__username__icontains=query)
            )

        if status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'active':
             queryset = queryset.filter(is_active=True)

        if has_sessions == 'true':
            # Get companies with game sessions
            companies_with_sessions = GameSession.objects.values_list('company_id', flat=True).distinct()
            queryset = queryset.filter(id__in=companies_with_sessions)
        elif has_sessions == 'false':
            # Get companies without game sessions
            companies_with_sessions = GameSession.objects.values_list('company_id', flat=True).distinct()
            queryset = queryset.exclude(id__in=companies_with_sessions)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Manage Companies")
        # Pass filter values back to template for display/form persistence
        context['filter_status'] = self.request.GET.get('status', '')
        context['filter_has_sessions'] = self.request.GET.get('has_sessions', '')
        context['filter_q'] = self.request.GET.get('q', '') # Pass search query back
        return context

@superuser_required
class CompanyActivateToggleView(View):
    """
    Toggles the is_active status of a Company via POST request.
    """
    http_method_names = ['post'] # Only allow POST

    def post(self, request, **kwargs):
        company = get_object_or_404(Company, pk=kwargs.get('pk'))
        company.is_active = not company.is_active
        company.save(update_fields=['is_active'])

        if company.is_active:
            messages.success(request, _("Company '{name}' activated successfully.").format(name=company.name))
        else:
            messages.warning(request, _("Company '{name}' deactivated.").format(name=company.name))

        # Redirect back to the company list, potentially preserving filters
        redirect_url = reverse_lazy('superadmin:company_list')
        query_params = request.GET.urlencode() # Get original query params
        if query_params:
            redirect_url += f'?{query_params}'

        return redirect(redirect_url)

@superuser_required
class CompanyImpersonateView(View):
    """
    Initiates impersonation of a company owner (user) via POST request.
    Redirects to the django-impersonate start URL.
    """
    http_method_names = ['post']

    def post(self, request, **kwargs):
        user_pk_to_impersonate = kwargs.get('user_pk')

        # Get the user to impersonate
        User = get_user_model()
        try:
            user_to_impersonate = User.objects.get(pk=user_pk_to_impersonate)
        except User.DoesNotExist:
            messages.error(request, _("User not found."))
            return redirect('superadmin:company_list')

        # Redirect to django-impersonate
        impersonate_url = reverse_lazy('impersonate-start', args=[user_pk_to_impersonate])
        return redirect(impersonate_url)
