# UI Components

This document provides a detailed explanation of the UI components in the Corporate Prompt Master game, their interactions, and how they create an engaging user experience.

## Main Layout

The main layout is defined in `context_aware_index.html`:

```html
<div class="app-container">
    <!-- Left Sidebar -->
    <div class="sidebar">
        <!-- Sidebar content -->
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Chat interface -->
    </div>

    <!-- Right Sidebar -->
    <div class="right-sidebar">
        <!-- Organization chart and role progression -->
    </div>
</div>
```

The layout uses CSS Flexbox for the main container:

```css
.app-container {
    display: flex;
    min-height: 100vh;
    max-width: 1700px;
    margin: 0 auto;
    background-color: #fff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}
```

## Left Sidebar Components

The left sidebar contains:

1. **Character Information**:
   ```html
   <div class="character-info">
       <div class="character-header">Current Contact:</div>
       <div id="character-avatar" class="avatar">👩‍💼</div>
       <div id="character-name" class="character-name">Loading...</div>
       <div id="character-title" class="character-title">Loading...</div>
   </div>
   ```

2. **Game Statistics**:
   ```html
   <div class="game-stats">
       <div class="stat">
           <span class="stat-label">Current Role:</span>
           <span id="current-role" class="stat-value">Applicant</span>
       </div>
       <div class="stat">
           <span class="stat-label">Performance Score:</span>
           <span id="performance-score" class="stat-value">0</span>
       </div>
       <div class="stat">
           <span class="stat-label">Challenges Completed:</span>
           <span id="challenges-completed" class="stat-value">0/1</span>
       </div>
   </div>
   ```

3. **Context Information**:
   ```html
   <div class="context-info">
       <div>
           <span class="context-label">Current Manager:</span>
           <span id="current-manager">HR</span>
       </div>
       <div>
           <span class="context-label">Current Task:</span>
           <span id="current-task">Cover Letter</span>
       </div>
   </div>
   ```

The sidebar styling includes:

```css
.sidebar {
    width: 300px;
    background-color: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    overflow-y: hidden;
}
```

## Chat Interface

The central chat interface includes:

1. **Message Container**:
   ```html
   <div id="messages" class="messages">
       <!-- Messages will be added here dynamically -->
   </div>
   ```

2. **Input Area**:
   ```html
   <div class="input-area">
       <textarea id="prompt-input" placeholder="Type your response here..."></textarea>
       <div class="button-container">
           <button id="preview-button" class="preview-button">Preview</button>
           <button id="submit-button" class="submit-button">Submit</button>
       </div>
   </div>
   ```

Messages are dynamically added to the container using JavaScript:

```javascript
function displayMessage(sender, content, isUser = false, isSystem = false) {
    const messagesContainer = document.getElementById('messages');
    const messageElement = document.createElement('div');
    messageElement.className = `message ${isUser ? 'user-message' : ''} ${isSystem ? 'system-message' : ''}`;
    
    // Create avatar element
    const avatarElement = document.createElement('div');
    avatarElement.className = 'message-avatar';
    avatarElement.textContent = isUser ? '👤' : (gameState.characters[sender] ? gameState.characters[sender].avatar : '🤖');
    avatarElement.style.backgroundColor = getCharacterColor(sender);
    
    // Create message content
    const contentElement = document.createElement('div');
    contentElement.className = 'message-content';
    
    // Create sender name
    const nameElement = document.createElement('div');
    nameElement.className = 'message-sender';
    nameElement.textContent = isUser ? 'You' : (gameState.characters[sender] ? gameState.characters[sender].name : sender);
    
    // Create message text
    const textElement = document.createElement('div');
    textElement.className = 'message-text';
    textElement.innerHTML = content;
    
    // Assemble message
    contentElement.appendChild(nameElement);
    contentElement.appendChild(textElement);
    messageElement.appendChild(avatarElement);
    messageElement.appendChild(contentElement);
    
    // Add to container
    messagesContainer.appendChild(messageElement);
    
    // Scroll to bottom
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}
```

## Right Sidebar Components

The right sidebar contains:

1. **Organization Chart**:
   ```html
   <div class="org-chart-container">
       <h3>Company Hierarchy:</h3>
       <div id="org-chart" class="org-chart">
           <!-- Organization chart will be populated by JavaScript -->
       </div>
   </div>
   ```

2. **Role Progression**:
   ```html
   <div class="role-progression-container">
       <h3>Your Career Path:</h3>
       <div id="role-progression" class="role-progression">
           <!-- Role progression will be populated by JavaScript -->
       </div>
   </div>
   ```

The organization chart is generated dynamically:

```javascript
function updateOrgChart(orgChartHtml) {
    const orgChartElement = document.getElementById('org-chart');
    if (orgChartElement && orgChartHtml) {
        orgChartElement.innerHTML = orgChartHtml;
    }
}
```

## Modal Dialogs

The game includes several modal dialogs for different purposes:

1. **Preview Modal**:
   ```html
   <div id="preview-modal" class="modal">
       <div class="modal-content">
           <div class="modal-header">
               <h2>Response Preview</h2>
               <span class="close">&times;</span>
           </div>
           <div class="modal-body">
               <div id="preview-content"></div>
           </div>
           <div class="modal-footer">
               <button id="edit-button">Edit</button>
               <button id="submit-preview-button">Submit</button>
           </div>
       </div>
   </div>
   ```

2. **Celebration Modal**:
   ```html
   <div id="celebration-modal" class="modal celebration-modal">
       <div class="modal-content">
           <div class="modal-header">
               <h2>Congratulations!</h2>
               <span class="close">&times;</span>
           </div>
           <div class="modal-body">
               <div id="celebration-content"></div>
               <div class="celebration-animation">
                   <div class="confetti"></div>
               </div>
           </div>
           <div class="modal-footer">
               <button id="continue-button">Continue</button>
           </div>
       </div>
   </div>
   ```

Modal functionality is implemented in JavaScript:

```javascript
function showPreviewModal(content) {
    const modal = document.getElementById('preview-modal');
    const previewContent = document.getElementById('preview-content');
    previewContent.innerHTML = content;
    modal.style.display = 'block';
    document.body.classList.add('modal-open');
}

function closePreviewModal() {
    const modal = document.getElementById('preview-modal');
    modal.style.display = 'none';
    document.body.classList.remove('modal-open');
}
```

## Dark Mode Implementation

The game supports a dark mode toggle:

```html
<div class="theme-toggle">
    <label class="switch">
        <input type="checkbox" id="dark-mode-toggle">
        <span class="slider round"></span>
    </label>
    <span class="theme-label">Dark Mode</span>
</div>
```

Dark mode is implemented with CSS variables and a class-based approach:

```css
:root {
    --primary-bg-color: #ffffff;
    --secondary-bg-color: #f8f9fa;
    --text-color: #333333;
    --border-color: #e0e0e0;
    /* Additional variables */
}

.dark-mode {
    --primary-bg-color: #1e1e1e;
    --secondary-bg-color: #252525;
    --text-color: #e0e0e0;
    --border-color: #444444;
    /* Additional variables */
}
```

The toggle functionality is implemented in JavaScript:

```javascript
function toggleDarkMode() {
    const htmlElement = document.documentElement;
    const bodyElement = document.body;
    
    htmlElement.classList.toggle('dark-mode');
    bodyElement.classList.toggle('dark-mode');
    
    // Update localStorage preference
    const isDarkMode = htmlElement.classList.contains('dark-mode');
    localStorage.setItem('darkMode', isDarkMode ? 'enabled' : 'disabled');
    
    // Update charts and other components that need special handling
    updateChartsForDarkMode(isDarkMode);
}
```

## Responsive Design

The UI is responsive and adapts to different screen sizes:

```css
/* Tablet styles */
@media (max-width: 1024px) {
    .app-container {
        flex-direction: column;
    }
    
    .sidebar {
        width: 100%;
        max-height: 200px;
        overflow-y: auto;
    }
    
    .right-sidebar {
        display: none;
    }
    
    /* Additional tablet styles */
}

/* Mobile styles */
@media (max-width: 768px) {
    .sidebar {
        max-height: 150px;
    }
    
    .input-area {
        flex-direction: column;
    }
    
    /* Additional mobile styles */
}
```

Mobile-specific components are included:

```html
<div class="mobile-right-sidebar-content hidden">
    <!-- This content will only be shown on mobile devices -->
    <div id="mobile-org-chart-container" class="org-chart-container">
        <h3>Company Hierarchy:</h3>
        <div id="mobile-org-chart" class="org-chart">
            <!-- Mobile organization chart will be populated by JavaScript -->
        </div>
    </div>
</div>
```

## Component Interactions

The UI components interact through JavaScript event handlers:

```javascript
// Initialize event listeners
function initializeEventListeners() {
    // Submit button
    document.getElementById('submit-button').addEventListener('click', submitPrompt);
    
    // Preview button
    document.getElementById('preview-button').addEventListener('click', previewPrompt);
    
    // Edit button in preview modal
    document.getElementById('edit-button').addEventListener('click', closePreviewModal);
    
    // Submit button in preview modal
    document.getElementById('submit-preview-button').addEventListener('click', submitPreviewedPrompt);
    
    // Dark mode toggle
    document.getElementById('dark-mode-toggle').addEventListener('change', toggleDarkMode);
    
    // Additional event listeners
}
```

## CSS Architecture

The CSS is organized into multiple files for better maintainability:

1. **Core Styles** (`context_aware_styles.css`):
   - Main layout
   - Typography
   - Color schemes
   - Common components

2. **Component-Specific Styles**:
   - `org-chart-styles.css` - Organization chart styling
   - `role_progression_updated.css` - Role progression styling
   - `manager_feedback_styles.css` - Manager feedback styling
   - `celebration.css` - Celebration effects

3. **Feature-Specific Styles**:
   - `dark-light-mode.css` - Dark mode implementation
   - `responsive-preview.css` - Responsive design
   - `chart-hover-zoom.css` - Chart zoom functionality

This modular approach makes the codebase easier to maintain and extend with new features.
