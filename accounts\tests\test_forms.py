from django.test import TestCase
from django.contrib.auth.models import User
from ..forms import (
    UserRegistrationForm, CompanyCreationForm, TeamInvitationForm,
    UserSettingsForm, CompanySettingsForm
)
from ..models import Company, CompanyInvitation

class UserRegistrationFormTests(TestCase):
    def setUp(self):
        self.existing_user = User.objects.create_user(
            username='existing',
            email='<EMAIL>',
            password='existing123'
        )
    
    def test_valid_registration(self):
        """Test registration with valid data"""
        form_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password1': 'securepass123',
            'password2': 'securepass123'
        }
        form = UserRegistrationForm(data=form_data)
        self.assertTrue(form.is_valid())
    
    def test_existing_email(self):
        """Test registration with existing email"""
        form_data = {
            'username': 'newuser',
            'email': '<EMAIL>',  # Already exists
            'password1': 'securepass123',
            'password2': 'securepass123'
        }
        form = UserRegistrationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
    
    def test_password_match(self):
        """Test password confirmation matching"""
        form_data = {
            'username': 'testuser',
            'email': '<EMAIL>',
            'password1': 'securepass123',
            'password2': 'differentpass123'  # Doesn't match
        }
        form = UserRegistrationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('password2', form.errors)

class CompanyCreationFormTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_valid_company_creation(self):
        """Test company creation with valid data"""
        form_data = {
            'name': 'Test Company',
            'mission': 'To test things',
            'founded': 2025,
            'website': 'https://example.com',
            'list_in_directory': True
        }
        form = CompanyCreationForm(data=form_data)
        self.assertTrue(form.is_valid())
        
        # Test saving the form
        company = form.save(user=self.user)
        self.assertEqual(company.name, 'Test Company')
        self.assertEqual(company.owner, self.user)
        self.assertEqual(company.info['mission'], 'To test things')
    
    def test_invalid_year(self):
        """Test company creation with invalid founding year"""
        form_data = {
            'name': 'Test Company',
            'founded': 1700,  # Too early
        }
        form = CompanyCreationForm(data=form_data)
        self.assertFalse(form.is_valid())
        self.assertIn('founded', form.errors)
    
    def test_optional_fields(self):
        """Test company creation with minimal data"""
        form_data = {
            'name': 'Test Company',
        }
        form = CompanyCreationForm(data=form_data)
        self.assertTrue(form.is_valid())

class TeamInvitationFormTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )
        self.member = User.objects.create_user(
            username='member',
            email='<EMAIL>',
            password='member123'
        )
        self.company.members.add(self.member)
    
    def test_valid_invitation(self):
        """Test creating a valid invitation"""
        form_data = {
            'email': '<EMAIL>'
        }
        form = TeamInvitationForm(data=form_data)
        form.instance.company = self.company
        self.assertTrue(form.is_valid())
    
    def test_existing_member(self):
        """Test inviting an existing member"""
        form_data = {
            'email': '<EMAIL>'  # Already a member
        }
        form = TeamInvitationForm(data=form_data)
        form.instance.company = self.company
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)
    
    def test_duplicate_invitation(self):
        """Test creating duplicate invitation"""
        # Create initial invitation
        CompanyInvitation.objects.create(
            company=self.company,
            email='<EMAIL>'
        )
        
        # Try to create another invitation for same email
        form_data = {
            'email': '<EMAIL>'
        }
        form = TeamInvitationForm(data=form_data)
        form.instance.company = self.company
        self.assertFalse(form.is_valid())
        self.assertIn('email', form.errors)

class CompanySettingsFormTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user,
            info={
                'mission': 'Original mission',
                'founded': 2020,
                'website': 'https://original.com'
            }
        )
    
    def test_valid_settings_update(self):
        """Test updating company settings"""
        form_data = {
            'name': 'Updated Company',
            'mission': 'Updated mission',
            'founded': 2025,
            'website': 'https://updated.com'
        }
        form = CompanySettingsForm(data=form_data, instance=self.company)
        self.assertTrue(form.is_valid())
        
        # Test saving the form
        company = form.save()
        self.assertEqual(company.name, 'Updated Company')
        self.assertEqual(company.info['mission'], 'Updated mission')
        self.assertEqual(company.info['founded'], 2025)
    
    def test_partial_update(self):
        """Test updating only some fields"""
        form_data = {
            'name': 'Updated Company',
            'mission': 'Updated mission'
            # Omit other fields
        }
        form = CompanySettingsForm(data=form_data, instance=self.company)
        self.assertTrue(form.is_valid())
        
        company = form.save()
        self.assertEqual(company.name, 'Updated Company')
        self.assertEqual(company.info['mission'], 'Updated mission')
        self.assertEqual(company.info['founded'], 2020)  # Unchanged
