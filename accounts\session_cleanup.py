"""
Session cleanup middleware for impersonation.
"""
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.signals import user_logged_out
from django.dispatch import receiver

class SessionCleanupMiddleware(MiddlewareMixin):
    """
    Middleware to clean up impersonation session data.
    This middleware should be placed after the django-impersonate middleware.
    """

    def process_request(self, request):
        """Clean up impersonation session data for anonymous users."""
        # If user is not authenticated, clean up impersonation session data
        if not request.user.is_authenticated:
            # Check if there's impersonation data in the session
            if 'impersonate_id' in request.session:
                del request.session['impersonate_id']

            # Check if there's a real_user attribute on the request
            if hasattr(request, 'real_user'):
                delattr(request, 'real_user')

            # Check if there's an is_impersonate flag on the request
            if hasattr(request, 'is_impersonate'):
                request.is_impersonate = False

        return None

# Signal handler to clean up impersonation session data on logout
@receiver(user_logged_out)
def clean_impersonation_on_logout(sender, request, user, **kwargs):
    """Clean up impersonation session data when a user logs out."""
    # Clean up impersonation session data
    if 'impersonate_id' in request.session:
        del request.session['impersonate_id']

    # Clean up request attributes
    if hasattr(request, 'real_user'):
        delattr(request, 'real_user')

    if hasattr(request, 'is_impersonate'):
        request.is_impersonate = False
