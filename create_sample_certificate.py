import os
import sys
import django
import json

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from game.models import GameSession
from corporate.models import Certificate, CorporateUser, Company

def create_sample_certificate():
    # Get or create a user
    try:
        user = User.objects.first()
        if not user:
            print("No users found. Creating a sample user...")
            user = User.objects.create_user(
                username="sample_user",
                email="<EMAIL>",
                password="samplepassword",
                first_name="Sample",
                last_name="User"
            )
            print(f"Created user: {user.username}")
            
            # Create a company if needed
            company = Company.objects.first()
            if not company:
                company = Company.objects.create(
                    name="Sample Company",
                    is_active=True
                )
                print(f"Created company: {company.name}")
            
            # Create corporate user
            CorporateUser.objects.create(
                user=user,
                company=company,
                is_company_admin=True,
                status='active'
            )
            print(f"Created corporate user for {user.username}")
        else:
            print(f"Using existing user: {user.username}")
    except Exception as e:
        print(f"Error creating/getting user: {str(e)}")
        return
    
    # Create a game session
    try:
        game_session = GameSession.objects.create(
            user=user,
            current_role='senior_manager',
            performance_score=95,
            challenges_completed=10,
            role_challenges_completed=3,
            game_completed=True,
            completed_roles=json.dumps(['applicant', 'junior_assistant', 'assistant', 'senior_assistant', 'manager', 'senior_manager'])
        )
        print(f"Created game session for {user.username}")
    except Exception as e:
        print(f"Error creating game session: {str(e)}")
        return
    
    # Create a certificate
    try:
        certificate = Certificate.objects.create(
            user=user,
            game_session=game_session,
            title='Prompt Engineering Excellence',
            description='Has successfully completed the Corporate Prompt Master training program.',
            final_score=95,
            highest_role='senior_manager',
            completed_games=json.dumps(['Corporate Prompt Master', 'Advanced Prompt Engineering'])
        )
        print(f"Created certificate with ID: {certificate.id}")
        print(f"View certificate at: http://127.0.0.1:8000/corporate/certificates/")
        print(f"Download certificate at: http://127.0.0.1:8000/corporate/certificates/{certificate.id}/download/")
    except Exception as e:
        print(f"Error creating certificate: {str(e)}")
        return

if __name__ == "__main__":
    create_sample_certificate()
