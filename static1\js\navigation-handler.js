// Navigation Handler for Assistant Chat
document.addEventListener('DOMContentLoaded', function() {
    // Get DOM elements
    const navButtons = document.querySelectorAll('.nav-button[data-section-id]');
    const contentBlocks = document.querySelectorAll('.nav-section-content-block');
    const chatBox = document.getElementById('chat-box');
    const initialDisplayArea = document.getElementById('initial-display-area');

    // Log website data if available
    if (window.websiteData) {
        console.log('DEBUG - Navigation Handler - Website Data Keys:', Object.keys(window.websiteData));

        // Log each navigation item's data
        navButtons.forEach(btn => {
            const itemId = btn.getAttribute('data-item-id');
            const sectionId = btn.getAttribute('data-section-id');
            if (itemId) {
                const itemKey = `item_${itemId}`;
                console.log(`DEBUG - Navigation Handler - Item ${itemId} (${sectionId}) data:`,
                    window.websiteData[itemKey] || 'Not found in website_data');
            }
        });
    } else {
        console.log('DEBUG - Navigation Handler - No website data available');
    }

    // Track processing state
    let isProcessingNavClick = false;
    let lastClickedNavItem = null;
    let lastClickTimestamp = 0;
    let currentNavContext = ''; // Store current navigation context

    // Immediately display all content blocks in chat bubbles
    console.log('DEBUG - Navigation Handler - Found content blocks:', contentBlocks.length);

    // Hide initial display area if we have content blocks
    if (contentBlocks.length > 0 && initialDisplayArea) {
        initialDisplayArea.style.display = 'none';
    }

    // Get all navigation buttons to match with content blocks
    const navButtonsMap = {};
    navButtons.forEach(btn => {
        const sectionId = btn.getAttribute('data-section-id');
        const itemId = btn.getAttribute('data-item-id');
        if (sectionId) {
            navButtonsMap[sectionId] = {
                label: btn.textContent.trim(),
                itemId: itemId
            };
        }
    });

    // Process each content block
    contentBlocks.forEach(block => {
        if (block) {
            const sectionId = block.id.replace('nav-section-', '');
            const navLabel = block.getAttribute('data-nav-label') ||
                            (navButtonsMap[sectionId] ? navButtonsMap[sectionId].label : 'Section');

            console.log('DEBUG - Navigation Handler - Processing content block:', sectionId, navLabel);

            // Try to get content from websiteData if available
            let content = '';
            let useWebsiteData = false;

            if (window.websiteData) {
                // Find the matching button to get the item ID
                const matchingButton = Array.from(navButtons).find(btn => btn.getAttribute('data-section-id') === sectionId);
                if (matchingButton) {
                    const itemId = matchingButton.getAttribute('data-item-id');
                    if (itemId) {
                        const itemKey = `item_${itemId}`;
                        if (window.websiteData[itemKey]) {
                            // Found data in websiteData
                            const itemData = window.websiteData[itemKey];
                            if (typeof itemData === 'object' && itemData.content) {
                                content = itemData.content;
                            } else if (typeof itemData === 'string') {
                                content = itemData;
                            }

                            if (content) {
                                console.log(`DEBUG - Navigation Handler - Found content in websiteData for ${itemKey}`);
                                useWebsiteData = true;
                            }
                        }
                    }
                }
            }

            // If we didn't find content in websiteData, use the DOM content
            if (!useWebsiteData) {
                // Check if the content contains placeholder text
                const isPlaceholder = block.innerHTML.includes('Please click on this section') ||
                                     block.innerHTML.includes('placeholder content') ||
                                     block.innerHTML.trim() === '';

                // Only add non-placeholder content to the chat
                if (!isPlaceholder) {
                    content = block.innerHTML;
                } else {
                    console.log('DEBUG - Navigation Handler - Skipping placeholder content for:', sectionId, navLabel);
                    return; // Skip this block
                }
            }

            // If we have content to display
            if (content) {
                // Add the content to the chat with a header
                const messageContent = `<div><strong>${navLabel}</strong></div><hr>${content}`;

                // Log the content being added
                console.log('DEBUG - Navigation Handler - Adding content to chat:', {
                    sectionId: sectionId,
                    navLabel: navLabel,
                    contentSource: useWebsiteData ? 'websiteData' : 'DOM',
                    contentPreview: content.substring(0, 100) + '...'
                });

                addMessage(messageContent, 'assistant', true);
            }
        }
    });

    // Function to show content by section ID
    function showContentById(sectionId) {
        // DEBUG: Log the section ID being clicked
        console.log('DEBUG - Navigation Click - Section ID:', sectionId);

        // Prevent multiple clicks while processing
        if (isProcessingNavClick) {
            console.log('DEBUG - Navigation Click - Already processing, ignoring click');
            return;
        }

        // Set processing flag
        isProcessingNavClick = true;

        // Find the nav button for this section to get its label
        let navLabel = "";
        let itemId = null;
        navButtons.forEach(btn => {
            if (btn.getAttribute('data-section-id') === sectionId) {
                navLabel = btn.textContent.trim();
                itemId = btn.getAttribute('data-item-id');
                btn.classList.add('active');
                // DEBUG: Log the button details
                console.log('DEBUG - Navigation Button Details:', {
                    sectionId: sectionId,
                    itemId: itemId,
                    label: navLabel,
                    element: btn
                });
            } else {
                btn.classList.remove('active');
            }
        });

        // If we don't have an item ID, we can't proceed
        if (!itemId) {
            console.error('DEBUG - Navigation Click - No item ID found for section:', sectionId);
            addMessage(`<div><strong>Error</strong></div><hr><p>Could not find item ID for section: ${sectionId}</p>`, 'assistant', true);
            isProcessingNavClick = false;
            return;
        }

        // Check if this is the same item that was just clicked (within the last 1 second)
        const currentTime = Date.now();
        if (lastClickedNavItem === itemId && (currentTime - lastClickTimestamp) < 1000) {
            console.log('DEBUG - Navigation Click - Same item clicked too quickly, ignoring');
            isProcessingNavClick = false;
            return;
        }

        // Update last clicked item and timestamp
        lastClickedNavItem = itemId;
        lastClickTimestamp = currentTime;

        // Hide initial display area if visible
        if (initialDisplayArea && initialDisplayArea.style.display !== 'none') {
            initialDisplayArea.style.display = 'none';
        }

        // Log the website data status
        if (window.websiteData) {
            console.log('DEBUG - Navigation Click - Website data available:', {
                keys: Object.keys(window.websiteData),
                hasItemKey: window.websiteData[`item_${itemId}`] ? true : false,
                itemKey: `item_${itemId}`
            });
        } else {
            console.error('DEBUG - Navigation Click - No website data available');
        }

        // We'll always make an AJAX call to get the most up-to-date content
        console.log('DEBUG - Navigation Click - Always making AJAX call to get fresh content');

        // Make an AJAX call to get the content from the database
        // First, add a thinking message
        const thinkingId = `thinking-${Date.now()}`;
        const thinkingMessage = addMessage(`<div id="${thinkingId}" class="d-flex align-items-center"><span class="spinner-border spinner-border-sm me-2 text-primary" role="status" aria-hidden="true"></span> <span style="color: #333333;">Loading content from database...</span></div>`, 'assistant', true);

        // Get CSRF token
        function getCsrfToken() {
            const name = 'csrftoken';
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Get the URL from the meta tag
        const assistantId = document.querySelector('meta[name="assistant-id"]')?.content;
        const companyId = document.querySelector('meta[name="company-id"]')?.content;

        if (!assistantId || !companyId) {
            console.error('DEBUG - Navigation Click - Missing assistant-id or company-id meta tag');

            // Remove thinking message
            if (thinkingMessage) {
                chatBox.removeChild(thinkingMessage);
            }

            // Add error message
            addMessage(`<div><strong>Error</strong></div><hr><p>Could not find assistant ID or company ID.</p>`, 'assistant', true);

            // Reset processing flag
            isProcessingNavClick = false;
            return;
        }

        // Construct the URL
        const interactUrl = `/assistants/company/${companyId}/assistants/${assistantId}/interact/`;
        const csrfToken = getCsrfToken();

        // Prepare the request data
        const requestData = {
            navigation_click: true,
            section_id: sectionId,
            item_id: itemId
        };

        console.log('DEBUG - Navigation Click - Making AJAX request:', {
            url: interactUrl,
            data: requestData
        });

        // Make the AJAX request
        fetch(interactUrl, {
            method: 'POST',
            headers: {
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        })
        .then(response => {
            // Remove thinking message
            if (thinkingMessage) {
                chatBox.removeChild(thinkingMessage);
            }

            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('DEBUG - Navigation Click - AJAX response:', data);

            if (data.status === 'success' && data.content) {
                // Add the content to the chat
                addMessage(data.content, 'assistant', true);

                // Update the DOM content block with the new content
                const contentBlock = document.getElementById(`nav-section-${sectionId}`);
                if (contentBlock) {
                    contentBlock.innerHTML = data.content;
                }

                // Update the website data
                if (window.websiteData && itemId) {
                    const itemKey = `item_${itemId}`;
                    window.websiteData[itemKey] = { content: data.content };
                    console.log(`DEBUG - Navigation Click - Updated websiteData[${itemKey}]`);
                }
            } else {
                throw new Error(data.error || 'Unknown error');
            }
        })
        .catch(error => {
            console.error('DEBUG - Navigation Click - AJAX error:', error);

            // Add error message
            addMessage(`<div><strong>Error</strong></div><hr><p>Failed to load content: ${error.message}</p>`, 'assistant', true);

            // Try to use the DOM content as fallback
            const contentBlock = document.getElementById(`nav-section-${sectionId}`);
            if (contentBlock) {
                const isPlaceholder = contentBlock.innerHTML.includes('Please click on this section') ||
                                     contentBlock.innerHTML.includes('placeholder content') ||
                                     contentBlock.innerHTML.trim() === '';

                if (!isPlaceholder) {
                    // Add the content to the chat with a proper header
                    const content = `<div><strong>${navLabel}</strong></div><hr>${contentBlock.innerHTML}`;
                    addMessage(content, 'assistant', true);
                }
            }
        })
        .finally(() => {
            // Close sidebar on mobile after clicking a navigation item
            if (window.innerWidth < 768) {
                const sidebar = document.getElementById('chat-sidebar');
                const sidebarOverlay = document.getElementById('sidebar-overlay');

                if (sidebar && sidebar.classList.contains('active')) {
                    sidebar.classList.remove('active');
                    sidebar.style.left = '-350px';

                    // Update toggle text
                    const toggleTexts = document.querySelectorAll('.toggle-text');
                    toggleTexts.forEach(el => { el.textContent = 'Menu'; });

                    // Hide overlay
                    if (sidebarOverlay) {
                        sidebarOverlay.style.opacity = '0';
                        sidebarOverlay.style.pointerEvents = 'none';
                        setTimeout(() => { sidebarOverlay.style.display = 'none'; }, 300);
                    }
                }
            }

            // Reset processing flag
            isProcessingNavClick = false;
        });
    }

    // Function to add message to chat
    function addMessage(content, role, isHtml = false) {
        if (!chatBox) {
            console.error('DEBUG - Navigation Click - Chat box not found');
            return null; // Return null to indicate failure
        }

        console.log('DEBUG - Navigation Click - Adding message to chat:', {
            contentPreview: content.substring(0, 100) + '...',
            role: role,
            isHtml: isHtml,
            chatBoxId: chatBox.id
        });

        // Hide initial display area if it exists and messages are being added
        if (initialDisplayArea && initialDisplayArea.style.display !== 'none') {
            initialDisplayArea.style.display = 'none';
        }

        const messageDiv = document.createElement('div');
        messageDiv.classList.add('message', role === 'user' ? 'user-message' : 'assistant-message');

        // For navigation content, add special class
        if (role === 'assistant') {
            messageDiv.classList.add('nav-content-bubble');
        }

        const messageContentSpan = document.createElement('span');
        messageContentSpan.classList.add('message-content');

        if (isHtml) {
            messageContentSpan.innerHTML = content;
        } else {
            messageContentSpan.textContent = content;
        }

        // Get avatar URLs from meta tags or data attributes
        const assistantAvatarUrl = document.querySelector('meta[name="assistant-avatar"]')?.content || null;
        const userAvatarUrl = document.querySelector('meta[name="user-avatar"]')?.content || null;

        // Create avatar container
        const avatarContainer = document.createElement('div');
        avatarContainer.classList.add('chat-avatar-container', role === 'user' ? 'ms-2' : 'me-2');

        let avatarElement;

        // For assistant, check if we have a valid URL
        if (role === 'assistant') {
            if (assistantAvatarUrl && assistantAvatarUrl !== 'null') {
                avatarElement = document.createElement('img');
                avatarElement.src = assistantAvatarUrl;
                avatarElement.classList.add('chat-avatar');
                avatarElement.alt = "Assistant";
            } else {
                // Create a fallback icon for assistant
                avatarElement = document.createElement('div');
                avatarElement.classList.add('chat-avatar-icon');
                const iconElement = document.createElement('i');
                iconElement.classList.add('bi', 'bi-robot');
                avatarElement.appendChild(iconElement);
            }
        } else {
            // For user, check if we have a valid URL
            if (userAvatarUrl && userAvatarUrl !== 'null') {
                avatarElement = document.createElement('img');
                avatarElement.src = userAvatarUrl;
                avatarElement.classList.add('chat-avatar');
                avatarElement.alt = "User";
            } else {
                // Create a fallback icon for user
                avatarElement = document.createElement('div');
                avatarElement.classList.add('chat-avatar-icon');
                const iconElement = document.createElement('i');
                iconElement.classList.add('bi', 'bi-person-circle');
                avatarElement.appendChild(iconElement);
            }
        }
        avatarContainer.appendChild(avatarElement);

        // Apply specific styling based on role
        if (role === 'user') {
            // Add content first, then avatar for user messages
            messageDiv.appendChild(messageContentSpan);
            messageDiv.appendChild(avatarContainer);
        } else {
            // Add avatar first, then content for assistant messages
            messageDiv.appendChild(avatarContainer);
            messageDiv.appendChild(messageContentSpan);

            // For navigation content, add special styling
            if (messageDiv.classList.contains('nav-content-bubble')) {
                // Apply solid white background with blue left border
                messageContentSpan.style.background = '#ffffff';
                messageContentSpan.style.backgroundColor = '#ffffff';
                messageContentSpan.style.backgroundImage = 'none';
                messageContentSpan.style.color = '#333333';
                messageContentSpan.style.border = '1px solid rgba(0, 0, 0, 0.05)';
                messageContentSpan.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.03)';
                messageContentSpan.style.borderLeft = '3px solid #0d6efd';

                // Make sure content is properly displayed
                messageContentSpan.style.maxWidth = '100%';
                messageContentSpan.style.width = '100%';
                messageContentSpan.style.padding = '15px';

                // Add some styling for content inside the bubble
                const contentStyles = document.createElement('style');
                contentStyles.textContent = `
                    .nav-content-bubble .message-content h1,
                    .nav-content-bubble .message-content h2,
                    .nav-content-bubble .message-content h3 {
                        margin-top: 0.5rem;
                        margin-bottom: 0.5rem;
                        font-weight: 600;
                    }
                    .nav-content-bubble .message-content p {
                        margin-bottom: 0.75rem;
                    }
                    .nav-content-bubble .message-content ul,
                    .nav-content-bubble .message-content ol {
                        padding-left: 1.5rem;
                    }
                `;
                document.head.appendChild(contentStyles);
            }
        }

        chatBox.appendChild(messageDiv);

        // Ensure the message is visible by scrolling to it
        messageDiv.scrollIntoView({ behavior: 'smooth', block: 'end' });

        // Also scroll the window to ensure the message is visible
        requestAnimationFrame(() => {
            window.scrollTo({ top: document.body.scrollHeight, behavior: 'smooth' });
        });

        console.log('DEBUG - Navigation Click - Message added to chat successfully');

        // Return the message div so it can be modified by the caller
        return messageDiv;
    }

    // Attach click listeners to navigation buttons using event delegation
    // This is now handled by button-fix.js to avoid multiple event listeners
    console.log('DEBUG - Navigation Handler: Event delegation is now handled by button-fix.js');

    // Export functions for use in other scripts
    window.showNavContent = showContentById;
    window.currentNavContext = currentNavContext;
});
