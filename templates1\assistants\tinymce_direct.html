{% load static %}
<!-- Direct TinyMCE implementation -->
<script src="{% static 'tinymce/tinymce.min.js' %}"></script>
<script>
// Function to load TinyMCE from different sources
function loadTinyMCE() {
    console.log("Attempting to load TinyMCE");

    // Try loading from staticfiles using Django's static tag
    var script = document.createElement('script');
    script.src = "{% static 'tinymce/tinymce.min.js' %}";
    script.onload = function() {
        console.log("TinyMCE loaded from staticfiles");
        initTinyMCE();
    };
    script.onerror = function() {
        console.error("Failed to load TinyMCE from staticfiles, trying CDN");
        // Try loading from CDN
        var cdnScript = document.createElement('script');
        cdnScript.src = 'https://cdn.tiny.cloud/1/no-api-key/tinymce/5/tinymce.min.js';
        cdnScript.onload = function() {
            console.log("TinyMCE loaded from CDN");
            initTinyMCE();
        };
        cdnScript.onerror = function() {
            console.error("Failed to load TinyMCE from CDN");
            document.getElementById('tinymce-fallback').style.display = 'block';
        };
        document.head.appendChild(cdnScript);
    };
    document.head.appendChild(script);
}

// Function to initialize TinyMCE
function initTinyMCE() {
    console.log("Initializing TinyMCE");
    if (typeof tinymce === 'undefined') {
        console.error("TinyMCE not available");
        document.getElementById('tinymce-fallback').style.display = 'block';
        return;
    }

    // Remove any existing instances
    if (tinymce.get('id_text_content')) {
        tinymce.get('id_text_content').remove();
    }

    // Initialize TinyMCE
    tinymce.init({
        selector: '#id_text_content',
        height: 300,
        menubar: false,
        plugins: [
            'advlist autolink lists link image charmap print preview anchor',
            'searchreplace visualblocks code fullscreen',
            'insertdatetime media table paste code help wordcount'
        ],
        toolbar: 'undo redo | formatselect | bold italic backcolor | ' +
                'alignleft aligncenter alignright alignjustify | ' +
                'bullist numlist outdent indent | removeformat | help',
        content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
        setup: function (editor) {
            editor.on('init', function() {
                console.log("TinyMCE editor initialized");
                // Force visibility of the editor
                var editorContainer = editor.getContainer();
                if (editorContainer) {
                    editorContainer.style.visibility = 'visible';
                    editorContainer.style.display = 'block';
                }
            });
        }
    });
}

// Load TinyMCE when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM loaded, loading TinyMCE");
    loadTinyMCE();

    // Also initialize when the contributions tab is clicked
    var contributionsTab = document.getElementById('contributions-tab');
    if (contributionsTab) {
        contributionsTab.addEventListener('click', function() {
            console.log("Contributions tab clicked");
            setTimeout(initTinyMCE, 100);
        });
    }
});

// Also try to initialize when the window is fully loaded
window.addEventListener('load', function() {
    console.log("Window loaded");
    setTimeout(initTinyMCE, 500);
});

// Direct initialization attempt
if (typeof tinymce !== 'undefined') {
    console.log("TinyMCE already available, initializing directly");
    setTimeout(initTinyMCE, 100);
} else {
    console.log("TinyMCE not available yet, will initialize when loaded");
    loadTinyMCE();
}
</script>
