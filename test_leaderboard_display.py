import os
import sys
import django
from django.test import Client
from django.urls import reverse

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from game.models import Company, CompanyLeaderboard, LeaderboardEntry

def test_leaderboard_display():
    """Test the leaderboard display"""
    # Create a test client
    client = Client()
    
    # Get the first test user
    try:
        user = User.objects.get(username='testuser1')
    except User.DoesNotExist:
        print("Test user not found. Please run test_leaderboard.py first.")
        return
    
    # Log in
    logged_in = client.login(username='testuser1', password='testuser1password')
    if not logged_in:
        print("Failed to log in as testuser1")
        return
    
    print(f"Logged in as {user.username}")
    
    # Get the company
    try:
        company = Company.objects.get(name='Test Company')
    except Company.DoesNotExist:
        print("Test company not found. Please run test_leaderboard.py first.")
        return
    
    # Get the leaderboard URL
    leaderboard_url = f"/game/company/{company.slug}/leaderboard/"
    
    # Make a request to the leaderboard page
    response = client.get(leaderboard_url)
    
    # Check the response
    if response.status_code == 200:
        print(f"Successfully accessed leaderboard at {leaderboard_url}")
        
        # Check if the page contains the test users
        content = response.content.decode('utf-8')
        
        if 'testuser1' in content:
            print("Found testuser1 in the leaderboard")
        else:
            print("testuser1 not found in the leaderboard")
        
        if 'testuser2' in content:
            print("Found testuser2 in the leaderboard")
        else:
            print("testuser2 not found in the leaderboard")
        
        if 'testuser3' in content:
            print("Found testuser3 in the leaderboard")
        else:
            print("testuser3 not found in the leaderboard")
        
        if 'AnonymousUser' in content:
            print("WARNING: AnonymousUser found in the leaderboard")
        
        # Check if the page contains the scores
        if '70 points' in content or '70' in content:
            print("Found testuser1's score in the leaderboard")
        else:
            print("testuser1's score not found in the leaderboard")
        
        if '250 points' in content or '250' in content:
            print("Found testuser2's score in the leaderboard")
        else:
            print("testuser2's score not found in the leaderboard")
        
        if '430 points' in content or '430' in content:
            print("Found testuser3's score in the leaderboard")
        else:
            print("testuser3's score not found in the leaderboard")
    else:
        print(f"Failed to access leaderboard: {response.status_code}")

def main():
    print("=== Testing Leaderboard Display ===")
    test_leaderboard_display()

if __name__ == "__main__":
    main()
