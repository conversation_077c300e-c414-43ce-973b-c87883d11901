/**
 * Modal Fix CSS
 * Ensures proper handling of modal backdrops and prevents overlay issues
 */

/* Ensure modal backdrop is properly removed */
.modal-backdrop {
    opacity: 0.5;
    transition: opacity 0.15s linear;
}

.modal-backdrop.fade {
    opacity: 0;
}

.modal-backdrop.show {
    opacity: 0.5;
}

/* Force backdrop to be removed when modal is closed */
.modal.fade.hide + .modal-backdrop {
    display: none !important;
    opacity: 0 !important;
}

/* Ensure body returns to normal state after modal is closed */
body:not(.modal-open) .modal-backdrop {
    display: none !important;
    opacity: 0 !important;
}

/* Fix for modal stacking issues */
.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}

/* Ensure modal content is properly displayed */
.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.2);
    border-radius: 0.3rem;
    outline: 0;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* Dark mode support for modals */
[data-theme="dark"] .modal-content {
    background-color: #2a2a2a;
    color: #fff;
    border-color: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .modal-header {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modal-footer {
    border-top-color: rgba(255, 255, 255, 0.1);
}

/* Fix for modal z-index issues */
.modal {
    z-index: 1050 !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}

/* Ensure multiple backdrops don't stack */
.modal-backdrop + .modal-backdrop {
    display: none !important;
}
