/* Home Page Redesign for Corporate Prompt Master
 * A sleek, professional design with modern aesthetics
 */

/* Hero section enhancements */
.hero-section {
  position: relative;
  padding: 5rem 0;
  overflow: hidden;
  border-radius: 0;
  margin-bottom: 3rem;
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  line-height: 1.2;
  margin-bottom: 1.5rem;
  background: linear-gradient(90deg, #ffffff, #e0e0e0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero-subtitle {
  font-size: 1.25rem;
  font-weight: 400;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 600px;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Animated background elements */
.hero-bg-element {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(58, 134, 255, 0.3), rgba(139, 92, 246, 0.3));
  filter: blur(60px);
  z-index: 1;
}

.hero-bg-element-1 {
  width: 400px;
  height: 400px;
  top: -100px;
  right: -100px;
  animation: float 15s ease-in-out infinite;
}

.hero-bg-element-2 {
  width: 300px;
  height: 300px;
  bottom: -50px;
  left: -50px;
  animation: float 20s ease-in-out infinite reverse;
}

@keyframes float {
  0% { transform: translate(0, 0) rotate(0deg); }
  50% { transform: translate(30px, 30px) rotate(5deg); }
  100% { transform: translate(0, 0) rotate(0deg); }
}

/* Feature cards */
.feature-card {
  height: 100%;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-10px);
}

.feature-card .card-body {
  padding: 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.feature-icon-wrapper {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  background: linear-gradient(135deg, rgba(58, 134, 255, 0.15), rgba(139, 92, 246, 0.15));
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon-wrapper {
  transform: scale(1.1) rotate(5deg);
  background: linear-gradient(135deg, rgba(58, 134, 255, 0.3), rgba(139, 92, 246, 0.3));
}

.feature-icon {
  font-size: 2.5rem;
  color: #3a86ff;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
  color: #0ea5e9;
}

.feature-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.feature-description {
  color: var(--text-secondary, #e0e0e0);
  margin-bottom: 1.5rem;
  flex-grow: 1;
}

.feature-card .btn {
  margin-top: auto;
}

/* Info sections */
.info-section {
  padding: 4rem 0;
  position: relative;
}

.info-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 2rem;
  text-align: center;
}

.info-section .lead {
  text-align: center;
  max-width: 800px;
  margin: 0 auto 3rem;
}

/* Why prompt engineering matters section */
.benefits-list {
  padding-left: 1.5rem;
}

.benefits-list li {
  margin-bottom: 1rem;
  position: relative;
  padding-left: 0.5rem;
}

.benefits-list li::before {
  content: "•";
  color: #3a86ff;
  font-weight: bold;
  position: absolute;
  left: -1.5rem;
}

/* How it works section */
.how-it-works .accordion {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.how-it-works .accordion-item {
  border: none;
  margin-bottom: 0;
}

.how-it-works .accordion-button {
  font-size: 1.1rem;
  font-weight: 600;
  padding: 1.25rem 1.5rem;
}

.how-it-works .accordion-button:not(.collapsed) {
  box-shadow: none;
}

.how-it-works .accordion-body {
  padding: 1.5rem;
  font-size: 1rem;
}

/* CTA section */
.cta-section {
  padding: 5rem 0;
  text-align: center;
  background: linear-gradient(135deg, rgba(58, 134, 255, 0.1), rgba(139, 92, 246, 0.1));
  border-radius: 12px;
  margin: 4rem 0;
}

.cta-section h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
}

.cta-section p {
  max-width: 600px;
  margin: 0 auto 2rem;
  font-size: 1.1rem;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-section {
    padding: 4rem 0;
  }
  
  .feature-card .card-body {
    padding: 2rem;
  }
}

@media (max-width: 767.98px) {
  .hero-title {
    font-size: 2rem;
  }
  
  .hero-section {
    padding: 3rem 0;
  }
  
  .hero-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .hero-buttons .btn {
    width: 100%;
  }
  
  .feature-card {
    margin-bottom: 1.5rem;
  }
}

/* Dark mode specific styles */
.dark-mode .hero-title {
  background: linear-gradient(90deg, #ffffff, #a0a0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark-mode .feature-icon-wrapper {
  box-shadow: 0 0 20px rgba(58, 134, 255, 0.3);
}

.dark-mode .feature-card:hover {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(58, 134, 255, 0.3);
}

.dark-mode .benefits-list li::before {
  color: #3a86ff;
}

.dark-mode .how-it-works .accordion {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.dark-mode .cta-section {
  background: linear-gradient(135deg, rgba(58, 134, 255, 0.15), rgba(139, 92, 246, 0.15));
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}
