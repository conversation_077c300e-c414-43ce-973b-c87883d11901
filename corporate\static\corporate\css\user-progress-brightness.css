/* 
 * User Progress Text Brightness Enhancement
 * This CSS file makes user-related text brighter on the user progress page
 * while leaving everything else as is.
 */

/* Make table headers brighter */
.table thead th {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5) !important;
}

/* Make user names brighter */
.table tbody tr td .user-name,
.table tbody tr td .d-flex .user-name,
.table tbody tr td h6.user-name {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5) !important;
}

/* Make usernames brighter */
.table tbody tr td small.user-handle,
.table tbody tr td .user-handle {
    color: #ffffff !important;
}

/* Make all user data in table cells brighter */
.table tbody tr td {
    color: #ffffff !important;
}

/* Make "Not specified" text brighter */
.table tbody tr td span.text-muted,
.table tbody tr td span[style*="color: #333333; font-style: italic;"] {
    color: #ffffff !important;
    font-style: italic;
}

/* Make "No certificates" text brighter */
.table tbody tr td span.text-muted {
    color: #ffffff !important;
}

/* Ensure user avatars have good contrast */
.avatar-placeholder {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Make badge text brighter while keeping the background color */
.badge.bg-danger,
.badge.bg-secondary,
.badge.bg-success,
.badge.bg-primary {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Make user information in detail view brighter */
.table th,
.table td {
    color: #ffffff !important;
}

/* Make card titles brighter */
.card-body h6 {
    color: #ffffff !important;
    font-weight: bold !important;
}

/* Make progress summary numbers brighter */
.card.bg-light .card-body h3 {
    color: #ffffff !important;
    font-weight: bold !important;
}

/* Make progress summary labels brighter */
.card.bg-light .card-body p.text-muted {
    color: #ffffff !important;
}

/* Override background color for summary cards */
.card.bg-light {
    background-color: #2a2a2a !important;
}
