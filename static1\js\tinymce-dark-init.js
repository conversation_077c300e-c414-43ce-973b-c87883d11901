/**
 * TinyMCE Dark Theme Initialization
 * This script initializes TinyMCE with a dark theme and custom styling
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize TinyMCE for all elements with the 'apply-tinymce' class
    tinymce.init({
        selector: '.apply-tinymce',
        skin: 'oxide-dark', // Use the built-in dark skin
        content_css: [
            '/static/css/tinymce-dark-theme.css', // Our custom CSS for the content
            'dark' // Use TinyMCE's built-in dark content CSS
        ],
        height: 300,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | formatselect | ' +
                'bold italic underline strikethrough | alignleft aligncenter ' +
                'alignright alignjustify | bullist numlist outdent indent | ' +
                'link image table | removeformat | help',
        // Add custom styling to the editor
        setup: function(editor) {
            // Add a class to the editor container when it's initialized
            editor.on('init', function() {
                // Add our custom CSS to the editor iframe
                editor.dom.addStyle(`
                    body {
                        background-color: #1a1a1a !important;
                        color: #e0e0e0 !important;
                        padding: 15px !important;
                        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
                    }
                    p, h1, h2, h3, h4, h5, h6, li, td, th {
                        color: #e0e0e0 !important;
                    }
                    a {
                        color: #4d81d6 !important;
                    }
                    table {
                        border-collapse: collapse !important;
                    }
                    table td, table th {
                        border: 1px solid #333 !important;
                        padding: 8px !important;
                    }
                    img {
                        max-width: 100% !important;
                        height: auto !important;
                        border-radius: 6px !important;
                    }
                    blockquote {
                        border-left: 3px solid #4d81d6 !important;
                        padding-left: 15px !important;
                        margin-left: 15px !important;
                        color: #b0b0b0 !important;
                    }
                    code {
                        background-color: #2a2a2a !important;
                        padding: 2px 4px !important;
                        border-radius: 3px !important;
                        color: #e0e0e0 !important;
                    }
                `);
            });
        },
        // Set a max character count with warning
        max_chars: 500,
        setup: function(editor) {
            editor.on('keyup', function(e) {
                var content = editor.getContent({format: 'text'});
                var charCount = content.length;
                var maxChars = editor.settings.max_chars;
                
                if (charCount > maxChars) {
                    var warningElement = document.getElementById('tinymce-char-warning-' + editor.id);
                    if (!warningElement) {
                        warningElement = document.createElement('div');
                        warningElement.id = 'tinymce-char-warning-' + editor.id;
                        warningElement.className = 'tinymce-char-warning';
                        warningElement.style.color = '#ff6b6b';
                        warningElement.style.marginTop = '5px';
                        warningElement.style.fontSize = '0.9em';
                        editor.getContainer().parentNode.insertBefore(warningElement, editor.getContainer().nextSibling);
                    }
                    warningElement.innerHTML = 'Warning: Character limit exceeded. Current: ' + charCount + ' / Max: ' + maxChars;
                } else {
                    var warningElement = document.getElementById('tinymce-char-warning-' + editor.id);
                    if (warningElement) {
                        warningElement.remove();
                    }
                }
            });
        },
        // Allow image uploads
        images_upload_url: '/assistant/tinymce/upload/',
        images_upload_handler: function (blobInfo, success, failure) {
            var xhr, formData;
            xhr = new XMLHttpRequest();
            xhr.withCredentials = false;
            xhr.open('POST', '/assistant/tinymce/upload/');
            
            // Get CSRF token from cookie
            var csrftoken = getCookie('csrftoken');
            
            xhr.onload = function() {
                var json;
                if (xhr.status != 200) {
                    failure('HTTP Error: ' + xhr.status);
                    return;
                }
                json = JSON.parse(xhr.responseText);
                if (!json || typeof json.location != 'string') {
                    failure('Invalid JSON: ' + xhr.responseText);
                    return;
                }
                success(json.location);
            };
            
            formData = new FormData();
            formData.append('file', blobInfo.blob(), blobInfo.filename());
            formData.append('csrfmiddlewaretoken', csrftoken);
            
            xhr.send(formData);
        },
        // Function to get CSRF token from cookie
        getCookie: function(name) {
            var cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                var cookies = document.cookie.split(';');
                for (var i = 0; i < cookies.length; i++) {
                    var cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    });

    // Apply custom styling to file inputs
    const fileInputs = document.querySelectorAll('input[type="file"].form-control');
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            // Add a selected class when a file is chosen
            if (this.files.length > 0) {
                this.classList.add('file-selected');
            } else {
                this.classList.remove('file-selected');
            }
        });
    });
});

// Helper function to get CSRF token
function getCookie(name) {
    var cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        var cookies = document.cookie.split(';');
        for (var i = 0; i < cookies.length; i++) {
            var cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
