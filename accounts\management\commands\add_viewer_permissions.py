"""
Management command to add view permissions for viewers on assistants.

This command ensures that all viewers have the necessary permissions to view assistants.
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from guardian.shortcuts import assign_perm, get_perms
from accounts.models import Company, Membership
from assistants.models import Assistant

class Command(BaseCommand):
    help = 'Adds view permissions for viewers on assistants.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company',
            type=int,
            help='Specify a company ID to update permissions for just that company',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information about permissions being assigned',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually making changes',
        )

    def handle(self, *args, **options):
        company_id = options.get('company')
        verbose = options.get('verbose', False)
        dry_run = options.get('dry_run', False)
        
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE: No changes will be made"))
        
        # Get companies to process
        if company_id:
            companies = Company.objects.filter(id=company_id)
            if not companies.exists():
                self.stdout.write(self.style.ERROR(f"Company with ID {company_id} not found."))
                return
        else:
            companies = Company.objects.all()
            
        self.stdout.write(f"Processing {companies.count()} companies...")
        
        # Define the permissions that viewers should have on assistants
        viewer_assistant_perms = [
            'assistants.view_assistant',
        ]
        
        # Track statistics
        companies_processed = 0
        viewers_processed = 0
        assistants_processed = 0
        permissions_assigned = 0
        errors = 0
        
        # Get the viewer group
        try:
            viewer_group = Group.objects.get(name='Company Guests')
        except Group.DoesNotExist:
            self.stdout.write(self.style.ERROR("Viewer group 'Company Guests' not found."))
            return
        
        # Process each company
        for company in companies:
            self.stdout.write(f"Processing company '{company.name}' (ID: {company.id})")
            companies_processed += 1
            
            # Get all viewers for this company
            viewer_memberships = Membership.objects.filter(
                company=company,
                user__groups=viewer_group
            )
            
            if not viewer_memberships.exists():
                self.stdout.write(f"  No viewers found for company '{company.name}'")
                continue
                
            self.stdout.write(f"  Found {viewer_memberships.count()} viewers")
            
            # Get all assistants for this company
            assistants = Assistant.objects.filter(company=company)
            if not assistants.exists():
                self.stdout.write(f"  No assistants found for company '{company.name}'")
                continue
                
            self.stdout.write(f"  Found {assistants.count()} assistants")
            
            # Process each viewer
            for membership in viewer_memberships:
                viewer = membership.user
                viewers_processed += 1
                
                if verbose:
                    self.stdout.write(f"  Processing viewer '{viewer.username}'")
                
                # Process each assistant
                for assistant in assistants:
                    assistants_processed += 1
                    
                    if verbose:
                        self.stdout.write(f"    Processing assistant '{assistant.name}' (ID: {assistant.id})")
                    
                    # Get current permissions for the viewer on this assistant
                    current_perms = get_perms(viewer, assistant)
                    if verbose:
                        self.stdout.write(f"      Current permissions: {current_perms}")
                    
                    # Assign any missing permissions
                    missing_perms = []
                    for perm in viewer_assistant_perms:
                        perm_codename = perm.split('.')[-1]
                        if perm_codename not in current_perms:
                            missing_perms.append(perm)
                    
                    if not missing_perms:
                        if verbose:
                            self.stdout.write(self.style.SUCCESS(f"      All permissions already assigned"))
                        continue
                    
                    for perm in missing_perms:
                        try:
                            if not dry_run:
                                assign_perm(perm, viewer, assistant)
                                permissions_assigned += 1
                                if verbose:
                                    self.stdout.write(self.style.SUCCESS(f"      Assigned '{perm}' to viewer"))
                            else:
                                self.stdout.write(self.style.WARNING(f"      Would assign '{perm}' to viewer (dry run)"))
                        except Exception as e:
                            self.stdout.write(self.style.ERROR(f"      Error assigning '{perm}' to viewer: {e}"))
                            errors += 1
        
        # Print summary
        self.stdout.write("\nSummary:")
        self.stdout.write(f"  Companies processed: {companies_processed}")
        self.stdout.write(f"  Viewers processed: {viewers_processed}")
        self.stdout.write(f"  Assistants processed: {assistants_processed}")
        if not dry_run:
            self.stdout.write(f"  Permissions assigned: {permissions_assigned}")
        else:
            self.stdout.write(f"  Permissions that would be assigned: {permissions_assigned}")
        self.stdout.write(f"  Errors encountered: {errors}")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("\nThis was a dry run. No changes were made."))
            self.stdout.write(self.style.WARNING("Run without --dry-run to apply the changes."))
        else:
            self.stdout.write(self.style.SUCCESS("\nDone adding viewer permissions on assistants."))
