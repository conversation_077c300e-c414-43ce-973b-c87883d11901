/**
 * Chat Theme Transition JS
 * Ensures proper theme transitions for chat bubbles
 */

document.addEventListener('DOMContentLoaded', function() {
    // Function to apply theme-specific styles to chat elements
    function updateChatTheme(isDarkMode) {
        console.log('Updating chat theme to:', isDarkMode ? 'dark' : 'light');

        // Get all chat elements
        const assistantMessages = document.querySelectorAll('.assistant-message .message-content');
        const userMessages = document.querySelectorAll('.user-message .message-content');
        const navContentBubbles = document.querySelectorAll('.nav-content-bubble .message-content');

        // Apply appropriate styles based on theme
        if (isDarkMode) {
            // Dark mode styles
            assistantMessages.forEach(function(el) {
                el.style.background = 'linear-gradient(145deg, #2a2a2a, #222222)';
                el.style.backgroundColor = '#2a2a2a';
                el.style.color = '#ffffff';
                el.style.borderColor = '#333333';
                el.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
            });

            userMessages.forEach(function(el) {
                el.style.background = 'linear-gradient(145deg, #0066ff, #0055cc)';
                el.style.backgroundColor = '#0066ff';
                el.style.color = '#ffffff';
                el.style.border = 'none';
                el.style.boxShadow = '0 5px 15px rgba(0, 102, 255, 0.3)';
            });

            navContentBubbles.forEach(function(el) {
                el.style.background = 'linear-gradient(145deg, #252525, #1e1e1e)';
                el.style.backgroundColor = '#252525';
                el.style.color = '#ffffff';
                el.style.borderColor = '#333333';
                el.style.borderLeftColor = '#0077ff';
                el.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05)';
            });
        } else {
            // Light mode styles - MATCHING DARK MODE
            assistantMessages.forEach(function(el) {
                el.style.background = 'linear-gradient(145deg, #2a2a2a, #222222)';
                el.style.backgroundColor = '#2a2a2a';
                el.style.color = '#ffffff';
                el.style.borderColor = '#333333';
                el.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
                el.style.borderRadius = '18px 18px 18px 4px';
                el.style.padding = '1rem 1.25rem';
                el.style.fontWeight = '400';
                el.style.letterSpacing = '0.01em';
            });

            userMessages.forEach(function(el) {
                el.style.background = 'linear-gradient(145deg, #0066ff, #0055cc)';
                el.style.backgroundColor = '#0066ff';
                el.style.color = '#ffffff';
                el.style.border = 'none';
                el.style.boxShadow = '0 5px 15px rgba(0, 102, 255, 0.3)';
                el.style.borderRadius = '18px 18px 4px 18px';
                el.style.padding = '1rem 1.25rem';
                el.style.fontWeight = '500';
                el.style.letterSpacing = '0.01em';
            });

            navContentBubbles.forEach(function(el) {
                el.style.background = 'linear-gradient(145deg, #252525, #1e1e1e)';
                el.style.backgroundColor = '#252525';
                el.style.color = '#ffffff';
                el.style.borderColor = '#333333';
                el.style.borderLeftColor = '#0077ff';
                el.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05)';
                el.style.borderRadius = '8px';
                el.style.padding = '1.25rem';
                el.style.marginBottom = '1.5rem';
            });
        }
    }

    // Initial theme check and application
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
    updateChatTheme(isDarkMode);

    // Set up a MutationObserver to watch for theme changes
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.attributeName === 'data-theme') {
                const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
                updateChatTheme(isDarkMode);
            }
        });
    });

    // Start observing the document element for data-theme attribute changes
    observer.observe(document.documentElement, { attributes: true });

    // Set up a MutationObserver to watch for new chat messages
    const chatBox = document.querySelector('.chat-box') || document.querySelector('#chat-box');
    if (chatBox) {
        const chatObserver = new MutationObserver(function() {
            const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
            updateChatTheme(isDarkMode);
        });

        chatObserver.observe(chatBox, { childList: true, subtree: true });
    }

    // Listen for the themeChanged custom event
    document.addEventListener('themeChanged', function(e) {
        console.log('Theme changed event received:', e.detail.theme);
        const isDarkMode = e.detail.theme === 'dark';
        updateChatTheme(isDarkMode);
    });
});
