{% load static account_tags rating_tags %}

<div class="company-header d-flex align-items-center justify-content-between p-3 bg-white shadow-sm rounded mb-4">
    <div class="d-flex align-items-center">
        <!-- Company Logo -->
        <div class="company-logo me-3">
            {% if company.info.logo and company.info.logo.url %}
                <img src="{{ company.info.logo.url }}" alt="{{ company.name }} Logo" class="rounded-circle" style="width: 60px; height: 60px; object-fit: cover;">
            {% else %}
                <div class="bg-light rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                    <i class="bi bi-building text-muted" style="font-size: 1.8rem;"></i>
                </div>
            {% endif %}
        </div>
        
        <!-- Company Info -->
        <div>
            <h1 class="h4 mb-0">{{ company.name }}</h1>
            <p class="text-muted small mb-0">{{ company.industry }}</p>
        </div>
    </div>
    
    <!-- Actions -->
    <div class="d-flex align-items-center">
        <!-- Rating Display -->
        <div class="me-3">
            <div id="rating-display-{{ company.id }}">
                {% if company_listing %}
                    {% render_stars company_listing.avg_rating company_listing.total_ratings %}
                {% else %}
                    {% render_stars 0 0 %}
                {% endif %}
            </div>
            {% if user.is_authenticated %}
                <button type="button" class="btn btn-sm btn-link text-decoration-none p-0 mt-1"
                        data-bs-toggle="modal" data-bs-target="#ratingModal"
                        data-company-id="{{ company.id }}"
                        data-company-name="{{ company.name }}">
                    <i class="bi bi-star me-1"></i>Rate
                </button>
            {% endif %}
        </div>
        
        <!-- Like Button -->
        {% if user.is_authenticated %}
            <button
                class="like-button btn btn-sm p-1 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %}"
                data-item-id="{{ company.id }}"
                data-item-type="company"
                title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                style="background: none; border: none;">
                <i class="bi bi-heart{% if is_favorited %}-fill{% endif %} me-1"></i>
                {% if is_favorited %}Favorited{% else %}Favorite{% endif %}
            </button>
        {% endif %}
        
        <!-- Share Button -->
        <button class="btn btn-sm btn-outline-secondary ms-2" id="shareButton">
            <i class="bi bi-share me-1"></i> Share
        </button>
        
        <!-- QR Code Button -->
        <button class="btn btn-sm btn-outline-secondary ms-2" data-bs-toggle="modal" data-bs-target="#qrCodeModal">
            <i class="bi bi-qr-code me-1"></i> QR
        </button>
    </div>
</div>

<!-- Rating Modal -->
<div class="modal fade" id="ratingModal" tabindex="-1" aria-labelledby="ratingModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="ratingModalLabel">Rate <span id="modalCompanyName">this company</span></h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="ratingForm">
          {% csrf_token %}
          <p class="mb-3">How would you rate your experience with <strong id="modalCompanyNameInner">this company</strong>?</p>
          
          <div class="modal-stars text-center mb-4">
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="1">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="2">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="3">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="4">
              <i class="bi bi-star text-secondary"></i>
            </button>
            <button type="button" class="btn btn-lg modal-star-btn" data-rating-value="5">
              <i class="bi bi-star text-secondary"></i>
            </button>
          </div>
          
          <div id="modalErrorMsg" class="alert alert-danger" style="display: none;"></div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="submitRatingBtn" disabled>Submit Rating</button>
      </div>
    </div>
  </div>
</div>

<!-- Folder Options Modal -->
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="folderOptionsModalLabel">Add to Favorites</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Add <strong id="modalFolderNameItemName">this item</strong> to favorites:</p>

        <!-- Option 1: Save without folder -->
        <div class="d-grid mb-3">
            <button type="button" class="btn btn-outline-primary text-start" id="saveNoFolderBtn">
                <i class="bi bi-bookmark-heart me-2"></i>Save (Uncategorized)
            </button>
        </div>

        <hr>

        <!-- Option 2: Add to existing folder -->
        <div id="existingFoldersSection" class="mb-3" style="display: none;">
            <label for="selectFolder" class="form-label small mb-1">Add to existing folder:</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-folder-symlink"></i></span>
                <select class="form-select" id="selectFolder">
                    <option selected disabled value="">Choose folder...</option>
                    <!-- Options will be populated by JS -->
                </select>
                <button class="btn btn-primary" type="button" id="addToFolderBtn" disabled>
                    <i class="bi bi-plus-lg"></i> Add
                </button>
            </div>
        </div>

        <!-- Option 3: Create new folder -->
        <div>
            <label for="newFolderName" class="form-label small mb-1">Or create a new folder:</label>
            <div class="input-group">
                 <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
                <input type="text" class="form-control" id="newFolderName" placeholder="New folder name...">
                <button class="btn btn-success" type="button" id="createFolderAndSaveBtn" disabled>
                   <i class="bi bi-check-lg"></i> Create & Save
                </button>
            </div>
        </div>

        <div id="folderModalErrorMsg" class="text-danger small mt-3" style="display: none;"></div>
      </div>
    </div>
  </div>
</div>

<!-- QR Code Modal -->
<div class="modal fade" id="qrCodeModal" tabindex="-1" aria-labelledby="qrCodeModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-sm modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="qrCodeModalLabel">Scan QR Code</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body text-center">
        {% if company.qr_code %}
          <img src="{{ company.qr_code.url }}" alt="QR Code" class="img-fluid">
        {% else %}
          <p class="text-muted">QR code not available</p>
        {% endif %}
        <p class="mt-2 small text-muted">Scan to visit company page</p>
      </div>
    </div>
  </div>
</div>

<script>
// JavaScript for handling the share button
document.addEventListener('DOMContentLoaded', function() {
    const shareButton = document.getElementById('shareButton');
    if (shareButton) {
        shareButton.addEventListener('click', function() {
            if (navigator.share) {
                navigator.share({
                    title: '{{ company.name }}',
                    text: '{{ company.info.description|default:"Check out this company!" }}',
                    url: window.location.href
                })
                .then(() => console.log('Share successful'))
                .catch((error) => console.log('Error sharing:', error));
            } else {
                // Fallback for browsers that don't support the Web Share API
                const tempInput = document.createElement('input');
                tempInput.value = window.location.href;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                alert('URL copied to clipboard!');
            }
        });
    }
});
</script>
