/* Solid Colors CSS Override */

/* Force background color */
html, body {
    background-color: #e8f4ff !important;
    background: #e8f4ff !important;
    background-image: none !important;
}

/* Force chat container styles */
.chat-container, .general-chat-container {
    background-color: #ffffff !important;
    background: #ffffff !important;
    background-image: none !important;
    border: 1px solid #e0e0e0 !important;
    border-radius: 1.25rem !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
}

/* Force chat box styles */
.chat-box, .general-chat-box, #chat-box {
    background-color: #f9f9f9 !important;
    background: #f9f9f9 !important;
    background-image: none !important;
    border: 1px solid #f0f0f0 !important;
    border-radius: 1.25rem !important;
}

/* Force user message styles */
.user-message .message-content {
    background-color: #3b7dd8 !important;
    background: #3b7dd8 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: none !important;
}

/* Force assistant message styles */
.assistant-message .message-content {
    background-color: #ffffff !important;
    background: #ffffff !important;
    background-image: none !important;
    color: #333333 !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Force chat form styles */
#chat-form {
    background-color: transparent !important;
    background: transparent !important;
    background-image: none !important;
    width: 100% !important;
    margin: 0 auto !important;
    position: relative !important;
}

/* Force send button styles */
#send-button {
    background-color: #3b7dd8 !important;
    background: #3b7dd8 !important;
    background-image: none !important;
    color: #ffffff !important;
    border: none !important;
    border-radius: 1.5rem !important;
    padding-left: 1.25rem !important;
    padding-right: 1.25rem !important;
}

/* Force input field styles */
#message-input {
    background-color: #ffffff !important;
    background: #ffffff !important;
    background-image: none !important;
    color: #333333 !important;
    border: 1px solid #d0d0d0 !important;
    border-radius: 1.5rem !important;
    height: 46px !important;
}

/* Force hover effects */
.user-message:hover .message-content {
    background-color: #3069b9 !important;
    background: #3069b9 !important;
    background-image: none !important;
}

.assistant-message:hover .message-content {
    background-color: #ffffff !important;
    background: #ffffff !important;
    background-image: none !important;
}

#send-button:hover {
    background-color: #3069b9 !important;
    background: #3069b9 !important;
    background-image: none !important;
}
