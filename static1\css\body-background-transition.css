/**
 * Body Background Transition CSS
 * Ensures smooth transition between light and dark mode for the body background
 * with enhanced dark mode styling
 */

/* Apply transition to body background */
body {
  transition: background 0.5s ease, color 0.3s ease;
}

/* Override any inline styles for body background in light mode */
body[style*="background-color"] {
  background-color: var(--ef-background) !important;
  background: var(--ef-background) !important;
}

/* Override any inline styles for body background in dark mode with subtle pattern */
[data-theme="dark"] body {
  background-color: #121212 !important;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(30, 30, 30, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(20, 20, 20, 0.2) 0%, transparent 50%),
    linear-gradient(to bottom, #121212, #0a0a0a) !important;
  background-attachment: fixed !important;
  color: #ffffff !important;
}

/* Override any inline styles for body background in dark mode */
[data-theme="dark"] body[style*="background-color"] {
  background-color: #121212 !important;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(30, 30, 30, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(20, 20, 20, 0.2) 0%, transparent 50%),
    linear-gradient(to bottom, #121212, #0a0a0a) !important;
  background-attachment: fixed !important;
}

/* Override assistant_chat.html body style */
[data-theme="dark"] body[style*="background-color: #e8f4ff"] {
  background-color: #121212 !important;
  background-image:
    radial-gradient(circle at 25% 25%, rgba(30, 30, 30, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(20, 20, 20, 0.2) 0%, transparent 50%),
    linear-gradient(to bottom, #121212, #0a0a0a) !important;
  background-attachment: fixed !important;
}

/* Add subtle noise texture */
[data-theme="dark"] body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.03;
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
  z-index: -1;
}

/* Ensure main content area has proper background */
[data-theme="dark"] main {
  background-color: var(--dark-bg-primary);
  background: var(--dark-bg-primary);
}

/* Ensure all containers have proper background */
[data-theme="dark"] .container,
[data-theme="dark"] .container-fluid {
  background-color: transparent;
  background: transparent;
}

/* Ensure all rows have proper background */
[data-theme="dark"] .row {
  background-color: transparent;
  background: transparent;
}

/* Ensure all columns have proper background */
[data-theme="dark"] [class*="col-"] {
  background-color: transparent;
  background: transparent;
}
