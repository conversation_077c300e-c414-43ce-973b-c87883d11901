/**
 * Force Solid Colors for Chat Messages
 * This script ensures that all chat messages have solid colors, not gradients
 */

document.addEventListener('DOMContentLoaded', function() {
    // Apply solid colors immediately
    forceSolidColors();

    // Set up a MutationObserver to watch for new messages
    const chatBox = document.getElementById('chat-box');
    if (chatBox) {
        // Use a debounced version to prevent excessive calls
        let timeout;
        const observer = new MutationObserver(function(mutations) {
            // Clear any existing timeout
            if (timeout) {
                clearTimeout(timeout);
            }

            // Set a new timeout to run the function after a delay
            timeout = setTimeout(function() {
                // Check if any relevant nodes were added
                let shouldApply = false;
                for (const mutation of mutations) {
                    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                        shouldApply = true;
                        break;
                    }
                }

                if (shouldApply) {
                    forceSolidColors();
                }
            }, 500);
        });

        // Start observing the chat box for added messages with more specific parameters
        observer.observe(chatBox, { childList: true, subtree: false });
    }
});

/**
 * Force solid colors on all chat messages
 */
function forceSolidColors() {
    // User messages
    document.querySelectorAll('.user-message .message-content').forEach(function(el) {
        el.style.background = '#3b7dd8';
        el.style.backgroundColor = '#3b7dd8';
        el.style.backgroundImage = 'none';
        el.style.color = '#ffffff';
        el.style.border = 'none';
        el.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
    });

    // Assistant messages
    document.querySelectorAll('.assistant-message .message-content').forEach(function(el) {
        el.style.background = '#ffffff';
        el.style.backgroundColor = '#ffffff';
        el.style.backgroundImage = 'none';
        el.style.color = '#333333';
        el.style.border = '1px solid rgba(0, 0, 0, 0.05)';
        el.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.05)';
    });

    // Navigation content bubbles
    document.querySelectorAll('.nav-content-bubble .message-content').forEach(function(el) {
        el.style.background = '#ffffff';
        el.style.backgroundColor = '#ffffff';
        el.style.backgroundImage = 'none';
        el.style.color = '#333333';
        el.style.border = '1px solid rgba(0, 0, 0, 0.05)';
        el.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.03)';
        el.style.borderLeft = '3px solid #0d6efd';
    });

    // Remove any pseudo-elements that might add gradients
    const style = document.createElement('style');
    style.textContent = `
        .message-content::before,
        .message-content::after {
            display: none !important;
            content: none !important;
            background: none !important;
            background-image: none !important;
        }

        .user-message,
        .assistant-message {
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
        }

        .user-message .message-content {
            background: #3b7dd8 !important;
            background-color: #3b7dd8 !important;
            background-image: none !important;
            color: #ffffff !important;
            border: none !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }

        .assistant-message .message-content {
            background: #ffffff !important;
            background-color: #ffffff !important;
            background-image: none !important;
            color: #333333 !important;
            border: 1px solid rgba(0, 0, 0, 0.05) !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
        }

        .nav-content-bubble .message-content {
            background: #ffffff !important;
            background-color: #ffffff !important;
            background-image: none !important;
            color: #333333 !important;
            border: 1px solid rgba(0, 0, 0, 0.05) !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.03) !important;
            border-left: 3px solid #0d6efd !important;
        }
    `;

    // Add the style to the head if it doesn't already exist
    if (!document.getElementById('force-solid-colors-style')) {
        style.id = 'force-solid-colors-style';
        document.head.appendChild(style);
    }
}
