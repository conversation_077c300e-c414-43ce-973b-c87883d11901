{% load i18n %}
<!-- Header component that includes the navigation bar -->
<header class="main-header">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-controller"></i> Corporate Prompt Master
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">
                            <i class="bi bi-house"></i> {% trans "Home" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/game/">
                            <i class="bi bi-controller"></i> {% trans "Play Game" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/leaderboard/">
                            <i class="bi bi-trophy"></i> {% trans "Leaderboard" %}
                        </a>
                    </li>
                    {% if request.user.is_superuser %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'superadmin:dashboard' %}">
                            <i class="bi bi-shield-lock"></i> {% trans "Superadmin" %}
                        </a>
                    </li>
                    {% endif %}

                    <li class="nav-item">
                        <a class="nav-link btn btn-info text-white px-3 mx-2" href="{% url 'game:help_redirect' %}" target="_blank">
                            <i class="bi bi-question-circle"></i> {% trans "Help" %}
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    {% if request.user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-person-circle"></i> {{ request.user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                            <li><a class="dropdown-item" href="/profile/"><i class="bi bi-person me-2"></i>{% trans "Profile" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'corporate:corporate_register' %}"><i class="bi bi-plus-circle me-2"></i>{% trans "Create Company" %}</a></li>
                            {% if request.user.is_staff %}
                            <li><a class="dropdown-item" href="{% url 'admin:index' %}"><i class="bi bi-gear me-2"></i>{% trans "Admin" %}</a></li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/logout/"><i class="bi bi-box-arrow-right me-2"></i>{% trans "Logout" %}</a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="/login/">
                            <i class="bi bi-box-arrow-in-right"></i> {% trans "Login" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/register/">
                            <i class="bi bi-person-plus"></i> {% trans "Register" %}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
</header>
