/* Organization Chart Styles - Enhanced Version */

.org-chart-container {
    margin: 20px 0;
    padding: 20px;
    background-color: #f8f9fa;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    overflow: hidden;
    border: 1px solid #e9ecef;
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

.org-chart-container h2 {
    text-align: center;
    margin-bottom: 25px;
    color: #d1d5d9;
    font-size: 1.5rem;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.org-chart {
    display: flex;
    flex-direction: column;
    gap: 40px;
    align-items: center;
    padding: 10px;
}

.org-chart-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 40px;
    align-items: center;
}

.org-level {
    display: flex;
    justify-content: center;
    width: 100%;
    gap: 25px;
    position: relative;
    padding: 10px 0;
}

.level-title {
    font-weight: 600;
    color: #495057;
    background-color: #e9ecef;
    padding: 5px 15px;
    border-radius: 20px;
    margin-bottom: 15px;
    display: inline-block;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    font-size: 0.9rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

.level-roles {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
    width: 100%;
}

/* Lines connecting levels */
.org-level::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 50%;
    height: 20px;
    width: 3px;
    background-color: #adb5bd;
    transform: translateX(-50%);
}

.org-level:first-child::before {
    display: none;
}

.org-node {
    padding: 15px 20px;
    background-color: #fff;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    text-align: center;
    min-width: 160px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    font-weight: 500;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 5px;
}

.org-node:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.12);
}

.node-indicator {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: #6c757d;
}

.node-title {
    font-size: 0.95rem;
    line-height: 1.3;
}

/* Status styles */
.org-node.completed {
    background-color: #e8f5e9;
    border-color: #a5d6a7;
    color: #2e7d32;
}

.org-node.completed .node-indicator {
    color: #4caf50;
}

.org-node.current {
    background-color: #e3f2fd;
    border-color: #90caf9;
    color: #0d47a1;
    transform: scale(1.15);
    box-shadow: 0 0 20px rgba(33, 150, 243, 0.4);
    z-index: 10;
    border-width: 3px;
    font-weight: 600;
}

.org-node.current .node-indicator {
    color: #2196f3;
}

.org-node.current::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-top: 8px solid transparent;
    border-bottom: 8px solid transparent;
    border-left: 8px solid #2196f3;
}

.org-node.future {
    background-color: #fafafa;
    border-color: #e0e0e0;
    color: #9e9e9e;
    opacity: 0.8;
}

.current-marker {
    position: absolute;
    bottom: -22px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    font-size: 0.7rem;
    background-color: #2196f3;
    color: white;
    padding: 3px 8px;
    border-radius: 12px;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(33, 150, 243, 0.3);
    white-space: nowrap;
    letter-spacing: 0.5px;
}

/* Task list styles */
.task-list-container {
    margin-top: 30px;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.task-list-container h3 {
    margin-bottom: 15px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.task-list {
    list-style-type: none;
    padding: 0;
}

.task-item {
    padding: 12px 15px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.task-item:hover {
    background-color: #e9ecef;
}

.task-item.completed {
    background-color: #d4edda;
    border-left-color: #28a745;
}

.task-item.completed::after {
    content: '✓';
    float: right;
    color: #28a745;
    font-weight: bold;
}

.task-item.current {
    background-color: #cce5ff;
    border-left-color: #007bff;
    font-weight: bold;
}

.task-title {
    font-weight: bold;
    margin-bottom: 5px;
}

.task-description {
    color: #6c757d;
    font-size: 0.9em;
}

/* Position info styles */
.position-info {
    margin-top: 20px;
    padding: 15px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.position-info h3 {
    margin-bottom: 10px;
    color: #333;
}

.position-title {
    font-weight: bold;
    color: #007bff;
    margin-bottom: 5px;
}

.position-description {
    color: #6c757d;
    margin-bottom: 15px;
}

.progress-container {
    margin-top: 15px;
}

.progress-bar-container {
    height: 10px;
    background-color: #e9ecef;
    border-radius: 5px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background-color: #28a745;
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    margin-top: 5px;
    text-align: right;
    font-size: 0.8em;
    color: #6c757d;
}

/* Hide any system undefined text */
.org-chart::after,
.org-chart-content::after {
    display: none !important;
    visibility: hidden !important;
    content: none !important;
}

/* Hide any text that appears outside of the chart structure */
.org-chart > *:not(.org-level):not(.org-chart-content),
.org-chart-content > *:not(.org-level) {
    display: none !important;
}

/* Responsive styles */
@media (max-width: 768px) {
    .org-chart {
        gap: 20px;
    }

    .org-node {
        min-width: 120px;
        padding: 10px;
        font-size: 0.9em;
    }

    .org-level {
        flex-wrap: wrap;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    .org-chart-container {
        padding: 10px;
    }

    .org-node {
        min-width: 100px;
        padding: 8px;
        font-size: 0.8em;
    }

    .task-item {
        padding: 10px;
    }
}
