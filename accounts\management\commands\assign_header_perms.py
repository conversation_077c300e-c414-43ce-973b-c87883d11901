from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import Group, Permission, User
from accounts.models import Membership, Company
from guardian.shortcuts import assign_perm, get_perms

class Command(BaseCommand):
    help = 'Assigns required permissions (view_company_activity, view_company) to users in specific groups for their companies.'

    def handle(self, *args, **options):
        # Define permissions and the groups they apply to
        permissions_map = {
            'accounts.view_company_activity': ["Company Members", "Company Administrators"], # For header links
            'accounts.view_company': ["Company Administrators"], # For viewing team page
            'accounts.manage_invites_links': ["Company Administrators"], # For inviting members/managing links
        }
        app_label = 'accounts' # Assuming all perms are in the 'accounts' app

        # Verify all permissions exist
        for perm_full_name in permissions_map.keys():
            perm_app_label, perm_codename = perm_full_name.split('.')
            try:
                Permission.objects.get(content_type__app_label=perm_app_label, codename=perm_codename)
            except Permission.DoesNotExist:
                raise CommandError(f"Required permission '{perm_full_name}' not found. Make sure migrations are run.")

        # Get target group objects
        all_group_names = set(g for groups in permissions_map.values() for g in groups)
        target_groups = {}
        # Corrected loop: Iterate through the unique group names derived from permissions_map
        for group_name in all_group_names:
            try:
                target_groups[group_name] = Group.objects.get(name=group_name)
            except Group.DoesNotExist:
                self.stdout.write(self.style.WARNING(f"Group '{group_name}' not found. Skipping this group."))

        if not target_groups:
            raise CommandError("None of the required groups were found. Aborting.")

        self.stdout.write(f"Checking permissions for groups: {', '.join(target_groups.keys())}")
        self.stdout.write(f"Permissions map: {permissions_map}")

        total_assigned_count = 0
        total_skipped_count = 0
        processed_users_companies = set() # Track (user_id, company_id) pairs processed

        # Iterate through target groups and their users
        for group_name, group_obj in target_groups.items():
            self.stdout.write(f"\nProcessing group: '{group_name}'")
            # Find memberships for users in this group
            memberships_for_group = Membership.objects.filter(user__groups=group_obj).select_related('user', 'company')

            for membership in memberships_for_group:
                user = membership.user
                company = membership.company
                user_company_pair = (user.id, company.id)

                # Skip if we've already processed permissions for this user/company combo
                # This prevents redundant checks if a user is in multiple target groups (e.g., Admin and Member)
                if user_company_pair in processed_users_companies:
                    continue

                processed_users_companies.add(user_company_pair)

                # Get permissions already assigned to this user for this company
                current_perms = get_perms(user, company)

                # Determine which permissions *should* be assigned based on *all* groups the user belongs to
                perms_to_assign_for_user = set()
                user_groups = user.groups.filter(name__in=target_groups.keys()) # Check only relevant groups
                for user_group in user_groups:
                    for perm, applicable_groups in permissions_map.items():
                        if user_group.name in applicable_groups:
                            perms_to_assign_for_user.add(perm)


                # Assign missing permissions
                assigned_for_this_user = 0
                skipped_for_this_user = 0
                for perm_full_name in perms_to_assign_for_user:
                    if perm_full_name not in current_perms:
                        try:
                            assign_perm(perm_full_name, user, company)
                            self.stdout.write(self.style.SUCCESS(f"  Assigned '{perm_full_name}' to user '{user.username}' for company '{company.name}'"))
                            assigned_for_this_user += 1
                        except Exception as e:
                            self.stdout.write(self.style.ERROR(f"  Error assigning perm '{perm_full_name}' to user '{user.username}' for company '{company.name}': {e}"))
                            skipped_for_this_user += 1
                    else:
                        skipped_for_this_user += 1

                total_assigned_count += assigned_for_this_user
                total_skipped_count += skipped_for_this_user


        self.stdout.write(self.style.SUCCESS(f"\nCommand finished."))
        self.stdout.write(f"Total unique user/company pairs processed: {len(processed_users_companies)}")
        self.stdout.write(f"Total permissions assigned: {total_assigned_count}")
        self.stdout.write(f"Total permissions skipped (already present or error): {total_skipped_count}")
