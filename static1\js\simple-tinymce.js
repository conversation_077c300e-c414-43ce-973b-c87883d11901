/**
 * Simple TinyMCE Implementation with Custom Toolbar
 * This script creates a simplified version of TinyMCE with a custom toolbar
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize the simplified TinyMCE for elements with the 'simple-tinymce' class
    const simpleEditors = document.querySelectorAll('.simple-tinymce');
    
    simpleEditors.forEach(editorElement => {
        // Create the custom toolbar
        const toolbarContainer = document.createElement('div');
        toolbarContainer.className = 'tinymce-toolbar';
        
        // Group 1: Text formatting
        const formattingGroup = document.createElement('div');
        formattingGroup.className = 'toolbar-group';
        
        const boldButton = createToolbarButton('B', 'Bold', 'bold');
        const italicButton = createToolbarButton('I', 'Italic', 'italic');
        const underlineButton = createToolbarButton('U', 'Underline', 'underline');
        const strikeButton = createToolbarButton('S', 'Strikethrough', 'strikethrough');
        
        formattingGroup.appendChild(boldButton);
        formattingGroup.appendChild(italicButton);
        formattingGroup.appendChild(underlineButton);
        formattingGroup.appendChild(strikeButton);
        
        // Group 2: Alignment and lists
        const alignmentGroup = document.createElement('div');
        alignmentGroup.className = 'toolbar-group';
        
        const alignLeftButton = createToolbarButton('⫷', 'Align Left', 'alignleft');
        const alignCenterButton = createToolbarButton('≡', 'Align Center', 'aligncenter');
        const alignRightButton = createToolbarButton('⫸', 'Align Right', 'alignright');
        const justifyButton = createToolbarButton('☰', 'Justify', 'justify');
        const bulletListButton = createToolbarButton('•', 'Bullet List', 'bullist');
        const numberedListButton = createToolbarButton('1.', 'Numbered List', 'numlist');
        
        alignmentGroup.appendChild(alignLeftButton);
        alignmentGroup.appendChild(alignCenterButton);
        alignmentGroup.appendChild(alignRightButton);
        alignmentGroup.appendChild(justifyButton);
        alignmentGroup.appendChild(bulletListButton);
        alignmentGroup.appendChild(numberedListButton);
        
        // Group 3: Insert elements
        const insertGroup = document.createElement('div');
        insertGroup.className = 'toolbar-group';
        
        const linkButton = createToolbarButton('🔗', 'Insert Link', 'link');
        const imageButton = createToolbarButton('🖼️', 'Insert Image', 'image');
        const tableButton = createToolbarButton('⊞', 'Insert Table', 'table');
        
        insertGroup.appendChild(linkButton);
        insertGroup.appendChild(imageButton);
        insertGroup.appendChild(tableButton);
        
        // Add all groups to the toolbar
        toolbarContainer.appendChild(formattingGroup);
        toolbarContainer.appendChild(alignmentGroup);
        toolbarContainer.appendChild(insertGroup);
        
        // Insert the toolbar before the editor
        editorElement.parentNode.insertBefore(toolbarContainer, editorElement);
        
        // Initialize TinyMCE
        tinymce.init({
            target: editorElement,
            skin: 'oxide-dark',
            content_css: [
                '/static/css/tinymce-dark-theme.css',
                'dark'
            ],
            menubar: false,
            toolbar: false, // Hide the default toolbar
            statusbar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            height: 250,
            setup: function(editor) {
                // Add custom styling to the editor
                editor.on('init', function() {
                    editor.dom.addStyle(`
                        body {
                            background-color: #1a1a1a !important;
                            color: #e0e0e0 !important;
                            padding: 15px !important;
                            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif !important;
                        }
                        p, h1, h2, h3, h4, h5, h6, li, td, th {
                            color: #e0e0e0 !important;
                        }
                        a {
                            color: #4d81d6 !important;
                        }
                        table {
                            border-collapse: collapse !important;
                        }
                        table td, table th {
                            border: 1px solid #333 !important;
                            padding: 8px !important;
                        }
                        img {
                            max-width: 100% !important;
                            height: auto !important;
                            border-radius: 6px !important;
                        }
                    `);
                    
                    // Connect custom toolbar buttons to editor commands
                    connectToolbarButtons(editor, toolbarContainer);
                });
            }
        });
    });
    
    // Helper function to create toolbar buttons
    function createToolbarButton(text, title, command) {
        const button = document.createElement('button');
        button.textContent = text;
        button.title = title;
        button.setAttribute('data-command', command);
        button.setAttribute('data-button-fixed', 'true');
        return button;
    }
    
    // Helper function to connect toolbar buttons to TinyMCE commands
    function connectToolbarButtons(editor, toolbar) {
        const buttons = toolbar.querySelectorAll('button[data-command]');
        
        buttons.forEach(button => {
            button.addEventListener('click', function() {
                const command = this.getAttribute('data-command');
                
                // Handle different command types
                switch(command) {
                    case 'bold':
                    case 'italic':
                    case 'underline':
                    case 'strikethrough':
                    case 'alignleft':
                    case 'aligncenter':
                    case 'alignright':
                    case 'alignjustify':
                    case 'bullist':
                    case 'numlist':
                        editor.execCommand(command);
                        break;
                    case 'link':
                        editor.execCommand('mceLink');
                        break;
                    case 'image':
                        editor.execCommand('mceImage');
                        break;
                    case 'table':
                        editor.execCommand('mceInsertTable');
                        break;
                }
                
                // Toggle active state for formatting buttons
                if (['bold', 'italic', 'underline', 'strikethrough', 
                     'alignleft', 'aligncenter', 'alignright', 'alignjustify'].includes(command)) {
                    if (editor.queryCommandState(command)) {
                        this.classList.add('active');
                    } else {
                        this.classList.remove('active');
                    }
                }
            });
            
            // Update button state when selection changes
            editor.on('NodeChange', function() {
                const command = button.getAttribute('data-command');
                if (['bold', 'italic', 'underline', 'strikethrough', 
                     'alignleft', 'aligncenter', 'alignright', 'alignjustify'].includes(command)) {
                    if (editor.queryCommandState(command)) {
                        button.classList.add('active');
                    } else {
                        button.classList.remove('active');
                    }
                }
            });
        });
    }
});
