{% extends 'game/company/base.html' %}

{% block title %}Create Company{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">Create Your Company</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Create a company to invite team members and track their progress in the Corporate Prompt Master game.
                    </p>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">Company Name</label>
                            {{ form.name }}
                            {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if form.name.help_text %}
                            <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Create Company</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">What You Get</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-people-fill text-primary fs-4"></i>
                                </div>
                                <div class="ms-3">
                                    <h6>Team Management</h6>
                                    <p class="text-muted small mb-0">Invite team members and track their progress</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-trophy-fill text-primary fs-4"></i>
                                </div>
                                <div class="ms-3">
                                    <h6>Leaderboards</h6>
                                    <p class="text-muted small mb-0">Company-specific leaderboards to foster competition</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-book-fill text-primary fs-4"></i>
                                </div>
                                <div class="ms-3">
                                    <h6>Custom Courses</h6>
                                    <p class="text-muted small mb-0">Create custom courses tailored to your team's needs</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex mb-3">
                                <div class="flex-shrink-0">
                                    <i class="bi bi-graph-up text-primary fs-4"></i>
                                </div>
                                <div class="ms-3">
                                    <h6>Performance Analytics</h6>
                                    <p class="text-muted small mb-0">Track team performance and identify areas for improvement</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize form elements
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form elements
        const nameInput = document.getElementById('{{ form.name.id_for_label }}');
        if (nameInput) {
            nameInput.classList.add('form-control');
            nameInput.placeholder = 'Enter your company name';
        }
    });
</script>
{% endblock %}
