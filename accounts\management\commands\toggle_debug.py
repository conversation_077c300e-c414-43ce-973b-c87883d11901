import os
from django.core.management.base import BaseCommand
from django.conf import settings

class Command(BaseCommand):
    help = 'Toggle Django DEBUG setting between True and False'

    def handle(self, *args, **options):
        # Get settings file path
        settings_file = settings.BASE_DIR / 'company_assistant' / 'settings.py'
        
        if not os.path.exists(settings_file):
            self.stderr.write(
                self.style.ERROR(f'Settings file not found at {settings_file}')
            )
            return
        
        # Read current settings file
        with open(settings_file, 'r') as f:
            content = f.read()
        
        # Find DEBUG setting line
        debug_line = None
        debug_value = None
        new_lines = []
        
        for line in content.split('\n'):
            if line.strip().startswith('DEBUG = '):
                debug_line = line
                debug_value = 'True' if 'True' in line else 'False'
                # Toggle the value
                new_line = line.replace(debug_value, 'True' if debug_value == 'False' else 'False')
                new_lines.append(new_line)
            else:
                new_lines.append(line)
        
        if not debug_line:
            self.stderr.write(
                self.style.ERROR('DEBUG setting not found in settings.py')
            )
            return
        
        # Write updated content back to file
        try:
            with open(settings_file, 'w') as f:
                f.write('\n'.join(new_lines))
            
            new_value = 'True' if debug_value == 'False' else 'False'
            self.stdout.write(
                self.style.SUCCESS(f'Debug mode is now: {new_value}')
            )
            
            # Add warning if debug is enabled
            if new_value == 'True':
                self.stdout.write(
                    self.style.WARNING(
                        'Warning: Debug mode is enabled. Do not use in production!'
                    )
                )
        except Exception as e:
            self.stderr.write(
                self.style.ERROR(f'Error updating settings file: {str(e)}')
            )
            return
        
        # Remind about server restart
        self.stdout.write(
            self.style.WARNING(
                'Note: You need to restart the Django server for changes to take effect.'
            )
        )
