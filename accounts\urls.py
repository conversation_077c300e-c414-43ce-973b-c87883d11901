from django.urls import path
from django.contrib.auth import views as auth_views
from . import auth_views as views_module # Import the auth_views.py module directly
from .debug_views import debug_impersonation # Import debug view from separate file

# Import the custom login view directly
from django.contrib.auth.views import LoginView
from django.urls import reverse_lazy

class CustomLoginView(LoginView):
    def get_success_url(self):
        # Always redirect to the 'home' page regardless of 'next' parameter
        return reverse_lazy('home')

app_name = 'accounts'

urlpatterns = [
    # Authentication
    # Use the custom login view that always redirects to 'home'
    path('login/', CustomLoginView.as_view(template_name='accounts/login.html'), name='login'),
    path('logout/', auth_views.LogoutView.as_view(http_method_names=['post', 'get']), name='logout'),
    path('register/', views_module.register, name='register'),

    # Password Management
    path('password_change/', auth_views.PasswordChangeView.as_view(
        template_name='accounts/password_change.html',
        success_url=reverse_lazy('accounts:dashboard')
    ), name='password_change'),
    path('password_change/done/', auth_views.PasswordChangeDoneView.as_view(
        template_name='accounts/password_change_done.html'
    ), name='password_change_done'),
    path('password_reset/', auth_views.PasswordResetView.as_view(
        template_name='accounts/password_reset.html',
        email_template_name='accounts/password_reset_email.html',
        subject_template_name='accounts/password_reset_subject.txt'
    ), name='password_reset'),
    path('password_reset/done/', auth_views.PasswordResetDoneView.as_view(
        template_name='accounts/password_reset_done.html'
    ), name='password_reset_done'),
    path('reset/<uidb64>/<token>/', auth_views.PasswordResetConfirmView.as_view(
        template_name='accounts/password_reset_confirm.html'
    ), name='password_reset_confirm'),
    path('reset/done/', auth_views.PasswordResetCompleteView.as_view(
        template_name='accounts/password_reset_complete.html'
    ), name='password_reset_complete'),

    # User Management
    path('settings/', views_module.user_settings, name='user_settings'),
    path('logout-all/', views_module.logout_all, name='logout_all'), # Added logout_all URL
    path('dashboard/', views_module.dashboard, name='dashboard'),

    # Company Management (Internal/Dashboard)
    path('company/create/', views_module.company_create, name='company_create'),
    path('company/<int:company_id>/', views_module.company_detail, name='company_detail'), # Main company detail view
    path('company/<int:company_id>/manage/', views_module.company_detail, name='manage_company_detail'), # Renamed from company_detail, points to existing view
    path('company/<int:company_id>/settings/', views_module.company_settings, name='company_settings'), # Changed uuid to int
    path('community/<int:company_id>/settings/', views_module.community_settings, name='community_settings'), # Added community settings URL
    path('company/<int:company_id>/delete/', views_module.company_delete, name='company_delete'), # Added company delete URL
    path('company/<int:company_id>/transfer_ownership/', views_module.transfer_ownership, name='transfer_ownership'), # Added transfer ownership URL
    path('company/switch/', views_module.company_switch, name='company_switch'),

    # Company Public Detail (Slug-based, for QR codes)
    path('company/<slug:slug>/', views_module.public_company_detail_view, name='public_company_detail'), # New public view using slug

    # Team Management
    # Team Management
    path('company/<int:company_id>/team/', views_module.company_team, name='company_team'), # Changed uuid to int
    path('company/<int:company_id>/invite/', views_module.invite_member, name='invite_member'), # Changed uuid to int
    path('company/<int:company_id>/member/<int:user_id>/remove/', # Changed uuid to int
         views_module.remove_member, name='remove_member'),

    # Invitation Management
    path('accept/<str:token>/', views_module.accept_invitation, name='accept_invitation'),
    path('company/<int:company_id>/invitation/<int:invitation_id>/cancel/', # Changed uuid to int
         views_module.cancel_invitation, name='cancel_invitation'),
    path('company/<int:company_id>/invitation/<int:invitation_id>/resend/', # Changed uuid to int
         views_module.resend_invitation, name='resend_invitation'),

    # Registration Link
    path('join/<uuid:token>/', views_module.join_via_link, name='join_via_link'),
    path('reg-link-qr/<uuid:token>/', views_module.registration_link_qr_code, name='reg_link_qr'), # New URL for QR code
    path('links/<uuid:token>/deactivate/', views_module.deactivate_registration_link, name='deactivate_reg_link'), # Added deactivate URL
    path('links/<uuid:token>/delete/', views_module.delete_registration_link, name='delete_reg_link'), # Added delete URL

    # QR Code Generation
    path('company/<int:company_id>/generate-qr-code/', views_module.generate_company_qr_code, name='generate_company_qr_code'),

    path('delete-account/', views_module.delete_account, name='delete_account'), # Added delete_account URL (likely needed by modal)

    # Debug URLs
    path('debug/impersonation/', debug_impersonation, name='debug_impersonation'),
]
