{% load static account_tags rating_tags %}

{# Featured Company Card Partial - Enhanced design for carousel display #}
<div class="list-group-item position-relative directory-card" data-company-id="{{ listing.company.id }}">
    {# Show Featured badge #}
    {% if listing.company.is_featured %}
    <span class="badge bg-success position-absolute top-0 start-0 m-2" style="z-index: 10; font-size: 0.7em; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
        <i class="bi bi-star-fill me-1"></i>Featured
    </span>
    {% endif %}

    <div class="row g-4 text-center">
        {# Logo #}
        <div class="col-12 d-flex justify-content-center">
            <a href="{% url 'accounts:public_company_detail' slug=listing.company.slug %}" class="text-decoration-none">
                <div class="logo-container">
                    {% if listing.company.info.logo %}
                        <img src="{{ listing.company.info.logo.url }}" alt="{{ listing.company.name }} logo">
                    {% else %}
                        <div class="logo-placeholder">
                            <i class="bi bi-building-fill"></i>
                        </div>
                    {% endif %}
                </div>
            </a>
        </div>

        {# Company Name and Industry #}
        <div class="col-12">
            <h6 class="mb-2">
                <a href="{% url 'accounts:public_company_detail' slug=listing.company.slug %}" class="text-decoration-none directory-item-link-wrapper">
                    {{ listing.company.name }}
                </a>
            </h6>
            {% if listing.company.info.industry %}
            <p class="mb-2 text-muted">
                <i class="bi bi-building me-1"></i>{{ listing.company.info.industry }}
            </p>
            {% endif %}
            <div class="mb-3">
                {% for category in listing.categories.all %}
                    <span class="badge bg-secondary tag-badge">{{ category.name }}</span>
                {% endfor %}
                {% for tag in listing.tags %}
                    <span class="badge bg-light text-dark border tag-badge">{{ tag }}</span>
                {% endfor %}
            </div>
        </div>

        {# Description - Added for better information #}
        <div class="col-12">
            <p class="mb-3 item-description text-start">
                {{ listing.description|default:listing.company.info.description|default:"No description available."|truncatewords:15 }}
            </p>
        </div>

        {# Contact Info #}
        <div class="col-12 text-center mb-2">
            <ul class="list-unstyled small text-muted contact-info">
                {% if listing.company.info.city %}
                    <li class="mb-1">
                        <i class="bi bi-geo-alt"></i>
                        <span>{{ listing.company.info.city }}{% if listing.company.info.country %}, {{ listing.company.info.country }}{% endif %}</span>
                    </li>
                {% endif %}
                {% if listing.website|default:listing.company.info.website %}
                    <li>
                        <i class="bi bi-link-45deg"></i>
                        <span>{{ listing.website|default:listing.company.info.website }}</span>
                    </li>
                {% endif %}
            </ul>
        </div>

        {# Rating and Action Buttons #}
        <div class="col-12 d-flex justify-content-center align-items-center">
            {# Rating Display #}
            {% with avg_rating=listing.avg_rating total_ratings=listing.total_ratings %}
                {% if avg_rating > 0 %}
                    <div class="rating-display-container me-3" id="rating-display-company-{{ listing.company.id }}">
                        {% render_stars avg_rating total_ratings %}
                    </div>
                {% else %}
                    <div class="text-muted fst-italic me-3" id="no-rating-placeholder-{{ listing.company.id }}">(No ratings yet)</div>
                {% endif %}
            {% endwith %}

            {# Rate Button #}
            {% if user.is_authenticated %}
                <button type="button"
                        class="btn btn-outline-secondary btn-sm rate-company-btn action-btn me-2"
                        data-bs-toggle="modal"
                        data-bs-target="#ratingModal"
                        data-company-id="{{ listing.company.id }}"
                        data-company-name="{{ listing.company.name|escapejs }}">
                    <i class="bi bi-star"></i> Rate
                </button>

                {# Favorite Button #}
                <button
                    class="like-button btn btn-sm p-1 {% if listing.company.id in saved_company_ids %}text-danger{% else %}text-secondary{% endif %}"
                    data-item-id="{{ listing.company.id }}"
                    data-item-type="company"
                    title="{% if listing.company.id in saved_company_ids %}Unlike{% else %}Like{% endif %}"
                    style="background: none; border: none; cursor: pointer;">
                    <i class="bi bi-heart-fill" style="font-size: 1.2rem;"></i>
                </button>
            {% endif %}
        </div>
    </div>
</div>
