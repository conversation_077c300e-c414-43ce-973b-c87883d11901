/**
 * JavaScript to ensure the right toggle button is hidden in mobile and tablet modes
 */

// Execute when the DOM is fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Hide right toggle button immediately
    hideRightToggleButton();
    
    // Also hide on window resize
    window.addEventListener('resize', hideRightToggleButton);
    
    // Hide on orientation change (for mobile devices)
    window.addEventListener('orientationchange', hideRightToggleButton);
    
    // Set up a mutation observer to catch dynamically added toggle buttons
    setupMutationObserver();
});

/**
 * Hide the right toggle button using various selectors
 */
function hideRightToggleButton() {
    // Check if we're in mobile or tablet mode
    if (window.innerWidth <= 1024) {
        // Array of possible selectors for right toggle buttons
        const possibleSelectors = [
            '.right-sidebar-toggle',
            '.right-toggle',
            '.toggle-right',
            '#right-sidebar-toggle',
            '#right-toggle',
            '#toggle-right',
            '[class*="right-toggle"]',
            '[class*="toggle-right"]',
            '[id*="right-toggle"]',
            '[id*="toggle-right"]',
            '[aria-label*="right sidebar"]',
            '[aria-label*="Right sidebar"]',
            'button[onclick*="right"]',
            'a[onclick*="right"]',
            'div[onclick*="right"]',
            'span[onclick*="right"]',
            'button[data-target*="right"]',
            'a[data-target*="right"]',
            'div[data-target*="right"]',
            'span[data-target*="right"]',
            '[data-sidebar="right"]',
            '[data-target*="right-sidebar"]',
            '[data-toggle*="right-sidebar"]',
            '[data-controls*="right-sidebar"]'
        ];
        
        // Try each selector and hide matching elements
        possibleSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    hideElement(element);
                });
            } catch (error) {
                console.log('Error with selector:', selector, error);
            }
        });
        
        // Find elements positioned on the right side
        findAndHideRightPositionedElements();
        
        // Find elements with right-related text content
        findAndHideElementsByTextContent();
        
        // Find elements with right-pointing icons
        findAndHideElementsWithRightIcons();
    }
}

/**
 * Hide an element using multiple CSS properties
 * @param {Element} element - The element to hide
 */
function hideElement(element) {
    if (element) {
        element.style.display = 'none';
        element.style.visibility = 'hidden';
        element.style.opacity = '0';
        element.style.pointerEvents = 'none';
        element.style.position = 'absolute';
        element.style.left = '-9999px';
        element.style.width = '0';
        element.style.height = '0';
        element.style.overflow = 'hidden';
        
        // Add a class for additional CSS targeting
        element.classList.add('forcibly-hidden');
        
        // Disable the element if it's a button or link
        if (element.tagName === 'BUTTON' || element.tagName === 'A') {
            element.disabled = true;
            element.setAttribute('aria-hidden', 'true');
            element.tabIndex = -1;
        }
    }
}

/**
 * Find elements positioned on the right side of the screen
 */
function findAndHideRightPositionedElements() {
    // Get all elements
    const allElements = document.querySelectorAll('*');
    
    // Check each element's computed style
    allElements.forEach(element => {
        const style = window.getComputedStyle(element);
        
        // Check if it's positioned on the right
        if (style.position === 'fixed' || style.position === 'absolute') {
            const right = parseInt(style.right);
            if (!isNaN(right) && right < 50) {
                // Check if it looks like a toggle button
                if (isLikelyToggleButton(element)) {
                    hideElement(element);
                }
            }
        }
    });
}

/**
 * Find elements with text content related to right sidebar
 */
function findAndHideElementsByTextContent() {
    // Get all elements
    const allElements = document.querySelectorAll('button, a, span, div');
    
    // Check each element's text content
    allElements.forEach(element => {
        const text = element.textContent.toLowerCase();
        if (text.includes('right') && 
            (text.includes('sidebar') || text.includes('toggle') || text.includes('panel'))) {
            hideElement(element);
        }
    });
}

/**
 * Find elements with right-pointing icons
 */
function findAndHideElementsWithRightIcons() {
    // Common icon classes
    const iconSelectors = [
        'i.fa-chevron-right',
        'i.fa-chevron-left',
        'i.fa-angle-right',
        'i.fa-angle-left',
        'i.fa-caret-right',
        'i.fa-caret-left',
        'i.fa-arrow-right',
        'i.fa-arrow-left',
        'i.fa-bars',
        'i.fa-navicon',
        'i.fa-reorder',
        'i.fa-ellipsis-v',
        'i.fa-ellipsis-h',
        'svg[class*="arrow"]',
        'svg[class*="chevron"]',
        'svg[class*="angle"]',
        'svg[class*="caret"]'
    ];
    
    // Find icons
    iconSelectors.forEach(selector => {
        try {
            const icons = document.querySelectorAll(selector);
            icons.forEach(icon => {
                // Get the parent button or link
                const parent = findToggleParent(icon);
                if (parent) {
                    // Check if it's positioned on the right
                    const style = window.getComputedStyle(parent);
                    const right = parseInt(style.right);
                    if (!isNaN(right) && right < 100) {
                        hideElement(parent);
                    }
                }
            });
        } catch (error) {
            console.log('Error with icon selector:', selector, error);
        }
    });
}

/**
 * Find the parent element that's likely a toggle button
 * @param {Element} element - The element to check
 * @returns {Element|null} The parent toggle button or null
 */
function findToggleParent(element) {
    let current = element;
    for (let i = 0; i < 5; i++) { // Check up to 5 levels up
        if (!current.parentElement) {
            return null;
        }
        current = current.parentElement;
        if (isLikelyToggleButton(current)) {
            return current;
        }
    }
    return null;
}

/**
 * Check if an element is likely a toggle button
 * @param {Element} element - The element to check
 * @returns {boolean} True if the element is likely a toggle button
 */
function isLikelyToggleButton(element) {
    // Check tag name
    const tagName = element.tagName.toLowerCase();
    if (tagName === 'button' || tagName === 'a') {
        return true;
    }
    
    // Check class names
    const className = element.className.toLowerCase();
    if (className.includes('toggle') || className.includes('button') || className.includes('btn')) {
        return true;
    }
    
    // Check for click handlers
    if (element.hasAttribute('onclick') || element.hasAttribute('data-toggle') || 
        element.hasAttribute('data-target')) {
        return true;
    }
    
    // Check for small size (toggle buttons are usually small)
    const rect = element.getBoundingClientRect();
    if (rect.width < 50 && rect.height < 50) {
        return true;
    }
    
    return false;
}

/**
 * Set up a mutation observer to catch dynamically added toggle buttons
 */
function setupMutationObserver() {
    // Create an observer instance
    const observer = new MutationObserver(function(mutations) {
        // Check if we need to hide the right toggle button
        if (window.innerWidth <= 1024) {
            hideRightToggleButton();
        }
    });
    
    // Start observing the document with the configured parameters
    observer.observe(document.body, { 
        childList: true, 
        subtree: true 
    });
}
