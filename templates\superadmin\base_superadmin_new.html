<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Superadmin Dashboard - Corporate Prompt Master{% endblock %}</title>
    {% load static %}
    {% load i18n %}

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Base Layout CSS -->
    <style>
        :root {
            --sidebar-width: 250px;
            --header-height: 56px;
            --primary-color: #4e73df;
            --secondary-color: #858796;
            --success-color: #1cc88a;
            --info-color: #36b9cc;
            --warning-color: #f6c23e;
            --danger-color: #e74a3b;
            --light-color: #f8f9fc;
            --dark-color: #5a5c69;
        }

        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .main-header {
            height: var(--header-height);
            z-index: 1030;
            background-color: var(--primary-color);
            color: white;
        }

        .main-container {
            display: flex;
            flex: 1;
        }

        /* Sidebar styles are now handled by sidebar-hover.css */

        .main-content {
            flex: 1;
            /* margin-left is now handled by body padding in sidebar-hover.css */
            padding: 1.5rem;
            min-height: calc(100vh - var(--header-height)); /* Keep vertical spacing */
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>

    <!-- Custom CSS -->
    {% block extra_css %}
    <link rel="stylesheet" href="{% static 'game/css/sidebar-hover.css' %}">
    <style>
      /* Superadmin Custom Styles */
      .card {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
        margin-bottom: 1.5rem;
      }

      .card-header {
        background-color: white;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        padding: 1rem 1.25rem;
      }

      .breadcrumb {
        background-color: transparent !important;
        margin-bottom: 1.5rem;
        padding: 0;
      }

      /* Stats cards */
      .border-left-primary {
        border-left: 0.25rem solid var(--primary-color) !important;
      }

      .border-left-success {
        border-left: 0.25rem solid var(--success-color) !important;
      }

      .border-left-info {
        border-left: 0.25rem solid var(--info-color) !important;
      }

      .border-left-warning {
        border-left: 0.25rem solid var(--warning-color) !important;
      }

      .border-left-danger {
        border-left: 0.25rem solid var(--danger-color) !important;
      }

      .text-xs {
        font-size: 0.7rem;
      }

      .text-primary {
        color: var(--primary-color) !important;
      }

      .text-success {
        color: var(--success-color) !important;
      }

      .text-info {
        color: var(--info-color) !important;
      }

      .text-warning {
        color: var(--warning-color) !important;
      }

      .text-danger {
        color: var(--danger-color) !important;
      }

      .text-gray-300 {
        color: #dddfeb !important;
      }

      .text-gray-800 {
        color: #5a5c69 !important;
      }
    </style>
    {% endblock %}

    <!-- Additional head content -->
    {% block head_extras %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Include Sidebar -->
    {% include 'superadmin/sidebar.html' %}

    <!-- Main Content -->
    <div class="main-content">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="{% url 'superadmin:dashboard' %}">{% trans "Superadmin" %}</a></li>
                {% block breadcrumbs %}{% endblock %}
            </ol>
        </nav>

        <!-- Page Content -->
        <div class="container-fluid">
            {% block superadmin_content %}
            {# Specific superadmin page content goes here #}
            {% endblock superadmin_content %}
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.3.min.js"></script>

    <!-- Base Layout JS -->
    <script>
        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleButton = document.querySelector('.navbar-toggler');
            const sidebar = document.querySelector('.sidebar');

            if (toggleButton && sidebar) {
                toggleButton.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    sidebar.classList.remove('show');
                }
            });
        });
    </script>

    <!-- Custom JS -->
    {% block extra_js %}{% endblock %}
</body>
</html>
