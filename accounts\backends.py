from django.contrib.auth.backends import Model<PERSON>ackend
from django.contrib.auth import get_user_model
from django.db.models import Q

UserModel = get_user_model()

class EmailOrUsernameModelBackend(ModelBackend):
    """
    Authenticates against settings.AUTH_USER_MODEL using either username or email.
    """
    def authenticate(self, request, username=None, password=None, **kwargs):
        try:
            # Try to fetch the user by username or email
            user = UserModel.objects.get(Q(username__iexact=username) | Q(email__iexact=username))
        except UserModel.DoesNotExist:
            # Run the default password hasher once to reduce the timing
            # difference between an existing and a non-existing user (#20760).
            UserModel().set_password(password)
            return None
        except UserModel.MultipleObjectsReturned:
             # This case should ideally not happen if username/email are unique constraints,
             # but handle it just in case. Prefer username match if possible.
             user = UserModel.objects.filter(username__iexact=username).first()
             if not user:
                 user = UserModel.objects.filter(email__iexact=username).first()
             # If still multiple or none, authentication fails
             if not user or UserModel.objects.filter(Q(username__iexact=username) | Q(email__iexact=username)).count() > 1:
                 return None

        if user.check_password(password) and self.user_can_authenticate(user):
            return user
        return None

    def get_user(self, user_id):
        try:
            user = UserModel._default_manager.get(pk=user_id)
        except UserModel.DoesNotExist:
            return None
        return user if self.user_can_authenticate(user) else None
