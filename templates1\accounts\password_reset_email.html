{% autoescape off %}
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white !important;
            text-decoration: none;
            border-radius: 4px;
            margin: 20px 0;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            font-size: 0.9em;
            color: #666;
        }
        .important {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Password Reset Request</h1>
    </div>

    <p>Hello {{ user.get_full_name|default:user.username }},</p>

    <p>You're receiving this email because you requested a password reset for your 24seven account.</p>

    <div class="important">
        <p><strong>Username:</strong> {{ user.username }}</p>
        <p>Please click the button below to choose a new password:</p>
    </div>

    <div style="text-align: center;">
        <a href="{{ protocol }}://{{ domain }}{% url 'accounts:password_reset_confirm' uidb64=uid token=token %}"
           class="button">
            Reset Password
        </a>
    </div>

    <p>If the button above doesn't work, you can also copy and paste this link into your browser:</p>
    <p style="word-break: break-all;">
        {{ protocol }}://{{ domain }}{% url 'accounts:password_reset_confirm' uidb64=uid token=token %}
    </p>

    <div class="important">
        <p><strong>Important:</strong></p>
        <ul>
            <li>This link will expire in 24 hours.</li>
            <li>For security, this link can only be used once.</li>
            <li>If you did not request this password reset, you can safely ignore this email.</li>
        </ul>
    </div>

    <div class="footer">
        <p>
            Thanks,<br>
            The 24seven Team
        </p>
        <p style="font-size: 0.8em;">
            This is an automated message, please do not reply to this email.<br>
            If you need assistance, <NAME_EMAIL>
        </p>
    </div>
</body>
</html>
{% endautoescape %}
