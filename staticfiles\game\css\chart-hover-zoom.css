/* Chart Double-Click Zoom Styles */

/* Common styles for double-click zoom containers */
.double-click-zoom-container {
    position: relative;
    cursor: pointer;
}

/* Zoom indicator - Enhanced */
.zoom-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(59, 130, 246, 0.9);
    color: white;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    opacity: 0.9;
    transition: all 0.3s ease;
    z-index: 5;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    border: 2px solid rgba(255, 255, 255, 0.7);
}

.zoom-indicator::after {
    content: 'Double-click to zoom';
    position: absolute;
    bottom: -30px;
    right: 0;
    background-color: #334155;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transform: translateY(5px);
    transition: all 0.2s ease;
    pointer-events: none;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.zoom-indicator:hover::after {
    opacity: 1;
    transform: translateY(0);
}

.zoom-indicator:hover {
    transform: scale(1.15);
    background-color: rgba(59, 130, 246, 1);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.5);
    border-color: white;
}

/* Zoom overlay - Enhanced Version */
.zoom-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 23, 42, 0.75);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.4s ease, visibility 0.4s ease;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
}

.zoom-overlay.visible {
    opacity: 1;
    visibility: visible;
}

.zoom-content {
    background-color: #ffffff;
    border-radius: 16px;
    padding: 30px;
    width: 90%;
    max-width: 1400px;
    height: auto;
    max-height: 90vh;
    position: relative;
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
    transform: translateY(20px) scale(0.98);
    transition: transform 0.4s cubic-bezier(0.16, 1, 0.3, 1);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.08);
}

.zoom-overlay.visible .zoom-content {
    transform: translateY(0) scale(1);
}

.zoom-content-inner {
    flex: 1;
    overflow: auto;
    padding: 10px;
    display: flex;
    align-items: flex-start;
    justify-content: center;
    min-height: 500px;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.zoom-content-inner::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.zoom-content-inner::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 10px;
}

.zoom-content-inner::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 10px;
    border: 2px solid #f1f5f9;
}

.zoom-content-inner::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
}

.zoom-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background-color: #ef4444; /* Red background for better visibility */
    color: white; /* White icon for contrast */
    border: 2px solid white; /* White border for emphasis */
    border-radius: 50%;
    width: 44px; /* Larger size */
    height: 44px; /* Larger size */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 24px; /* Larger icon */
    z-index: 10;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2); /* Stronger shadow */
}

/* Add a "CLOSE" label below the button */
.zoom-close::after {
    content: 'CLOSE';
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 12px;
    font-weight: bold;
    color: #333;
    background-color: white;
    padding: 2px 8px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.zoom-close:hover {
    background-color: #dc2626; /* Darker red on hover */
    color: white;
    transform: scale(1.15); /* Slightly larger on hover */
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3); /* Stronger shadow on hover */
}

/* Zoom modal title - Enhanced */
.zoom-modal-title {
    margin-top: 0;
    margin-bottom: 25px;
    color: #1e293b;
    text-align: center;
    font-size: 28px;
    font-weight: 600;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 15px;
    letter-spacing: 0.5px;
    position: relative;
}

.zoom-modal-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background-color: #3b82f6;
}

/* Specific styles for org chart zoom - Enhanced */
.org-chart-zoom .zoom-content {
    background-color: #ffffff;
    background-image: linear-gradient(to bottom, #f8fafc, #ffffff);
}

.zoomed-org-chart-container {
    width: 100%;
    overflow: auto;
    padding: 20px;
    background-color: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.03);
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
    /* Improved scrolling behavior */
    scroll-behavior: smooth;
    max-height: 70vh; /* Limit height to ensure scrolling is needed */
}

/* Dark mode for zoomed org chart container */
html.dark-mode .zoomed-org-chart-container,
body.dark-mode .zoomed-org-chart-container {
    background-color: #1e1e1e;
    border-color: #444;
    box-shadow: inset 0 2px 5px rgba(255, 255, 255, 0.03);
    scrollbar-color: #555 #333;
}

.zoomed-org-chart-container::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.zoomed-org-chart-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 10px;
}

.zoomed-org-chart-container::-webkit-scrollbar-thumb {
    background-color: #cbd5e1;
    border-radius: 10px;
    border: 2px solid #f1f5f9;
}

.zoomed-org-chart-container::-webkit-scrollbar-thumb:hover {
    background-color: #94a3b8;
}

/* Dark mode scrollbar styles */
html.dark-mode .zoomed-org-chart-container::-webkit-scrollbar-track,
body.dark-mode .zoomed-org-chart-container::-webkit-scrollbar-track {
    background: #333;
}

html.dark-mode .zoomed-org-chart-container::-webkit-scrollbar-thumb,
body.dark-mode .zoomed-org-chart-container::-webkit-scrollbar-thumb {
    background-color: #555;
    border: 2px solid #333;
}

html.dark-mode .zoomed-org-chart-container::-webkit-scrollbar-thumb:hover,
body.dark-mode .zoomed-org-chart-container::-webkit-scrollbar-thumb:hover {
    background-color: #666;
}

.org-chart-zoom .org-chart-content {
    transform: scale(1);
    transform-origin: center top;
    padding: 30px;
    background-color: #ffffff;
    width: 100%;
    height: auto;
    margin: 0 auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    transition: background-color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Dark mode for org chart zoom content */
html.dark-mode .org-chart-zoom .org-chart-content,
body.dark-mode .org-chart-zoom .org-chart-content {
    background-color: #e9ecef;
    border-color: #dee2e6;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Specific styles for role progression zoom */
.role-progression-zoom .zoom-content {
    background-color: #ffffff;
    transition: background-color 0.3s ease;
}

.zoomed-role-progression-container {
    width: 100%;
    overflow: auto;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 8px;
    border: 1px solid #eee;
    transition: background-color 0.3s ease, border-color 0.3s ease;
    /* Improved scrolling behavior */
    scroll-behavior: smooth;
    max-height: 70vh; /* Limit height to ensure scrolling is needed */
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Dark mode for role progression zoom */
html.dark-mode .role-progression-zoom .zoom-content,
body.dark-mode .role-progression-zoom .zoom-content {
    background-color: #252525;
}

html.dark-mode .zoomed-role-progression-container,
body.dark-mode .zoomed-role-progression-container {
    background-color: #1e1e1e;
    border-color: #444;
}

.role-progression-zoom .role-progression {
    transform: scale(1);
    transform-origin: center center;
    padding: 20px;
    background-color: #ffffff;
    width: 100%;
    height: auto;
    margin: 0 auto;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* Hover effect for charts */
.double-click-zoom-container {
    transition: box-shadow 0.3s, transform 0.3s;
    position: relative;
}

.double-click-zoom-container:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

/* Zoom controls - Enhanced */
.zoom-controls {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 12px;
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 10px 16px;
    border-radius: 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
}

.zoom-controls:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    transform: translateX(-50%) translateY(-2px);
}

.zoom-control-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8fafc;
    border: 1px solid #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 18px;
    transition: all 0.2s ease;
    color: #475569;
    position: relative;
}

.zoom-control-btn::after {
    content: attr(title);
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%) scale(0);
    background-color: #334155;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    transition: all 0.2s ease;
    pointer-events: none;
}

.zoom-control-btn:hover::after {
    opacity: 1;
    transform: translateX(-50%) scale(1);
}

.zoom-control-btn:hover {
    background-color: #e2e8f0;
    color: #1e293b;
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.zoom-control-btn.zoom-in {
    background-color: #ebf5ff;
    color: #3b82f6;
}

.zoom-control-btn.zoom-out {
    background-color: #fff1f2;
    color: #ef4444;
}

.zoom-control-btn.zoom-reset {
    background-color: #f0fdf4;
    color: #22c55e;
}

/* Scroll indicator */
.scroll-indicator {
    position: absolute;
    bottom: 20px;
    left: 20px;
    background-color: #3b82f6;
    color: white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    animation: pulse 2s infinite;
}

.scroll-indicator::after {
    content: 'Use mouse wheel to scroll';
    position: absolute;
    left: 50px;
    white-space: nowrap;
    background-color: #334155;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Chart placeholder */
.chart-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200px;
    background-color: #f9f9f9;
    border-radius: 8px;
    color: #666;
    font-style: italic;
    padding: 20px;
    text-align: center;
    border: 1px dashed #ddd;
}

/* Hide HTML comments that might be visible */
.org-chart-content:after,
.role-progression:after {
    display: none !important;
    visibility: hidden !important;
    content: none !important;
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .zoom-content {
        width: 95%;
        max-height: 85vh;
        padding: 15px;
        border-radius: 8px;
    }

    .org-chart-zoom .org-chart-content,
    .role-progression-zoom .role-progression {
        transform: scale(0.9);
        padding: 5px;
    }

    .zoom-close {
        top: 8px;
        right: 8px;
        width: 36px; /* Still larger than original but smaller than desktop */
        height: 36px;
        font-size: 18px;
    }

    /* Ensure the CLOSE label is visible on mobile */
    .zoom-close::after {
        bottom: -22px;
        font-size: 10px;
        padding: 1px 6px;
    }

    /* Mobile styles for scroll indicator */
    .scroll-indicator {
        bottom: 10px;
        left: 10px;
        width: 32px;
        height: 32px;
    }

    .scroll-indicator::after {
        content: 'Scroll';
        left: 40px;
        font-size: 10px;
        padding: 3px 6px;
    }

    .zoom-controls {
        bottom: 10px;
        padding: 6px 10px;
    }

    .zoom-control-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .zoom-content-inner {
        min-height: 300px;
    }
}
