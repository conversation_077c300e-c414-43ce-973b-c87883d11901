/* Dark and Light Mode Styles */

:root {
    /* Light mode variables (default) */
    --bg-color: #f5f5f5;
    --main-bg: #fff;
    --text-color: #333;
    --sidebar-bg: #f8f9fa;
    --sidebar-border: #e0e0e0;
    --header-bg: #f8f9fa;
    --header-border: #e0e0e0;
    --message-bg: #f0f2f5;
    --message-you-bg: #e3f2fd;
    --ai-message-bg: #f0f7ea;
    --ai-message-border: #4caf50;
    --preview-bg: #e6f2ff;
    --preview-border: #b8d4ff;
    --similarity-bg: #f8f9fa;
    --similarity-border: #e0e0e0;
    --manager-feedback-bg: #fffdf7;
    --manager-feedback-border: #ffca28;
    --button-primary-bg: #4caf50;
    --button-primary-text: white;
    --button-secondary-bg: #f0f0f0;
    --button-secondary-text: #333;
    --button-preview-bg: #4a86e8;
    --button-preview-text: white;
    --input-border: #ccc;
    --input-focus-border: #4a86e8;
    --modal-bg: white;
    --modal-overlay: rgba(0, 0, 0, 0.5);
    --context-info-bg: #f0f7ff;
    --context-info-border: #d0e3ff;
    --context-label-color: #0066cc;
    --context-value-bg: #e6f0ff;
    --context-value-border: #c0d6ff;
    --org-chart-bg: #f8f9fa;
    --org-chart-border: #e0e0e0;
    --current-role-bg: #e3f2fd;
    --current-role-border: #4a86e8;
    --completed-role-bg: #e8f5e9;
    --completed-role-border: #4caf50;
    --future-role-opacity: 0.7;
    --code-bg: #f0f0f0;
    --blockquote-border: #ccc;
    --blockquote-color: #666;
    --toggle-bg: #333;
    --progress-bar-bg: #4caf50;
    --progress-container-bg: #e0e0e0;
    --avatar-bg: #4a86e8;
    --avatar-color: white;
    --role-display-bg: #f8f9fa;
    --role-display-color: #333;
    --quality-good-bg: #4caf50;
    --quality-okay-bg: #ff9800;
    --quality-bad-bg: #f44336;
    --scrollbar-track: #f1f1f1;
    --scrollbar-thumb: #c1c1c1;
    --scrollbar-thumb-hover: #a1a1a1;
    --preview-content-bg: #f8f9fa;
    --preview-header-bg: #e6f2ff;
    --preview-actions-bg: #f8f9fa;
    --improvement-feedback-bg: #fff;
    --improvement-feedback-border: #e9ecef;
    --zoom-modal-header-bg: #f8f9fa;
    --zoom-modal-content-bg: #fff;
}

/* Dark mode styles */
html.dark-mode,
body.dark-mode {
    /* New Theme Variables */
    --text-color-bright: #FFFFFF;
    --accent-color-blue: #007ACC;
    --accent-color-blue-rgb: 0, 122, 204; /* Added for rgba() usage */
    --border-color-subtle: #444444;

    /* Base colors */
    --bg-color: #1E1E1E; /* Updated for new theme */
    --main-bg: #2D2D30;  /* Updated for new theme - panel bg */
    --text-color: #e0e0e0; /* Remains light gray */

    /* Sidebar colors */
    --sidebar-bg: #2D2D30; /* Use panel bg */
    --sidebar-border: var(--border-color-subtle); /* Use new subtle border */

    /* Header colors */
    --header-bg: #2D2D30; /* Use panel bg */
    --header-border: var(--border-color-subtle); /* Use new subtle border */

    /* Message colors */
    --message-bg: #2a2a2a;
    --message-you-bg: #1a3a5a;
    --ai-message-bg: #1e3b1e;
    --ai-message-border: #2e7d32;

    /* Preview colors */
    --preview-bg: #1a2a3a;
    --preview-border: #2a4a6a;
    --similarity-bg: #252525;
    --similarity-border: #333;
    --manager-feedback-bg: #2a2a1a;
    --manager-feedback-border: #b38f00;
    --preview-content-bg: #1a2a3a;
    --preview-header-bg: #1a2a3a;
    --preview-actions-bg: #252525;
    --improvement-feedback-bg: #252525;
    --improvement-feedback-border: #333;
    --zoom-modal-header-bg: #1e1e1e;
    --zoom-modal-content-bg: #252525;

    /* Button colors */
    --button-primary-bg: var(--accent-color-blue);
    --button-primary-text: var(--text-color-bright);
    --button-secondary-bg: #333; /* Keeping secondary as is for now */
    --button-secondary-text: #e0e0e0; /* Keeping secondary as is for now */
    --button-preview-bg: var(--accent-color-blue); /* Align preview button with accent */
    --button-preview-text: var(--text-color-bright); /* Align preview button with accent */

    /* Input colors */
    --input-border: var(--border-color-subtle);
    --input-focus-border: var(--accent-color-blue);

    /* Modal colors */
    --modal-bg: #252525;
    --modal-overlay: rgba(0, 0, 0, 0.7);

    /* Context info colors */
    --context-info-bg: #1a2a3a;
    --context-info-border: #2a4a6a;
    --context-label-color: #4a86e8;
    --context-value-bg: #1a3a5a;
    --context-value-border: #2a4a6a;

    /* Organization chart colors */
    --org-chart-bg: #252525;
    --org-chart-border: #333;
    --current-role-bg: #1a3a5a;
    --current-role-border: #4a86e8;
    --completed-role-bg: #1e3b1e;
    --completed-role-border: #2e7d32;
    --future-role-opacity: 0.5;

    /* Markdown content colors */
    --code-bg: #2a2a2a;
    --blockquote-border: #444;
    --blockquote-color: #aaa;

    /* Misc colors */
    --toggle-bg: #e0e0e0;
    --progress-bar-bg: #1a5fb4;
    --progress-container-bg: #333;
    --avatar-bg: #1a5fb4;
    --avatar-color: #e0e0e0;
    --role-display-bg: #1e1e1e;
    --role-display-color: #e0e0e0;
    --quality-good-bg: #2e7d32;
    --quality-okay-bg: #b38f00;
    --quality-bad-bg: #c62828;

    /* Scrollbar colors */
    --scrollbar-track: #1e1e1e;
    --scrollbar-thumb: #444;
    --scrollbar-thumb-hover: #555;
}
/* Right Sidebar Dark Mode Specifics */

/* Ensure general text color for the right sidebar */
html.dark-mode .right-sidebar {
    color: var(--text-color); /* --text-color is #e0e0e0 in dark mode */
}

/* Section Title: "CAREER INFORMATION" which is .right-sidebar-title */
html.dark-mode .right-sidebar .right-sidebar-title {
    /* color: var(--text-color); is already handled by the variable in the global style */
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 10px;
}

/* Section Title: "Company Hierarchy" (h3 in org-chart-container) */
html.dark-mode .right-sidebar #org-chart-container > h3 {
    color: var(--text-color-bright); /* Brighter white for this title */
    font-size: 1.1em;
    margin-bottom: 10px;
}

/* Section Title: "Career Path" (h3 in role-progression-header) */
html.dark-mode .right-sidebar #role-progression-container .role-progression-header > h3 {
    color: var(--text-color); /* Light gray */
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 10px;
}

/* Organization Chart Nodes within Right Sidebar */
html.dark-mode .right-sidebar #org-chart .org-node {
    background-color: #3c3c3c; /* Medium dark gray */
    color: var(--text-color); /* Light gray text */
    border: 1px solid var(--border-color-subtle);
    border-radius: 4px;
    padding: 5px 10px;
    text-align: center;
}

/* Org Chart Connector Lines */
html.dark-mode .right-sidebar #org-chart .org-level::before {
    background-color: var(--border-color-subtle);
}

/* Career Path Items within Right Sidebar */
/* Using assumed classes .path-item and .current-path-step */
html.dark-mode .right-sidebar #role-progression-content .path-item { /* Default item */
    color: var(--text-color); /* Light gray text */
    padding: 5px 0;
}

html.dark-mode .right-sidebar #role-progression-content .path-item.current-path-step { /* Active item */
    background-color: #3c3c3c; /* Dark node-like background */
    color: var(--text-color-bright); /* Brighter text */
    padding: 5px 10px;
    border-radius: 4px;
    display: inline-block;
}

/* Current Role Display (using hypothetical IDs from prompt) */
html.dark-mode .right-sidebar #current-role-display {
    margin-top: 20px;
    text-align: center;
    color: var(--text-color);
}

html.dark-mode .right-sidebar #current-role-display .current-role-label {
    color: var(--text-color);
}

html.dark-mode .right-sidebar #current-role-display .current-role-value {
    color: var(--text-color-bright);
    font-weight: bold;
}
/* General button styling */
button {
    background-color: var(--button-primary-bg); /* Uses themed primary button colors */
    color: var(--button-primary-text);
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s ease, color 0.3s ease, opacity 0.3s ease;
}

button:hover {
    opacity: 0.9;
}

/* Apply theme variables */
html, body {
    font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.app-container {
    background-color: var(--main-bg);
    transition: background-color 0.3s ease;
}

/* Sidebar styling */
.sidebar {
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--sidebar-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.sidebar-header {
    border-bottom: 1px solid var(--sidebar-border);
    transition: border-color 0.3s ease;
}

.character-info, .game-stats {
    border-bottom: 1px solid var(--sidebar-border);
    transition: border-color 0.3s ease;
}

.logo h1, .subtitle {
    color: var(--text-color);
    transition: color 0.3s ease;
}

.avatar {
    background-color: var(--avatar-bg);
    color: var(--avatar-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.character-name, .character-title, .stat-label, .stat-value {
    color: var(--text-color);
    transition: color 0.3s ease;
}

.progress-container {
    background-color: var(--progress-container-bg);
    transition: background-color 0.3s ease;
}

.progress-bar {
    background-color: var(--progress-bar-bg);
    transition: background-color 0.3s ease;
}

/* Context info styling */
.context-info {
    background-color: var(--context-info-bg);
    border: 1px solid var(--context-info-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.context-label {
    color: var(--context-label-color);
    transition: color 0.3s ease;
}

#current-manager, #current-task {
    background-color: var(--context-value-bg);
    border: 1px solid var(--context-value-border);
    color: var(--text-color);
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Header styling */
.header {
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--header-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.role-display {
    color: var(--role-display-color);
    background-color: var(--role-display-bg);
    transition: color 0.3s ease, background-color 0.3s ease;
}

.mobile-sidebar-toggle span {
    background-color: var(--text-color);
    transition: background-color 0.3s ease;
}

/* Messages styling */
.messages-container {
    background-color: var(--bg-color);
    transition: background-color 0.3s ease;
}

.message-content {
    background-color: var(--message-bg);
    transition: background-color 0.3s ease;
}

.message.from-you .message-content {
    background-color: var(--message-you-bg);
    transition: background-color 0.3s ease;
}

.message-content.ai-response {
    background-color: var(--ai-message-bg);
    border-left: 4px solid var(--ai-message-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.message-sender {
    color: var(--text-color);
    transition: color 0.3s ease;
}

/* Ensure CEO name is readable in dark mode header */
body.dark-mode .header .message-sender-ceo,
html.dark-mode .header .message-sender-ceo {
    color: #ffffff;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.25) 0%, transparent 100%);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: 700;
    letter-spacing: 0.03em;
}

/* Special styling for the CEO name in the top navigation bar */
.nav-ceo-name {
    color: #ffffff;
    background-color: rgba(0, 0, 0, 0.5);
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Preview styling */
.preview-content {
    background-color: var(--preview-content-bg);
    border: 1px solid var(--preview-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.preview-header {
    background-color: var(--preview-header-bg);
    transition: background-color 0.3s ease;
}

.preview-actions {
    background-color: var(--preview-actions-bg);
    transition: background-color 0.3s ease;
}

.similarity-container {
    background-color: var(--similarity-bg);
    border: 1px solid var(--similarity-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.manager-feedback-content {
    background-color: var(--manager-feedback-bg);
    border-left: 3px solid var(--manager-feedback-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.improvement-feedback {
    background-color: var(--improvement-feedback-bg);
    border: 1px solid var(--improvement-feedback-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.zoom-modal-header {
    background-color: var(--zoom-modal-header-bg);
    transition: background-color 0.3s ease;
}

.zoomed-preview-content {
    background-color: var(--zoom-modal-content-bg);
    transition: background-color 0.3s ease;
}

.quality-indicator.good {
    background-color: var(--quality-good-bg);
    transition: background-color 0.3s ease;
}

.quality-indicator.okay {
    background-color: var(--quality-okay-bg);
    transition: background-color 0.3s ease;
}

.quality-indicator.bad {
    background-color: var(--quality-bad-bg);
    transition: background-color 0.3s ease;
}

/* Input area styling */
.input-area {
    background-color: var(--main-bg);
    border-top: 1px solid var(--sidebar-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.prompt-input, .response-editor {
    background-color: var(--main-bg);
    color: var(--text-color);
    border: 1px solid var(--input-border);
    padding: 10px; /* Added padding */
    border-radius: 5px; /* Added border-radius */
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

.prompt-input:focus, .response-editor:focus {
    border-color: var(--input-focus-border);
    box-shadow: 0 0 0 2px rgba(74, 134, 232, 0.2);
}

/* Button styling */
.preview-button {
    background-color: var(--button-preview-bg);
    color: var(--button-preview-text);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.primary-button {
    background-color: var(--button-primary-bg);
    color: var(--button-primary-text);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.secondary-button {
    background-color: var(--button-secondary-bg);
    color: var(--button-secondary-text);
    border: 1px solid var(--sidebar-border);
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

/* Right sidebar styling */
.right-sidebar {
    background-color: var(--sidebar-bg);
    border-left: 1px solid var(--sidebar-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.right-sidebar-header {
    border-bottom: 1px solid var(--sidebar-border);
    transition: border-color 0.3s ease;
}

.right-sidebar-title {
    color: var(--text-color);
    transition: color 0.3s ease;
}

/* Organization chart styling */
.org-chart {
    background-color: var(--org-chart-bg);
    border: 1px solid var(--org-chart-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.hierarchy-role {
    background-color: var(--main-bg);
    border: 1px solid var(--sidebar-border);
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

.hierarchy-role.current-role {
    border: 2px solid var(--current-role-border);
    background-color: var(--current-role-bg);
}

.hierarchy-role.completed-role {
    border: 1px solid var(--completed-role-border);
    background-color: var(--completed-role-bg);
}

.hierarchy-role.future-role {
    opacity: var(--future-role-opacity);
}

/* Modal styling */
.modal {
    background-color: var(--modal-overlay);
    transition: background-color 0.3s ease;
}

.modal-content {
    background-color: var(--modal-bg);
    transition: background-color 0.3s ease;
}

.modal-content h2, .modal-content p {
    color: var(--text-color);
    transition: color 0.3s ease;
}

/* Toggle button for dark/light mode */
.theme-toggle {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background-color: var(--toggle-bg);
    color: var(--bg-color);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    border: none;
    font-size: 22px;
    transition: all 0.3s ease;
    opacity: 0.9;
}

.theme-toggle:hover {
    transform: scale(1.1);
    opacity: 1;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

html.dark-mode .theme-toggle,
body.dark-mode .theme-toggle {
    background-color: #f0f0f0;
    color: #121212;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

html:not(.dark-mode) .theme-toggle,
body:not(.dark-mode) .theme-toggle {
    background-color: #333;
    color: #f5f5f5;
}

html.dark-mode .theme-toggle:hover,
body.dark-mode .theme-toggle:hover {
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.6);
}

/* Markdown content styling */
.message-text code {
    background-color: var(--code-bg);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.message-text pre {
    background-color: var(--code-bg);
    color: var(--text-color);
    transition: background-color 0.3s ease, color 0.3s ease;
}

.message-text pre code {
    background-color: transparent;
}

.message-text blockquote {
    border-left: 3px solid var(--blockquote-border);
    color: var(--blockquote-color);
    transition: border-color 0.3s ease, color 0.3s ease;
}

.message-text th {
    background-color: var(--code-bg);
    transition: background-color 0.3s ease;
}

.message-text td, .message-text th {
    border: 1px solid var(--sidebar-border);
    transition: border-color 0.3s ease;
}

/* Loading overlay */
.loading-overlay {
    background-color: var(--modal-overlay);
    transition: background-color 0.3s ease;
}

.loading-text {
    color: var(--text-color);
    transition: color 0.3s ease;
}

/* Additional dark mode fixes */
body.dark-mode .sidebar-collapse-indicator,
body.dark-mode .right-sidebar-collapse-indicator {
    background-color: #333;
    color: #aaa;
}

body.dark-mode .mobile-sidebar-toggle span {
    background-color: #e0e0e0;
}

body.dark-mode .right-sidebar-toggle span {
    background-color: #4a86e8;
}

body.dark-mode .message-time {
    color: #aaa;
}

body.dark-mode .preview-header h3,
body.dark-mode .edit-header h3,
body.dark-mode .zoom-modal-header h2 {
    color: #e0e0e0;
}

body.dark-mode .preview-content {
    color: #e0e0e0;
}

body.dark-mode .similarity-label,
body.dark-mode .feedback-details-label,
body.dark-mode .section-scores-label,
body.dark-mode .prompt-evaluation-label {
    color: #aaa;
}

body.dark-mode .feedback-details-list li {
    color: #ccc;
}

body.dark-mode .similarity-score {
    color: #4a86e8;
}

body.dark-mode .instructions h3,
body.dark-mode .org-chart-container h3,
body.dark-mode .role-progression-header h3 {
    color: #e0e0e0;
}

body.dark-mode .org-chart-container h2,
html.dark-mode .org-chart-container h2 {
    color: #d1d5d9;
}

body.dark-mode .role-progression h3,
html.dark-mode .role-progression h3 {
    color: #dbd4d4;
}

/* Dark mode for org-chart-container */
body.dark-mode .org-chart-container,
html.dark-mode .org-chart-container {
    background-color: #252525;
    border-color: #444;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Dark mode for role-progression-container */
body.dark-mode .role-progression-container,
html.dark-mode .role-progression-container {
    background-color: #1e1e1e;
    border-color: #444;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Dark mode for role-progression-header */
body.dark-mode .role-progression-header,
html.dark-mode .role-progression-header {
    background-color: #252525;
    border-color: #444;
}

/* Dark mode for role-progression-content */
body.dark-mode .role-progression-content,
html.dark-mode .role-progression-content {
    background-color: #1e1e1e;
    color: #e0e0e0;
}

/* Dark mode for role nodes and connectors */
body.dark-mode .department-header,
html.dark-mode .department-header {
    color: #aaa;
}

body.dark-mode .department-header.active-department,
html.dark-mode .department-header.active-department {
    background-color: #1a3a5a;
    color: #90caf9;
}

body.dark-mode .current-role,
html.dark-mode .current-role {
    color: #ccc;
}

body.dark-mode .current-role strong,
html.dark-mode .current-role strong {
    color: #90caf9;
}

body.dark-mode .career-progress,
html.dark-mode .career-progress {
    color: #aaa;
}

body.dark-mode .progress-bar,
html.dark-mode .progress-bar {
    background-color: #333;
}

body.dark-mode .role-node::after,
html.dark-mode .role-node::after {
    background-color: #444;
    color: #e0e0e0;
}

/* Dark mode for context-info */
body.dark-mode .context-info,
html.dark-mode .context-info {
    background-color: #252525;
    border: 1px solid #444;
    color: #e0e0e0;
}

body.dark-mode .context-label,
html.dark-mode .context-label {
    color: #90caf9;
}

/* Dark mode for enhanced evaluation container */
body.dark-mode .enhanced-evaluation-container,
html.dark-mode .enhanced-evaluation-container {
    background-color: #252525;
    border-left-color: #1a5fb4;
}

body.dark-mode .enhanced-evaluation-label,
html.dark-mode .enhanced-evaluation-label {
    color: #e0e0e0;
}

body.dark-mode .improvement-feedback,
html.dark-mode .improvement-feedback {
    background-color: #1e1e1e;
    border-color: #444;
    color: #e0e0e0;
}

body.dark-mode .instructions li,
body.dark-mode .instructions p {
    color: #ccc;
}

/* Fix for input focus in dark mode */
body.dark-mode .prompt-input:focus,
body.dark-mode .response-editor:focus {
    box-shadow: 0 0 0 2px rgba(26, 95, 180, 0.4);
}

/* Fix for spinner in dark mode */
body.dark-mode .spinner {
    border: 5px solid #333;
    border-top: 5px solid #4a86e8;
}

/* Fix for org chart nodes in dark mode */
body.dark-mode .org-node {
    background-color: #252525;
    border-color: #444;
    color: #e0e0e0;
}

/* Fix for code elements in dark mode */
body.dark-mode .message-text code,
html.dark-mode .message-text code,
body.dark-mode code,
html.dark-mode code,
body.dark-mode .hljs,
html.dark-mode .hljs,
body.dark-mode pre,
html.dark-mode pre {
    background-color: #2a2a2a !important;
    color: #e0e0e0 !important;
}

body.dark-mode .message-text pre,
html.dark-mode .message-text pre,
body.dark-mode pre,
html.dark-mode pre {
    background-color: #2a2a2a !important;
    color: #e0e0e0 !important;
}

body.dark-mode .message-text pre code,
html.dark-mode .message-text pre code,
body.dark-mode pre code,
html.dark-mode pre code {
    background-color: transparent !important;
}

/* Fix for syntax highlighting in dark mode */
body.dark-mode .hljs,
html.dark-mode .hljs,
body.dark-mode .hljs-tag,
html.dark-mode .hljs-tag,
body.dark-mode .hljs-keyword,
html.dark-mode .hljs-keyword,
body.dark-mode .hljs-selector-tag,
html.dark-mode .hljs-selector-tag,
body.dark-mode .hljs-literal,
html.dark-mode .hljs-literal,
body.dark-mode .hljs-strong,
html.dark-mode .hljs-strong,
body.dark-mode .hljs-name,
html.dark-mode .hljs-name {
    color: #e0e0e0 !important;
}

body.dark-mode .hljs-code,
html.dark-mode .hljs-code {
    color: #e0e0e0 !important;
}

body.dark-mode .hljs-attribute,
html.dark-mode .hljs-attribute,
body.dark-mode .hljs-symbol,
html.dark-mode .hljs-symbol,
body.dark-mode .hljs-regexp,
html.dark-mode .hljs-regexp,
body.dark-mode .hljs-link,
html.dark-mode .hljs-link {
    color: #d88 !important;
}

body.dark-mode .hljs-string,
html.dark-mode .hljs-string,
body.dark-mode .hljs-bullet,
html.dark-mode .hljs-bullet,
body.dark-mode .hljs-subst,
html.dark-mode .hljs-subst,
body.dark-mode .hljs-title,
html.dark-mode .hljs-title,
body.dark-mode .hljs-section,
html.dark-mode .hljs-section,
body.dark-mode .hljs-emphasis,
html.dark-mode .hljs-emphasis,
body.dark-mode .hljs-type,
html.dark-mode .hljs-type,
body.dark-mode .hljs-built_in,
html.dark-mode .hljs-built_in,
body.dark-mode .hljs-builtin-name,
html.dark-mode .hljs-builtin-name,
body.dark-mode .hljs-selector-attr,
html.dark-mode .hljs-selector-attr,
body.dark-mode .hljs-selector-pseudo,
html.dark-mode .hljs-selector-pseudo,
body.dark-mode .hljs-addition,
html.dark-mode .hljs-addition,
body.dark-mode .hljs-variable,
html.dark-mode .hljs-variable,
body.dark-mode .hljs-template-tag,
html.dark-mode .hljs-template-tag,
body.dark-mode .hljs-template-variable,
html.dark-mode .hljs-template-variable {
    color: #a5d6a7 !important;
}

body.dark-mode .hljs-comment,
html.dark-mode .hljs-comment,
body.dark-mode .hljs-quote,
html.dark-mode .hljs-quote,
body.dark-mode .hljs-deletion,
html.dark-mode .hljs-deletion,
body.dark-mode .hljs-meta,
html.dark-mode .hljs-meta {
    color: #999 !important;
}

body.dark-mode .hljs-doctag,
html.dark-mode .hljs-doctag,
body.dark-mode .hljs-keyword,
html.dark-mode .hljs-keyword,
body.dark-mode .hljs-formula,
html.dark-mode .hljs-formula {
    color: #88c !important;
}

body.dark-mode .hljs-number,
html.dark-mode .hljs-number,
body.dark-mode .hljs-literal,
html.dark-mode .hljs-literal {
    color: #f78c6c !important;
}

body.dark-mode .hljs-selector-id,
html.dark-mode .hljs-selector-id,
body.dark-mode .hljs-selector-class,
html.dark-mode .hljs-selector-class {
    color: #89ddff !important;
}

body.dark-mode .org-node.current {
    background-color: #1a3a5a;
    border-color: #4a86e8;
    color: #e0e0e0;
}

body.dark-mode .org-node.completed {
    background-color: #1e3b1e;
    border-color: #2e7d32;
    color: #e0e0e0;
}

body.dark-mode .org-node.future {
    background-color: #1e1e1e;
    border-color: #333;
    color: #aaa;
}

body.dark-mode .level-title {
    background-color: #252525;
    color: #aaa;
}

/* Fix for modal buttons in dark mode */
body.dark-mode .close-button {
    color: #e0e0e0;
}

body.dark-mode .close-button:hover {
    color: #fff;
}

/* Fix for system messages in dark mode */
body.dark-mode .message.system-message {
    background-color: #1a2a3a;
    border-left: 4px solid #4a86e8;
    color: #e0e0e0;
}

body.dark-mode .message.system-message.error {
    background-color: #2a1a1a;
    border-left: 4px solid #c62828;
    color: #e0e0e0;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--scrollbar-track);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--scrollbar-thumb);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--scrollbar-thumb-hover);
}

/* Firefox scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--scrollbar-thumb) var(--scrollbar-track);
}

/* Dark mode for developer tools and code elements */
body.dark-mode .CodeMirror,
html.dark-mode .CodeMirror,
body.dark-mode .cm-s-default,
html.dark-mode .cm-s-default {
    background-color: #2a2a2a !important;
    color: #e0e0e0 !important;
}

body.dark-mode .cm-s-default .cm-keyword,
html.dark-mode .cm-s-default .cm-keyword {
    color: #88c !important;
}

body.dark-mode .cm-s-default .cm-string,
html.dark-mode .cm-s-default .cm-string {
    color: #a5d6a7 !important;
}

body.dark-mode .cm-s-default .cm-number,
html.dark-mode .cm-s-default .cm-number {
    color: #f78c6c !important;
}

body.dark-mode .cm-s-default .cm-comment,
html.dark-mode .cm-s-default .cm-comment {
    color: #999 !important;
}

/* Additional styles for developer tools elements */
body.dark-mode .CodeMirror-gutters,
html.dark-mode .CodeMirror-gutters {
    background-color: #252525 !important;
    border-right: 1px solid #333 !important;
}

body.dark-mode .CodeMirror-linenumber,
html.dark-mode .CodeMirror-linenumber {
    color: #777 !important;
}

body.dark-mode .CodeMirror-cursor,
html.dark-mode .CodeMirror-cursor {
    border-left: 1px solid #e0e0e0 !important;
}

body.dark-mode .CodeMirror-selected,
html.dark-mode .CodeMirror-selected {
    background-color: rgba(74, 134, 232, 0.3) !important;
}

body.dark-mode .CodeMirror-activeline-background,
html.dark-mode .CodeMirror-activeline-background {
    background-color: rgba(255, 255, 255, 0.05) !important;
}

/* Fix for any other code-related elements */
body.dark-mode [class*="code-"],
html.dark-mode [class*="code-"],
body.dark-mode [class*="syntax-"],
html.dark-mode [class*="syntax-"],
body.dark-mode [class*="editor-"],
html.dark-mode [class*="editor-"],
body.dark-mode [class*="devtools-"],
html.dark-mode [class*="devtools-"],
body.dark-mode [class*="webkit-"],
html.dark-mode [class*="webkit-"],
body.dark-mode [class*="console-"],
html.dark-mode [class*="console-"],
body.dark-mode [class*="inspector-"],
html.dark-mode [class*="inspector-"],
body.dark-mode [class*="source-"],
html.dark-mode [class*="source-"],
body.dark-mode [class*="styles-"],
html.dark-mode [class*="styles-"],
body.dark-mode [class*="elements-"],
html.dark-mode [class*="elements-"],
body.dark-mode [class*="network-"],
html.dark-mode [class*="network-"] {
    background-color: #2a2a2a !important;
    color: #e0e0e0 !important;
}

/* Dark mode for task feedback modals */
body.dark-mode .failure-feedback,
html.dark-mode .failure-feedback,
body.dark-mode .success-feedback, /* Assuming a similar class for success */
html.dark-mode .success-feedback,
body.dark-mode .task-feedback-modal, /* A more generic class if used */
html.dark-mode .task-feedback-modal {
    background-color: var(--modal-bg) !important; /* Use the dark modal background */
    color: var(--text-color) !important; /* Use the dark mode text color */
    border: 1px solid var(--border-color-subtle) !important; /* Optional: add a subtle border */
}

body.dark-mode .failure-feedback p,
html.dark-mode .failure-feedback p,
body.dark-mode .success-feedback p,
html.dark-mode .success-feedback p,
body.dark-mode .task-feedback-modal p,
html.dark-mode .task-feedback-modal p,
body.dark-mode .failure-feedback h2, /* If there's an h2 title */
html.dark-mode .failure-feedback h2,
body.dark-mode .success-feedback h2,
html.dark-mode .success-feedback h2,
body.dark-mode .task-feedback-modal h2,
html.dark-mode .task-feedback-modal h2 {
    color: var(--text-color) !important;
}

/* Ensure buttons within these modals also respect dark mode if not already covered */
body.dark-mode .failure-feedback button,
html.dark-mode .failure-feedback button,
body.dark-mode .success-feedback button,
html.dark-mode .success-feedback button,
body.dark-mode .task-feedback-modal button,
html.dark-mode .task-feedback-modal button {
    background-color: var(--button-primary-bg) !important;
    color: var(--button-primary-text) !important;
}


/* Force all elements with inline styles in dark mode */
body.dark-mode [style*="background-color: white"],
html.dark-mode [style*="background-color: white"],
body.dark-mode [style*="background-color: #fff"],
html.dark-mode [style*="background-color: #fff"],
body.dark-mode [style*="background-color: rgb(255, 255, 255)"],
html.dark-mode [style*="background-color: rgb(255, 255, 255)"] {
    background-color: #2a2a2a !important;
}

body.dark-mode [style*="color: black"],
html.dark-mode [style*="color: black"],
body.dark-mode [style*="color: #000"],
html.dark-mode [style*="color: #000"],
body.dark-mode [style*="color: rgb(0, 0, 0)"],
html.dark-mode [style*="color: rgb(0, 0, 0)"] {
    color: #e0e0e0 !important;
}

/* Dark mode for failure score info */
body.dark-mode .failure-score-info,
html.dark-mode .failure-score-info {
    background-color: var(--message-bg); /* Or a slightly different dark background */
    color: var(--text-color); /* General text color for dark mode */
    border: 1px solid var(--border-color-subtle); /* Subtle border */
    padding: 10px;
    border-radius: 4px;
}

body.dark-mode .failure-score-info p,
html.dark-mode .failure-score-info p {
    color: var(--text-color); /* Ensure paragraph text is also themed */
}

/* Dark mode for promotion messages and their specific sections */
body.dark-mode .message.is-promotion .message-content {
    background-color: var(--main-bg); /* Use main panel background for a more neutral promotion look */
    border-left: 4px solid var(--accent-color-blue); /* Use accent blue for promotion */
}

body.dark-mode .message.is-promotion .message-text h2,
body.dark-mode .message.is-promotion .message-text h3,
body.dark-mode .message.is-promotion .message-text h4,
body.dark-mode .message.is-promotion .message-text p,
body.dark-mode .message.is-promotion .message-text li {
    color: var(--text-color); /* Ensure all text elements inherit dark mode text color */
}

body.dark-mode .message.is-promotion .manager-info {
    margin-top: 10px;
    padding: 10px; /* Increased padding */
    background-color: var(--message-you-bg); /* Using the 'user message' background for distinction */
    border: 1px solid var(--accent-color-blue); /* Accent border */
    color: var(--text-color-bright); /* Brighter text for emphasis */
    border-radius: 4px;
    text-align: center; /* Centering the text */
}

/* Next Manager Information Section - Light Mode */
.next-manager-section {
    margin: 25px 0;
    padding: 20px;
    background-color: #e8f5e9; /* Soft green background */
    border-left: 5px solid #43a047; /* Green accent border */
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    text-align: center;
    font-size: 1.15em;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.next-manager-section::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(67, 160, 71, 0.15) 0%, transparent 60%);
    z-index: 0;
}

.next-manager-section::after {
    content: "👨‍💼";
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5em;
    opacity: 0.2;
    z-index: 0;
}

.next-manager-section strong {
    color: #2e7d32; /* Darker green for emphasis */
    font-weight: bold;
    position: relative;
    z-index: 1;
    font-size: 1.2em;
    letter-spacing: 0.02em;
}

.next-manager-title {
    display: block;
    margin-top: 8px;
    font-style: italic;
    color: #388e3c;
    position: relative;
    z-index: 1;
    font-weight: 500;
}

.next-manager-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

/* Next Manager Information Section - Dark Mode */
html.dark-mode .next-manager-section,
body.dark-mode .next-manager-section {
    background-color: #1e3b1e; /* Dark green background */
    border-left: 5px solid #4caf50; /* Green accent border */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    color: #e0e0e0; /* Light text color */
}

html.dark-mode .next-manager-section::before,
body.dark-mode .next-manager-section::before {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2) 0%, transparent 60%);
}

html.dark-mode .next-manager-section strong,
body.dark-mode .next-manager-section strong {
    color: #81c784; /* Light green for emphasis in dark mode */
}

html.dark-mode .next-manager-title,
body.dark-mode .next-manager-title {
    color: #66bb6a; /* Lighter green for title in dark mode */
}

body.dark-mode .message.is-promotion .promotion-note {
    margin-top: 8px;
    padding: 5px 8px; /* Added padding */
    font-style: italic;
    color: var(--text-color);
    opacity: 0.9; /* Slightly less transparent */
    background-color: rgba(var(--accent-color-blue-rgb), 0.15); /* Subtle blue background */
    border-radius: 4px; /* Rounded corners */
    display: inline-block; /* To make background fit content */
}

body.dark-mode .message.is-promotion .improvement-section {
    margin-top: 15px;
    padding: 10px; /* Added padding for the section */
    border-top: 1px solid var(--border-color-subtle);
    background-color: rgba(var(--accent-color-blue-rgb), 0.05); /* Very subtle blue tint */
    border-radius: 4px; /* Rounded corners for the section */
}

body.dark-mode .message.is-promotion .improvement-section h3,
body.dark-mode .message.is-promotion .tips-section h4 {
    color: var(--text-color-bright); /* Brighter for headings in this section */
    margin-bottom: 8px; /* Spacing for headings */
}

body.dark-mode .message.is-promotion .tips-section ul {
    list-style-position: inside;
    padding-left: 0;
}

body.dark-mode .message.is-promotion .tips-section li {
    margin-bottom: 4px;
    color: var(--text-color); /* Ensure list items are also styled */
}
