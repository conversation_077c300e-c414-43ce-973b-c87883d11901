import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from django.contrib.auth.models import User
from corporate.models import Company, CorporateUser

def update_company_owners():
    """
    Update the owner field for all companies based on the CorporateUser relationship.
    For each company, find the first user who is a company admin and set them as the owner.
    """
    companies = Company.objects.all()
    updated_count = 0
    
    for company in companies:
        # Find the first admin user for this company
        admin_user = CorporateUser.objects.filter(
            company=company,
            is_company_admin=True
        ).first()
        
        if admin_user:
            company.owner = admin_user.user
            company.save()
            updated_count += 1
            print(f"Updated company '{company.name}' with owner '{admin_user.user.username}'")
        else:
            print(f"No admin user found for company '{company.name}'")
    
    print(f"Updated {updated_count} companies with owner information.")

if __name__ == "__main__":
    update_company_owners()
