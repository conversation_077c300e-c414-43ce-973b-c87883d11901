from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User, Group # Import Group
from django.conf import settings # Import settings
from django.core.exceptions import ValidationError, ObjectDoesNotExist
from django.utils.translation import gettext_lazy as _
from django.utils import timezone # Import timezone for expires_at validation
import pytz # Import pytz for timezones
from django.contrib.auth import get_user_model # Import get_user_model
from .models import Company, CompanyInformation, CompanyInvitation as TeamInvitation, Membership, RegistrationLink, UserProfile # Import UserProfile
from .utils import sanitize_company_name, validate_domain, generate_invite_token # Import generate_invite_token

# Define placeholder classes to avoid import errors
class CompanyCategory:
    @classmethod
    def objects(cls):
        return None

class AssistantFolder:
    @classmethod
    def objects(cls):
        return None

User = get_user_model() # Get the User model correctly

class CustomUserCreationForm(UserCreationForm):
    email = forms.EmailField(
        required=True,
        help_text='Required. Enter a valid email address.'
    )
    first_name = forms.CharField(
        max_length=30,
        required=False,
        help_text='Optional.'
    )
    last_name = forms.CharField(
        max_length=30,
        required=False,
        help_text='Optional.'
    )

    class Meta(UserCreationForm.Meta):
        model = User
        fields = ('username', 'email', 'first_name', 'last_name', 'password1', 'password2') # Added password2 back

    def clean_email(self):
        email = self.cleaned_data['email']
        if User.objects.filter(email=email).exists():
            raise ValidationError('A user with that email already exists.')
        return email

class CompanyCreationForm(forms.ModelForm):
    mission = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False
        # Removed help_text to avoid parsing issue
    )
    website = forms.URLField(
        required=False,
        help_text='Website (optional).'
    )
    contact_email = forms.EmailField(
        required=False,
        help_text='Primary contact email (optional).'
    )
    contact_phone = forms.CharField(
        max_length=30, # Adjust max_length as needed
        required=False,
        help_text='Primary contact phone (optional).'
    )
    list_in_directory = forms.BooleanField(
        required=False,
        initial=False,
        help_text='List in the public directory.'
    )

    class Meta:
        model = Company
        fields = ['name', 'entity_type']

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)
        self.fields['name'].widget.attrs.update({
            'placeholder': 'Enter company name',
            'autofocus': True
        })

    def clean_name(self):
        name = self.cleaned_data['name']
        sanitized_name = sanitize_company_name(name)
        if Company.objects.filter(name__iexact=sanitized_name).exists():
            raise ValidationError('A company with this name already exists.')
        return sanitized_name

    def save(self, commit=True):
        company = super().save(commit=False)
        if self.user:
            company.owner = self.user
        if commit:
            company.save()
            # Re-adding get_or_create here as a workaround for potential signal/reload issues
            CompanyInformation.objects.get_or_create(
                company=company,
                defaults={
                    'mission': self.cleaned_data.get('mission', ''),
                    'website': self.cleaned_data.get('website', ''),
                    'contact_email': self.cleaned_data.get('contact_email', ''), # Add contact_email
                    'contact_phone': self.cleaned_data.get('contact_phone', ''), # Add contact_phone
                    'list_in_directory': self.cleaned_data.get('list_in_directory', False)
                }
            )
        return company

class CompanySettingsForm(forms.ModelForm):
    class Meta:
        model = CompanyInformation
        # Explicitly list all fields instead of using exclude
        fields = [
            'mission', 'description', 'website', 'contact_email', 'contact_phone',
            'timezone', 'language', 'address_line1', 'address_line2', 'city',
            'postal_code', 'country', 'logo', 'industry', 'size', 'founded',
            'linkedin', 'twitter', 'facebook', 'custom_domain', 'list_in_directory'
        ]
        widgets = {
            'logo': forms.ClearableFileInput(attrs={'accept': 'image/*', 'class': 'form-control'}),
            'mission': forms.Textarea(attrs={'rows': 3, 'placeholder': "Brief description of your company's mission", 'class': 'form-control'}),
            'website': forms.URLInput(attrs={'class': 'form-control'}),
            'industry': forms.TextInput(attrs={'class': 'form-control'}),
            'size': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'rows': 4, 'class': 'form-control'}),
            'contact_email': forms.EmailInput(attrs={'class': 'form-control'}),
            'contact_phone': forms.TextInput(attrs={'class': 'form-control'}),
            'address_line1': forms.TextInput(attrs={'placeholder': 'Street Address', 'class': 'form-control'}),
            'address_line2': forms.TextInput(attrs={'placeholder': 'Apartment, suite, etc. (optional)', 'class': 'form-control'}),
            'city': forms.TextInput(attrs={'placeholder': 'City', 'class': 'form-control'}),
            'postal_code': forms.TextInput(attrs={'placeholder': 'Postal Code', 'class': 'form-control'}),
            'country': forms.TextInput(attrs={'placeholder': 'Country', 'class': 'form-control'}),
            'founded': forms.NumberInput(attrs={'min': '1800', 'class': 'form-control'}),
            'linkedin': forms.URLInput(attrs={'class': 'form-control'}),
            'twitter': forms.URLInput(attrs={'class': 'form-control'}),
            'facebook': forms.URLInput(attrs={'class': 'form-control'}),
            'timezone': forms.Select(attrs={'class': 'form-select'}),
            'language': forms.Select(attrs={'class': 'form-select'}),
            'custom_domain': forms.TextInput(attrs={'class': 'form-control'}),
            'list_in_directory': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    # Define choices for timezone and language directly in the form
    timezone = forms.ChoiceField(
        choices=[(tz, tz) for tz in pytz.common_timezones],
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    language = forms.ChoiceField(
        choices=settings.LANGUAGES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    def __init__(self, *args, **kwargs):
        current_year = kwargs.pop('current_year', None)
        super().__init__(*args, **kwargs)
        # Safely get the 'founded' field and check if it exists before modifying widget
        founded_field = self.fields.get('founded')
        if current_year and founded_field:
            founded_field.widget.attrs['max'] = current_year

    def clean_custom_domain(self):
        domain = self.cleaned_data.get('custom_domain') # Use .get() for safety
        if domain and not validate_domain(domain):
            raise ValidationError('Invalid domain format.')
        return domain

# New form for Company Tier and Featured status requests
class CompanyDirectorySettingsForm(forms.ModelForm):
    # Restore class-level definitions
    request_new_tier = forms.ChoiceField(
        choices=[('', '--- No Change ---')] + Company.TIER_REQUEST_CHOICES,
        required=False,
        label="Request Tier Upgrade",
        help_text="Select a paid tier (Gold, Silver, Bronze) to request an upgrade (requires superadmin approval)."
    )
    request_featured_status = forms.BooleanField(
        required=False,
        label="Request Featured Status",
        help_text="Check this box to request this company be featured (requires superadmin approval)."
    )

    class Meta:
        model = Company
        # Only include duration fields from the model, not tier/is_featured directly
        fields = [
            'requested_tier_duration',
            'requested_featured_duration',
        ]
        widgets = {
            'requested_tier_duration': forms.Select(attrs={'class': 'form-select'}),
            'requested_featured_duration': forms.Select(attrs={'class': 'form-select'}),
        }
        labels = {
            'requested_tier_duration': 'Requested Tier Duration',
            'requested_featured_duration': 'Requested Featured Duration',
        }
        help_texts = {
            'requested_tier_duration': 'Select duration if requesting a paid tier upgrade.',
            'requested_featured_duration': 'Select duration if requesting featured status.',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Apply widgets to the fields (which should now exist from class definition)
        self.fields['request_new_tier'].widget = forms.Select(attrs={'class': 'form-select'})
        self.fields['request_featured_status'].widget = forms.CheckboxInput(attrs={'class': 'form-check-input'})

        # Make duration fields not required by default
        self.fields['requested_tier_duration'].required = False
        self.fields['requested_featured_duration'].required = False
        # Set choices for duration fields
        self.fields['requested_tier_duration'].choices = [('', '--- Select Duration ---')] + Company.DURATION_CHOICES
        self.fields['requested_featured_duration'].choices = [('', '--- Select Duration ---')] + Company.DURATION_CHOICES

        # Set initial values for the request fields based on current status
        if self.instance and self.instance.pk:
            # Use self.fields[] to access the fields after they are added
            # Do NOT set initial for request_new_tier, let it default to 'No Change'
            self.fields['request_featured_status'].initial = self.instance.is_featured

class TeamInvitationForm(forms.ModelForm):
    emails = forms.CharField(
        widget=forms.Textarea(attrs={
            'rows': 3,
            'placeholder': 'Enter email addresses (one per line)'
        }),
        help_text='Enter one email address per line.'
    )
    # Removed role ModelChoiceField - Role/permission assignment handled in the view
    # We might add a ChoiceField here later if admins should select intended permission level
    message = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False,
        help_text='Optional message to include in the invitation email.'
    )

    class Meta:
        model = TeamInvitation
        # Removed 'role' from fields
        fields = ['emails', 'message']

    def clean_emails(self):
        emails_raw = self.cleaned_data.get('emails', '')
        emails = emails_raw.split('\n')
        cleaned_emails = []
        for email in emails:
            email = email.strip()
            if email:
                try:
                    forms.EmailField().clean(email) # Validate each email
                    cleaned_emails.append(email)
                except ValidationError:
                     raise ValidationError(f'Invalid email address found: {email}')
        if not cleaned_emails:
            raise ValidationError("Please enter at least one valid email address.")
        return cleaned_emails

    def save(self, company, invited_by, commit=True):
        invitations = []
        cleaned_emails = self.cleaned_data.get('emails', [])
        # selected_role = self.cleaned_data.get('role') # Role is no longer in the form

        for email in cleaned_emails:
            # Skip if user is already a member (using Membership model)
            try:
                user = User.objects.get(email=email)
                if Membership.objects.filter(company=company, user=user).exists():
                    continue
            except User.DoesNotExist:
                pass # User doesn't exist yet, can be invited

            # Skip if pending invitation exists for this company and email
            if TeamInvitation.objects.filter(
                company=company,
                email=email,
                status='pending'
            ).exists():
                continue

            # Generate unique token
            token = generate_invite_token() # Use the utility function

            invitation = TeamInvitation(
                company=company,
                email=email,
                invited_by=invited_by,
                # role=selected_role, # Role is no longer set here
                token=token # Assign generated token
            )

            if commit:
                invitation.save()
            invitations.append(invitation)
        return invitations

# Removed old UserSettingsForm

# New form combining User and UserProfile fields
class UserProfileForm(forms.ModelForm):
    # Fields from User model
    first_name = forms.CharField(max_length=30, required=False)
    last_name = forms.CharField(max_length=30, required=False)
    email = forms.EmailField(required=True)

    # Fields from UserProfile model
    avatar = forms.ImageField(
        required=False,
        # Use FileInput for cleaner rendering, hide with CSS/JS in template
        widget=forms.FileInput(attrs={'accept': 'image/*'})
    )
    bio = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        required=False
    )

    class Meta:
        model = User # Specify the primary model
        # Fields handled by this form (combining User and UserProfile)
        fields = ['first_name', 'last_name', 'email', 'avatar', 'bio']

    def __init__(self, *args, **kwargs):
        # Expecting 'instance' to be the User instance
        super().__init__(*args, **kwargs)
        if self.instance and self.instance.pk:
            # Populate User fields
            self.fields['first_name'].initial = self.instance.first_name
            self.fields['last_name'].initial = self.instance.last_name
            self.fields['email'].initial = self.instance.email
            # Populate UserProfile fields (handle potential DoesNotExist)
            try:
                profile = self.instance.profile # Access profile via related_name
                self.fields['avatar'].initial = profile.avatar
                self.fields['bio'].initial = profile.bio
            except ObjectDoesNotExist:
                # If profile doesn't exist, create it silently or handle as needed
                # For now, fields will just be empty
                pass

    def clean_email(self):
        # Validate email uniqueness, excluding the current user
        email = self.cleaned_data['email']
        if User.objects.exclude(pk=self.instance.pk).filter(email=email).exists():
            raise ValidationError('A user with that email already exists.')
        return email

    def save(self, commit=True):
        user = self.instance # User instance passed to the form

        # Update User fields
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.email = self.cleaned_data['email']

        # Get or create UserProfile
        profile, created = UserProfile.objects.get_or_create(user=user)

        # Update UserProfile fields
        profile.bio = self.cleaned_data['bio']
        # Handle avatar upload - only update if a new file was provided
        if 'avatar' in self.files:
            # Check if a file was actually uploaded (self.files['avatar'] is not None)
            uploaded_file = self.files.get('avatar')
            if uploaded_file:
                profile.avatar = uploaded_file
            else:
                # No file uploaded, but 'avatar' might be in self.files if the field exists.
                # If we want to allow clearing, we need a separate mechanism
                # since FileInput doesn't have a 'clear' checkbox.
                # For now, if no file is uploaded, we don't change the existing avatar.
                pass
        # Removed the check for `self.cleaned_data.get('avatar') is False` as FileInput has no clear checkbox

        if commit:
            user.save()
            profile.save()

        return user # Return the user instance


class RegistrationLinkForm(forms.ModelForm):
    """Form for creating and managing registration links."""
    intended_group = forms.ModelChoiceField(
        queryset=Group.objects.all(), # Queryset for roles (groups)
        required=True, # Make role selection mandatory
        label="Assign Role",
        help_text="Select the role the user will be assigned upon joining.",
        widget=forms.Select(attrs={'class': 'form-select'}) # Use standard select widget
    )
    expires_at = forms.DateTimeField(
        required=False,
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local', 'class': 'form-control'}), # Added form-control
        help_text='Optional: Link will expire after this date/time.'
    )
    max_uses = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'}), # Added form-control
        help_text='Optional: Limit the number of times this link can be used.'
    )

    class Meta:
        model = RegistrationLink
        # Simplified fields list
        fields = ['notes', 'intended_group', 'expires_at', 'max_uses', 'is_active']
        widgets = {
            'notes': forms.TextInput(attrs={'class': 'form-control'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        # Exclude company, created_by, token, uses_count (set automatically)

    def __init__(self, *args, **kwargs):
        self.company = kwargs.pop('company', None)
        super().__init__(*args, **kwargs)
        # TODO: Filter 'intended_group' queryset based on company-specific groups if needed.
        # For now, it shows all groups. Example filter (if using naming convention):
        # self.fields['intended_group'].queryset = Group.objects.filter(name__startswith=f"company_{self.company.id}_") | Group.objects.filter(name__in=['Admin', 'Member']) # Example standard roles

    def clean_expires_at(self):
        expires_at = self.cleaned_data.get('expires_at')
        if expires_at and expires_at < timezone.now():
            raise ValidationError("Expiration date cannot be in the past.")
        return expires_at

    def save(self, company, created_by, commit=True):
        link = super().save(commit=False)
        link.company = company
        link.created_by = created_by
        # Token is generated automatically by model default
        if commit:
            link.save()
            # Need to save M2M fields after the main instance is saved
            self.save_m2m()
        return link
