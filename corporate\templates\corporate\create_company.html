{% extends 'corporate/base.html' %}
{% load static %}

{% block title %}Create Company - Corporate Prompt Master{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="h3 mb-3">Create New Company</h1>
                <p class="text-muted">
                    Create a new company to manage your team and track their progress
                </p>

                {% if has_profile %}
                <div class="alert alert-warning mt-3">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> You are already associated with the company "{{ current_company.name }}".
                    Creating a new company will move your profile to the new company.
                </div>
                {% endif %}
            </div>

            <!-- Company Form -->
            <div class="card shadow">
                <div class="card-body p-4">
                    <form method="post" enctype="multipart/form-data" novalidate>
                        {% csrf_token %}

                        {% if company_form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in company_form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="mb-3">
                            <label for="{{ company_form.name.id_for_label }}" class="form-label">Company Name</label>
                            {{ company_form.name }}
                            {% if company_form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in company_form.name.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ company_form.description.id_for_label }}" class="form-label">Description</label>
                            {{ company_form.description }}
                            {% if company_form.description.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in company_form.description.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="{{ company_form.phone_number.id_for_label }}" class="form-label">Phone Number <span class="text-danger">*</span></label>
                            {{ company_form.phone_number }}
                            {% if company_form.phone_number.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in company_form.phone_number.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            {% if company_form.phone_number.help_text %}
                            <div class="form-text">{{ company_form.phone_number.help_text }}</div>
                            {% endif %}
                        </div>

                        <div class="mb-4">
                            <label for="{{ company_form.logo.id_for_label }}" class="form-label">Company Logo</label>
                            {{ company_form.logo }}
                            {% if company_form.logo.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in company_form.logo.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                            <div class="form-text">Optional. Recommended size: 200x200 pixels.</div>
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-2"></i>Create Company
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-transparent p-4">
                    <div class="d-grid">
                        <a href="{% url 'corporate:corporate_dashboard' %}" class="btn btn-light">
                            <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            </div>

            <!-- Help Text -->
            <div class="text-center mt-4">
                <p class="text-muted small mb-0">
                    Note: New companies require approval by an administrator before becoming active.
                </p>
                {% if has_profile %}
                <p class="text-muted small mt-2">
                    <i class="fas fa-info-circle me-1"></i>
                    Due to system limitations, a user can only be associated with one company at a time.
                    If you need to manage multiple companies, please contact support.
                </p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
