"""
Middleware for the corporate app.
"""
import logging
from django.utils.deprecation import MiddlewareMixin

logger = logging.getLogger(__name__)

class ImpersonationCompanyMiddleware(MiddlewareMixin):
    """
    Middleware to handle company switching during impersonation.
    This middleware should be placed after the django-impersonate middleware.
    """

    def process_request(self, request):
        """Process each request to handle company switching during impersonation."""
        # Ensure the session is initialized
        if not hasattr(request, 'session'):
            logger.warning("Request has no session attribute")
            return None

        # Make sure the session is loaded
        if not request.session.session_key:
            request.session.save()
            logger.debug(f"Created new session: {request.session.session_key}")

        # Check if user is authenticated
        if not hasattr(request, 'user') or not request.user.is_authenticated:
            return None

        # Check if this is an impersonation start request with active_company_id parameter
        if request.path.startswith('/impersonate/') and 'active_company_id' in request.GET:
            active_company_id = request.GET.get('active_company_id')
            logger.debug(f"Setting active_company_id from impersonation request: {active_company_id}")
            request.session['active_company_id'] = active_company_id
            # Explicitly save the session
            request.session.modified = True

        # If impersonation is active and we're stopping, preserve the active_company_id
        if hasattr(request, 'impersonator') and request.impersonator and request.path == '/impersonate/stop/':
            active_company_id = request.session.get('active_company_id')
            logger.debug(f"Preserving active_company_id during impersonation stop: {active_company_id}")
            # Store it in a temporary session variable
            request.session['preserved_active_company_id'] = active_company_id
            # Explicitly save the session
            request.session.modified = True

        # If we just stopped impersonation, restore the active_company_id
        if not hasattr(request, 'impersonator') or not request.impersonator:
            if 'preserved_active_company_id' in request.session:
                active_company_id = request.session.pop('preserved_active_company_id')
                logger.debug(f"Restoring active_company_id after impersonation: {active_company_id}")
                request.session['active_company_id'] = active_company_id
                # Explicitly save the session
                request.session.modified = True

        return None
