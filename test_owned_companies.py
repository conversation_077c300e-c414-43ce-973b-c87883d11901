import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from django.db.models import Count
from corporate.models import Company

# Function to test the owned_companies field
def test_owned_companies():
    print("Testing owned_companies field...")
    
    # Get all users
    users = User.objects.all()
    print(f"Found {users.count()} users")
    
    # Try to annotate with owned_corporate_companies count
    try:
        users_with_counts = users.annotate(
            company_count=Count('owned_corporate_companies', distinct=True)
        )
        print("Successfully annotated users with owned_corporate_companies count")
        
        # Print some results
        for user in users_with_counts[:5]:  # Limit to first 5 users
            print(f"User: {user.username}, Owned companies: {user.company_count}")
            
    except Exception as e:
        print(f"Error annotating users with owned_corporate_companies count: {e}")
    
    # Try to annotate with owned_companies count (this should fail)
    try:
        users_with_counts = users.annotate(
            company_count=Count('owned_companies', distinct=True)
        )
        print("Successfully annotated users with owned_companies count (this should have failed)")
        
    except Exception as e:
        print(f"Expected error annotating users with owned_companies count: {e}")
    
    # Try to filter by owned_corporate_companies
    try:
        # Get the first company
        company = Company.objects.first()
        if company:
            users_filtered = User.objects.filter(owned_corporate_companies=company)
            print(f"Successfully filtered users by owned_corporate_companies: {users_filtered.count()} users")
        else:
            print("No companies found")
            
    except Exception as e:
        print(f"Error filtering users by owned_corporate_companies: {e}")
    
    # Try to filter by owned_companies (this should fail)
    try:
        # Get the first company
        company = Company.objects.first()
        if company:
            users_filtered = User.objects.filter(owned_companies=company)
            print(f"Successfully filtered users by owned_companies (this should have failed)")
        else:
            print("No companies found")
            
    except Exception as e:
        print(f"Expected error filtering users by owned_companies: {e}")

# Run the test
if __name__ == "__main__":
    test_owned_companies()
