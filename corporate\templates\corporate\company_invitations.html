{% extends 'corporate/base.html' %}

{% block title %}Manage Invitations - {{ company.name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2 class="mb-3">Manage Invitations</h2>
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-envelope me-2"></i>{{ company.name }} Invitations</h5>
                    <div>
                        <button type="button" class="btn btn-light btn-sm me-2" data-bs-toggle="modal" data-bs-target="#inviteUserModal">
                            <i class="fas fa-user-plus me-1"></i> Invite User
                        </button>
                        <button type="button" class="btn btn-light btn-sm me-2" data-bs-toggle="modal" data-bs-target="#bulkInviteModal">
                            <i class="fas fa-users me-1"></i> Bulk Invite
                        </button>
                        <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#createLinkModal">
                            <i class="fas fa-link me-1"></i> Create Link
                        </button>
                    </div>
                </div>

                <!-- Pending Invitations -->
                <div class="card-body">
                    <h5 class="card-title">Pending Invitations</h5>
                    {% if pending_invitations %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Email</th>
                                    <th>Role</th>
                                    <th>Job Title</th>
                                    <th>Department</th>
                                    <th>Invited By</th>
                                    <th>Invited At</th>
                                    <th>Expires</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for invitation in pending_invitations %}
                                <tr>
                                    <td>{{ invitation.email }}</td>
                                    <td>
                                        {% if invitation.is_admin %}
                                        <span class="badge bg-danger">Admin</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Member</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ invitation.job_title|default:"Not specified" }}</td>
                                    <td>{{ invitation.department|default:"Not specified" }}</td>
                                    <td>{{ invitation.invited_by.username }}</td>
                                    <td>{{ invitation.invited_at|date:"M d, Y H:i" }}</td>
                                    <td>{{ invitation.expires_at|date:"M d, Y H:i" }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary copy-link"
                                                    data-link="{{ request.scheme }}://{{ request.get_host }}{% url 'corporate:accept_invitation' token=invitation.token %}">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <a href="{% url 'corporate:delete_invitation' invitation_id=invitation.id %}"
                                               class="btn btn-sm btn-outline-danger"
                                               onclick="return confirm('Are you sure you want to delete this invitation?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No pending invitations.
                    </div>
                    {% endif %}
                </div>

                <!-- Pending Users from Registration Links -->
                <div class="card-body border-top">
                    <h5 class="card-title">Pending User Approvals</h5>
                    {% if pending_users %}
                    <div class="alert alert-warning mb-3">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-triangle me-3"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-0">These users are waiting for your approval to join the company. They may have registered using invitation links or through other registration methods. Review and approve or reject their requests.</p>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>User</th>
                                    <th>Email</th>
                                    <th>Requested Role</th>
                                    <th>Registration Date</th>
                                    <th>Registration Link</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for corp_user in pending_users %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar me-2">
                                                {% if corp_user.user.profile.avatar %}
                                                <img src="{{ corp_user.user.profile.avatar.url }}" alt="Avatar" class="rounded-circle" width="40">
                                                {% else %}
                                                <div class="avatar-placeholder rounded-circle text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: #ffc107; color: #212529; font-weight: bold; font-size: 16px;">
                                                    {{ corp_user.user.username|first|upper }}
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ corp_user.user.get_full_name|default:corp_user.user.username }}</h6>
                                                <small class="text-muted">@{{ corp_user.user.username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ corp_user.user.email }}</td>
                                    <td>
                                        {% if corp_user.is_company_admin %}
                                        <span class="badge bg-danger">Admin</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Member</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ corp_user.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        {% if corp_user.registration_link %}
                                        <span class="badge {% if corp_user.registration_link.approval_type == 'auto' %}bg-success{% else %}bg-warning text-dark{% endif %}">
                                            {{ corp_user.registration_link.get_approval_type_display }}
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary">Direct Registration</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <form method="post" action="{% url 'corporate:company_invitations' %}" class="me-1">
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="approve_user">
                                                <input type="hidden" name="user_id" value="{{ corp_user.user.id }}">
                                                <button type="submit" class="btn btn-sm btn-success">
                                                    <i class="fas fa-check me-1"></i> Approve
                                                </button>
                                            </form>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#rejectUserModal{{ corp_user.user.id }}">
                                                <i class="fas fa-times me-1"></i> Reject
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Reject User Modal -->
                                <div class="modal fade" id="rejectUserModal{{ corp_user.user.id }}" tabindex="-1" aria-labelledby="rejectUserModalLabel{{ corp_user.user.id }}" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title" id="rejectUserModalLabel{{ corp_user.user.id }}">Reject User Request</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to reject <strong>{{ corp_user.user.get_full_name|default:corp_user.user.username }}</strong>'s request to join {{ company.name }}?</p>
                                                <p class="text-danger">This action cannot be undone.</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <form method="post" action="{% url 'corporate:company_invitations' %}">
                                                    {% csrf_token %}
                                                    <input type="hidden" name="action" value="reject_user">
                                                    <input type="hidden" name="user_id" value="{{ corp_user.user.id }}">
                                                    <button type="submit" class="btn btn-danger">Reject Request</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No pending user approvals.
                    </div>
                    {% endif %}
                </div>

                <!-- Registration Links -->
                <div class="card-body border-top">
                    <h5 class="card-title">Registration Links</h5>
                    {% if registration_links %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Role</th>
                                    <th>Approval Type</th>
                                    <th>Created By</th>
                                    <th>Created At</th>
                                    <th>Expires</th>
                                    <th>Uses</th>
                                    <th>Status</th>
                                    <th>Notes</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for link in registration_links %}
                                <tr>
                                    <td>
                                        {% if link.intended_role == 'admin' %}
                                        <span class="badge bg-danger">Admin</span>
                                        {% else %}
                                        <span class="badge bg-secondary">Member</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if link.approval_type == 'auto' %}
                                        <span class="badge bg-success">Automatic</span>
                                        {% else %}
                                        <span class="badge bg-warning text-dark">Admin Approval</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ link.created_by.username }}</td>
                                    <td>{{ link.created_at|date:"M d, Y H:i" }}</td>
                                    <td>{{ link.expires_at|date:"M d, Y H:i"|default:"Never" }}</td>
                                    <td>
                                        {% if link.max_uses %}
                                        {{ link.uses_count }} / {{ link.max_uses }}
                                        {% else %}
                                        {{ link.uses_count }} / ∞
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if link.is_valid %}
                                        <span class="badge bg-success">Active</span>
                                        {% else %}
                                        <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ link.notes|default:"" }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary copy-link"
                                                    data-link="{{ request.scheme }}://{{ request.get_host }}{% url 'corporate:register_with_link' token=link.token %}">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <a href="{% url 'corporate:delete_registration_link' token=link.token %}"
                                               class="btn btn-sm btn-outline-danger"
                                               onclick="return confirm('Are you sure you want to delete this registration link?');">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i> No registration links.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Invite User Modal -->
<div class="modal fade" id="inviteUserModal" tabindex="-1" aria-labelledby="inviteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="submit_invite_form" value="1">
                <div class="modal-header">
                    <h5 class="modal-title" id="inviteUserModalLabel">Invite User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="{{ invite_form.email.id_for_label }}" class="form-label">Email Address</label>
                        {{ invite_form.email }}
                        {% if invite_form.email.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in invite_form.email.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="{{ invite_form.job_title.id_for_label }}" class="form-label">Job Title (Optional)</label>
                        {{ invite_form.job_title }}
                    </div>
                    <div class="mb-3">
                        <label for="{{ invite_form.department.id_for_label }}" class="form-label">Department (Optional)</label>
                        {{ invite_form.department }}
                    </div>
                    <div class="mb-3 form-check">
                        {{ invite_form.is_admin }}
                        <label class="form-check-label" for="{{ invite_form.is_admin.id_for_label }}">
                            Make this user a company administrator
                        </label>
                    </div>
                    <div class="mb-3">
                        <label for="{{ invite_form.message.id_for_label }}" class="form-label">Personal Message (Optional)</label>
                        {{ invite_form.message }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Invitation</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Invite Modal -->
<div class="modal fade" id="bulkInviteModal" tabindex="-1" aria-labelledby="bulkInviteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="submit_bulk_invite_form" value="1">
                <div class="modal-header">
                    <h5 class="modal-title" id="bulkInviteModalLabel">Bulk Invite Users</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="{{ bulk_invite_form.emails.id_for_label }}" class="form-label">Email Addresses</label>
                        {{ bulk_invite_form.emails }}
                        <div class="form-text">{{ bulk_invite_form.emails.help_text }}</div>
                        {% if bulk_invite_form.emails.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in bulk_invite_form.emails.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3 form-check">
                        {{ bulk_invite_form.is_admin }}
                        <label class="form-check-label" for="{{ bulk_invite_form.is_admin.id_for_label }}">
                            {{ bulk_invite_form.is_admin.help_text }}
                        </label>
                    </div>
                    <div class="mb-3">
                        <label for="{{ bulk_invite_form.message.id_for_label }}" class="form-label">Personal Message (Optional)</label>
                        {{ bulk_invite_form.message }}
                        <div class="form-text">{{ bulk_invite_form.message.help_text }}</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Invitations</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Link Modal -->
<div class="modal fade" id="createLinkModal" tabindex="-1" aria-labelledby="createLinkModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="submit_reg_link_form" value="1">
                <div class="modal-header">
                    <h5 class="modal-title" id="createLinkModalLabel">Create Registration Link</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="{{ reg_link_form.intended_role.id_for_label }}" class="form-label">Role</label>
                        {{ reg_link_form.intended_role }}
                    </div>
                    <div class="mb-3">
                        <label for="{{ reg_link_form.approval_type.id_for_label }}" class="form-label">Approval Type</label>
                        {{ reg_link_form.approval_type }}
                        <div class="form-text">{{ reg_link_form.approval_type.help_text }}</div>
                    </div>
                    <div class="mb-3">
                        <label for="{{ reg_link_form.expires_at.id_for_label }}" class="form-label">Expiration Date (Optional)</label>
                        {{ reg_link_form.expires_at }}
                        <div class="form-text">{{ reg_link_form.expires_at.help_text }}</div>
                    </div>
                    <div class="mb-3">
                        <label for="{{ reg_link_form.max_uses.id_for_label }}" class="form-label">Maximum Uses (Optional)</label>
                        {{ reg_link_form.max_uses }}
                        <div class="form-text">{{ reg_link_form.max_uses.help_text }}</div>
                    </div>
                    <div class="mb-3">
                        <label for="{{ reg_link_form.notes.id_for_label }}" class="form-label">Notes (Optional)</label>
                        {{ reg_link_form.notes }}
                        <div class="form-text">{{ reg_link_form.notes.help_text }}</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Link</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Copy link functionality
        document.querySelectorAll('.copy-link').forEach(function(button) {
            button.addEventListener('click', function() {
                const link = this.getAttribute('data-link');
                navigator.clipboard.writeText(link).then(function() {
                    // Show tooltip or notification
                    alert('Link copied to clipboard!');
                });
            });
        });
    });
</script>
{% endblock %}
