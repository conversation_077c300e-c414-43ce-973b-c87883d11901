from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth.models import Group, Permission, User
from django.contrib.contenttypes.models import ContentType
from accounts.models import Company
from guardian.shortcuts import assign_perm
from guardian.models import GroupObjectPermission, UserObjectPermission # Import UserObjectPermission

class Command(BaseCommand):
    help = "Assigns the 'accounts.manage_company_assistants' permission to a specified group OR the company owner for specific or all companies."

    def add_arguments(self, parser):
        group = parser.add_mutually_exclusive_group() # Group or Owner, not both
        group.add_argument(
            '--group',
            type=str,
            # default='Company Members', # Remove default, make it optional
            help="Name of the group to assign/remove permissions for.",
        )
        group.add_argument(
            '--assign-to-owner',
            action='store_true',
            help="Assign/remove permission directly to the company owner instead of a group.",
        )
        parser.add_argument(
            '--company-id',
            type=int,
            help='Optional ID of a specific company to assign permissions for.',
        )
        parser.add_argument(
            '--remove',
            action='store_true',
            help='Remove the permission instead of assigning it.',
        )

    def handle(self, *args, **options):
        group_name = options['group']
        assign_to_owner = options['assign_to_owner']
        company_id = options['company_id']
        remove_perm_flag = options['remove']

        if not group_name and not assign_to_owner:
             raise CommandError("You must specify either --group or --assign-to-owner.")
        if group_name and assign_to_owner:
             raise CommandError("You cannot specify both --group and --assign-to-owner.") # Should be handled by mutually_exclusive_group

        # Permission details
        app_label = 'accounts'
        perm_codename = 'manage_company_assistants'
        permission_string = f'{app_label}.{perm_codename}'

        target_group = None
        if group_name:
            try:
                target_group = Group.objects.get(name=group_name)
            except Group.DoesNotExist:
                raise CommandError(f"Group '{group_name}' does not exist.")

        companies_query = Company.objects.all()
        if company_id:
            companies_query = companies_query.filter(id=company_id)

        if not companies_query.exists():
            if company_id:
                raise CommandError(f"Company with ID {company_id} not found.")
            else:
                self.stdout.write(self.style.WARNING("No companies found."))
                return

        action_word = "Removing" if remove_perm_flag else "Assigning"
        target_type = "owner" if assign_to_owner else f"group '{group_name}'"

        assigned_count = 0
        removed_count = 0
        already_exists_count = 0
        not_found_count = 0
        owner_missing_count = 0

        # Ensure the Permission object exists first (outside the loop)
        try:
            content_type = ContentType.objects.get_for_model(Company)
            permission, created = Permission.objects.get_or_create(
                codename=perm_codename,
                content_type=content_type,
                defaults={'name': f'Can manage company assistants'} # Provide a default name
            )
            if created:
                 self.stdout.write(self.style.WARNING(f"Created missing permission object: '{permission_string}'"))
        except ContentType.DoesNotExist:
             self.stdout.write(self.style.ERROR(f"Error: ContentType for Company model not found. Cannot proceed."))
             return
        except Exception as e:
             self.stdout.write(self.style.ERROR(f"Error ensuring permission '{permission_string}' exists: {e}. Cannot proceed."))
             return


        for company in companies_query:
            target_user = None
            if assign_to_owner:
                target_user = company.owner
                if not target_user:
                    self.stdout.write(self.style.WARNING(f"Company '{company.name}' (ID: {company.id}) has no owner. Skipping owner assignment."))
                    owner_missing_count += 1
                    continue
                target_principal = target_user # Use user object for assignment/check
                principal_log_name = f"owner '{target_user.username}'"
                ObjectPermissionModel = UserObjectPermission # Use UserObjectPermission for checks/removal
            else:
                target_principal = target_group # Use group object for assignment/check
                principal_log_name = f"group '{group_name}'"
                ObjectPermissionModel = GroupObjectPermission # Use GroupObjectPermission for checks/removal

            # Build base filter arguments
            filter_kwargs = {
                'permission': permission,
                'content_type': content_type,
                'object_pk': company.pk,
            }
            if assign_to_owner:
                filter_kwargs['user'] = target_user
            else:
                filter_kwargs['group'] = target_group

            # Process only the single required permission
            if remove_perm_flag:
                # Remove the permission
                deleted_count, _ = ObjectPermissionModel.objects.filter(**filter_kwargs).delete()
                if deleted_count > 0:
                    self.stdout.write(self.style.SUCCESS(f"Removed permission '{permission_string}' from {principal_log_name} for company '{company.name}' (ID: {company.id})"))
                    removed_count += 1
                else:
                    self.stdout.write(self.style.NOTICE(f"Permission '{permission_string}' was not found for {principal_log_name} on company '{company.name}' (ID: {company.id})"))
                    not_found_count += 1
            else:
                # Assign the permission
                # Check if it already exists first
                perm_exists = ObjectPermissionModel.objects.filter(**filter_kwargs).exists()

                if perm_exists:
                    self.stdout.write(self.style.NOTICE(f"Permission '{permission_string}' already exists for {principal_log_name} on company '{company.name}' (ID: {company.id})"))
                    already_exists_count += 1
                else:
                    try:
                        # Now assign it using guardian
                        assign_perm(permission, target_principal, company) # Pass the permission object and principal (user or group)
                        self.stdout.write(self.style.SUCCESS(f"Assigned permission '{permission_string}' to {principal_log_name} for company '{company.name}' (ID: {company.id})"))
                        assigned_count += 1
                    except Exception as e:
                         # Catch potential errors during assignment
                         self.stdout.write(self.style.ERROR(f"Error assigning permission '{permission_string}' for company '{company.name}' to {principal_log_name}: {e}"))


        self.stdout.write(f"\n{action_word} process complete for {target_type}.")
        if assign_to_owner and owner_missing_count > 0:
             self.stdout.write(self.style.WARNING(f"Skipped {owner_missing_count} companies because they had no owner."))
        if remove_perm_flag:
            self.stdout.write(f"Permissions removed: {removed_count}")
            self.stdout.write(f"Permissions not found: {not_found_count}")
        else:
            self.stdout.write(f"Permissions assigned: {assigned_count}")
            self.stdout.write(f"Permissions already existed: {already_exists_count}")
