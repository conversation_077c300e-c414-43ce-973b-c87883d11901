"""
LLM Feedback Generator Module for Rwenzori Innovations Game

This module provides functions to generate personalized feedback from managers
based on the player's performance and response. The feedback focuses specifically on:
1. Performance assessment with an overall score
2. Brief mention of strengths and areas for improvement
3. Clear recommendation on whether to advance or redo the task
"""

import logging
import os
from openai import OpenAI
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Load environment variables
load_dotenv()

def generate_manager_feedback(player_response, task_id, manager_name, performance_metrics, max_retries=2):
    """
    Generate focused performance feedback from a manager that includes an overall score
    and a clear recommendation on whether to advance or redo the task.

    Args:
        player_response: The player's response to the task
        task_id: The ID of the current task
        manager_name: The name of the manager providing feedback
        performance_metrics: Dictionary containing performance metrics (score, grade, etc.)
        max_retries: Maximum number of retry attempts

    Returns:
        The generated feedback text that includes:
        1. Performance assessment with overall score
        2. Brief mention of strengths and areas for improvement
        3. Clear recommendation on whether to ADVANCE or REDO the task
    """
    # Extract performance metrics
    overall_score = performance_metrics.get('overall_score', 0)
    grade = performance_metrics.get('grade', 'okay')
    feedback_details = performance_metrics.get('feedback_details', [])

    # BUGFIX: Use formatted score if available
    formatted_score = performance_metrics.get('formatted_score', None)
    if formatted_score is None:
        # Check if we have a display_score (0-100 scale)
        display_score = performance_metrics.get('display_score', None)
        if display_score is not None:
            # Format as percentage for display
            formatted_score = f"{display_score:.0f}%"
            logging.info(f"Using display_score to create formatted value: {formatted_score}")
        else:
            # Format the score to one decimal place for consistent display
            formatted_score = f"{overall_score:.1f}"
            logging.info(f"Formatted score not provided, using calculated value: {formatted_score}")

    # Normalize the score to a 0-10 scale for consistent evaluation
    normalized_score = overall_score
    if overall_score > 10:  # If score is on a 0-100 scale
        normalized_score = overall_score / 10

    # Import the minimum promotion score constant if available
    try:
        from enhanced_evaluation import MINIMUM_PROMOTION_SCORE, MINIMUM_COMPLETION_SCORE
        min_promotion_score = MINIMUM_PROMOTION_SCORE  # Should be 7
        min_completion_score = MINIMUM_COMPLETION_SCORE  # Should be 6
    except ImportError:
        # Default values if import fails
        min_promotion_score = 7
        min_completion_score = 6

    # Determine feedback tone and performance level based on normalized score (0-10 scale)
    if normalized_score >= 9:
        tone = "highly positive and enthusiastic"
        performance_level = "outstanding"
    elif normalized_score >= 8:
        tone = "very positive and encouraging"
        performance_level = "excellent"
    elif normalized_score >= min_promotion_score:  # 7+
        tone = "positive with minor suggestions"
        performance_level = "very good"
    elif normalized_score >= min_completion_score:  # 6+
        tone = "generally positive with constructive suggestions"
        performance_level = "good"
    elif normalized_score >= 5:
        tone = "balanced with equal praise and constructive feedback"
        performance_level = "satisfactory"
    elif normalized_score >= 4:
        tone = "constructive with some positive elements"
        performance_level = "needs some improvement"
    elif normalized_score >= 3:
        tone = "primarily constructive with specific improvement areas"
        performance_level = "needs significant improvement"
    else:
        tone = "direct but respectful about necessary improvements"
        performance_level = "requires substantial improvement"

    # Format feedback details for the prompt
    formatted_feedback = "\n".join([f"- {detail}" for detail in feedback_details])

    # Determine advancement recommendation based on the grade
    # Task only passes if grade is "good" (score >= 80, normalized_score >= 8)
    if grade == "good":
        recommendation = "ADVANCE to the next task"
    else: # "okay" or "bad"
        recommendation = "REDO this task before advancing"

    # Construct the feedback generation prompt
    feedback_prompt = f"""
You are {manager_name}, a manager at Rwenzori Innovations. You need to provide performance feedback on an employee's response to a task.

Task ID: {task_id}
Overall Performance Score: {formatted_score} 
Performance Level: {performance_level}
Recommendation: {recommendation}

Detailed Performance Metrics:
{formatted_feedback}

Employee's Response:
{player_response[:500]}... (response truncated for brevity)

As {manager_name}, write focused performance feedback that is {tone}. Your feedback MUST:
1. Start with a clear statement of the overall score - ALWAYS use exactly "Overall Score: {formatted_score}" as the score
2. Briefly mention 1-2 specific strengths from their work
3. Briefly mention 1-2 specific areas that need improvement
4. Provide a CLEAR and DIRECT recommendation on whether they should ADVANCE to the next task or REDO this task
5. Keep a professional tone appropriate for a corporate performance review
6. Be concise (3-5 sentences total)

The feedback should focus ONLY on performance assessment, overall score, and a clear recommendation on whether to advance or redo the task.
IMPORTANT: Always start your feedback with "Overall Score: {formatted_score}" exactly as written.
"""

    # Use the Gemini API key from environment or use the default one
    api_key = os.environ.get("GEMINI_API_KEY", "AIzaSyD6W5JpEt5tXKkme3ra6ctmyw-inqe97jk")

    retry_count = 0
    while retry_count <= max_retries:
        try:
            logging.info(f"Calling Gemini API for feedback generation for task {task_id}")
            logging.info(f"Manager: {manager_name}, Grade: {grade}, Score: {overall_score}")

            # Initialize the OpenAI client with Gemini configuration
            client = OpenAI(
                api_key=api_key,
                base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
            )

            # Prepare messages for the API call
            messages = [
                {"role": "system", "content": f"You are {manager_name}, a manager at Rwenzori Innovations providing feedback on employee work."},
                {"role": "user", "content": feedback_prompt}
            ]

            logging.info(f"Sending request to Gemini API for manager feedback")

            # Make the API call
            response = client.chat.completions.create(
                model="gemini-2.0-flash",  # Using Gemini model
                messages=messages,
                temperature=0.7,  # Higher temperature for more varied feedback
                max_tokens=500
            )

            # Extract the response content
            content = response.choices[0].message.content

            logging.info(f"Successfully received feedback from Gemini API")
            logging.info(f"Generated feedback (first 100 chars): {content[:100]}...")

            return content

        except Exception as e:
            logging.error(f"Error calling Gemini API for feedback generation: {str(e)}")
            retry_count += 1
            if retry_count <= max_retries:
                logging.info(f"Retrying API call (attempt {retry_count}/{max_retries})")
            else:
                logging.error(f"Failed to generate feedback after {max_retries} attempts")
                # Normalize the score for consistent evaluation
                normalized_score = overall_score
                if overall_score > 10:  # If score is on a 0-100 scale
                    normalized_score = overall_score / 10
                
                # Fallback recommendation based on "good" grade (score >= 80, normalized_score >= 8)
                if normalized_score >= 8: # "good" grade
                    return f"Overall Score: {formatted_score} Your work demonstrates strong understanding and attention to detail. RECOMMENDATION: ADVANCE to the next task. Your performance meets our expectations for this assignment."
                elif normalized_score >= 6: # "okay" grade
                    return f"Overall Score: {formatted_score} Your work covers the main requirements but lacks some detail and depth. RECOMMENDATION: REDO this task before advancing. Focus on improving structure and specificity."
                else:  # "bad" grade
                    return f"Overall Score: {formatted_score} Your submission falls below our minimum requirements for advancement. RECOMMENDATION: REDO this task with greater attention to the guidelines and requirements before proceeding."


# Test function to verify the feedback generator
def test_feedback_generator():
    """
    Test function to verify the feedback generator with different performance levels and score scales.
    """
    test_response = "This is a sample response for testing the feedback generator."
    test_task_id = "test_task"
    test_manager_name = "Sarah Chen"

    # Test high performance on 0-10 scale (should recommend ADVANCE)
    high_metrics_10 = {
        'overall_score': 8.5,
        'grade': 'good',
        'feedback_details': ['Excellent structure', 'Clear communication', 'Minor formatting issues']
    }

    # Test medium performance on 0-10 scale (should recommend ADVANCE with caution)
    medium_metrics_10 = {
        'overall_score': 6.2,
        'grade': 'okay',
        'feedback_details': ['Good attempt', 'Covers basics', 'Lacks detail', 'Structure needs improvement']
    }

    # Test low performance on 0-10 scale (should recommend REDO)
    low_metrics_10 = {
        'overall_score': 4.3,
        'grade': 'bad',
        'feedback_details': ['Incomplete response', 'Missing key requirements', 'Poor organization']
    }

    # Test high performance on 0-100 scale (should recommend ADVANCE)
    high_metrics_100 = {
        'overall_score': 85,
        'grade': 'good',
        'feedback_details': ['Excellent structure', 'Clear communication', 'Minor formatting issues']
    }

    # Test medium performance on 0-100 scale (should recommend ADVANCE with caution)
    medium_metrics_100 = {
        'overall_score': 62,
        'grade': 'okay',
        'feedback_details': ['Good attempt', 'Covers basics', 'Lacks detail', 'Structure needs improvement']
    }

    # Test low performance on 0-100 scale (should recommend REDO)
    low_metrics_100 = {
        'overall_score': 43,
        'grade': 'bad',
        'feedback_details': ['Incomplete response', 'Missing key requirements', 'Poor organization']
    }

    # Test borderline cases
    borderline_high = {
        'overall_score': 7.0,  # Exactly at promotion threshold
        'grade': 'good',
        'feedback_details': ['Just meets promotion threshold']
    }

    borderline_medium = {
        'overall_score': 6.0,  # Exactly at completion threshold
        'grade': 'okay',
        'feedback_details': ['Just meets completion threshold']
    }

    print("\n=== TESTING FEEDBACK GENERATOR ===\n")

    print("=== HIGH PERFORMANCE FEEDBACK (0-10 scale) ===")
    high_feedback_10 = generate_manager_feedback(test_response, test_task_id, test_manager_name, high_metrics_10, max_retries=0)
    print(high_feedback_10)
    print("\n")

    print("=== MEDIUM PERFORMANCE FEEDBACK (0-10 scale) ===")
    medium_feedback_10 = generate_manager_feedback(test_response, test_task_id, test_manager_name, medium_metrics_10, max_retries=0)
    print(medium_feedback_10)
    print("\n")

    print("=== LOW PERFORMANCE FEEDBACK (0-10 scale) ===")
    low_feedback_10 = generate_manager_feedback(test_response, test_task_id, test_manager_name, low_metrics_10, max_retries=0)
    print(low_feedback_10)
    print("\n")

    print("=== HIGH PERFORMANCE FEEDBACK (0-100 scale) ===")
    high_feedback_100 = generate_manager_feedback(test_response, test_task_id, test_manager_name, high_metrics_100, max_retries=0)
    print(high_feedback_100)
    print("\n")

    print("=== MEDIUM PERFORMANCE FEEDBACK (0-100 scale) ===")
    medium_feedback_100 = generate_manager_feedback(test_response, test_task_id, test_manager_name, medium_metrics_100, max_retries=0)
    print(medium_feedback_100)
    print("\n")

    print("=== LOW PERFORMANCE FEEDBACK (0-100 scale) ===")
    low_feedback_100 = generate_manager_feedback(test_response, test_task_id, test_manager_name, low_metrics_100, max_retries=0)
    print(low_feedback_100)
    print("\n")

    print("=== BORDERLINE HIGH FEEDBACK (exactly 7.0) ===")
    borderline_high_feedback = generate_manager_feedback(test_response, test_task_id, test_manager_name, borderline_high, max_retries=0)
    print(borderline_high_feedback)
    print("\n")

    print("=== BORDERLINE MEDIUM FEEDBACK (exactly 6.0) ===")
    borderline_medium_feedback = generate_manager_feedback(test_response, test_task_id, test_manager_name, borderline_medium, max_retries=0)
    print(borderline_medium_feedback)

    print("\n=== TESTING COMPLETE ===\n")


# Run the test function when this file is executed directly
if __name__ == "__main__":
    test_feedback_generator()
