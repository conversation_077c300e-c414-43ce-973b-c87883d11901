#!/usr/bin/env python
"""
Script to clean up duplicate game sessions in the database.
"""
import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from game.models import GameSession, Message
from django.db.models import Count

def cleanup_duplicate_sessions():
    """Clean up duplicate game sessions."""
    print("Starting cleanup of duplicate game sessions...")
    
    # Find duplicate sessions for authenticated users
    print("\n1. Checking for duplicate sessions for authenticated users...")
    user_duplicates = GameSession.objects.filter(user__isnull=False).values('user').annotate(count=Count('id')).filter(count__gt=1)
    
    for duplicate in user_duplicates:
        user_id = duplicate['user']
        count = duplicate['count']
        print(f"User {user_id} has {count} sessions")
        
        # Get all sessions for this user, ordered by creation date (keep the newest)
        sessions = GameSession.objects.filter(user_id=user_id).order_by('-created_at')
        sessions_to_delete = sessions[1:]  # Keep the first (newest), delete the rest
        
        for session in sessions_to_delete:
            print(f"  Deleting session {session.id} (created: {session.created_at})")
            # Delete associated messages first
            Message.objects.filter(game_session=session).delete()
            session.delete()
    
    # Find duplicate sessions for anonymous users (by session_id)
    print("\n2. Checking for duplicate sessions for anonymous users...")
    session_duplicates = GameSession.objects.filter(session_id__isnull=False).values('session_id').annotate(count=Count('id')).filter(count__gt=1)
    
    for duplicate in session_duplicates:
        session_id = duplicate['session_id']
        count = duplicate['count']
        print(f"Session ID {session_id} has {count} sessions")
        
        # Get all sessions for this session_id, ordered by creation date (keep the newest)
        sessions = GameSession.objects.filter(session_id=session_id).order_by('-created_at')
        sessions_to_delete = sessions[1:]  # Keep the first (newest), delete the rest
        
        for session in sessions_to_delete:
            print(f"  Deleting session {session.id} (created: {session.created_at})")
            # Delete associated messages first
            Message.objects.filter(game_session=session).delete()
            session.delete()
    
    print("\n3. Final statistics:")
    total_sessions = GameSession.objects.count()
    user_sessions = GameSession.objects.filter(user__isnull=False).count()
    anonymous_sessions = GameSession.objects.filter(session_id__isnull=False).count()
    
    print(f"Total game sessions: {total_sessions}")
    print(f"User sessions: {user_sessions}")
    print(f"Anonymous sessions: {anonymous_sessions}")
    
    print("\nCleanup completed!")

if __name__ == "__main__":
    cleanup_duplicate_sessions()
