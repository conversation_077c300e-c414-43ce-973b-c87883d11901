#!/usr/bin/env python
"""
Script to fix the migration issue with NewLeaderboardEntry model.
This script directly modifies the Django migrations record in the database
to mark the problematic migration as applied.
"""
import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.db import connection

def fix_migration_issue():
    """Fix the migration issue by directly updating the django_migrations table"""
    print("Attempting to fix migration issue...")
    
    # Check if the migration is already in the django_migrations table
    with connection.cursor() as cursor:
        cursor.execute(
            "SELECT * FROM django_migrations WHERE app='game' AND name='0008_alter_gamesession_company_and_more'"
        )
        migration_exists = cursor.fetchone()
        
        if migration_exists:
            print("Migration already exists in the database. No action needed.")
            return
        
        # Insert the migration record to mark it as applied
        print("Inserting migration record to mark it as applied...")
        cursor.execute(
            "INSERT INTO django_migrations (app, name, applied) VALUES (%s, %s, datetime('now'))",
            ['game', '0008_alter_gamesession_company_and_more']
        )
        
        print("Migration record inserted successfully.")
        
        # Verify the insertion
        cursor.execute(
            "SELECT * FROM django_migrations WHERE app='game' AND name='0008_alter_gamesession_company_and_more'"
        )
        if cursor.fetchone():
            print("Verification successful. Migration is now marked as applied.")
        else:
            print("Verification failed. Migration record was not inserted correctly.")

if __name__ == "__main__":
    fix_migration_issue()
