/* Global TinyMCE Styles */

/* Fix for TinyMCE visibility issue */
.tox-tinymce {
    visibility: visible !important;
    display: block !important;
    opacity: 1 !important;
}

.tox-tinymce[style*="visibility: hidden"] {
    visibility: visible !important;
}

/* Additional TinyMCE styling */
.tox-editor-container {
    background-color: white !important;
    visibility: visible !important;
    width: 100% !important;
}

.tinymce-editor {
    min-height: 300px;
}

/* Make sure the editor is visible */
.tox-tinymce {
    border-radius: 0.25rem !important;
    border: 1px solid #ced4da !important;
    box-shadow: none !important;
    width: 100% !important;
    max-width: none !important;
    min-height: 400px !important; /* Increased height for multiple toolbars */
}

/* Adjust height for multiple toolbars */
.tox-editor-container {
    min-height: 350px !important;
}

.tox .tox-statusbar {
    border-top: 1px solid #ced4da !important;
}

.tox .tox-toolbar, .tox .tox-toolbar__overflow, .tox .tox-toolbar__primary {
    background: #f8f9fa !important;
}

/* Fix for TinyMCE iframe */
.tox-edit-area__iframe {
    visibility: visible !important;
    display: block !important;
    width: 100% !important;
}

/* Ensure TinyMCE toolbar is visible */
.tox-toolbar__primary,
.tox-toolbar-overlord,
.tox-toolbar__overflow {
    visibility: visible !important;
    display: flex !important;
}

/* Ensure multiple toolbars are visible */
.tox-editor-header {
    visibility: visible !important;
    display: block !important;
}

/* Enhance toolbar buttons */
.tox-tbtn {
    visibility: visible !important;
    display: flex !important;
    border-radius: 3px !important;
    margin: 0 1px !important;
}

/* Improve button hover state */
.tox-tbtn:hover {
    background-color: #e9ecef !important;
    box-shadow: 0 0 0 1px rgba(0,0,0,0.1) !important;
}

/* Active button state */
.tox-tbtn--enabled,
.tox-tbtn--active {
    background-color: #e2e6ea !important;
    box-shadow: inset 0 0 0 1px rgba(0,0,0,0.1) !important;
}

/* Ensure toolbar button icons are visible */
.tox-icon,
.tox-tbtn__icon-wrap {
    visibility: visible !important;
    display: flex !important;
}

/* Fix for TinyMCE menu bar */
.tox-menubar {
    visibility: visible !important;
    display: flex !important;
}

/* Fix for TinyMCE edit area */
.tox-edit-area {
    width: 100% !important;
}

/* Fallback textarea styling */
.tinymce-fallback {
    display: none;
    margin-top: 10px;
}

/* Fallback textarea when shown */
.tinymce-fallback.visible {
    display: block;
}

/* Ensure TinyMCE is above other elements */
.tox-tinymce-aux {
    z-index: 10000 !important;
}
