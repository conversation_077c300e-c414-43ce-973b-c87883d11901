"""
Management command to fix owner permissions on assistants.

This command ensures that all company owners have the correct permissions on their assistants.
It can be run to fix permissions for all companies or for a specific company.
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Permission
from django.contrib.contenttypes.models import ContentType
from guardian.shortcuts import assign_perm, get_perms
from accounts.models import Company
from assistants.models import Assistant

class Command(BaseCommand):
    help = 'Ensures all company owners have the correct permissions on their assistants.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company',
            type=int,
            help='Specify a company ID to update permissions for just that company',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information about permissions being assigned',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without actually making changes',
        )
        parser.add_argument(
            '--create-missing',
            action='store_true',
            help='Create missing permissions if they do not exist',
        )

    def handle(self, *args, **options):
        company_id = options.get('company')
        verbose = options.get('verbose', False)
        dry_run = options.get('dry_run', False)
        create_missing = options.get('create_missing', False)

        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN MODE: No changes will be made"))

        # Get companies to process
        if company_id:
            companies = Company.objects.filter(id=company_id)
            if not companies.exists():
                self.stdout.write(self.style.ERROR(f"Company with ID {company_id} not found."))
                return
        else:
            companies = Company.objects.all()

        self.stdout.write(f"Processing {companies.count()} companies...")

        # Define the permissions that owners should have on assistants
        assistant_perms = [
            'assistants.view_assistant',
            'assistants.change_assistant',
            'assistants.delete_assistant',
            'assistants.view_assistant_usage',
            'assistants.view_assistant_analytics'
        ]

        # Ensure permissions exist if requested
        if create_missing:
            self.stdout.write("Checking if permissions exist...")
            assistant_content_type = ContentType.objects.get_for_model(Assistant)

            for perm_string in assistant_perms:
                app_label, codename = perm_string.split('.')
                try:
                    # Check if permission exists
                    Permission.objects.get(
                        content_type=assistant_content_type,
                        codename=codename
                    )
                    if verbose:
                        self.stdout.write(f"  Permission '{perm_string}' exists")
                except Permission.DoesNotExist:
                    # Create permission
                    name = ' '.join(word.capitalize() for word in codename.split('_'))
                    if not dry_run:
                        Permission.objects.create(
                            codename=codename,
                            name=name,
                            content_type=assistant_content_type
                        )
                        self.stdout.write(self.style.SUCCESS(f"  Created permission '{perm_string}'"))
                    else:
                        self.stdout.write(self.style.WARNING(f"  Would create permission '{perm_string}' (dry run)"))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"  Error checking/creating permission '{perm_string}': {e}"))

        # Track statistics
        companies_processed = 0
        companies_skipped = 0
        assistants_processed = 0
        permissions_assigned = 0
        errors = 0

        # Process each company
        for company in companies:
            owner = company.owner
            if not owner:
                self.stdout.write(self.style.WARNING(f"Company '{company.name}' (ID: {company.id}) has no owner. Skipping."))
                companies_skipped += 1
                continue

            self.stdout.write(f"Processing company '{company.name}' (ID: {company.id}) with owner '{owner.username}'")
            companies_processed += 1

            # Get all assistants for this company
            assistants = Assistant.objects.filter(company=company)
            if verbose:
                self.stdout.write(f"  Found {assistants.count()} assistants")

            # Process each assistant
            for assistant in assistants:
                if verbose:
                    self.stdout.write(f"  Processing assistant '{assistant.name}' (ID: {assistant.id})")
                assistants_processed += 1

                # Get current permissions for the owner on this assistant
                current_perms = get_perms(owner, assistant)
                if verbose:
                    self.stdout.write(f"    Current permissions: {current_perms}")

                # Assign any missing permissions
                missing_perms = []
                for perm in assistant_perms:
                    perm_codename = perm.split('.')[-1]
                    if perm_codename not in current_perms:
                        missing_perms.append(perm)

                if not missing_perms:
                    if verbose:
                        self.stdout.write(self.style.SUCCESS(f"    All permissions already assigned"))
                    continue

                for perm in missing_perms:
                    try:
                        if not dry_run:
                            assign_perm(perm, owner, assistant)
                            permissions_assigned += 1
                            if verbose:
                                self.stdout.write(self.style.SUCCESS(f"    Assigned '{perm}' to owner"))
                        else:
                            self.stdout.write(self.style.WARNING(f"    Would assign '{perm}' to owner (dry run)"))
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"    Error assigning '{perm}' to owner: {e}"))
                        errors += 1

                # Verify permissions after assignment
                if verbose and not dry_run:
                    final_perms = get_perms(owner, assistant)
                    self.stdout.write(f"    Final permissions: {final_perms}")

            self.stdout.write(self.style.SUCCESS(f"Processed all assistants for company '{company.name}'"))

        # Print summary
        self.stdout.write("\nSummary:")
        self.stdout.write(f"  Companies processed: {companies_processed}")
        self.stdout.write(f"  Companies skipped: {companies_skipped}")
        self.stdout.write(f"  Assistants processed: {assistants_processed}")
        if not dry_run:
            self.stdout.write(f"  Permissions assigned: {permissions_assigned}")
        else:
            self.stdout.write(f"  Permissions that would be assigned: {permissions_assigned}")
        self.stdout.write(f"  Errors encountered: {errors}")

        if dry_run:
            self.stdout.write(self.style.WARNING("\nThis was a dry run. No changes were made."))
            self.stdout.write(self.style.WARNING("Run without --dry-run to apply the changes."))
        else:
            self.stdout.write(self.style.SUCCESS("\nDone fixing owner permissions on assistants."))
