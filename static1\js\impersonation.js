/**
 * Impersonation UI functionality
 */
document.addEventListener('DOMContentLoaded', function() {
    // Check if we're in impersonation mode by looking for various indicators
    const impersonatedUserMeta = document.querySelector('meta[name="impersonated-user"]');
    const realUserMeta = document.querySelector('meta[name="real-user"]');
    const isImpersonateMeta = document.querySelector('meta[name="is-impersonating"]');

    // Check for debug info in the page
    const debugRealUser = document.getElementById('debug-real-user');

    // Force impersonation mode if any of these indicators are present
    const isImpersonating =
        (isImpersonateMeta && isImpersonateMeta.getAttribute('content') === 'true') ||
        (window.debug_impersonation_info && window.debug_impersonation_info.is_impersonate === true);

    if (isImpersonating) {
        // Ensure body has the impersonating class
        document.body.classList.add('is-impersonating');

        // Create floating button if it doesn't exist
        if (!document.querySelector('.impersonation-float-button')) {
            createFloatingButton();
        }

        // Ensure the impersonation indicator is visible
        const indicator = document.querySelector('.impersonation-indicator');
        if (indicator) {
            // Make sure it's visible
            indicator.style.display = 'block';
        } else {
            // Create it if it doesn't exist
            createImpersonationIndicator();
        }

        // Set up periodic check to ensure the indicator remains visible, but at a lower frequency
        setInterval(checkImpersonationUI, 5000);

        // Also run once after a short delay to ensure it's applied
        setTimeout(checkImpersonationUI, 1000);
    }
});

/**
 * Creates the floating impersonation button
 */
function createFloatingButton() {

    // Remove any existing buttons/dropdowns to avoid duplicates
    const existingButton = document.querySelector('.impersonation-float-button');
    if (existingButton) {
        existingButton.remove();
    }

    const existingDropdown = document.querySelector('.impersonation-dropdown');
    if (existingDropdown) {
        existingDropdown.remove();
    }

    // Create new button
    const button = document.createElement('div');
    button.className = 'impersonation-float-button';
    button.id = 'impersonation-float-button';
    button.innerHTML = '<i class="bi bi-person-badge"></i>';
    button.title = 'Impersonation Options';

    // Apply styles directly to ensure visibility
    button.style.position = 'fixed';
    button.style.bottom = '20px';
    button.style.right = '20px';
    button.style.backgroundColor = '#dc3545';
    button.style.color = 'white';
    button.style.borderRadius = '50%';
    button.style.width = '60px';
    button.style.height = '60px';
    button.style.display = 'flex';
    button.style.alignItems = 'center';
    button.style.justifyContent = 'center';
    button.style.boxShadow = '0 4px 10px rgba(0,0,0,0.3)';
    button.style.zIndex = '9998';
    button.style.cursor = 'pointer';
    button.style.fontSize = '24px';

    // Create dropdown menu
    const dropdown = document.createElement('div');
    dropdown.className = 'impersonation-dropdown';
    dropdown.id = 'impersonation-dropdown';

    // Apply styles directly to ensure proper rendering
    dropdown.style.position = 'fixed';
    dropdown.style.bottom = '85px';
    dropdown.style.right = '20px';
    dropdown.style.backgroundColor = 'white';
    dropdown.style.borderRadius = '5px';
    dropdown.style.boxShadow = '0 4px 10px rgba(0,0,0,0.3)';
    dropdown.style.zIndex = '9997';
    dropdown.style.display = 'none';
    dropdown.style.padding = '10px';
    dropdown.style.minWidth = '200px';

    dropdown.innerHTML = `
        <div class="dropdown-header" style="font-weight: bold; padding: 5px; color: #333;">Impersonation Options</div>
        <div class="dropdown-divider" style="height: 1px; background-color: #e9ecef; margin: 5px 0;"></div>
        <a href="/superadmin/dashboard/" class="dropdown-item" style="display: block; padding: 8px 10px; color: #333; text-decoration: none; border-radius: 3px;">
            <i class="bi bi-shield-lock"></i> Return to Superadmin
        </a>
        <a href="/impersonate/stop/" class="dropdown-item" style="display: block; padding: 8px 10px; color: #333; text-decoration: none; border-radius: 3px;">
            <i class="bi bi-x-circle"></i> Stop Impersonating
        </a>
        <a href="/debug/impersonation/" target="_blank" class="dropdown-item" style="display: block; padding: 8px 10px; color: #333; text-decoration: none; border-radius: 3px; background-color: #fff3cd;">
            <i class="bi bi-bug"></i> Debug Impersonation
        </a>
    `;

    // Toggle dropdown when button is clicked
    button.addEventListener('click', function(event) {
        event.stopPropagation();
        if (dropdown.style.display === 'none' || dropdown.style.display === '') {
            dropdown.style.display = 'block';
        } else {
            dropdown.style.display = 'none';
        }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function(event) {
        if (!button.contains(event.target) && !dropdown.contains(event.target)) {
            dropdown.style.display = 'none';
        }
    });

    // Add to document
    document.body.appendChild(button);
    document.body.appendChild(dropdown);
}

/**
 * Creates the impersonation indicator at the top of the page
 */
function createImpersonationIndicator() {

    // Get impersonated and real user names from various sources
    let impersonatedUser = document.querySelector('meta[name="impersonated-user"]')?.content;
    let realUser = document.querySelector('meta[name="real-user"]')?.content;

    // Try to get from debug info
    if (!impersonatedUser || !realUser) {
        const debugRealUser = document.getElementById('debug-real-user');
        const debugCurrentUser = document.getElementById('debug-current-user');

        if (debugCurrentUser && debugCurrentUser.textContent) {
            impersonatedUser = debugCurrentUser.textContent;
        }

        if (debugRealUser && debugRealUser.textContent) {
            realUser = debugRealUser.textContent;
        }
    }

    // Try to get from window variables
    if (!impersonatedUser && window.currentUsername) {
        impersonatedUser = window.currentUsername;
    }

    if (!realUser && window.realUserUsername) {
        realUser = window.realUserUsername;
    }

    // Try to get from debug info object
    if (window.debug_impersonation_info) {
        if (!impersonatedUser && window.debug_impersonation_info.impersonated_user_username) {
            impersonatedUser = window.debug_impersonation_info.impersonated_user_username;
        }

        if (!realUser && window.debug_impersonation_info.real_user_username) {
            realUser = window.debug_impersonation_info.real_user_username;
        }
    }

    // Fallback values
    impersonatedUser = impersonatedUser || 'current user';
    realUser = realUser || 'superadmin';

    const indicator = document.createElement('div');
    indicator.id = 'impersonation-indicator';
    indicator.className = 'impersonation-indicator';
    indicator.style.display = 'block';
    indicator.style.visibility = 'visible';
    indicator.style.opacity = '1';
    indicator.style.zIndex = '9999';
    indicator.innerHTML = `
        <i class="bi bi-person-badge"></i>
        <strong>IMPERSONATION MODE:</strong> You are currently impersonating <strong>${impersonatedUser}</strong>.
        (Logged in as ${realUser})
        <a href="/superadmin/dashboard/" class="btn btn-sm">
            <i class="bi bi-shield-lock"></i> Return to Superadmin
        </a>
        <a href="/impersonate/stop/" class="btn btn-sm">
            <i class="bi bi-x-circle"></i> Stop Impersonating
        </a>
    `;

    // Add to document - try to insert at the very top
    const firstChild = document.body.firstChild;
    if (firstChild) {
        document.body.insertBefore(indicator, firstChild);
    } else {
        document.body.prepend(indicator);
    }

    // Add padding to body
    document.body.classList.add('is-impersonating');
    document.body.style.paddingTop = '50px';
}

/**
 * Checks that the impersonation UI elements are visible
 */
function checkImpersonationUI() {
    // Check if we're in impersonation mode by looking for various indicators
    const impersonatedUserMeta = document.querySelector('meta[name="impersonated-user"]');
    const realUserMeta = document.querySelector('meta[name="real-user"]');
    const isImpersonateMeta = document.querySelector('meta[name="is-impersonating"]');

    // Check for debug info in the page
    const debugRealUser = document.getElementById('debug-real-user');

    // Force impersonation mode if any of these indicators are present
    const isImpersonating =
        (isImpersonateMeta && isImpersonateMeta.getAttribute('content') === 'true') ||
        (window.debug_impersonation_info && window.debug_impersonation_info.is_impersonate === true);

    // Only proceed if we're in impersonation mode
    if (!isImpersonating) {
        return;
    }

    // Check indicator
    let indicator = document.querySelector('.impersonation-indicator');

    // If indicator doesn't exist, create it
    if (!indicator) {
        createImpersonationIndicator();
        indicator = document.querySelector('.impersonation-indicator');
    }

    // Make sure indicator is visible
    if (indicator) {
        const style = window.getComputedStyle(indicator);
        if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0' || parseFloat(style.opacity) < 0.5) {
            indicator.style.display = 'block';
            indicator.style.visibility = 'visible';
            indicator.style.opacity = '1';
            indicator.style.zIndex = '9999';
        }
    }

    // Check floating button
    let button = document.querySelector('.impersonation-float-button');

    // If button doesn't exist, create it
    if (!button) {
        createFloatingButton();
        button = document.querySelector('.impersonation-float-button');
    }

    // Make sure button is visible
    if (button) {
        const style = window.getComputedStyle(button);
        if (style.display === 'none' || style.visibility === 'hidden' || style.opacity === '0' || parseFloat(style.opacity) < 0.5) {
            button.style.display = 'flex';
            button.style.visibility = 'visible';
            button.style.opacity = '1';
            button.style.zIndex = '9998';
        }
    }

    // Ensure body has padding
    if (!document.body.classList.contains('is-impersonating')) {
        document.body.classList.add('is-impersonating');
        document.body.style.paddingTop = '50px';
    }
}
