from django.contrib import admin
from .models import (
    Company, CorporateUser, Certificate,
    Leaderboard, LeaderboardEntry, GameAccess,
    CompanyInvitation, RegistrationLink
)

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ('name', 'owner', 'created_at', 'allow_leaderboard')
    search_fields = ('name', 'owner__username', 'owner__email')
    list_filter = ('allow_leaderboard',)
    raw_id_fields = ('owner',)


@admin.register(CorporateUser)
class CorporateUserAdmin(admin.ModelAdmin):
    list_display = ('user', 'company', 'job_title', 'is_company_admin')
    list_filter = ('company', 'is_company_admin')
    search_fields = ('user__username', 'user__email', 'job_title', 'department')
    raw_id_fields = ('user',)


@admin.register(Certificate)
class CertificateAdmin(admin.ModelAdmin):
    list_display = ('user', 'title', 'issue_date', 'verification_code', 'final_score')
    search_fields = ('user__username', 'user__email', 'verification_code')
    list_filter = ('issue_date', 'template')
    readonly_fields = ('id', 'verification_code')
    raw_id_fields = ('user', 'game_session')


@admin.register(Leaderboard)
class LeaderboardAdmin(admin.ModelAdmin):
    list_display = ('name', 'company', 'is_global', 'time_period', 'active')
    list_filter = ('is_global', 'time_period', 'active')
    search_fields = ('name', 'company__name')


@admin.register(LeaderboardEntry)
class LeaderboardEntryAdmin(admin.ModelAdmin):
    list_display = ('user', 'leaderboard', 'score', 'highest_role', 'recorded_at')
    list_filter = ('leaderboard', 'recorded_at')
    search_fields = ('user__username', 'user__email')
    raw_id_fields = ('user', 'leaderboard')


@admin.register(GameAccess)
class GameAccessAdmin(admin.ModelAdmin):
    list_display = ('company', 'game_name', 'is_active', 'access_granted_at', 'access_expires_at')
    list_filter = ('is_active', 'access_granted_at')
    search_fields = ('company__name', 'game_name', 'game_id')
    raw_id_fields = ('company',)


@admin.register(CompanyInvitation)
class CompanyInvitationAdmin(admin.ModelAdmin):
    list_display = ('email', 'company', 'status', 'invited_by', 'invited_at', 'expires_at')
    list_filter = ('status', 'invited_at', 'company')
    search_fields = ('email', 'company__name', 'invited_by__username')
    raw_id_fields = ('company', 'invited_by')
    readonly_fields = ('token',)


@admin.register(RegistrationLink)
class RegistrationLinkAdmin(admin.ModelAdmin):
    list_display = ('company', 'intended_role', 'created_by', 'is_active', 'uses_count', 'created_at')
    list_filter = ('is_active', 'intended_role', 'created_at', 'company')
    search_fields = ('company__name', 'created_by__username', 'notes')
    raw_id_fields = ('company', 'created_by')
    readonly_fields = ('token', 'uses_count')
