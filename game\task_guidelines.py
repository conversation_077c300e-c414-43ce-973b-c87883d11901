"""
Task Guidelines Module for Rwenzori Innovations Game

This module provides functions to get guidelines for specific tasks
and format them for inclusion in prompts.
"""

def get_guidelines_for_task(task_id):
    """
    Get the guidelines for a specific task.

    Args:
        task_id: The ID of the task

    Returns:
        A list of guidelines for the task
    """
    # This is a simplified version - in a real implementation,
    # these would be loaded from a database or configuration file
    guidelines = {
        "cover_letter": [
            "Address the specific requirements mentioned in the job posting",
            "Highlight relevant skills and experiences",
            "Maintain a professional tone",
            "Keep it concise (1 page maximum)",
            "Include a proper greeting and closing",
            "Express enthusiasm for the position"
        ],
        "job_analysis": [
            "Identify all key skills and qualifications",
            "Categorize responsibilities clearly",
            "Analyze the work environment and expectations",
            "Present information in a structured format",
            "Include both technical and soft skill requirements",
            "Highlight any unique aspects of the position"
        ],
        "onboarding_plan": [
            "Include a clear timeline with specific milestones",
            "Address both administrative and training needs",
            "Consider team integration activities",
            "Specify resources needed for each step",
            "Include feedback mechanisms",
            "Ensure compliance with company policies"
        ],
        "marketing_campaign": [
            "Define clear target audience segments",
            "Include specific marketing channels and tactics",
            "Provide a realistic budget breakdown",
            "Set measurable goals and KPIs",
            "Include a timeline with key milestones",
            "Address potential challenges and contingencies"
        ],
        "competitor_analysis": [
            "Provide detailed comparison of features and pricing",
            "Identify competitive advantages and vulnerabilities",
            "Include specific positioning recommendations",
            "Use data and metrics to support analysis",
            "Consider market trends and future developments",
            "Organize information in a clear, structured format"
        ]
    }

    # Return guidelines for the task or a default set if not found
    return guidelines.get(task_id, ["Be professional", "Be thorough", "Be clear and concise"])

def format_guidelines_for_prompt(guidelines):
    """
    Format a list of guidelines into a string for inclusion in a prompt.

    Args:
        guidelines: A list of guideline strings

    Returns:
        A formatted string of guidelines
    """
    return "\n".join([f"- {guideline}" for guideline in guidelines])
