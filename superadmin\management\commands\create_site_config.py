from django.core.management.base import BaseCommand
from superadmin.models import SiteConfiguration


class Command(BaseCommand):
    help = 'Creates a default SiteConfiguration instance if one does not exist'

    def handle(self, *args, **options):
        if not SiteConfiguration.objects.exists():
            site_config = SiteConfiguration.objects.create(
                help_url="https://example.com/help"  # Default help URL
            )
            self.stdout.write(self.style.SUCCESS(
                f'Successfully created SiteConfiguration with ID {site_config.id}'))
        else:
            self.stdout.write(self.style.WARNING(
                'A SiteConfiguration instance already exists. No new instance was created.'))
