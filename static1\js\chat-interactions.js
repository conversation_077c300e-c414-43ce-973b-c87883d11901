// Initialize flag answer functionality
function initFlagAnswerButtons() {
    const flagButtons = document.querySelectorAll('.flag-answer-btn');
    const flagModal = document.getElementById('flagAnswerModal');
    const flagQuestion = document.getElementById('flag-question');
    const flagAnswer = document.getElementById('flag-answer');
    const submitFlagBtn = document.getElementById('submit-flag-btn');
    const flagForm = document.getElementById('flag-answer-form');

    if (!flagModal || !flagButtons.length) return;

    // Set up flag buttons
    flagButtons.forEach(button => {
        button.addEventListener('click', function() {
            const question = this.getAttribute('data-question');
            const answer = this.getAttribute('data-answer');

            flagQuestion.value = question;
            flagAnswer.value = answer;

            // Show the modal
            const bsModal = new bootstrap.Modal(flagModal);
            bsModal.show();
        });
    });

    // Handle flag submission
    if (submitFlagBtn) {
        submitFlagBtn.addEventListener('click', function() {
            const formData = new FormData(flagForm);
            const companyId = document.querySelector('meta[name="company-id"]').content;
            const assistantId = document.querySelector('meta[name="assistant-id"]').content;

            fetch(`/assistant/company/${companyId}/assistants/${assistantId}/flag-question/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Close the modal
                    bootstrap.Modal.getInstance(flagModal).hide();

                    // Show success message
                    showToast('Thank you for your feedback! We will use it to improve our assistant.');

                    // Reset the form
                    flagForm.reset();
                } else {
                    showToast('Error: ' + (data.error || 'Failed to submit feedback'), 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showToast('Error submitting feedback. Please try again.', 'error');
            });
        });
    }
}

// Initialize upvote answer functionality
function initUpvoteAnswerButtons() {
    const upvoteButtons = document.querySelectorAll('.upvote-answer-btn');

    if (!upvoteButtons.length) return;

    upvoteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const interactionId = this.getAttribute('data-interaction-id');
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

            // Send AJAX request to upvote
            fetch(`/assistant/interaction/${interactionId}/upvote/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update the button appearance
                    if (data.upvote_status === 'added') {
                        button.classList.remove('btn-outline-success');
                        button.classList.add('btn-success');
                        button.innerHTML = '<i class="bi bi-hand-thumbs-up-fill me-1"></i> Helpful';

                        // Show a thank you message
                        const thankYouDiv = document.createElement('div');
                        thankYouDiv.className = 'small text-success mt-1';
                        thankYouDiv.innerHTML = '<i class="bi bi-check-circle me-1"></i> ' + data.message;
                        button.parentNode.appendChild(thankYouDiv);

                        // Remove the message after 3 seconds
                        setTimeout(() => {
                            thankYouDiv.remove();
                        }, 3000);
                    } else {
                        button.classList.remove('btn-success');
                        button.classList.add('btn-outline-success');
                        button.innerHTML = '<i class="bi bi-hand-thumbs-up me-1"></i> Helpful';
                    }
                }
            })
            .catch(error => {
                console.error('Error upvoting answer:', error);
                showToast('Error upvoting answer. Please try again.', 'error');
            });
        });
    });
}

// Helper function to show toast messages
function showToast(message, type = 'success') {
    // Check if toastr is available
    if (typeof toastr !== 'undefined') {
        toastr[type](message);
    } else {
        // Fallback to alert if toastr is not available
        alert(message);
    }
}

// Initialize all interaction buttons when the document is ready
document.addEventListener('DOMContentLoaded', function() {
    initFlagAnswerButtons();
    initUpvoteAnswerButtons();

    // Also add a mutation observer to handle dynamically added buttons
    const chatBox = document.getElementById('chat-box');
    if (chatBox) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    // Re-initialize buttons when new messages are added
                    initFlagAnswerButtons();
                    initUpvoteAnswerButtons();
                }
            });
        });

        // Start observing the chat box for added messages
        observer.observe(chatBox, { childList: true, subtree: true });
    }
});
