import os
import uuid
import json
from datetime import datetime
from django.conf import settings
from django.template.loader import render_to_string
from django.utils import timezone
from .models import Certificate

# Check if weasyprint is available
try:
    from weasyprint import HTML, CSS
    from weasyprint.text.fonts import FontConfiguration
    WEASYPRINT_AVAILABLE = True
except (ImportError, OSError) as e:
    print(f"WeasyPrint not available: {str(e)}")
    WEASYPRINT_AVAILABLE = False
    # Define dummy classes to prevent import errors
    class HTML:
        def __init__(self, **kwargs):
            pass
        def write_pdf(self, **kwargs):
            return None

    class CSS:
        def __init__(self, **kwargs):
            pass

    class FontConfiguration:
        pass


def generate_certificate_html(user, game_session, template='standard', certificate=None):
    """
    Generate HTML for a certificate based on the specified template.

    Args:
        user: The user who completed the game
        game_session: The completed game session
        template: The certificate template to use
        certificate: Optional certificate object to use for additional data

    Returns:
        str: HTML content for the certificate
    """
    # Get user's full name or username
    full_name = f"{user.first_name} {user.last_name}".strip()
    if not full_name:
        full_name = user.username

    # Get company name if available
    company_name = None
    try:
        if hasattr(user, 'corporate_profile'):
            company_name = user.corporate_profile.company.name
    except:
        pass

    # Get highest role achieved
    highest_role = game_session.current_role

    # Format date
    issue_date = timezone.now()
    formatted_date = issue_date.strftime("%B %d, %Y")

    # Generate verification code
    verification_code = str(uuid.uuid4())[:8].upper()

    # Get completed games
    completed_games = []
    if certificate and hasattr(certificate, 'get_completed_games'):
        completed_games = certificate.get_completed_games()
    elif hasattr(game_session, 'get_completed_roles'):
        # If no certificate is provided, use the game session's completed roles
        completed_roles = game_session.get_completed_roles()
        if completed_roles:
            completed_games = ["Corporate Prompt Master"]

    # If there are no completed games but the game is completed, add the current game
    if not completed_games and game_session.game_completed:
        completed_games = ["Corporate Prompt Master"]

    # Context for the template
    context = {
        'user': user,
        'full_name': full_name,
        'company_name': company_name,
        'game_session': game_session,
        'highest_role': highest_role.replace('_', ' ').title(),
        'score': game_session.performance_score,
        'issue_date': formatted_date,
        'verification_code': verification_code,
        'certificate_id': str(uuid.uuid4()),
        'completed_games': completed_games,
    }

    # Render the appropriate template
    template_path = f'corporate/certificates/{template}.html'
    try:
        html_content = render_to_string(template_path, context)
    except:
        # Fallback to standard template if the specified one doesn't exist
        html_content = render_to_string('corporate/certificates/standard.html', context)

    return html_content


def generate_certificate_pdf(user, game_session, template='standard'):
    """
    Generate a PDF certificate for a user who completed the game.

    Args:
        user: The user who completed the game
        game_session: The completed game session
        template: The certificate template to use

    Returns:
        tuple: (pdf_bytes, certificate_object) or (None, certificate_object) if weasyprint is not available
    """
    # Generate verification code
    verification_code = str(uuid.uuid4())[:8].upper()

    # Determine completed games
    completed_games = []
    if hasattr(game_session, 'get_completed_roles'):
        completed_roles = game_session.get_completed_roles()
        if completed_roles:
            completed_games = ["Corporate Prompt Master"]

    # If there are no completed games but the game is completed, add the current game
    if not completed_games and game_session.game_completed:
        completed_games = ["Corporate Prompt Master"]

    # Create or update certificate record
    certificate, created = Certificate.objects.update_or_create(
        user=user,
        game_session=game_session,
        defaults={
            'title': "Prompt Engineering Excellence",
            'description': "Has successfully completed the Corporate Prompt Master training program.",
            'issue_date': timezone.now(),
            'template': template,
            'final_score': game_session.performance_score,
            'highest_role': game_session.current_role,
            'verification_code': verification_code,
            'completed_games': json.dumps(completed_games),
        }
    )

    # Generate HTML content with the certificate object
    html_content = generate_certificate_html(user, game_session, template, certificate)

    # If weasyprint is not available, return None for pdf_bytes
    if not WEASYPRINT_AVAILABLE:
        return None, certificate

    # Configure fonts
    font_config = FontConfiguration()

    # Get CSS for the certificate
    css_path = os.path.join(settings.STATIC_ROOT, 'corporate/css/certificate.css')
    css = CSS(filename=css_path) if os.path.exists(css_path) else None

    # Generate PDF
    html = HTML(string=html_content)
    if css:
        pdf_bytes = html.write_pdf(font_config=font_config, stylesheets=[css])
    else:
        pdf_bytes = html.write_pdf(font_config=font_config)

    return pdf_bytes, certificate


def verify_certificate(verification_code):
    """
    Verify a certificate using its verification code.

    Args:
        verification_code: The verification code to check

    Returns:
        Certificate or None: The certificate if found, None otherwise
    """
    try:
        return Certificate.objects.get(verification_code=verification_code)
    except Certificate.DoesNotExist:
        return None
