"""
Character definitions for the Context-Aware Game.
This module contains all character information used in the game.
"""

# Characters
CHARACTERS = {
    # HR Department
    "hr": {
        "name": "<PERSON><PERSON>",
        "title": "HR Manager",
        "avatar": "👩‍💼",
        "color": "#4a86e8"
    },
    # Team Lead (for junior_assistant)
    "manager": {
        "name": "<PERSON>",
        "title": "Team Lead",
        "avatar": "👨‍💼",
        "color": "#43a047"
    },
    # Marketing Department
    "vp_marketing": {
        "name": "Faith Nakato",
        "title": "VP of Marketing",
        "avatar": "👩‍💼",
        "color": "#e91e63"
    },
    # Operations Department
    "vp": {  # Keeping this for backward compatibility
        "name": "<PERSON> Atim",
        "title": "VP of Operations",
        "avatar": "👩‍💼",
        "color": "#f57c00"
    },
    "vp_operations": {
        "name": "<PERSON> Atim",
        "title": "VP of Operations",
        "avatar": "👩‍💼",
        "color": "#f57c00"
    },
    # Finance Department
    "vp_finance": {
        "name": "<PERSON>",
        "title": "VP of Finance",
        "avatar": "👨‍💼",
        "color": "#009688"
    },
    # Executive Level
    "coo": {
        "name": "Daniel Wasswa",
        "title": "Chief Operating Officer",
        "avatar": "👨‍💼",
        "color": "#7b1fa2"
    },
    "ceo": {
        "name": "Joy Nantongo",
        "title": "Chief Executive Officer",
        "avatar": "👩‍💼",
        "color": "#c62828"
    },
    "board": {
        "name": "Board of Directors",
        "title": "Board of Directors",
        "avatar": "👥",
        "color": "#000000"
    },
    # System messages
    "system": {
        "name": "System",
        "title": "Game System",
        "avatar": "🖥️",
        "color": "#607d8b"
    },
    # HR Department - Director
    "hr_director": {
        "name": "Peace Akello",
        "title": "HR Director",
        "avatar": "👩‍💼",
        "color": "#6a1b9a" # Professional Purple
    }
}

def get_character(character_id):
    """
    Get a character by ID.
    
    Args:
        character_id (str): The ID of the character to retrieve
        
    Returns:
        dict: The character information or None if not found
    """
    return CHARACTERS.get(character_id)

def get_character_name(character_id):
    """
    Get a character's name by ID.
    
    Args:
        character_id (str): The ID of the character to retrieve
        
    Returns:
        str: The character's name or "Unknown" if not found
    """
    character = get_character(character_id)
    return character["name"] if character else "Unknown"

def get_all_characters():
    """
    Get all characters.
    
    Returns:
        dict: All character information
    """
    return CHARACTERS
