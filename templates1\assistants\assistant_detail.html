{% extends "assistants/base.html" %}
{% load static crispy_forms_tags %}

{% block title %}{{ assistant.name }} Details{% endblock %}

{% block page_header %}
    {# New wrapper for logo + title/status row #}
    <div class="d-flex align-items-center mb-2">
        {# Logo with Fallback #}
        <div class="me-3 flex-shrink-0">
            {% with logo_url=assistant.get_logo_url %}
                {% if logo_url %}
                    <img src="{{ logo_url }}" alt="{{ assistant.name }} logo" class="rounded" style="height: 50px; width: auto; max-width: 100%; object-fit: contain;">
                {% else %}
                    <div class="bg-light rounded d-flex align-items-center justify-content-center" style="height: 50px; width: 50px;">
                        <i class="bi bi-robot text-muted fs-4"></i>
                    </div>
                {% endif %}
            {% endwith %}
        </div>
        {# Existing Title/Status/Description #}
        <div class="flex-grow-1">
             <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center"> {# Wrap title and button #}
                    <h1 class="h2 me-2 mb-0">{{ assistant.name }}</h1>
                    {# Favorite Button - Added #}
                    {% if user.is_authenticated %}
                        <button
                    class="like-button btn btn-sm p-1 {% if is_favorited %}text-danger{% else %}text-secondary{% endif %}"
                    data-item-id="{{ assistant.id }}"
                    data-item-type="assistant"
                    title="{% if is_favorited %}Remove from Favorites{% else %}Add to Favorites{% endif %}"
                    style="background: none; border: none; line-height: 1;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" class="bi bi-heart-fill" viewBox="0 0 16 16">
                        <path fill-rule="evenodd" d="M8 1.314C12.438-3.248 23.534 4.735 8 15-7.534 4.736 3.562-3.248 8 1.314"/>
                    </svg>
                </button>
                    {% endif %}
                    {# End Favorite Button #}
                </div>
                <span class="badge bg-{% if assistant.is_active %}success{% else %}secondary{% endif %}">
                    {% if assistant.is_active %}Active{% else %}Inactive{% endif %}
                </span>
            </div>
            <p class="text-muted mb-0">{{ assistant.description|default:"No description provided." }}</p> {# Moved description here #}
        </div>
    </div>
{% endblock %}

{% block main_content %}
<div class="row">
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                Chat with {{ assistant.name }}
            </div>
            <div class="card-body" style="height: 400px; overflow-y: scroll;" id="chat-log">
                {% for interaction in interactions reversed %} {# Show oldest first #}
                    <div class="message user-message mb-2">
                        <strong>You:</strong> {{ interaction.prompt|linebreaksbr }}
                    </div>
                    <div class="message assistant-message mb-2">
                        <strong>{{ assistant.name }}:</strong> {{ interaction.response|linebreaksbr }}
                        {# Rating Stars - Added #}
                        <div class="rating-stars mt-1" data-interaction-id="{{ interaction.id }}">
                            <small>Rate this response:</small>
                            {% for i in "12345" %}
                                <span class="star" data-value="{{ i }}" style="cursor: pointer; color: {% if interaction.rating >= i|add:0 %}gold{% else %}grey{% endif %}; font-size: 1.2em;">★</span>
                            {% endfor %}
                            <small class="rating-feedback ms-2" style="display: {% if interaction.rating %}inline{% else %}none{% endif %};">Thanks!</small>
                        </div>
                         {# End Rating Stars #}
                    </div>
                {% empty %}
                    <p class="text-center text-muted">No chat history yet. Start the conversation!</p>
                {% endfor %}
            </div>
            <div class="card-footer">
                {# Add hx-indicator span #}
                <span id="chat-indicator" class="htmx-indicator text-muted small mb-2" style="display: block;">Sending...</span>
                {# Add HTMX attributes to the form #}
                <form id="chat-form"
                      method="post"
                      action="{% url 'assistants:interact' company.id assistant.id %}"
                      hx-post="{% url 'assistants:interact' company.id assistant.id %}"
                      hx-target="#chat-log"
                      hx-swap="beforeend"
                      hx-indicator="#chat-indicator">
                    {% csrf_token %}
                    <div class="input-group">
                        {{ form.message|as_crispy_field }}
                        <button class="btn btn-primary" type="submit">Send</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">Assistant Info</h5>
                <ul class="list-unstyled">
                    <li><strong>Type:</strong> {{ assistant.get_assistant_type_display }}</li>
                    <li><strong>Model:</strong> {{ assistant.get_model_display }}</li>
                    <li><strong>Temperature:</strong> {{ assistant.temperature }}</li>
                    <li><strong>Max Tokens:</strong> {{ assistant.max_tokens }}</li>
                    <li><strong>Created:</strong> {{ assistant.created_at|date:"Y-m-d H:i" }}</li>
                </ul>
                <hr>

                {# --- Add QR Code Display Here --- #}
                <div class="text-center mb-3" id="qr-code-container"> {# Center the QR code #}
                    <h6 class="card-subtitle mb-2 text-muted">Scan QR Code</h6>
                    {% if assistant.qr_code %}
                        <img src="{{ assistant.qr_code.url }}" alt="QR Code for {{ assistant.name }}" class="img-fluid" style="max-width: 150px;" id="assistant-qr-code-img"> {# Display the image #}
                        <div class="mt-2">
                            <a href="{{ assistant.qr_code.url }}" download class="btn btn-outline-secondary btn-sm me-1" id="qr-code-download-btn">
                                <i class="bi bi-download"></i> Download
                            </a>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="regenerate-assistant-qr-code-btn" data-assistant-id="{{ assistant.id }}">
                                <i class="bi bi-arrow-clockwise"></i> Regenerate
                            </button>
                        </div>
                    {% else %}
                        <div class="alert alert-info mb-3">
                            <p class="mb-0">QR code not available.</p>
                        </div>
                        <button type="button" class="btn btn-primary btn-sm" id="regenerate-assistant-qr-code-btn" data-assistant-id="{{ assistant.id }}">
                            <i class="bi bi-qr-code"></i> Generate QR Code
                        </button>
                    {% endif %}
                </div>
                <hr>
                {# --- End QR Code Display --- #}

                <h6 class="card-subtitle mb-2 text-muted">System Prompt</h6>
                <p class="small bg-light p-2 rounded">
                    {{ assistant.system_prompt|default:"Default system prompt."|linebreaksbr }}
                </p>
                {% if assistant.knowledge_base %}
                    <hr>
                    <h6 class="card-subtitle mb-2 text-muted">Knowledge Base</h6>
                    <p><a href="{{ assistant.knowledge_base.url }}" target="_blank">View File</a></p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{# Add JS for chat interaction later #}

{# Folder Options Modal (Copied from list view) #}
<div class="modal fade" id="folderOptionsModal" tabindex="-1" aria-labelledby="folderOptionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="folderOptionsModalLabel">Add to Favorites</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="mb-3">Add <strong id="modalFolderNameItemName">this item</strong> to favorites:</p>

        {# Option 1: Save without folder #}
        <div class="d-grid mb-3">
            <button type="button" class="btn btn-outline-primary text-start" id="saveNoFolderBtn">
                <i class="bi bi-bookmark-heart me-2"></i>Save (Uncategorized)
            </button>
        </div>

        <hr>

        {# Option 2: Add to existing folder #}
        <div id="existingFoldersSection" class="mb-3" style="display: none;">
            <label for="selectFolder" class="form-label small mb-1">Add to existing folder:</label>
            <div class="input-group">
                <span class="input-group-text"><i class="bi bi-folder-symlink"></i></span>
                <select class="form-select" id="selectFolder">
                    <option selected disabled value="">Choose folder...</option>
                    {# Options will be populated by JS #}
                </select>
                <button class="btn btn-primary" type="button" id="addToFolderBtn" disabled>
                    <i class="bi bi-plus-lg"></i> Add
                </button>
            </div>
        </div>

        {# Option 3: Create new folder #}
        <div>
            <label for="newFolderName" class="form-label small mb-1">Or create a new folder:</label>
            <div class="input-group">
                 <span class="input-group-text"><i class="bi bi-folder-plus"></i></span>
                <input type="text" class="form-control" id="newFolderName" placeholder="New folder name...">
                <button class="btn btn-success" type="button" id="createFolderAndSaveBtn" disabled>
                   <i class="bi bi-check-lg"></i> Create & Save
                </button>
            </div>
        </div>

        <div id="folderModalErrorMsg" class="text-danger small mt-3" style="display: none;"></div>
      </div>
    </div>
  </div>
</div>
{# End Folder Options Modal #}

{% endblock %}

{% block extra_js %}
<script>
// Helper function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    return csrfInput ? csrfInput.value : null;
}

// --- Rating Stars Logic ---
function handleRatingStars() {
    const chatLog = document.getElementById('chat-log');
    if (!chatLog) {
        console.error("Rating Stars Error: Chat log element (#chat-log) not found.");
        return;
    }
    console.log("Rating Stars: Attaching click listener to #chat-log");

    chatLog.addEventListener('click', function(event) {
        console.log("Rating Stars: Click detected inside #chat-log. Target:", event.target);
        const star = event.target.closest('.star'); // Use closest to handle clicks inside the star icon if needed

        if (star) {
            console.log("Rating Stars: Click target is a star or inside a star.");
            const ratingContainer = star.closest('.rating-stars');
            if (!ratingContainer) {
                console.error("Rating Stars Error: Could not find parent .rating-stars container.");
                return;
            }

            const interactionId = ratingContainer.dataset.interactionId; // Use dataset
            const ratingValue = star.dataset.value; // Use dataset
            const feedbackElement = ratingContainer.querySelector('.rating-feedback');
            const starsInContainer = ratingContainer.querySelectorAll('.star'); // Use different variable name

            console.log(`Rating Stars: Interaction ID: ${interactionId}, Rating Value: ${ratingValue}`);

            // Prevent re-rating
            if (ratingContainer.classList.contains('rated')) {
                console.log(`Rating Stars: Interaction ${interactionId} already rated.`);
                return;
            }

            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                console.error("Rating Stars Error: CSRF token not found!");
                alert("Could not submit rating. Please refresh the page.");
                return;
            }

            // Send the rating to the backend
            const url = "{% url 'assistants:rate_interaction' company.id assistant.id %}";
            console.log(`Rating Stars: Preparing fetch to ${url} with interaction=${interactionId}, rating=${ratingValue}`);

            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({ // Use URLSearchParams for robust encoding
                    'interaction_id': interactionId,
                    'rating': ratingValue
                })
            })
            .then(async response => { // Make async to handle potential JSON error parsing
                console.log(`Rating Stars: Received response status: ${response.status}`);
                const isJson = response.headers.get('content-type')?.includes('application/json');
                let responseData = null;
                try {
                    responseData = isJson ? await response.json() : await response.text(); // Get text if not JSON
                    console.log("Rating Stars: Response data:", responseData);
                } catch (jsonError) {
                    console.error("Rating Stars: Error parsing JSON response", jsonError);
                    responseData = await response.text(); // Fallback to text if JSON parsing fails
                    console.log("Rating Stars: Response text fallback:", responseData);
                }

                if (!response.ok) {
                    const errorMessage = (typeof responseData === 'object' && responseData?.message) ? responseData.message : `HTTP error ${response.status}`;
                    console.error(`Rating Stars: Rating submission failed: ${errorMessage}`, responseData);
                    throw new Error(errorMessage);
                }
                return responseData; // Return parsed data or text
            })
            .then(data => {
                // Check if data is an object and has status property
                if (typeof data === 'object' && data !== null && data.status === 'success') {
                    console.log('Rating Stars: Rating submitted successfully:', data.message);
                    // Update stars visually
                    starsInContainer.forEach(s => {
                        s.style.color = s.dataset.value <= ratingValue ? 'gold' : 'grey';
                    });
                    // Show feedback and mark as rated
                    if (feedbackElement) feedbackElement.style.display = 'inline';
                    ratingContainer.classList.add('rated');
                } else {
                    // Handle cases where response was ok but status wasn't 'success' or data wasn't expected format
                    console.error('Rating Stars: Rating submission failed (unexpected success format):', data);
                    alert('Failed to submit rating. Unexpected response from server.');
                }
            })
            .catch(error => {
                console.error('Rating Stars: Error during fetch/processing:', error);
                alert(`An error occurred while submitting the rating: ${error.message}`);
            });
        } else {
             console.log("Rating Stars: Click target was not a star.");
        }
    });
}
// --- End Rating Stars Logic ---


// --- Favorite Button & Folder Modal Logic (Adapted for Detail Page) ---
function handleFavoriteButtons() {
    // Target the specific button on the detail page
    const likeButton = document.querySelector('.like-button[data-item-type="assistant"]');
    const folderModalElement = document.getElementById('folderOptionsModal');
    let folderModal = null;
    if (folderModalElement) {
        folderModal = new bootstrap.Modal(folderModalElement);
    } else {
        console.error("Folder options modal element not found.");
        // Don't return entirely, maybe rating still works
    }

    if (!likeButton) {
        console.log("Like button not found on this page.");
        return;
    }

    // Store current item details for modal actions
    let currentModalItemId = likeButton.dataset.itemId; // Get ID directly
    let currentModalItemType = likeButton.dataset.itemType; // Get type directly

    // --- Main Click Handler (Like Button) ---
    likeButton.addEventListener('click', async (event) => {
        event.preventDefault();
        event.stopPropagation();

        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            console.error("CSRF token not found!");
            alert("Action failed. Please refresh.");
            return;
        }

        // Use the stored item ID and type
        const itemId = currentModalItemId;
        const itemType = currentModalItemType;
        const url = "{% url 'directory:toggle_saved_item' %}";

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-CSRFToken': csrfToken,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: new URLSearchParams({ 'item_id': itemId, 'item_type': itemType })
            });

            const data = await response.json();

            if (!response.ok) throw new Error(data.message || `HTTP error ${response.status}`);

            if (data.status === 'success' && data.action === 'unfavorited') {
                // Item was unfavorited successfully
                updateHeartIcon(likeButton, false);
            } else if (data.status === 'options') {
                // Item is not saved, show folder options modal
                if (folderModal) { // Check if modal exists
                    populateAndShowFolderModal(data);
                } else {
                    alert("Could not display folder options."); // Fallback if modal failed to init
                }
            } else {
                // Unexpected success response
                console.error('Unexpected response from toggle_saved_item:', data);
                alert('An unexpected error occurred.');
            }

        } catch (error) {
            console.error('Error handling favorite click:', error);
            alert(`An error occurred: ${error.message}`);
        }
    });

    // --- Helper: Update Heart Icon ---
    function updateHeartIcon(button, isSaved) {
        if (!button) return;
        if (isSaved) {
            button.classList.remove('text-secondary');
            button.classList.add('text-danger');
            button.title = 'Unlike';
        } else {
            button.classList.remove('text-danger');
            button.classList.add('text-secondary');
            button.title = 'Like';
        }
    }

    // --- Helper: Populate and Show Folder Modal ---
    function populateAndShowFolderModal(data) {
        if (!folderModalElement || !folderModal) return;

        // Set item name
        const itemNameElement = folderModalElement.querySelector('#modalFolderNameItemName');
        if (itemNameElement) itemNameElement.textContent = data.item_name || 'this item';

        // Populate existing folders dropdown
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const existingFoldersSection = folderModalElement.querySelector('#existingFoldersSection');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');

        selectFolder.innerHTML = '<option selected disabled value="">Choose folder...</option>'; // Reset options
        if (data.folders && data.folders.length > 0) {
            data.folders.forEach(folder => {
                const option = document.createElement('option');
                option.value = folder.id;
                option.textContent = folder.name;
                selectFolder.appendChild(option);
            });
            existingFoldersSection.style.display = 'block';
            addToFolderBtn.disabled = true; // Disable until a folder is selected
        } else {
            existingFoldersSection.style.display = 'none';
        }

        // Reset other fields
        folderModalElement.querySelector('#newFolderName').value = '';
        folderModalElement.querySelector('#createFolderAndSaveBtn').disabled = true;
        folderModalElement.querySelector('#folderModalErrorMsg').style.display = 'none';
        folderModalElement.querySelector('#folderModalErrorMsg').textContent = '';

        // Show the modal
        folderModal.show();
    }

    // --- Folder Modal Event Listeners ---
    if (folderModalElement) {
        const selectFolder = folderModalElement.querySelector('#selectFolder');
        const addToFolderBtn = folderModalElement.querySelector('#addToFolderBtn');
        const newFolderNameInput = folderModalElement.querySelector('#newFolderName');
        const createFolderBtn = folderModalElement.querySelector('#createFolderAndSaveBtn');
        const saveNoFolderBtn = folderModalElement.querySelector('#saveNoFolderBtn');
        const errorMsgElement = folderModalElement.querySelector('#folderModalErrorMsg');

        // Enable/disable "Add" button based on selection
        selectFolder.addEventListener('change', () => {
            addToFolderBtn.disabled = !selectFolder.value;
        });

        // Enable/disable "Create & Save" button based on input
        newFolderNameInput.addEventListener('input', () => {
            createFolderBtn.disabled = !newFolderNameInput.value.trim();
        });

        // Action: Save without folder
        saveNoFolderBtn.addEventListener('click', async () => {
            await handleSaveFolderAction("{% url 'directory:save_item_no_folder' %}", {});
        });

        // Action: Add to existing folder
        addToFolderBtn.addEventListener('click', async () => {
            const folderId = selectFolder.value;
            if (!folderId) return;
            await handleSaveFolderAction("{% url 'directory:add_item_to_folder' %}", { folder_id: folderId });
        });

        // Action: Create folder and save
        createFolderBtn.addEventListener('click', async () => {
            const folderName = newFolderNameInput.value.trim();
            if (!folderName) return;
            await handleSaveFolderAction("{% url 'directory:create_folder_and_save' %}", { folder_name: folderName });
        });

        // Generic function to handle the save actions
        async function handleSaveFolderAction(url, additionalParams) {
            if (!currentModalItemId || !currentModalItemType) {
                showFolderModalError("Item details missing. Please close and retry.");
                return;
            }
            const csrfToken = getCsrfToken();
            if (!csrfToken) {
                 showFolderModalError("CSRF token missing. Please refresh.");
                 return;
            }

            showFolderModalError(''); // Clear previous errors

            const bodyParams = new URLSearchParams({
                'item_id': currentModalItemId,
                'item_type': currentModalItemType,
                ...additionalParams
            });

            try {
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'X-CSRFToken': csrfToken,
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: bodyParams
                });
                const data = await response.json();

                if (!response.ok || data.status !== 'success') {
                    throw new Error(data.message || `HTTP error ${response.status}`);
                }

                // Success! Update the heart icon on the page and close modal
                // Find the button again in case the DOM changed slightly
                const originalButton = document.querySelector(`.like-button[data-item-id="${currentModalItemId}"][data-item-type="${currentModalItemType}"]`);
                updateHeartIcon(originalButton, true);
                if (folderModal) folderModal.hide();

            } catch (error) {
                console.error(`Error saving favorite via modal (${url}):`, error);
                showFolderModalError(`Error: ${error.message}`);
            }
        }

        function showFolderModalError(message) {
            errorMsgElement.textContent = message;
            errorMsgElement.style.display = message ? 'block' : 'none';
        }
    }
}
// --- End Favorite Button & Folder Modal Logic ---


// --- HTMX Chat Form Logic ---
function handleChatForm() {
    const chatForm = document.getElementById('chat-form');
    const chatLog = document.getElementById('chat-log');
    const messageInput = document.getElementById('id_message'); // Assuming crispy forms default ID

    if (!chatForm || !chatLog || !messageInput) {
        console.error("Chat form elements not found. HTMX chat logic cannot initialize.");
        return;
    }

    // 1. Add user message immediately on submit (before HTMX request)
    chatForm.addEventListener('htmx:beforeRequest', function(evt) {
        const userMessage = messageInput.value.trim();
        if (userMessage) {
            const userMessageDiv = document.createElement('div');
            userMessageDiv.classList.add('message', 'user-message', 'mb-2');
            // Basic escaping to prevent simple HTML injection
            const strongTag = document.createElement('strong');
            strongTag.textContent = 'You: ';
            userMessageDiv.appendChild(strongTag);
            userMessageDiv.appendChild(document.createTextNode(userMessage)); // Use textNode for safety

            chatLog.appendChild(userMessageDiv);
            chatLog.scrollTop = chatLog.scrollHeight; // Scroll down
        }
    });

    // 2. Clear input and scroll after successful HTMX swap
    document.body.addEventListener('htmx:afterSwap', function(evt) {
        // Check if the swap happened in our chat log
        if (evt.detail.target.id === 'chat-log') {
            messageInput.value = ''; // Clear the input field
            chatLog.scrollTop = chatLog.scrollHeight; // Scroll to the bottom
            // Re-initialize rating handlers for the newly added message
            // We need a more robust way if multiple listeners become an issue,
            // but for now, re-calling might be okay if the previous listener
            // was attached to the chatLog itself (event delegation).
            // If handleRatingStars attaches listeners directly to stars, this is needed.
            // Let's assume handleRatingStars uses event delegation on chatLog.
            // If not, it needs adjustment.
        }
    });

     // 3. Handle potential HTMX errors (optional but good)
     chatForm.addEventListener('htmx:responseError', function(evt) {
        console.error("HTMX request error:", evt.detail.error);
        const errorDiv = document.createElement('div');
        errorDiv.classList.add('message', 'assistant-message', 'text-danger', 'mb-2');
        errorDiv.textContent = `Error: Could not send message. ${evt.detail.xhr.statusText || 'Network error'}`;
        chatLog.appendChild(errorDiv);
        chatLog.scrollTop = chatLog.scrollHeight;
     });

     // Ensure CSRF token is included in HTMX requests
     document.body.addEventListener('htmx:configRequest', function(evt) {
        const csrfToken = getCsrfToken();
        if (csrfToken && evt.detail.verb === 'post') { // Only add for POST
            evt.detail.headers['X-CSRFToken'] = csrfToken;
        }
    });

}
// --- End HTMX Chat Form Logic ---


// Initialize handlers when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM loaded, initializing handlers.");
    handleRatingStars();
    handleFavoriteButtons(); // Corrected function name
    handleChatForm(); // Initialize chat form logic
    // Add other initializations here if needed

    // Initial scroll to bottom if chat log has content
    const chatLog = document.getElementById('chat-log');
    if(chatLog) {
        chatLog.scrollTop = chatLog.scrollHeight;
    }
});

</script>
{% endblock %}
