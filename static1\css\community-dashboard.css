/* Community Dashboard Styles */

/* Message styling */
.message {
    padding: 10px 15px;
    border-radius: 10px;
    margin-bottom: 10px;
    max-width: 80%;
}

.user-message {
    background-color: #f0f2f5;
    margin-left: auto;
    margin-right: 0;
    text-align: right;
}

.assistant-message {
    background-color: #e7f3ff;
    margin-left: 0;
    margin-right: auto;
}

/* Context card styling */
.contexts-list .card {
    transition: all 0.2s ease;
}

.contexts-list .card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.context-content {
    line-height: 1.6;
}

/* Rating stars */
.rating-stars .star {
    transition: transform 0.1s ease;
}

.rating-stars .star:hover {
    transform: scale(1.2);
}

/* Tab styling */
.nav-tabs .nav-link {
    color: #6c757d;
    font-weight: 500;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    font-weight: 600;
}

/* HTMX indicator */
.htmx-indicator {
    opacity: 0;
    transition: opacity 200ms ease-in;
}
.htmx-request .htmx-indicator {
    opacity: 1;
}
.htmx-request.htmx-indicator {
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .message {
        max-width: 90%;
    }
}
