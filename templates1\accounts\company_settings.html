{% extends 'base/layout.html' %}
{% load static %}
{% load crispy_forms_tags %}
{# Remove widget_tweaks load tag #}

{% block title %}Company Settings - {{ company.name }} - 24seven{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Company Settings</h1>
            <p class="text-muted mb-0">
                Manage settings for {{ company.name }}
            </p>
        </div>
        <div class="col-auto">
            <a href="{% url 'accounts:company_detail' company.id %}" class="btn btn-light">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Company
            </a>
        </div>
    </div>

    <div class="row g-4">
        <!-- Settings Form -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {{ form.non_field_errors }}
                            </div>
                        {% endif %}

                        <!-- Basic Information Card -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Basic Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                            <!-- Mission -->
                            <div class="col-12">
                                <label for="{{ form.mission.id_for_label }}" class="form-label">
                                    Company Mission
                                </label>
                                {# Render using standard Django syntax #}
                                {{ form.mission }}
                                {% if form.mission.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.mission.errors|join:", " }}
                                    </div>
                                {% endif %}
                                <div class="form-text">{{ form.mission.help_text }}</div>
                            </div>

                            <!-- Website -->
                            <div class="col-md-6">
                                <label for="{{ form.website.id_for_label }}" class="form-label">
                                    Website
                                </label>
                                {# Render using standard Django syntax #}
                                {{ form.website }}
                                {% if form.website.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.website.errors|join:", " }}
                                    </div>
                                {% endif %}
                                {% if form.website.help_text %}
                                    <div class="form-text">{{ form.website.help_text }}</div>
                                {% endif %}
                            </div>

                            <!-- Industry -->
                            <div class="col-md-6">
                                <label for="{{ form.industry.id_for_label }}" class="form-label">
                                    Industry
                                </label>
                                {# Render using standard Django syntax #}
                                {{ form.industry }}
                                {% if form.industry.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.industry.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Size -->
                            <div class="col-md-6">
                                <label for="{{ form.size.id_for_label }}" class="form-label">
                                    Company Size
                                </label>
                                {# Render using standard Django syntax #}
                                {{ form.size }}
                                {% if form.size.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.size.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Founded Year -->
                            <div class="col-md-6">
                                <label for="{{ form.founded.id_for_label }}" class="form-label">
                                    Founded Year
                                </label>
                                {# Replace render_field with standard Django rendering + manual attributes #}
                                <input type="{{ form.founded.field.widget.input_type }}"
                                       name="{{ form.founded.name }}"
                                       {% if form.founded.value != None %}value="{{ form.founded.value }}"{% endif %}
                                       class="form-control {% if form.founded.errors %}is-invalid{% endif %}"
                                       id="{{ form.founded.id_for_label }}"
                                       {% if form.founded.field.widget.attrs.min %}min="{{ form.founded.field.widget.attrs.min }}"{% endif %}
                                       {% if form.founded.field.widget.attrs.max %}max="{{ form.founded.field.widget.attrs.max }}"{% endif %}
                                       {% if form.founded.field.required %}required{% endif %}>
                                {% if form.founded.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.founded.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>
                                </div> {# End .row g-3 #}
                            </div> {# End .card-body #}
                        </div> {# End Basic Information Card #}

                        <!-- Company Branding Card -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Company Branding</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="{{ form.logo.id_for_label }}" class="form-label">Company Logo</label>
                                    {% if form.instance.logo %}
                                        <div class="mb-2">
                                            <img src="{{ form.instance.logo.url }}" alt="Current Logo" style="max-height: 80px; max-width: 200px;" class="img-thumbnail">
                                        </div>
                                    {% endif %}
                                    {{ form.logo }}
                                    <div class="form-text">Upload a new logo. Select 'Clear' to remove the current logo.</div>
                                    {% if form.logo.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.logo.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div> {# End .card-body #}
                        </div> {# End Company Branding Card #}


                        <!-- Contact Information Card -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Contact Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                            <!-- Contact Email -->
                            <div class="col-md-6">
                                <label for="{{ form.contact_email.id_for_label }}" class="form-label">
                                    Contact Email
                                </label>
                                {{ form.contact_email }}
                                {% if form.contact_email.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.contact_email.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Contact Phone -->
                            <div class="col-md-6">
                                <label for="{{ form.contact_phone.id_for_label }}" class="form-label">
                                    Contact Phone
                                </label>
                                {{ form.contact_phone }}
                                {% if form.contact_phone.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.contact_phone.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Address Line 1 -->
                            <div class="col-12">
                                {{ form.address_line1.label_tag }} {# Use label_tag for consistency #}
                                {{ form.address_line1 }} {# Renders the input with widget attrs #}
                                {% if form.address_line1.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.address_line1.errors|join:", " }}
                                    </div>
                                {% endif %}
                                {% if form.address_line1.help_text %}
                                    <div class="form-text">{{ form.address_line1.help_text }}</div>
                                {% endif %}
                            </div>
                            <!-- Address Line 2 -->
                            <div class="col-12">
                                {{ form.address_line2.label_tag }} <span class="text-muted">(Optional)</span>
                                {{ form.address_line2 }} {# Renders the input with widget attrs #}
                                {% if form.address_line2.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.address_line2.errors|join:", " }}
                                    </div>
                                {% endif %}
                                {% if form.address_line2.help_text %}
                                    <div class="form-text">{{ form.address_line2.help_text }}</div>
                                {% endif %}
                            </div>
                            <!-- City -->
                            <div class="col-md-6">
                                {{ form.city.label_tag }}
                                {{ form.city }} {# Renders the input with widget attrs #}
                                {% if form.city.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.city.errors|join:", " }}
                                    </div>
                                {% endif %}
                                {% if form.city.help_text %}
                                    <div class="form-text">{{ form.city.help_text }}</div>
                                {% endif %}
                            </div>
                            <!-- Postal Code -->
                            <div class="col-md-3">
                                {{ form.postal_code.label_tag }}
                                {{ form.postal_code }} {# Renders the input with widget attrs #}
                                {% if form.postal_code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.postal_code.errors|join:", " }}
                                    </div>
                                {% endif %}
                                {% if form.postal_code.help_text %}
                                    <div class="form-text">{{ form.postal_code.help_text }}</div>
                                {% endif %}
                            </div>
                            <!-- Country -->
                            <div class="col-md-3">
                                {{ form.country.label_tag }}
                                {{ form.country }} {# Renders the input with widget attrs #}
                                {% if form.country.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.country.errors|join:", " }}
                                    </div>
                                {% endif %}
                                {% if form.country.help_text %}
                                    <div class="form-text">{{ form.country.help_text }}</div>
                                {% endif %}
                            </div>
                                </div> {# End .row g-3 #}
                            </div> {# End .card-body #}
                        </div> {# End Contact Information Card #}


                        <!-- Social Media Card -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Social Media</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                            <div class="col-md-4">
                                <label for="{{ form.linkedin.id_for_label }}" class="form-label">
                                    <i class="bi bi-linkedin text-primary"></i> LinkedIn
                                </label>
                                {{ form.linkedin }}
                                {% if form.linkedin.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.linkedin.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4">
                                <label for="{{ form.twitter.id_for_label }}" class="form-label">
                                    <i class="bi bi-twitter text-info"></i> Twitter
                                </label>
                                {{ form.twitter }}
                                {% if form.twitter.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.twitter.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <div class="col-md-4">
                                <label for="{{ form.facebook.id_for_label }}" class="form-label">
                                    <i class="bi bi-facebook text-primary"></i> Facebook
                                </label>
                                {{ form.facebook }}
                                {% if form.facebook.errors %}
                                    <div class="invalid-feedback d-block">
                                        {{ form.facebook.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>
                                </div> {# End .row g-3 #}
                            </div> {# End .card-body #}
                        </div> {# End Social Media Card #}


                        <!-- Directory Settings Card -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Directory Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-3">
                                    {{ form.list_in_directory }}
                                    <label class="form-check-label" for="{{ form.list_in_directory.id_for_label }}">
                                        List in Public Directory
                                    </label>
                                    <div class="form-text">
                                        Make your company visible in the public directory.
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">
                                        Public Description
                                    </label>
                                    {{ form.description }}
                                    <div class="form-text">
                                        This description will be shown in the public directory.
                                    </div>
                                </div>
                                <!-- Categories Field -->
                                <div class="mb-3">
                                    <label class="form-label">{{ form.categories.label }}</label>
                                    {{ form.categories }} {# Renders checkboxes #}
                                    {% if form.categories.errors %}
                                        <div class="invalid-feedback d-block">
                                            {{ form.categories.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                    {% if form.categories.help_text %}
                                        <div class="form-text">{{ form.categories.help_text }}</div>
                                    {% endif %}
                                </div>

                                {# --- Company Tier & Featured Status/Request Section --- #}
                                <fieldset class="mb-4 p-3 border rounded bg-light-subtle">
                                    <legend class="fs-6 fw-bold mb-3">Directory Tier & Featured Status</legend>

                                    {# Pending Tier Alert #}
                                    {% if company.tier_change_pending %}
                                    <div class="alert alert-info d-flex align-items-center" role="alert">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        <div>
                                            A request to change the tier to <strong>{{ company.get_requested_tier_display }}</strong>
                                            {% if company.requested_tier_duration %}
                                                ({{ company.get_requested_tier_duration_display }})
                                            {% endif %}
                                            is pending superadmin approval.
                                        </div>
                                    </div>
                                    {% endif %}

                                    <div class="row">
                                        {# Current Tier Status #}
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label fw-bold">Current Tier:</label>
                                            <p class="form-control-plaintext">{{ company.get_tier_display }}
                                                {% if company.tier_expiry_date %}
                                                    (Expires: {{ company.tier_expiry_date|date:"Y-m-d" }})
                                                {% endif %}
                                            </p>
                                        </div>

                                        {# Tier Change Request Field (from dir_form) - Fully Manual Rendering #}
                                        <div class="col-md-6 mb-3">
                                            <label for="{{ dir_form.request_new_tier.id_for_label }}" class="form-label">
                                                {{ dir_form.request_new_tier.label }}
                                            </label>
                                            <select name="{{ dir_form.request_new_tier.name }}" id="{{ dir_form.request_new_tier.id_for_label }}" class="form-select">
                                                {% for value, text in dir_form.fields.request_new_tier.choices %} {# Iterate over field.choices #}
                                                    <option value="{{ value }}" {% if dir_form.request_new_tier.value|stringformat:"s" == value|stringformat:"s" %}selected{% endif %}>{{ text }}</option>
                                                {% endfor %}
                                            </select>
                                            {% if dir_form.request_new_tier.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ dir_form.request_new_tier.errors|join:", " }}
                                                </div>
                                            {% endif %}
                                            {% if dir_form.request_new_tier.help_text %}
                                                <div class="form-text">{{ dir_form.request_new_tier.help_text }}</div>
                                            {% endif %}
                                        </div>

                                        {# Tier Duration Request Field (Initially Hidden) - Manual Rendering #}
                                        <div class="col-md-6 mb-3" id="company-tier-duration-wrapper" style="display: none;">
                                            <label for="{{ dir_form.requested_tier_duration.id_for_label }}" class="form-label">
                                                {{ dir_form.requested_tier_duration.label }}
                                            </label>
                                            {{ dir_form.requested_tier_duration }} {# Renders the select widget #}
                                            {% if dir_form.requested_tier_duration.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ dir_form.requested_tier_duration.errors|join:", " }}
                                                </div>
                                            {% endif %}
                                            {% if dir_form.requested_tier_duration.help_text %}
                                                <div class="form-text">{{ dir_form.requested_tier_duration.help_text }}</div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <hr class="my-3">

                                    {# Pending Featured Alert #}
                                    {% if company.featured_request_pending %}
                                    <div class="alert alert-info d-flex align-items-center" role="alert">
                                        <i class="bi bi-info-circle-fill me-2"></i>
                                        <div>
                                            A request to feature this company
                                            {% if company.requested_featured_duration %}
                                                ({{ company.get_requested_featured_duration_display }})
                                            {% endif %}
                                            is pending superadmin approval.
                                        </div>
                                    </div>
                                    {% endif %}

                                    <div class="row">
                                        {# Current Featured Status #}
                                        <div class="col-md-6 mb-3">
                                            <label class="form-label fw-bold">Current Featured Status:</label>
                                            <p class="form-control-plaintext">
                                                {% if company.is_featured %}
                                                    <span class="badge bg-primary">Yes</span>
                                                    {% if company.featured_expiry_date %}
                                                        (Expires: {{ company.featured_expiry_date|date:"Y-m-d" }})
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-secondary">No</span>
                                                {% endif %}
                                            </p>
                                        </div>

                                        {# Featured Status Request Field (from dir_form) - Manual Rendering #}
                                        <div class="col-md-6 mb-3">
                                             <div class="form-check"> {# Wrap checkbox for better alignment #}
                                                {{ dir_form.request_featured_status }} {# Renders the checkbox widget #}
                                                <label for="{{ dir_form.request_featured_status.id_for_label }}" class="form-check-label">
                                                    {{ dir_form.request_featured_status.label }}
                                                </label>
                                            </div>
                                            {% if dir_form.request_featured_status.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ dir_form.request_featured_status.errors|join:", " }}
                                                </div>
                                            {% endif %}
                                            {% if dir_form.request_featured_status.help_text %}
                                                <div class="form-text">{{ dir_form.request_featured_status.help_text }}</div>
                                            {% endif %}
                                        </div>

                                        {# Featured Duration Request Field (Initially Hidden) - Manual Rendering #}
                                        <div class="col-md-6 mb-3" id="company-featured-duration-wrapper" style="display: none;">
                                             <label for="{{ dir_form.requested_featured_duration.id_for_label }}" class="form-label">
                                                {{ dir_form.requested_featured_duration.label }}
                                            </label>
                                            {{ dir_form.requested_featured_duration }} {# Renders the select widget #}
                                            {% if dir_form.requested_featured_duration.errors %}
                                                <div class="invalid-feedback d-block">
                                                    {{ dir_form.requested_featured_duration.errors|join:", " }}
                                                </div>
                                            {% endif %}
                                            {% if dir_form.requested_featured_duration.help_text %}
                                                <div class="form-text">{{ dir_form.requested_featured_duration.help_text }}</div>
                                            {% endif %}
                                        </div>
                                    </div>
                                </fieldset>
                                {# --- End Company Tier & Featured Section --- #}

                            </div> {# End .card-body #}
                        </div> {# End Directory Settings Card #}


                        <!-- Advanced Settings Card -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5 class="card-title mb-0">Advanced Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                            <!-- Timezone -->
                            <div class="col-md-6">
                                <label for="{{ form.timezone.id_for_label }}" class="form-label">
                                    Timezone
                                </label>
                                {{ form.timezone }}
                            </div>

                            <!-- Language -->
                            <div class="col-md-6">
                                <label for="{{ form.language.id_for_label }}" class="form-label">
                                    Language
                                </label>
                                {{ form.language }}
                            </div>

                            <!-- Custom Domain -->
                            <div class="col-12">
                                <label for="{{ form.custom_domain.id_for_label }}" class="form-label">
                                    Custom Domain
                                </label>
                                {{ form.custom_domain }}
                                <div class="form-text">
                                    Enter your domain to use a custom URL for your company workspace.
                                </div>
                            </div>
                                </div> {# End .row g-3 #}
                            </div> {# End .card-body #}
                        </div> {# End Advanced Settings Card #}


                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2 mt-4"> {# Added margin-top #}
                            <button type="reset" class="btn btn-light">Reset</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save me-2"></i>
                                Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Company QR Code -->
            <div class="card border-0 shadow-sm mb-4"> {# Added mb-4 for spacing #}
                <div class="card-body text-center">
                    <h5 class="card-title mb-3">Company QR Code</h5> {# Added mb-3 #}
                    <div id="qr-code-container">
                        {% if company.qr_code %}
                            <img src="{{ company.qr_code.url }}" alt="{{ company.name }} QR Code" class="img-fluid mb-3" style="max-width: 150px;" id="company-qr-code-img">
                            <div class="d-flex justify-content-center gap-2">
                                <a href="{{ company.qr_code.url }}" download class="btn btn-outline-secondary btn-sm" id="qr-code-download-btn">
                                    <i class="bi bi-download me-1"></i> Download
                                </a>
                                <button type="button" class="btn btn-outline-primary btn-sm" id="regenerate-qr-code-btn" data-company-id="{{ company.id }}">
                                    <i class="bi bi-arrow-clockwise me-1"></i> Regenerate
                                </button>
                            </div>
                        {% else %}
                            <div class="alert alert-info mb-3">
                                <p class="mb-0">QR code not available.</p>
                            </div>
                            <button type="button" class="btn btn-primary btn-sm" id="regenerate-qr-code-btn" data-company-id="{{ company.id }}">
                                <i class="bi bi-qr-code me-1"></i> Generate QR Code
                            </button>
                        {% endif %}
                    </div>
                </div>
            </div>
            <!-- End Company QR Code -->

            <!-- Danger Zone -->
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Danger Zone
                    </h5>
                </div>
                <div class="card-body">
                    <p class="card-text text-muted small">
                        These actions cannot be undone. Please be certain.
                    </p>

                    <div class="d-grid gap-2">
                        <button type="button"
                                class="btn btn-outline-danger"
                                data-bs-toggle="modal"
                                data-bs-target="#transferModal">
                            <i class="bi bi-arrow-left-right me-2"></i>
                            Transfer Ownership
                        </button>
                        {# Button to trigger delete modal via JS #}
                        <button type="button"
                                class="btn btn-outline-danger js-trigger-delete-company-modal"
                                data-company-name="{{ company.name|escapejs }}"
                                data-delete-url="{% url 'accounts:company_delete' company.id %}"> {# Assuming this URL name exists #}
                            <i class="bi bi-trash me-2"></i>
                            Delete Company
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transfer Ownership Modal -->
<div class="modal fade" id="transferModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            {# Form action uncommented as URL pattern 'accounts:transfer_ownership' is confirmed to exist #}
            <form method="post" action="{% url 'accounts:transfer_ownership' company.id %}">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title">Transfer Company Ownership</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p class="text-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        This action cannot be undone!
                    </p>
                    <p>
                        You will lose owner privileges and become an administrator.
                        The new owner will have full control over the company.
                    </p>
                    <div class="mb-3">
                        <label for="new_owner" class="form-label">Select New Owner</label>
                        <select name="new_owner" id="new_owner" class="form-select" required>
                            <option value="">Choose a member...</option>
                            {% for member in company.members.all %}
                                {% if member != user %}
                                    <option value="{{ member.id }}">
                                        {{ member.get_full_name|default:member.username }}
                                    </option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                    <div class="form-check mb-3">
                        <input type="checkbox"
                               class="form-check-input"
                               id="confirm_transfer"
                               required>
                        <label class="form-check-label" for="confirm_transfer">
                            I understand that I will lose ownership of this company
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger" disabled>Transfer Ownership</button> {# Disabled button #}
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Company Modal (Adapted) -->
<div class="modal fade" id="deleteModal" tabindex="-1"> {# Keep ID for now, JS targets this #}
    <div class="modal-dialog modal-dialog-centered"> {# Centered like assistant delete #}
        <div class="modal-content">
             {# Form removed, will be created by JS #}
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteCompanyModalLabel">Confirm Company Deletion</h5> {# Updated Label ID #}
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    {# Hidden input to store the actual delete URL #}
                    <input type="hidden" id="confirmDeleteCompanyUrlInput">
                    <p class="text-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        This action cannot be undone!
                    </p>
                    <p>
                        This will permanently delete the company "<strong id="deleteCompanyNameSpan"></strong>" and all associated data: {# Added span for name #}
                    </p>
                    <ul class="text-muted small">
                        <li>All team members will lose access</li>
                        <li>All company content will be deleted</li>
                        <li>All AI assistants will be removed</li>
                        <li>All settings and configurations will be lost</li>
                    </ul>
                    <div class="mb-3">
                        <label for="company_name" class="form-label">
                            Type your company name to confirm
                        </label>
                        <input type="text"
                               class="form-control"
                               id="company_name"
                               required
                               pattern="{{ company.name|escapejs }}"
                               placeholder="Type company name here"> {# Generic placeholder #}
                    </div>
                     {# Error message display area #}
                    <div id="deleteCompanyErrorMsg" class="text-danger small mt-2" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button> {# Changed btn-light to btn-secondary #}
                    <button type="button" id="confirmDeleteCompanyBtn" class="btn btn-danger" disabled>Delete Company</button> {# Changed type to button, added ID #}
                </div>
            {# Form removed #}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// Helper function to get CSRF token (ensure it's defined if not already in base template)
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
const csrftoken = getCookie('csrftoken'); // Get token once

document.addEventListener('DOMContentLoaded', function() {
    // --- Existing Transfer Ownership Modal Logic ---
    const transferForm = document.querySelector('#transferModal form');
    if (transferForm) {
        const transferCheckbox = transferForm.querySelector('#confirm_transfer');
        const transferButton = transferForm.querySelector('button[type="submit"]');

        if (transferCheckbox && transferButton) {
            transferCheckbox.addEventListener('change', function() {
                transferButton.disabled = !this.checked;
            });
            transferButton.disabled = true; // Initial state
        }
    }

    // --- Existing Form Dirty Check ---
    const mainForm = document.querySelector('.col-lg-8 .card form'); // More specific selector for the main settings form
    if (mainForm) {
        let formDirty = false;
        mainForm.addEventListener('change', function() {
            formDirty = true;
        });
        window.addEventListener('beforeunload', function(e) {
            if (formDirty) {
                e.preventDefault();
                e.returnValue = '';
            }
        });
        mainForm.addEventListener('submit', function() {
            formDirty = false; // Reset dirty flag on submit
        });
    }

    // --- New Delete Company Modal Logic ---
    const deleteCompanyModalElement = document.getElementById('deleteModal'); // Target the adapted modal
    if (deleteCompanyModalElement) {
        const companyNameInput = deleteCompanyModalElement.querySelector('#company_name');
        const confirmDeleteCompanyBtn = deleteCompanyModalElement.querySelector('#confirmDeleteCompanyBtn');
        const deleteCompanyUrlInput = deleteCompanyModalElement.querySelector('#confirmDeleteCompanyUrlInput');
        const deleteCompanyNameSpan = deleteCompanyModalElement.querySelector('#deleteCompanyNameSpan');
        const deleteCompanyErrorMsg = deleteCompanyModalElement.querySelector('#deleteCompanyErrorMsg');
        const expectedCompanyName = '{{ company.name|escapejs }}'; // Store expected name

        // Listener to trigger the modal
        document.querySelectorAll('.js-trigger-delete-company-modal').forEach(button => {
            button.addEventListener('click', function(event) {
                event.preventDefault();
                event.stopPropagation();

                const companyName = this.getAttribute('data-company-name');
                const deleteUrl = this.getAttribute('data-delete-url');

                // Populate modal
                deleteCompanyNameSpan.textContent = companyName;
                deleteCompanyUrlInput.value = deleteUrl;
                companyNameInput.value = ''; // Clear input field
                companyNameInput.setAttribute('pattern', companyName); // Set pattern for validation (optional)
                deleteCompanyErrorMsg.style.display = 'none';
                confirmDeleteCompanyBtn.disabled = true; // Start disabled

                // Show modal
                const modalInstance = bootstrap.Modal.getOrCreateInstance(deleteCompanyModalElement);
                modalInstance.show();
            });
        });

        // Listener for the company name input field
        companyNameInput.addEventListener('input', function() {
            // Enable button only if typed name matches expected name
            confirmDeleteCompanyBtn.disabled = this.value !== expectedCompanyName;
        });

        // Listener for the final confirmation button
        confirmDeleteCompanyBtn.addEventListener('click', function() {
            const deleteUrl = deleteCompanyUrlInput.value;
            const typedName = companyNameInput.value;

            // Final check (redundant if button is disabled, but safe)
            if (typedName !== expectedCompanyName) {
                deleteCompanyErrorMsg.textContent = 'Company name does not match.';
                deleteCompanyErrorMsg.style.display = 'block';
                return;
            }
            if (!deleteUrl) {
                 deleteCompanyErrorMsg.textContent = 'Could not determine delete action.';
                 deleteCompanyErrorMsg.style.display = 'block';
                 console.error("Delete URL not found in modal.");
                 return;
            }
             if (!csrftoken) {
                 deleteCompanyErrorMsg.textContent = "CSRF token not found. Please refresh.";
                 deleteCompanyErrorMsg.style.display = 'block';
                 return;
            }

            // Disable button and show spinner (optional)
            this.disabled = true;
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Deleting...';
            deleteCompanyErrorMsg.style.display = 'none';

            // Create and submit hidden form
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = deleteUrl;
            form.style.display = 'none';

            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrfmiddlewaretoken';
            csrfInput.value = csrftoken;
            form.appendChild(csrfInput);

            document.body.appendChild(form);
            form.submit();
        });
    }

    // --- Conditional Duration Fields for Company ---
    const companyRequestTierSelect = document.getElementById('id_request_new_tier'); // Use ID of new field
    const companyRequestFeaturedCheckbox = document.getElementById('id_request_featured_status'); // Use ID of new field
    const companyTierDurationWrapper = document.getElementById('company-tier-duration-wrapper');
    const companyFeaturedDurationWrapper = document.getElementById('company-featured-duration-wrapper');
    const companyStandardTierValue = "{{ Company.TIER_STANDARD }}";
    const currentCompanyTierValue = "{{ company.tier }}"; // Get current approved tier
    const currentCompanyFeaturedStatusRaw = "{{ company.is_featured|yesno:'true,false' }}";
    const currentCompanyFeaturedStatus = currentCompanyFeaturedStatusRaw === 'true';

    function toggleCompanyDurationFields() {
        let showTierDuration = false;
        let showFeaturedDuration = false;

        // Check Tier Request Selection
        // Show duration if a tier is selected AND it's different from the current tier
        // (Standard is excluded from choices, so no need to check for it here)
        if (companyRequestTierSelect && companyRequestTierSelect.value && companyRequestTierSelect.value !== currentCompanyTierValue) {
            showTierDuration = true;
        }

        // Check Featured Request Checkbox
        // Show duration if checkbox is checked AND it's different from the current status
        if (companyRequestFeaturedCheckbox && companyRequestFeaturedCheckbox.checked && !currentCompanyFeaturedStatus) {
            showFeaturedDuration = true;
        }

        // Apply display styles
        if (companyTierDurationWrapper) {
            companyTierDurationWrapper.style.display = showTierDuration ? 'block' : 'none';
            // console.log(`Company Tier Duration Visible: ${showTierDuration} (Requested Tier: ${companyRequestTierSelect ? companyRequestTierSelect.value : 'N/A'})`);
        } else {
            console.error("Company tier duration wrapper not found");
        }

        if (companyFeaturedDurationWrapper) {
            companyFeaturedDurationWrapper.style.display = showFeaturedDuration ? 'block' : 'none';
            // console.log(`Company Featured Duration Visible: ${showFeaturedDuration} (Requested Featured Checked: ${companyRequestFeaturedCheckbox ? companyRequestFeaturedCheckbox.checked : 'N/A'})`);
        } else {
             console.error("Company featured duration wrapper not found");
        }
    }

    // Add event listeners only if elements exist
    if (companyRequestTierSelect) {
        companyRequestTierSelect.addEventListener('change', toggleCompanyDurationFields);
        // console.log("Added change listener to company request tier select.");
    } else {
        console.error("Company request tier select element not found.");
    }

    if (companyRequestFeaturedCheckbox) {
        companyRequestFeaturedCheckbox.addEventListener('change', toggleCompanyDurationFields);
        // console.log("Added change listener to company request featured checkbox.");
    } else {
         console.error("Company request featured checkbox element not found.");
    }

    // Initial check on page load
    // console.log("Running initial toggleCompanyDurationFields on page load.");
    toggleCompanyDurationFields();
    // --- End Conditional Duration Fields ---

});
</script>
{% endblock %}
