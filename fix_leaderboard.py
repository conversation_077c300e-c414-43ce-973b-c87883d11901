import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from corporate.models import Company, Leaderboard, LeaderboardEntry

def fix_anonymous_user_entries():
    """Fix entries with AnonymousUser"""
    # Find the AnonymousUser entry
    anonymous_entries = LeaderboardEntry.objects.filter(user__username='AnonymousUser')

    if anonymous_entries.exists():
        print(f"Found {anonymous_entries.count()} entries with AnonymousUser")

        # Delete these entries
        anonymous_entries.delete()
        print("Deleted AnonymousUser entries")
    else:
        print("No AnonymousUser entries found")

def check_leaderboard_entries():
    """Check all leaderboard entries"""
    companies = Company.objects.all()
    print(f"Total companies: {companies.count()}")

    for company in companies:
        print(f"\nCompany: {company.name}")

        # Check leaderboards for this company
        leaderboards = Leaderboard.objects.filter(company=company, is_global=False)
        print(f"  Leaderboards: {leaderboards.count()}")

        for leaderboard in leaderboards:
            print(f"  - {leaderboard.name} ({leaderboard.time_period})")

            # Check entries for this leaderboard
            entries = LeaderboardEntry.objects.filter(leaderboard=leaderboard)
            print(f"    Entries: {entries.count()}")

            for entry in entries:
                print(f"    - {entry.user.username}: {entry.score} points, {entry.highest_role}")

def main():
    print("=== Fixing Leaderboard Entries ===")

    # Fix anonymous user entries
    fix_anonymous_user_entries()

    # Check leaderboard entries
    check_leaderboard_entries()

if __name__ == "__main__":
    main()
