/**
 * Community Action Buttons Styling
 * Improves contrast and readability of upvote and action buttons
 */

/* Upvote button - improved contrast and readability */
.upvote-btn,
.btn-outline-primary.upvote-btn,
button.upvote-btn,
.btn-outline-success.upvote-btn,
.btn-outline-success.upvote-answer-btn,
.upvote-answer-btn {
    background-color: #0055cc !important; /* Darker blue background for better contrast */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 700 !important; /* Extra bold text */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* Stronger text shadow for depth */
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3) !important; /* Enhanced shadow */
    border: 2px solid #ffffff !important; /* White border for contrast */
    padding: 0.6rem 1.2rem !important; /* More padding for better visibility */
    font-size: 1rem !important; /* Larger font size */
    line-height: 1.3 !important; /* Improved line height */
    border-radius: 0.5rem !important; /* Slightly larger border radius */
    transition: all 0.2s ease !important; /* Smooth transition */
    display: inline-flex !important; /* Ensure icon and text align properly */
    align-items: center !important; /* Center content vertically */
    justify-content: center !important; /* Center content horizontally */
    letter-spacing: 0.03em !important; /* Slightly increased letter spacing */
    min-height: 40px !important; /* Minimum height */
    min-width: 100px !important; /* Minimum width */
}

/* Upvote button hover state */
.upvote-btn:hover,
.btn-outline-primary.upvote-btn:hover,
button.upvote-btn:hover,
.btn-outline-success.upvote-btn:hover,
.btn-outline-success.upvote-answer-btn:hover,
.upvote-answer-btn:hover {
    background-color: #003d99 !important; /* Even darker blue on hover for better contrast */
    transform: translateY(-2px) !important; /* Slight lift effect */
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4) !important; /* Enhanced shadow on hover */
    border-color: #ffffff !important; /* Keep white border on hover */
}

/* Upvoted state */
.upvote-btn.upvoted,
.btn-outline-primary.upvote-btn.upvoted,
button.upvote-btn.upvoted,
.btn-success.upvote-btn,
.btn-success.upvote-answer-btn,
.upvote-answer-btn.upvoted {
    background-color: #003d99 !important; /* Darker blue for active state */
    color: #ffffff !important; /* White text */
    border-color: #ffffff !important; /* White border */
}

/* Comment button - improved contrast and readability */
.btn-outline-secondary,
button.btn-outline-secondary,
.btn-outline-warning,
button.btn-outline-warning,
.flag-answer-btn {
    background-color: #198754 !important; /* Green background for better visibility */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 700 !important; /* Extra bold text */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* Stronger text shadow for depth */
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3) !important; /* Enhanced shadow */
    border: 2px solid #ffffff !important; /* White border for contrast */
    padding: 0.6rem 1.2rem !important; /* More padding for better visibility */
    font-size: 1rem !important; /* Larger font size */
    line-height: 1.3 !important; /* Improved line height */
    border-radius: 0.5rem !important; /* Slightly larger border radius */
    transition: all 0.2s ease !important; /* Smooth transition */
    display: inline-flex !important; /* Ensure icon and text align properly */
    align-items: center !important; /* Center content vertically */
    justify-content: center !important; /* Center content horizontally */
    letter-spacing: 0.03em !important; /* Slightly increased letter spacing */
    min-height: 40px !important; /* Minimum height */
    min-width: 100px !important; /* Minimum width */
}

/* Comment button hover state */
.btn-outline-secondary:hover,
button.btn-outline-secondary:hover,
.btn-outline-warning:hover,
button.btn-outline-warning:hover,
.flag-answer-btn:hover {
    background-color: #146c43 !important; /* Darker green on hover for better contrast */
    transform: translateY(-2px) !important; /* Slight lift effect */
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4) !important; /* Enhanced shadow on hover */
    border-color: #ffffff !important; /* Keep white border on hover */
}

/* Delete button - improved contrast and readability */
.btn-outline-danger,
button.btn-outline-danger {
    background-color: #dc3545 !important; /* Red background */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 600 !important; /* Bolder text */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Subtle text shadow for depth */
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3) !important; /* Enhanced shadow */
    border: none !important; /* Remove border */
    padding: 0.5rem 1rem !important; /* More padding for better visibility */
    font-size: 0.9rem !important; /* Slightly larger font size */
    line-height: 1.2 !important; /* Improved line height */
    border-radius: 0.375rem !important; /* Consistent border radius */
    transition: all 0.2s ease !important; /* Smooth transition */
    display: inline-flex !important; /* Ensure icon and text align properly */
    align-items: center !important; /* Center content vertically */
    justify-content: center !important; /* Center content horizontally */
}

/* Delete button hover state */
.btn-outline-danger:hover,
button.btn-outline-danger:hover {
    background-color: #c82333 !important; /* Darker red on hover */
    transform: translateY(-2px) !important; /* Slight lift effect */
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4) !important; /* Enhanced shadow on hover */
}

/* Icon styling in buttons */
.upvote-btn i,
.btn-outline-primary.upvote-btn i,
.btn-outline-secondary i,
.btn-outline-danger i,
.upvote-answer-btn i,
.btn-outline-success i,
.btn-outline-warning i,
.flag-answer-btn i,
button i {
    margin-right: 0.75rem !important; /* More space between icon and text */
    font-size: 1.25rem !important; /* Larger icon for better visibility */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* Text shadow for depth */
    vertical-align: middle !important; /* Ensure proper vertical alignment */
    display: inline-block !important; /* Ensure proper display */
}

/* Ensure proper spacing between buttons */
.contribution-actions .btn,
.message-actions .btn {
    margin-right: 0.5rem !important; /* Add space between buttons */
    margin-bottom: 0.5rem !important; /* Add space below buttons for mobile */
}

/* Ensure buttons are properly sized on mobile */
@media (max-width: 768px) {
    .upvote-btn,
    .btn-outline-primary.upvote-btn,
    .btn-outline-secondary,
    .btn-outline-danger,
    .upvote-answer-btn,
    .btn-outline-success,
    .btn-outline-warning,
    .flag-answer-btn {
        padding: 0.5rem 1rem !important; /* Slightly smaller padding on mobile but still readable */
        font-size: 0.9rem !important; /* Slightly smaller font on mobile but still readable */
        min-width: 90px !important; /* Ensure minimum width on mobile */
    }
}

/* Special styling for the specific blue and green buttons in the community interface */
.message-actions .btn-outline-success,
.message-actions .upvote-answer-btn {
    background-color: #0055cc !important; /* Darker blue background */
    color: #ffffff !important; /* White text */
    font-size: 1.1rem !important; /* Larger font size */
    font-weight: 700 !important; /* Extra bold */
    padding: 0.7rem 1.3rem !important; /* Extra padding */
    border: 2px solid #ffffff !important; /* White border */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* Text shadow */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important; /* Stronger shadow */
    letter-spacing: 0.05em !important; /* Increased letter spacing */
    border-radius: 0.5rem !important; /* Rounded corners */
    min-width: 120px !important; /* Ensure minimum width */
}

.message-actions .btn-outline-warning,
.message-actions .flag-answer-btn {
    background-color: #198754 !important; /* Green background */
    color: #ffffff !important; /* White text */
    font-size: 1.1rem !important; /* Larger font size */
    font-weight: 700 !important; /* Extra bold */
    padding: 0.7rem 1.3rem !important; /* Extra padding */
    border: 2px solid #ffffff !important; /* White border */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important; /* Text shadow */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3) !important; /* Stronger shadow */
    letter-spacing: 0.05em !important; /* Increased letter spacing */
    border-radius: 0.5rem !important; /* Rounded corners */
    min-width: 120px !important; /* Ensure minimum width */
}

/* Hover states for these specific buttons */
.message-actions .btn-outline-success:hover,
.message-actions .upvote-answer-btn:hover {
    background-color: #003d99 !important; /* Darker blue on hover */
    transform: translateY(-3px) !important; /* More pronounced lift effect */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4) !important; /* Enhanced shadow on hover */
}

.message-actions .btn-outline-warning:hover,
.message-actions .flag-answer-btn:hover {
    background-color: #146c43 !important; /* Darker green on hover */
    transform: translateY(-3px) !important; /* More pronounced lift effect */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4) !important; /* Enhanced shadow on hover */
}
