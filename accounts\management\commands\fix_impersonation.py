"""
Management command to fix impersonation issues by ensuring all company owners have the proper permissions.
"""
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from guardian.shortcuts import assign_perm, get_perms
from accounts.models import Company
from accounts.permissions import OWNER_PERMS_COMPANY

class Command(BaseCommand):
    help = 'Fixes impersonation issues by ensuring all company owners have the proper permissions.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--user',
            type=int,
            help='Specify a user ID to fix permissions for just that user',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information about permissions being assigned',
        )

    def handle(self, *args, **options):
        user_id = options.get('user')
        verbose = options.get('verbose', False)
        User = get_user_model()

        # Get users to process
        if user_id:
            users = User.objects.filter(id=user_id)
            if not users.exists():
                self.stdout.write(self.style.ERROR(f"User with ID {user_id} not found."))
                return
        else:
            # Get all company owners
            company_owners = Company.objects.values_list('owner_id', flat=True).distinct()
            users = User.objects.filter(id__in=company_owners)

        self.stdout.write(f"Processing {users.count()} users...")

        # Process each user
        for user in users:
            self.stdout.write(f"Processing user '{user.username}' (ID: {user.id})")

            # Get companies owned by this user
            companies = Company.objects.filter(owner=user)
            if not companies.exists():
                self.stdout.write(self.style.WARNING(f"  User '{user.username}' is not a company owner. Skipping."))
                continue

            # Process each company
            for company in companies:
                self.stdout.write(f"  Processing company '{company.name}' (ID: {company.id})")

                # Get current permissions
                current_perms = get_perms(user, company)
                if verbose:
                    self.stdout.write(f"    Current permissions: {current_perms}")

                # Assign all owner permissions
                for perm_string in OWNER_PERMS_COMPANY:
                    try:
                        # Split the permission string into app_label and codename
                        app_label, codename = perm_string.split('.')

                        # Handle permissions differently based on app_label
                        if app_label == 'accounts':
                            # For accounts app, use the Company model
                            model_name = 'company'
                        elif app_label == 'assistants':
                            # For assistants app, use the Assistant model
                            model_name = 'assistant'
                        else:
                            # For other apps, use a default model
                            model_name = 'company'

                        try:
                            # Get the content type for the app and model
                            content_type = ContentType.objects.get(app_label=app_label, model=model_name)

                            # Check if the permission exists
                            try:
                                perm_obj = Permission.objects.get(content_type=content_type, codename=codename)

                                # For accounts permissions, assign to the company
                                if app_label == 'accounts':
                                    if codename not in current_perms:
                                        assign_perm(codename, user, company)
                                        if verbose:
                                            self.stdout.write(self.style.SUCCESS(f"    Assigned permission '{codename}' to owner"))

                                # For assistants permissions, we need to assign them to all assistants owned by the company
                                elif app_label == 'assistants':
                                    from assistants.models import Assistant
                                    assistants = Assistant.objects.filter(company=company)
                                    for assistant in assistants:
                                        try:
                                            assign_perm(codename, user, assistant)
                                            if verbose:
                                                self.stdout.write(self.style.SUCCESS(f"    Assigned permission '{codename}' to owner for assistant '{assistant.name}'"))
                                        except Exception as e:
                                            self.stdout.write(self.style.ERROR(f"    Error assigning assistant permission '{codename}' for assistant '{assistant.name}': {e}"))

                                    if verbose and not assistants.exists():
                                        self.stdout.write(f"    No assistants found for company '{company.name}'")

                            except Permission.DoesNotExist:
                                self.stdout.write(self.style.ERROR(f"    Permission '{perm_string}' does not exist in database"))
                                continue

                        except ContentType.DoesNotExist:
                            self.stdout.write(self.style.ERROR(f"    Content type for app '{app_label}' and model '{model_name}' does not exist"))
                            continue

                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f"    Error assigning permission '{perm_string}': {e}"))

                # Verify permissions after assignment
                final_perms = get_perms(user, company)
                if verbose:
                    self.stdout.write(f"    Final permissions: {final_perms}")

                # Check if any account permissions are missing
                account_perms = [p.split('.')[1] for p in OWNER_PERMS_COMPANY if p.startswith('accounts.')]
                missing_account_perms = set(account_perms) - set(final_perms)

                if missing_account_perms:
                    self.stdout.write(self.style.WARNING(f"    Missing account permissions: {missing_account_perms}"))
                else:
                    self.stdout.write(self.style.SUCCESS(f"    All account permissions assigned successfully"))

                # Check assistant permissions separately
                if verbose:
                    self.stdout.write(f"    Assistant permissions assigned to all company assistants")

                # Overall success message
                self.stdout.write(self.style.SUCCESS(f"    Permissions assignment completed"))

        self.stdout.write(self.style.SUCCESS("Done!"))
