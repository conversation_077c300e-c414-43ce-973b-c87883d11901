{% extends 'corporate/base.html' %}

{% block title %}Dashboard - Corporate Prompt Master{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2 class="mb-3">Welcome, {{ user.get_full_name|default:user.username }}</h2>
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-building me-2"></i>{{ company.name }} Dashboard</h5>
            </div>
            <div class="card-body">
                {% if is_admin and pending_users_count > 0 %}
                <div class="alert alert-warning mb-3">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="alert-heading">Pending User Approvals</h5>
                            <p class="mb-0">You have {{ pending_users_count }} user{{ pending_users_count|pluralize }} waiting for approval.
                               <a href="{% url 'corporate:company_users' %}" class="alert-link">Review and approve them now</a>.</p>
                        </div>
                    </div>
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-4 text-center mb-3">
                        {% if company.logo %}
                        <img src="{{ company.logo.url }}" alt="{{ company.name }} Logo" class="img-fluid company-logo mb-2" style="max-height: 100px;">
                        {% else %}
                        <div class="company-logo-placeholder mb-2">
                            <i class="fas fa-building fa-4x"></i>
                        </div>
                        {% endif %}
                        <h5>{{ company.name }}</h5>
                        <p class="text-muted">{{ corporate_profile.job_title }}</p>
                    </div>
                    <div class="col-md-8">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5>Company Information</h5>
                            {% if user == company.owner %}
                            <a href="{% url 'corporate:edit_company' %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit me-1"></i> Edit
                            </a>
                            {% endif %}
                        </div>
                        <p>{{ company.description|default:"No company description available." }}</p>

                        {% if company.pending_approval %}
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Pending Approval:</strong> Your company is currently pending approval by an administrator. Some features may be limited until approval is granted.
                        </div>
                        {% elif not company.is_active %}
                        <div class="alert alert-danger">
                            <i class="fas fa-ban me-2"></i>
                            <strong>Company Inactive:</strong> Your company is currently inactive. Please contact an administrator for assistance.
                        </div>
                        {% endif %}

                        <div class="row mt-3">
                            <div class="col-6">
                                <div class="stat-card">
                                    <div class="stat-card-value">{{ available_games.count }}</div>
                                    <div class="stat-card-label">Available Games</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="stat-card">
                                    <div class="stat-card-value">{{ user_certificates.count }}</div>
                                    <div class="stat-card-label">Your Certificates</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <h2 class="mb-3">Quick Actions</h2>
        <div class="list-group shadow">
            {% if request.user.is_superuser %}
            <a href="{% url 'superadmin:dashboard' %}" class="list-group-item list-group-item-action bg-warning">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1"><i class="fas fa-shield-alt me-2"></i>Return to Admin</h5>
                </div>
                <p class="mb-1">Return to the superadmin dashboard</p>
            </a>
            {% elif request.impersonator %}
            <a href="{% url 'impersonate-stop' %}" class="list-group-item list-group-item-action bg-danger text-white mb-2">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1"><i class="fas fa-sign-out-alt me-2"></i>Stop Impersonation</h5>
                </div>
                <p class="mb-1">End impersonation and return to superadmin dashboard</p>
            </a>
            <a href="{% url 'superadmin:dashboard' %}" target="_blank" class="list-group-item list-group-item-action bg-secondary text-white mb-2">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1"><i class="fas fa-external-link-alt me-2"></i>Open Superadmin Dashboard</h5>
                </div>
                <p class="mb-1">Open the superadmin dashboard in a new tab</p>
            </a>
            {% endif %}
            <a href="{% url 'corporate:game_list' %}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1"><i class="fas fa-gamepad me-2"></i>Play Games</h5>
                </div>
                <p class="mb-1">Start or continue your prompt engineering training</p>
            </a>
            <a href="{% url 'corporate:view_certificates' %}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1"><i class="fas fa-certificate me-2"></i>View Certificates</h5>
                </div>
                <p class="mb-1">Access your earned certificates</p>
            </a>
            <a href="{% url 'corporate:leaderboard' %}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1"><i class="fas fa-trophy me-2"></i>Leaderboard</h5>
                </div>
                <p class="mb-1">See how you rank against others</p>
            </a>
            {% if is_admin %}
            <a href="{% url 'corporate:company_users' %}" class="list-group-item list-group-item-action {% if pending_users_count > 0 %}list-group-item-warning{% endif %}">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1">
                        <i class="fas fa-users-cog me-2"></i>Manage Users
                        {% if pending_users_count > 0 %}
                        <span class="badge rounded-pill bg-danger">{{ pending_users_count }}</span>
                        {% endif %}
                    </h5>
                </div>
                <p class="mb-1">
                    Invite and manage company users
                    {% if pending_users_count > 0 %}
                    <strong class="text-danger">({{ pending_users_count }} user{{ pending_users_count|pluralize }} pending approval)</strong>
                    {% endif %}
                </p>
            </a>
            {% endif %}
            {% if user == company.owner %}
            <a href="{% url 'corporate:edit_company' %}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h5 class="mb-1"><i class="fas fa-edit me-2"></i>Edit Company</h5>
                </div>
                <p class="mb-1">Update company details and settings</p>
            </a>
            {% endif %}
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-gamepad me-2"></i>Available Games</h5>
            </div>
            <div class="card-body">
                {% if available_games %}
                <div class="list-group">
                    {% for game in available_games %}
                    <a href="{% url 'game:index' %}?company={{ company.slug }}" class="list-group-item list-group-item-action">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ game.game_name }}</h5>
                        </div>
                        <p class="mb-1">Prompt Engineering Training</p>
                        <small class="text-muted">Access granted: {{ game.access_granted_at|date:"M d, Y" }}</small>
                    </a>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No games are currently available for your company.
                </div>
                {% endif %}
            </div>
            <div class="card-footer text-center">
                <a href="{% url 'corporate:game_list' %}" class="btn btn-primary">
                    <i class="fas fa-gamepad me-2"></i>View All Games
                </a>
            </div>
        </div>

        {% include 'corporate/game_progress_widget.html' %}
    </div>

    <div class="col-md-6">
        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-trophy me-2"></i>Top Performers</h5>
            </div>
            <div class="card-body">
                {% if top_performers %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>User</th>
                                <th>Score</th>
                                <th>Highest Role</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for entry in top_performers %}
                            <tr {% if entry.user == user %}class="table-primary"{% endif %}>
                                <td>{{ forloop.counter }}</td>
                                <td>{{ entry.user.get_full_name|default:entry.user.username }}</td>
                                <td>{{ entry.score }}</td>
                                <td>{{ entry.highest_role|title }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No leaderboard entries yet. Be the first to complete a game!
                </div>
                {% endif %}
            </div>
            <div class="card-footer text-center">
                <a href="{% url 'corporate:leaderboard' %}" class="btn btn-primary">
                    <i class="fas fa-trophy me-2"></i>View Full Leaderboard
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-history me-2"></i>Recent Activity</h5>
            </div>
            <div class="card-body">
                {% if recent_sessions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>User</th>
                                <th>Role</th>
                                <th>Score</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in recent_sessions %}
                            <tr>
                                <td>{{ session.updated_at|date:"M d, Y H:i" }}</td>
                                <td>{{ session.user.get_full_name|default:session.user.username }}</td>
                                <td>{{ session.current_role|title }}</td>
                                <td>{{ session.performance_score }}</td>
                                <td>
                                    {% if session.game_completed %}
                                    <span class="badge bg-success">Completed</span>
                                    {% else %}
                                    <span class="badge bg-primary">In Progress</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No recent activity. Start playing to see your progress!
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
