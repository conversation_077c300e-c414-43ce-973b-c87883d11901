from .models import Company, CorporateUser
from superadmin.models import SiteConfiguration

def user_companies(request):
    """
    Context processor to add user's companies to all templates.
    """
    context = {
        'user_companies': None
    }

    # Only process for authenticated users
    if not request.user.is_authenticated:
        return context

    try:
        # Get the user's primary company from their corporate profile
        corporate_profile = request.user.corporate_profile
        primary_company = corporate_profile.company

        # Start with the primary company
        user_companies = [primary_company]

        # Find all other companies the user belongs to
        other_companies = Company.objects.filter(
            employees__user=request.user
        ).exclude(id=primary_company.id).order_by('name')

        # Add other companies to the list
        user_companies.extend(other_companies)

        # Update the context
        context['user_companies'] = user_companies
    except (CorporateUser.DoesNotExist, AttributeError):
        # User doesn't have a corporate profile
        pass

    return context

def help_url_processor(request):
    """
    Context processor to add the site-wide help URL to all templates.
    """
    try:
        site_config = SiteConfiguration.objects.first()
        return {'HELP_URL': site_config.help_url if site_config else None}
    except SiteConfiguration.DoesNotExist:
        return {'HELP_URL': None}


def seo_settings_processor(request):
    """
    Context processor to add SEO settings to all templates.
    """
    try:
        site_config = SiteConfiguration.objects.first()
        if site_config:
            return {
                'SEO_TITLE': site_config.site_title,
                'SEO_DESCRIPTION': site_config.site_description,
                'SEO_KEYWORDS': site_config.site_keywords,
                'GOOGLE_ANALYTICS_ID': site_config.google_analytics_id,
                'GOOGLE_VERIFICATION_TAG': site_config.google_verification_tag,
            }
    except SiteConfiguration.DoesNotExist:
        pass

    # Default values if no configuration exists
    return {
        'SEO_TITLE': 'Corporate Prompt Master',
        'SEO_DESCRIPTION': 'AI-powered game for corporate training and prompt engineering skills',
        'SEO_KEYWORDS': 'corporate training, AI game, prompt engineering, corporate learning',
        'GOOGLE_ANALYTICS_ID': None,
        'GOOGLE_VERIFICATION_TAG': None,
    }


def pending_users_processor(request):
    """
    Context processor to add the count of pending users to all templates.
    """
    context = {
        'pending_users_count': 0
    }

    # Only process for authenticated users
    if not request.user.is_authenticated:
        return context

    try:
        # Get the user's corporate profile
        corporate_profile = request.user.corporate_profile

        # Check if user is a company admin
        if corporate_profile.is_company_admin:
            # Get the count of pending users
            pending_users_count = CorporateUser.objects.filter(
                company=corporate_profile.company,
                status=CorporateUser.STATUS_PENDING
            ).count()

            context['pending_users_count'] = pending_users_count
    except (CorporateUser.DoesNotExist, AttributeError):
        # User doesn't have a corporate profile
        pass

    return context
