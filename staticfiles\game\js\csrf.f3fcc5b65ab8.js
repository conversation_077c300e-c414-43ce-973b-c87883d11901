// Django CSRF protection for AJAX requests

// Function to get CSR<PERSON> token from cookies
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            // Does this cookie string begin with the name we want?
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Set up CSRF token for AJAX requests
function setupCSRF() {
    const csrftoken = getCookie('csrftoken');
    
    // Add CSRF token to all AJAX requests
    const originalFetch = window.fetch;
    window.fetch = function(url, options) {
        options = options || {};
        
        // Only add CSRF token to same-origin POST, PUT, PATCH, DELETE requests
        if (options.method && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(options.method.toUpperCase())) {
            if (!url.startsWith('http') || url.startsWith(window.location.origin)) {
                options.headers = options.headers || {};
                options.headers['X-CSRFToken'] = csrftoken;
            }
        }
        
        return originalFetch(url, options);
    };
    
    console.log('CSRF protection set up for AJAX requests');
}

// Set up CSRF protection when the page loads
document.addEventListener('DOMContentLoaded', setupCSRF);
