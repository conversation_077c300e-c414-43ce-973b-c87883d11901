import os
import sys
import django
import json
import uuid

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from game.models import GameSession
from corporate.models import Certificate, CorporateUser, Company

def create_certificate_for_user(username=None):
    # Get or create a user
    if username:
        try:
            user = User.objects.get(username=username)
            print(f"Using specified user: {user.username}")
        except User.DoesNotExist:
            print(f"User {username} not found. Please specify a valid username.")
            return
    else:
        # Get the first non-anonymous user
        users = User.objects.filter(is_active=True).exclude(username='AnonymousUser')
        if users.exists():
            user = users.first()
            print(f"Using existing user: {user.username}")
        else:
            print("No valid users found. Creating a sample user...")
            user = User.objects.create_user(
                username=f"sample_user_{uuid.uuid4().hex[:8]}",
                email="<EMAIL>",
                password="samplepassword",
                first_name="Sample",
                last_name="User"
            )
            print(f"Created user: {user.username}")
            
            # Create a company if needed
            company = Company.objects.first()
            if not company:
                company = Company.objects.create(
                    name="Sample Company",
                    is_active=True
                )
                print(f"Created company: {company.name}")
            
            # Create corporate user
            CorporateUser.objects.create(
                user=user,
                company=company,
                is_company_admin=True,
                status='active'
            )
            print(f"Created corporate user for {user.username}")
    
    # Create a game session
    try:
        # Check if user already has a game session
        existing_sessions = GameSession.objects.filter(user=user)
        if existing_sessions.exists():
            game_session = existing_sessions.first()
            print(f"Using existing game session for {user.username}")
        else:
            game_session = GameSession.objects.create(
                user=user,
                current_role='senior_manager',
                performance_score=95,
                challenges_completed=10,
                role_challenges_completed=3,
                game_completed=True,
                completed_roles=json.dumps(['applicant', 'junior_assistant', 'assistant', 'senior_assistant', 'manager', 'senior_manager'])
            )
            print(f"Created game session for {user.username}")
    except Exception as e:
        print(f"Error with game session: {str(e)}")
        return
    
    # Create a certificate
    try:
        # Check if user already has a certificate
        existing_certs = Certificate.objects.filter(user=user)
        if existing_certs.exists():
            # Update the existing certificate
            certificate = existing_certs.first()
            certificate.completed_games = json.dumps(['Corporate Prompt Master', 'Advanced Prompt Engineering'])
            certificate.save()
            print(f"Updated existing certificate with ID: {certificate.id}")
        else:
            certificate = Certificate.objects.create(
                user=user,
                game_session=game_session,
                title='Prompt Engineering Excellence',
                description='Has successfully completed the Corporate Prompt Master training program.',
                final_score=95,
                highest_role='senior_manager',
                completed_games=json.dumps(['Corporate Prompt Master', 'Advanced Prompt Engineering'])
            )
            print(f"Created certificate with ID: {certificate.id}")
        
        print(f"View certificate at: http://127.0.0.1:8000/corporate/certificates/")
        print(f"Download certificate at: http://127.0.0.1:8000/corporate/certificates/{certificate.id}/download/")
        
        # Print login instructions
        print("\nTo view the certificate, make sure you're logged in as this user:")
        print(f"Username: {user.username}")
        if user.username.startswith('sample_user_'):
            print("Password: samplepassword")
        
    except Exception as e:
        print(f"Error with certificate: {str(e)}")
        return

if __name__ == "__main__":
    # Get username from command line if provided
    username = sys.argv[1] if len(sys.argv) > 1 else None
    create_certificate_for_user(username)
