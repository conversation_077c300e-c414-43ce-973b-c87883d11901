/**
 * Header Glass Buttons CSS
 * Adds 3D glass-look styling to buttons in the assistant header
 * Updated to fix button click issues
 */

/* Base glass button styling */
.general-assistant-header .btn-outline-secondary {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1),
              0 1px 3px rgba(0, 0, 0, 0.08),
              inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  color: #333333 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  transform: translateZ(0) !important;
  border-radius: 0.5rem !important;
  padding: 0.375rem 0.75rem !important;
  position: relative !important;
  overflow: visible !important; /* Changed from hidden to visible */
  z-index: 1051 !important; /* Added z-index to ensure buttons are clickable */
  cursor: pointer !important; /* Ensure pointer cursor */
  pointer-events: auto !important; /* Ensure clicks are registered */
}

/* Hover effect */
.general-assistant-header .btn-outline-secondary:hover {
  background: rgba(255, 255, 255, 0.15) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15),
              0 2px 4px rgba(0, 0, 0, 0.1),
              inset 0 0 0 1px rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) translateZ(0) !important;
  color: #0066ff !important;
  border-color: rgba(0, 102, 255, 0.3) !important;
}

/* Active/pressed effect */
.general-assistant-header .btn-outline-secondary:active {
  background: rgba(255, 255, 255, 0.05) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1),
              inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  transform: translateY(1px) translateZ(0) !important;
}

/* Add subtle shine effect */
/* Remove the ::before pseudo-element and replace with a simple background transition */
.general-assistant-header .btn-outline-secondary {
  transition: background-color 0.3s ease, transform 0.3s ease, box-shadow 0.3s ease !important;
}

.general-assistant-header .btn-outline-secondary:hover {
  background-color: rgba(255, 255, 255, 0.15) !important;
  transform: translateY(-1px) !important;
}

/* Dark mode specific styling for all button types in header */
[data-theme="dark"] .general-assistant-header .btn-outline-secondary,
[data-theme="dark"] .general-assistant-header .btn-outline-primary,
[data-theme="dark"] .general-assistant-header .btn-sm,
[data-theme="dark"] .general-assistant-header button {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2),
              0 1px 3px rgba(0, 0, 0, 0.1),
              inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .general-assistant-header .btn-outline-secondary:hover,
[data-theme="dark"] .general-assistant-header .btn-outline-primary:hover,
[data-theme="dark"] .general-assistant-header .btn-sm:hover,
[data-theme="dark"] .general-assistant-header button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3),
              0 2px 4px rgba(0, 0, 0, 0.15),
              inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border-color: rgba(0, 102, 255, 0.5) !important;
  text-shadow: 0 0 5px rgba(0, 102, 255, 0.5) !important;
}

/* Ensure icons in buttons are visible in dark mode */
[data-theme="dark"] .general-assistant-header .btn-outline-secondary i,
[data-theme="dark"] .general-assistant-header .btn-outline-primary i,
[data-theme="dark"] .general-assistant-header .btn-sm i,
[data-theme="dark"] .general-assistant-header button i,
[data-theme="dark"] .general-assistant-header .bi {
  color: #ffffff !important;
  text-shadow: 0 0 5px rgba(0, 102, 255, 0.3) !important;
  background-color: transparent !important;
  background: none !important;
  box-shadow: none !important;
}

/* Special styling for like button in dark mode */
[data-theme="dark"] .general-assistant-header .like-button {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2),
              0 1px 3px rgba(0, 0, 0, 0.1),
              inset 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  color: #ffffff !important;
}

[data-theme="dark"] .general-assistant-header .like-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3),
              0 2px 4px rgba(0, 0, 0, 0.15),
              inset 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 51, 102, 0.5) !important;
}

[data-theme="dark"] .general-assistant-header .btn-outline-secondary:active {
  background: rgba(255, 255, 255, 0.03) !important;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2),
              inset 0 0 0 1px rgba(255, 255, 255, 0.03) !important;
}

/* Special styling for like button */
.general-assistant-header .like-button {
  position: relative !important;
  overflow: hidden !important;
}

.general-assistant-header .like-button .bi-heart-fill {
  color: #ff3366 !important;
  filter: drop-shadow(0 0 2px rgba(255, 51, 102, 0.3)) !important;
}

[data-theme="dark"] .general-assistant-header .like-button .bi-heart-fill {
  color: #ff3366 !important;
  filter: drop-shadow(0 0 3px rgba(255, 51, 102, 0.5)) !important;
}

/* Enhanced header text styling to match glass buttons */
.general-assistant-header h4 {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.general-assistant-header h5 {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

[data-theme="dark"] .general-assistant-header h4 {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .general-assistant-header h5 {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
}
