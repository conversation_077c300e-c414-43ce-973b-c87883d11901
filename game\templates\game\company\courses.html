{% extends 'game/company/base.html' %}

{% block title %}Courses - {{ company.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Company Courses</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCourseModal">
            <i class="bi bi-plus-circle"></i> Create New Course
        </button>
    </div>
    
    <!-- Courses List -->
    <div class="row">
        {% for course in courses %}
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">{{ course.name }}</h5>
                    <div>
                        {% if course.is_active %}
                        <span class="badge bg-success">Active</span>
                        {% else %}
                        <span class="badge bg-secondary">Inactive</span>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    <p class="card-text">
                        {% if course.description %}
                        {{ course.description|truncatechars:150 }}
                        {% else %}
                        <span class="text-muted">No description provided.</span>
                        {% endif %}
                    </p>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <small class="text-muted">
                                <i class="bi bi-people"></i> 
                                {% if course.is_public %}
                                All company members
                                {% elif course.teams %}
                                Specific teams
                                {% else %}
                                Restricted access
                                {% endif %}
                            </small>
                        </div>
                        <div>
                            <small class="text-muted">
                                <i class="bi bi-calendar"></i> 
                                {{ course.created_at|date:"M d, Y" }}
                            </small>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a href="{% url 'game:company_course_detail' company_slug=company.slug course_id=course.id %}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-pencil"></i> Edit
                        </a>
                        <a href="#" class="btn btn-sm btn-outline-success">
                            <i class="bi bi-play-fill"></i> Play
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% empty %}
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-book" style="font-size: 3rem;"></i>
                    </div>
                    <h4>No Courses Yet</h4>
                    <p class="text-muted">Create your first course to get started.</p>
                    <button type="button" class="btn btn-primary mt-3" data-bs-toggle="modal" data-bs-target="#createCourseModal">
                        <i class="bi bi-plus-circle"></i> Create New Course
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<!-- Create Course Modal -->
<div class="modal fade" id="createCourseModal" tabindex="-1" aria-labelledby="createCourseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <form method="post">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="createCourseModalLabel">Create New Course</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="{{ form.name.id_for_label }}" class="form-label">Course Name</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.name.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.description.id_for_label }}" class="form-label">Description</label>
                        {{ form.description }}
                        {% if form.description.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.description.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                {{ form.is_active }}
                                <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                    Active
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check form-switch">
                                {{ form.is_public }}
                                <label class="form-check-label" for="{{ form.is_public.id_for_label }}">
                                    Available to all company members
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="{{ form.teams.id_for_label }}" class="form-label">Teams (comma-separated)</label>
                        {{ form.teams }}
                        <div class="form-text">Leave empty if available to all teams or if restricted to specific users.</div>
                        {% if form.teams.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.teams.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="{{ form.start_role.id_for_label }}" class="form-label">Starting Role</label>
                            {{ form.start_role }}
                            {% if form.start_role.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.start_role.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <label for="{{ form.max_role.id_for_label }}" class="form-label">Maximum Role</label>
                            {{ form.max_role }}
                            <div class="form-text">Leave empty for no limit.</div>
                            {% if form.max_role.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.max_role.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Course</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize form elements
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form elements
        const nameInput = document.getElementById('{{ form.name.id_for_label }}');
        if (nameInput) {
            nameInput.classList.add('form-control');
            nameInput.placeholder = 'Enter course name';
        }
        
        const descriptionTextarea = document.getElementById('{{ form.description.id_for_label }}');
        if (descriptionTextarea) {
            descriptionTextarea.classList.add('form-control');
            descriptionTextarea.placeholder = 'Enter course description';
        }
        
        const teamsInput = document.getElementById('{{ form.teams.id_for_label }}');
        if (teamsInput) {
            teamsInput.classList.add('form-control');
            teamsInput.placeholder = 'e.g. Engineering, Marketing, Sales';
        }
        
        const startRoleSelect = document.getElementById('{{ form.start_role.id_for_label }}');
        if (startRoleSelect) {
            startRoleSelect.classList.add('form-select');
        }
        
        const maxRoleSelect = document.getElementById('{{ form.max_role.id_for_label }}');
        if (maxRoleSelect) {
            maxRoleSelect.classList.add('form-select');
        }
        
        const isActiveCheckbox = document.getElementById('{{ form.is_active.id_for_label }}');
        if (isActiveCheckbox) {
            isActiveCheckbox.classList.add('form-check-input');
        }
        
        const isPublicCheckbox = document.getElementById('{{ form.is_public.id_for_label }}');
        if (isPublicCheckbox) {
            isPublicCheckbox.classList.add('form-check-input');
        }
    });
</script>
{% endblock %}
