# Core Django packages
Django
markdown
python-dotenv
openai

# Database adapters
psycopg2-binary  # PostgreSQL adapter

# WSGI servers (replacing Passenger)
gunicorn; sys_platform != 'win32'  # For Unix/Linux/macOS
waitress; sys_platform == 'win32'  # For Windows

# Authentication and permissions
django-guardian
django-impersonate

# CORS and API
django-cors-headers

# Static files
whitenoise

# Image processing
Pillow

# QR code generation
qrcode

# PDF generation
WeasyPrint

# Flask (for legacy components)
Flask
Flask-Cors

# Utilities
pytz
python-dateutil

# Development and testing
pytest
pytest-django

# Standard library packages (included for reference)
# uuid
# re
# json
# datetime
# logging
# os
# sys
