/**
 * Dark Mode Styling for Folder Options Modal
 * Enhances the appearance of the folder modal in dark mode
 */

/* Modal container styling */
#folderOptionsModal .modal-content {
  background-color: #1e1e1e;
  border: 1px solid #333333;
  border-radius: 0.5rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

#folderOptionsModal .modal-header {
  background-color: #252525;
  border-bottom: 1px solid #333333;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  padding: 1rem 1.5rem;
}

#folderOptionsModal .modal-title {
  color: #ffffff;
  font-weight: 600;
}

#folderOptionsModal .modal-body {
  padding: 1.5rem;
  color: #e0e0e0;
  background-color: #1e1e1e;
}

/* Folder list styling */
#folderOptionsModal .folder-list-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #444444;
  border-radius: 0.25rem;
  background-color: #252525;
  scrollbar-width: thin;
  scrollbar-color: #444444 #252525;
}

#folderOptionsModal .folder-list-container::-webkit-scrollbar {
  width: 8px;
}

#folderOptionsModal .folder-list-container::-webkit-scrollbar-track {
  background: #252525;
}

#folderOptionsModal .folder-list-container::-webkit-scrollbar-thumb {
  background-color: #444444;
  border-radius: 4px;
}

#folderOptionsModal .list-group-item {
  border: none;
  border-bottom: 1px solid #333333;
  padding: 0.75rem 1rem;
  transition: all 0.2s ease;
  background-color: #2a2a2a;
  color: #e0e0e0;
}

#folderOptionsModal .list-group-item:last-child {
  border-bottom: none;
}

#folderOptionsModal .list-group-item:hover {
  background-color: #333333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Button styling */
#folderOptionsModal .btn-primary {
  background-color: #0077ff;
  border-color: #0066dd;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  padding: 0.6rem 1.2rem;
  font-weight: 500;
  color: #ffffff;
}

#folderOptionsModal .btn-primary:hover {
  background-color: #0066dd;
  border-color: #0055cc;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

#folderOptionsModal .btn-success {
  background-color: #28a745;
  border-color: #218838;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  color: #ffffff;
}

#folderOptionsModal .btn-success:hover {
  background-color: #218838;
  border-color: #1e7e34;
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

#folderOptionsModal .btn-outline-secondary {
  border-color: #444444;
  color: #e0e0e0;
  background-color: transparent;
  transition: all 0.2s ease;
  padding: 0.6rem 1.2rem;
}

#folderOptionsModal .btn-outline-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: #666666;
}

/* Quick save section styling */
#folderOptionsModal .quick-save-section {
  margin-top: 1rem;
}

#folderOptionsModal .quick-save-section .btn {
  padding: 0.75rem 1.5rem;
  font-size: 1.05rem;
}

/* Form control styling */
#folderOptionsModal .form-control {
  background-color: #252525;
  border-color: #444444;
  color: #ffffff;
  box-shadow: none;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#folderOptionsModal .form-control:focus {
  border-color: #0077ff;
  box-shadow: 0 0 0 0.25rem rgba(0, 119, 255, 0.25);
}

/* Alert styling */
#folderOptionsModal .alert-danger {
  background-color: rgba(220, 53, 69, 0.2);
  border-color: #dc3545;
  color: #f8d7da;
}

/* Divider styling */
#folderOptionsModal hr {
  border-color: #333333;
  opacity: 0.5;
  margin: 1.5rem 0;
}

/* Text styling */
#folderOptionsModal strong {
  color: #0077ff;
  font-weight: 600;
}

/* Text muted styling */
#folderOptionsModal .text-muted {
  color: #aaaaaa;
}

/* Icon styling */
#folderOptionsModal .bi-folder {
  color: #ffc107;
}

#folderOptionsModal .bi-folder-plus {
  color: #aaaaaa;
}

#folderOptionsModal .bi-heart-fill {
  color: #dc3545;
}

#folderOptionsModal .bi-check-lg {
  color: #ffffff;
}

/* Folder modal content container */
#folderOptionsModal .folder-modal-content {
  display: flex;
  flex-direction: column;
}

/* Folder list section */
#folderOptionsModal .folder-list-section {
  margin-bottom: 1.5rem;
}

/* Input group text */
#folderOptionsModal .input-group-text {
  background-color: #252525;
  border-color: #444444;
  color: #ffffff;
}
