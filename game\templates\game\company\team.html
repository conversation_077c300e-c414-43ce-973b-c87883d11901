{% extends 'game/company/base.html' %}

{% block title %}Team Management - {{ company.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Team Management</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#inviteModal">
            <i class="bi bi-person-plus"></i> Invite Team Members
        </button>
    </div>
    
    <!-- Team Members -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Team Members</h5>
        </div>
        <div class="card-body p-0">
            <div class="list-group list-group-flush">
                {% for member in team_members %}
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">{{ member.user.username }}</h6>
                            <p class="mb-0 small text-muted">
                                {{ member.user.email }}
                                <span class="badge bg-secondary ms-2">{{ member.role }}</span>
                            </p>
                        </div>
                        <div>
                            {% if member.role != 'Owner' %}
                            <button type="button" class="btn btn-sm btn-outline-danger">
                                <i class="bi bi-person-x"></i> Remove
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="list-group-item text-center py-3">
                    <p class="mb-0 text-muted">No team members yet.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <!-- Game Sessions -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0">Recent Game Sessions</h5>
        </div>
        <div class="card-body p-0">
            <div class="list-group list-group-flush">
                {% for session in game_sessions %}
                <div class="list-group-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            {% if session.user %}
                            <strong>{{ session.user.username }}</strong>
                            {% else %}
                            <strong>Anonymous</strong>
                            {% endif %}
                            <small class="text-muted ms-2">{{ session.created_at|date:"M d, Y" }}</small>
                        </div>
                        <div>
                            {% if session.game_completed %}
                            <span class="badge bg-success">Completed</span>
                            {% else %}
                            <span class="badge bg-warning text-dark">In Progress</span>
                            {% endif %}
                            <span class="badge bg-primary ms-1">{{ session.performance_score }} pts</span>
                        </div>
                    </div>
                    <div class="mt-1 small">
                        <span class="text-muted">Current Role:</span> {{ session.current_role }}
                        {% if session.team %}
                        <span class="text-muted ms-3">Team:</span> {{ session.team }}
                        {% endif %}
                    </div>
                </div>
                {% empty %}
                <div class="list-group-item text-center py-3">
                    <p class="mb-0 text-muted">No game sessions yet.</p>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<!-- Invite Modal -->
<div class="modal fade" id="inviteModal" tabindex="-1" aria-labelledby="inviteModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="inviteModalLabel">Invite Team Members</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="{{ invite_form.emails.id_for_label }}" class="form-label">{{ invite_form.emails.label }}</label>
                        {{ invite_form.emails }}
                        {% if invite_form.emails.help_text %}
                        <div class="form-text">{{ invite_form.emails.help_text }}</div>
                        {% endif %}
                        {% if invite_form.emails.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in invite_form.emails.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="{{ invite_form.message.id_for_label }}" class="form-label">{{ invite_form.message.label }}</label>
                        {{ invite_form.message }}
                        {% if invite_form.message.help_text %}
                        <div class="form-text">{{ invite_form.message.help_text }}</div>
                        {% endif %}
                        {% if invite_form.message.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in invite_form.message.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Invitations</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Initialize form elements
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form elements
        const emailsTextarea = document.getElementById('{{ invite_form.emails.id_for_label }}');
        if (emailsTextarea) {
            emailsTextarea.classList.add('form-control');
        }
        
        const messageTextarea = document.getElementById('{{ invite_form.message.id_for_label }}');
        if (messageTextarea) {
            messageTextarea.classList.add('form-control');
        }
    });
</script>
{% endblock %}
