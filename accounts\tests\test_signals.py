from django.test import TestCase, override_settings
from django.contrib.auth.models import User
from django.core import mail
from django.conf import settings
from django.utils import timezone
from ..models import Company, CompanyInvitation
import os
import shutil
import tempfile

# Create a temp directory for test media files
TEMP_MEDIA_ROOT = tempfile.mkdtemp()

@override_settings(MEDIA_ROOT=TEMP_MEDIA_ROOT)
class SignalsTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )
    
    def tearDown(self):
        """Clean up test files"""
        try:
            shutil.rmtree(TEMP_MEDIA_ROOT)
        except Exception as e:
            print(f'Error cleaning up test files: {e}')
    
    def test_company_creation_signal(self):
        """Test signal handling for company creation"""
        # Owner should be automatically added as member
        self.assertTrue(self.company.members.filter(id=self.user.id).exists())
        
        # QR code should be generated
        self.assertTrue(self.company.qr_code)
        self.assertTrue(os.path.exists(self.company.qr_code.path))
        
        # Default assistant should be created
        self.assertTrue(self.company.assistants.exists())
        assistant = self.company.assistants.first()
        self.assertEqual(assistant.name, f"{self.company.name} Simple Assistant")
        self.assertEqual(assistant.type, 'simple')
    
    def test_company_deletion_signal(self):
        """Test signal handling for company deletion"""
        qr_code_path = self.company.qr_code.path
        
        # Create test assistant
        from assistants.models import Assistant
        assistant = Assistant.objects.create(
            name='Test Assistant',
            company=self.company,
            type='simple'
        )
        
        # Create test invitation
        invitation = CompanyInvitation.objects.create(
            company=self.company,
            email='<EMAIL>'
        )
        
        # Delete company
        self.company.delete()
        
        # Check file cleanup
        self.assertFalse(os.path.exists(qr_code_path))
        
        # Check related objects cleanup
        self.assertFalse(Assistant.objects.filter(id=assistant.id).exists())
        self.assertFalse(CompanyInvitation.objects.filter(id=invitation.id).exists())
    
    @override_settings(
        SITE_URL='https://example.com',
        DEFAULT_FROM_EMAIL='<EMAIL>'
    )
    def test_invitation_email_signal(self):
        """Test signal handling for sending invitation emails"""
        # Create invitation
        invitation = CompanyInvitation.objects.create(
            company=self.company,
            email='<EMAIL>'
        )
        
        # Check that email was sent
        self.assertEqual(len(mail.outbox), 1)
        email = mail.outbox[0]
        
        # Verify email content
        self.assertIn(self.company.name, email.subject)
        self.assertIn(str(invitation.token), email.body)
        self.assertEqual(email.to, ['<EMAIL>'])
        
        # Check HTML alternative
        html_content = email.alternatives[0][0]
        self.assertIn(self.company.name, html_content)
        self.assertIn(str(invitation.token), html_content)
    
    def test_company_membership_signals(self):
        """Test signals handling membership changes"""
        # Create test member
        member = User.objects.create_user(
            username='member',
            email='<EMAIL>',
            password='member123'
        )
        
        # Test adding member
        self.company.members.add(member)
        self.assertTrue(self.company.members.filter(id=member.id).exists())
        
        # Test removing member
        self.company.members.remove(member)
        self.assertFalse(self.company.members.filter(id=member.id).exists())
    
    def test_user_creation_signal(self):
        """Test signal handling for user creation"""
        # Create new user
        new_user = User.objects.create_user(
            username='newuser',
            email='<EMAIL>',
            password='newpass123'
        )
        
        # Verify user setup
        # Add your verification logic here if you implement user profiles
        # or other user-related initialization in the future
        self.assertTrue(User.objects.filter(id=new_user.id).exists())
