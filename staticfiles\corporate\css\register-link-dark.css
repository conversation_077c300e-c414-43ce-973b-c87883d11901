/* 
 * Dark Mode Styling for Registration Link Page
 * This file provides enhanced dark mode styling for the registration link page
 */

/* Card styling */
.dark-mode .card {
    background-color: #1e1e1e !important;
    border-color: #333333 !important;
    color: #ffffff !important;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
}

/* Card header styling */
.dark-mode .card-header {
    background-color: #252525 !important;
    border-bottom: 1px solid #333333 !important;
    color: #ffffff !important;
}

/* Card footer styling */
.dark-mode .card-footer {
    background-color: #252525 !important;
    border-top: 1px solid #333333 !important;
    color: #cccccc !important;
}

/* Form control styling */
.dark-mode .form-control {
    background-color: #2a2a2a !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

.dark-mode .form-control:focus {
    background-color: #333333 !important;
    border-color: #0d6efd !important;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
    color: #ffffff !important;
}

.dark-mode .form-control::placeholder {
    color: #888888 !important;
}

/* Form label styling */
.dark-mode .form-label {
    color: #ffffff !important;
    font-weight: 500 !important;
}

/* Button styling */
.dark-mode .btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}

.dark-mode .btn-primary:hover {
    background-color: #0b5ed7 !important;
    border-color: #0a58ca !important;
}

/* Link styling */
.dark-mode a {
    color: #5c94f0 !important;
}

.dark-mode a:hover {
    color: #7ca8ff !important;
}

/* Text styling */
.dark-mode h1, .dark-mode h2, .dark-mode h3, 
.dark-mode h4, .dark-mode h5, .dark-mode h6,
.dark-mode .h1, .dark-mode .h2, .dark-mode .h3,
.dark-mode .h4, .dark-mode .h5, .dark-mode .h6 {
    color: #ffffff !important;
}

.dark-mode .text-center {
    color: #ffffff !important;
}

.dark-mode .lead {
    color: #e0e0e0 !important;
}

.dark-mode .small {
    color: #cccccc !important;
}

/* Badge styling */
.dark-mode .badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

.dark-mode .badge.bg-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
}

/* Alert styling */
.dark-mode .alert-danger {
    background-color: #2c1215 !important;
    border-color: #842029 !important;
    color: #ea868f !important;
}

/* Auth container styling */
.dark-mode .auth-container {
    background-color: #121212 !important;
}

/* Auth footer styling */
.dark-mode .auth-footer {
    color: #aaaaaa !important;
}

/* Form feedback styling */
.dark-mode .invalid-feedback {
    color: #ea868f !important;
}

/* Form text styling */
.dark-mode .form-text {
    color: #aaaaaa !important;
}
