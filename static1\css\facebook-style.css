/* Facebook-like styling */

:root {
    --fb-blue: #1877f2;
    --fb-light-blue: #e7f3ff;
    --fb-bg: #f0f2f5;
    --fb-text: #050505;
    --fb-secondary-text: #65676b;
    --fb-divider: #ced0d4;
    --fb-hover: #f2f2f2;
    --fb-comment-bg: #f0f2f5;
}

body.facebook-style {
    background-color: var(--fb-bg);
    color: var(--fb-text);
}

/* Navbar styling */
.facebook-style .navbar {
    background-color: white !important;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.facebook-style .navbar-brand {
    color: var(--fb-blue);
    font-weight: bold;
}

/* Left sidebar */
.facebook-sidebar {
    position: sticky;
    top: 70px;
}

.facebook-sidebar .nav-link {
    color: var(--fb-text);
    border-radius: 8px;
    padding: 8px 12px;
    margin-bottom: 5px;
    transition: background-color 0.2s;
}

.facebook-sidebar .nav-link:hover {
    background-color: var(--fb-hover);
}

.facebook-sidebar .nav-link i {
    color: var(--fb-blue);
    font-size: 1.2rem;
    width: 36px;
    text-align: center;
}

/* Post creation card */
.post-create-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
}

.post-input {
    border-radius: 20px;
    background-color: var(--fb-bg);
    border: none;
    padding: 8px 16px;
    cursor: pointer;
}

.post-input:focus {
    background-color: white;
    box-shadow: none;
    border: 1px solid var(--fb-divider);
}

.post-actions {
    border-top: 1px solid var(--fb-divider);
    padding-top: 8px;
}

.post-action-btn {
    border-radius: 8px;
    padding: 8px 12px;
    color: var(--fb-secondary-text);
    background-color: transparent;
    transition: background-color 0.2s;
}

.post-action-btn:hover {
    background-color: var(--fb-hover);
}

/* Post card */
.post-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    margin-bottom: 16px;
    overflow: hidden;
}

.post-header {
    padding: 12px 16px;
    display: flex;
    align-items: center;
}

.post-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.post-user {
    font-weight: 600;
    margin-bottom: 0;
}

.post-time {
    color: var(--fb-secondary-text);
    font-size: 0.8rem;
}

.post-content {
    padding: 0 16px 16px;
}

.post-image {
    max-width: 100%;
    margin-bottom: 12px;
}

.post-footer {
    padding: 8px 16px;
    border-top: 1px solid var(--fb-divider);
}

.post-stats {
    display: flex;
    justify-content: space-between;
    color: var(--fb-secondary-text);
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.post-actions {
    display: flex;
    justify-content: space-around;
}

.post-action {
    display: flex;
    align-items: center;
    color: var(--fb-secondary-text);
    padding: 8px 0;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.post-action:hover {
    background-color: var(--fb-hover);
}

.post-action i {
    margin-right: 4px;
}

/* Keywords styling */
.keyword-badge {
    background-color: var(--fb-light-blue);
    color: var(--fb-blue);
    border-radius: 16px;
    padding: 4px 12px;
    margin-right: 8px;
    margin-bottom: 8px;
    display: inline-block;
    font-size: 0.8rem;
}

/* Modal styling */
.facebook-modal .modal-content {
    border-radius: 8px;
}

.facebook-modal .modal-header {
    border-bottom: 1px solid var(--fb-divider);
}

.facebook-modal .modal-title {
    font-weight: bold;
}

.facebook-modal .modal-footer {
    border-top: 1px solid var(--fb-divider);
}

/* Stats cards */
.stat-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    padding: 16px;
    margin-bottom: 16px;
}

.stat-card h6 {
    color: var(--fb-secondary-text);
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.stat-card .stat-value {
    font-size: 1.5rem;
    font-weight: bold;
}

.stat-card i {
    color: var(--fb-blue);
    font-size: 1.2rem;
    margin-right: 8px;
}

/* Infinite scroll loader */
.loader {
    text-align: center;
    padding: 20px 0;
}

.loader .spinner-border {
    color: var(--fb-blue);
}

/* TinyMCE customization */
.tox-tinymce {
    border-radius: 8px !important;
    border: 1px solid var(--fb-divider) !important;
}

/* Responsive adjustments */
@media (max-width: 991.98px) {
    .facebook-sidebar {
        position: static;
        margin-bottom: 16px;
    }
}
