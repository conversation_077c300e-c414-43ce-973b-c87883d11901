"""
Utility functions for the superadmin app.
"""
from django.contrib.auth import get_user_model
from corporate.models import Company

def get_impersonation_users(request):
    """
    Custom queryset for django-impersonate to determine which users can be impersonated.
    This function is referenced in settings via IMPERSONATE_CUSTOM_USER_QUERYSET.

    Args:
        request: The current request (required by django-impersonate but not used here)

    Returns all users that are company owners or have specific roles.
    """
    User = get_user_model()

    # Get all company owners
    company_owners = Company.objects.values_list('owner_id', flat=True).distinct()

    # Get all users with corporate profiles
    users_with_profiles = User.objects.filter(corporate_profile__isnull=False)

    # Combine the querysets
    impersonation_users = User.objects.filter(
        id__in=list(company_owners) + list(users_with_profiles.values_list('id', flat=True))
    ).distinct()

    return impersonation_users

def allow_impersonation(request):
    """
    Custom permission check for django-impersonate.
    This function is referenced in settings via IMPERSONATE_CUSTOM_ALLOW.

    Args:
        request: The current request

    Returns:
        bool: True if impersonation is allowed, False otherwise
    """
    # Only superusers can impersonate
    if not request.user.is_superuser:
        return False

    # Superusers are always allowed to impersonate
    return True
