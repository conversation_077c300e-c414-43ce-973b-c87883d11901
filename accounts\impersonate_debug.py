"""
Debug wrapper for django-impersonate middleware.
"""
import logging
import sys
from django.utils.deprecation import MiddlewareMixin
from impersonate.middleware import ImpersonateMiddleware

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)

class ImpersonateDebugMiddleware(MiddlewareMixin):
    """
    Debug wrapper for django-impersonate middleware.
    This middleware should be placed before the django-impersonate middleware.
    """
    
    def process_request(self, request):
        """Log information before django-impersonate processes the request."""
        logger.debug("ImpersonateDebugMiddleware.process_request called")
        logger.debug(f"Request path: {request.path}")
        logger.debug(f"Request method: {request.method}")
        logger.debug(f"Request user: {getattr(request, 'user', 'No user')}")
        
        # Check for impersonate query parameters
        impersonate_id = request.GET.get('impersonate')
        impersonate_stop = 'impersonate-stop' in request.GET or request.path.endswith('/impersonate/stop/')
        
        if impersonate_id:
            logger.info(f"Impersonation requested for user ID: {impersonate_id}")
        
        if impersonate_stop:
            logger.info("Impersonation stop requested")
        
        return None
    
    def process_response(self, request, response):
        """Log information after django-impersonate processes the request."""
        logger.debug("ImpersonateDebugMiddleware.process_response called")
        
        # Check if impersonation is active
        is_impersonating = getattr(request, 'is_impersonate', False)
        logger.debug(f"Is impersonating: {is_impersonating}")
        
        if is_impersonating:
            real_user = getattr(request, 'real_user', None)
            impersonated_user = getattr(request, 'user', None)
            
            if real_user and impersonated_user:
                logger.info(f"Impersonation active: {real_user.username} -> {impersonated_user.username}")
            else:
                logger.warning("Impersonation active but missing user information")
        
        return response
