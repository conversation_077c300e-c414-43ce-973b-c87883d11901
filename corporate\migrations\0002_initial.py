# Generated by Django 5.2 on 2025-05-21 16:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("corporate", "0001_initial"),
        ("game", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="certificate",
            name="game_session",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="certificate",
                to="game.gamesession",
            ),
        ),
        migrations.AddField(
            model_name="certificate",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="certificates",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="company",
            name="approved_by",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="approved_companies",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="company",
            name="owner",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="owned_corporate_companies",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="companyinvitation",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="invitations",
                to="corporate.company",
            ),
        ),
        migrations.AddField(
            model_name="companyinvitation",
            name="invited_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="sent_corporate_invitations",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="corporateuser",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="employees",
                to="corporate.company",
            ),
        ),
        migrations.AddField(
            model_name="corporateuser",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="corporate_profile",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="gameaccess",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="game_access",
                to="corporate.company",
            ),
        ),
        migrations.AddField(
            model_name="leaderboard",
            name="company",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="leaderboards",
                to="corporate.company",
            ),
        ),
        migrations.AddField(
            model_name="leaderboardentry",
            name="leaderboard",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="entries",
                to="corporate.leaderboard",
            ),
        ),
        migrations.AddField(
            model_name="leaderboardentry",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="leaderboard_entries",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="registrationlink",
            name="company",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="registration_links",
                to="corporate.company",
            ),
        ),
        migrations.AddField(
            model_name="registrationlink",
            name="created_by",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="created_corporate_registration_links",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="corporateuser",
            name="registration_link",
            field=models.ForeignKey(
                blank=True,
                help_text="The registration link used to join the company, if any",
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="registered_users",
                to="corporate.registrationlink",
            ),
        ),
        migrations.AlterUniqueTogether(
            name="companyinvitation",
            unique_together={("company", "email", "status")},
        ),
        migrations.AlterUniqueTogether(
            name="gameaccess",
            unique_together={("company", "game_id")},
        ),
        migrations.AddIndex(
            model_name="leaderboardentry",
            index=models.Index(
                fields=["game_id"], name="corporate_l_game_id_e6c0d7_idx"
            ),
        ),
    ]
