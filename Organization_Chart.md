# Organization Chart System

This document explains the organization chart system in the Corporate Prompt Master game, detailing how the corporate hierarchy is visualized and integrated with the game mechanics.

## Overview

The organization chart is a visual representation of the corporate hierarchy in the game. It shows the different levels of the organization, from entry-level positions to executive roles, and highlights the player's current position and progression path.

The chart serves multiple purposes:
1. Visualizing the player's current position in the company
2. Showing the player's progression path
3. Providing context for the corporate structure
4. Enhancing immersion in the corporate simulation

## Implementation Files

The organization chart system is implemented across several files:

1. **generate_org_chart.py**: Python module that generates the HTML for the organization chart
2. **org-chart.js**: JavaScript file that handles the interactive features of the chart
3. **org-chart-styles.css**: CSS file that styles the organization chart
4. **org-chart-integration.js**: JavaScript file that integrates the chart with the game
5. **chart-hover-zoom.js** and **chart-hover-zoom.css**: Files that implement zoom functionality

## Organization Structure

The organization structure is defined in `generate_org_chart.py`:

```python
def generate_org_chart_html(current_role, completed_roles=None):
    """Generate HTML for the organization chart."""
    if completed_roles is None:
        completed_roles = []
    
    # Define the organizational structure for the chart
    org_structure = [
        # Executive Level
        {
            "level": "Executive",
            "roles": ["shareholders", "ceo", "coo"]
        },
        # VP Level
        {
            "level": "Vice Presidents",
            "roles": ["vp_marketing", "vp_operations", "vp_finance", "hr_director"]
        },
        # Manager Level
        {
            "level": "Managers",
            "roles": [
                "advertising_manager", "sales_manager",  # Marketing
                "service_manager", "production_manager", "facilities_manager",  # Operations
                "accounts_receivable_manager", "accounts_payable_manager",  # Finance
                "hr_manager"  # HR
            ]
        },
        # Associate Level
        {
            "level": "Associates",
            "roles": [
                "marketing_associate", "sales_associate",  # Marketing
                "service_associate", "production_associate", "facilities_associate",  # Operations
                "accounts_receivable_associate", "accounts_payable_associate",  # Finance
                "hr_coordinator",  # HR
                "junior_assistant"  # Entry level
            ]
        },
        # Entry Level
        {
            "level": "Entry",
            "roles": ["applicant"]
        }
    ]
    
    # Role display names for better readability
    role_display_names = {
        "shareholders": "Shareholders",
        "ceo": "CEO",
        "coo": "COO",
        "vp_marketing": "VP Marketing",
        "vp_operations": "VP Operations",
        "vp_finance": "VP Finance",
        "hr_director": "HR Director",
        # Additional role display names...
    }
    
    # Start building the HTML
    html = '<div class="org-chart-content">'
    
    # Add each level
    for level in org_structure:
        html += f'<div class="org-level"><div class="level-title">{level["level"]}</div><div class="level-roles">'
        
        # Add roles for this level
        for role in level["roles"]:
            # Determine role status
            if role == current_role:
                status = "current"
            elif role in completed_roles:
                status = "completed"
            else:
                status = "future"
            
            # Get display name
            display_name = role_display_names.get(role, role.replace("_", " ").title())
            
            # Add role to HTML
            html += f'<div class="org-role {status}" data-role="{role}">{display_name}</div>'
        
        html += '</div></div>'
    
    html += '</div>'
    
    return html
```

## HTML Structure

The generated HTML has a nested structure that represents the organizational hierarchy:

```html
<div class="org-chart-content">
    <div class="org-level">
        <div class="level-title">Executive</div>
        <div class="level-roles">
            <div class="org-role future" data-role="shareholders">Shareholders</div>
            <div class="org-role future" data-role="ceo">CEO</div>
            <div class="org-role future" data-role="coo">COO</div>
        </div>
    </div>
    <!-- Additional levels -->
</div>
```

## CSS Styling

The organization chart is styled using `org-chart-styles.css`:

```css
.org-chart-container {
    margin-bottom: 20px;
    overflow: hidden;
}

.org-chart {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: auto;
    max-height: 400px;
}

.org-chart-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.org-level {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.level-title {
    font-weight: bold;
    color: #555;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.level-roles {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.org-role {
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 0.9rem;
    background-color: #e9ecef;
    border: 1px solid #dee2e6;
    cursor: pointer;
    transition: all 0.2s ease;
}

/* Role status styling */
.org-role.current {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
    font-weight: bold;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.25);
}

.org-role.completed {
    background-color: #cce5ff;
    border-color: #b8daff;
    color: #004085;
}

.org-role.future {
    background-color: #e9ecef;
    border-color: #dee2e6;
    color: #6c757d;
}
```

## JavaScript Interaction

The organization chart has interactive features implemented in `org-chart.js`:

```javascript
// Initialize the organization chart
function initOrgChart() {
    // Add event listeners to role elements
    const roleElements = document.querySelectorAll('.org-role');
    roleElements.forEach(role => {
        role.addEventListener('click', handleRoleClick);
        role.addEventListener('mouseenter', handleRoleHover);
        role.addEventListener('mouseleave', handleRoleLeave);
    });
}

// Handle role click
function handleRoleClick(event) {
    const role = event.currentTarget;
    const roleId = role.getAttribute('data-role');
    
    // Show role details
    showRoleDetails(roleId);
}

// Show role details
function showRoleDetails(roleId) {
    // Get role information
    const roleInfo = getRoleInfo(roleId);
    
    // Create and show modal with role details
    const modal = document.createElement('div');
    modal.className = 'role-details-modal';
    
    // Add role information to modal
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${roleInfo.displayName}</h3>
                <span class="close">&times;</span>
            </div>
            <div class="modal-body">
                <p><strong>Department:</strong> ${roleInfo.department}</p>
                <p><strong>Reports to:</strong> ${roleInfo.reportsTo}</p>
                <p><strong>Responsibilities:</strong></p>
                <ul>
                    ${roleInfo.responsibilities.map(r => `<li>${r}</li>`).join('')}
                </ul>
            </div>
        </div>
    `;
    
    // Add modal to document
    document.body.appendChild(modal);
    
    // Add event listener to close button
    const closeButton = modal.querySelector('.close');
    closeButton.addEventListener('click', () => {
        document.body.removeChild(modal);
    });
}
```

## Zoom Functionality

The chart includes zoom functionality implemented in `chart-hover-zoom.js` and `chart-hover-zoom.css`:

```javascript
// Initialize zoom functionality
function initZoom() {
    const chartContainer = document.querySelector('.org-chart');
    if (!chartContainer) return;
    
    // Add event listeners for zoom
    chartContainer.addEventListener('mouseenter', handleZoomIn);
    chartContainer.addEventListener('mouseleave', handleZoomOut);
    
    // Add touch support for mobile
    chartContainer.addEventListener('touchstart', handleTouchZoom);
}

// Handle zoom in
function handleZoomIn() {
    const chartContent = document.querySelector('.org-chart-content');
    if (!chartContent) return;
    
    // Apply zoom transform
    chartContent.style.transform = 'scale(1.2)';
    chartContent.style.transformOrigin = 'center top';
    chartContent.style.transition = 'transform 0.3s ease';
}

// Handle zoom out
function handleZoomOut() {
    const chartContent = document.querySelector('.org-chart-content');
    if (!chartContent) return;
    
    // Reset zoom transform
    chartContent.style.transform = 'scale(1)';
}
```

## Integration with Game

The organization chart is integrated with the game in `context_aware_game.py`:

```python
@app.route('/get_game_state')
def get_game_state():
    global game_state
    
    # Initialize game state if needed
    if not game_state:
        game_state = {
            "current_role": "applicant",
            "performance_score": 5,
            "challenges_completed": 0,
            "role_challenges_completed": 0,
            "messages": [],
            "game_completed": False,
            "current_manager": "hr",
            "current_task": "cover_letter",
            "completed_roles": []
        }
    
    # Get the challenges required for the current role
    current_role = game_state["current_role"]
    role_info = ROLE_PROGRESSION.get(current_role, {})
    challenges_required = role_info.get("challenges_required", 3)
    
    # Generate role progression visualization
    role_progression_html = generate_role_progression_html(
        game_state["current_role"],
        game_state["completed_roles"]
    )
    
    # Generate organization chart
    org_chart_html = generate_org_chart_html(
        game_state["current_role"],
        game_state["completed_roles"]
    )
    
    # Create response with debug info
    response_data = {
        "status": "success",
        "messages": game_state["messages"],
        "current_role": game_state["current_role"],
        "performance_score": game_state["performance_score"],
        "characters": CHARACTERS,
        "game_completed": game_state["game_completed"],
        "current_manager": game_state["current_manager"],
        "manager_name": manager_name,
        "current_task": game_state["current_task"],
        "role_challenges_completed": game_state["role_challenges_completed"],
        "total_challenges_completed": game_state["challenges_completed"],
        "completed_roles": game_state["completed_roles"],
        "challenges_required": challenges_required,
        "role_progression": ROLE_PROGRESSION,
        "role_progression_html": role_progression_html,
        "org_chart_html": org_chart_html
    }
    
    return jsonify(response_data)
```

## Dark Mode Support

The organization chart supports dark mode through CSS variables:

```css
/* Light mode */
:root {
    --org-chart-bg: #f8f9fa;
    --org-chart-border: #dee2e6;
    --org-level-title: #555;
    --org-level-border: #ddd;
    --org-role-bg: #e9ecef;
    --org-role-border: #dee2e6;
    --org-role-text: #6c757d;
    --org-role-current-bg: #d4edda;
    --org-role-current-border: #c3e6cb;
    --org-role-current-text: #155724;
    --org-role-completed-bg: #cce5ff;
    --org-role-completed-border: #b8daff;
    --org-role-completed-text: #004085;
}

/* Dark mode */
.dark-mode {
    --org-chart-bg: #252525;
    --org-chart-border: #444;
    --org-level-title: #ccc;
    --org-level-border: #444;
    --org-role-bg: #333;
    --org-role-border: #555;
    --org-role-text: #ccc;
    --org-role-current-bg: #1e4620;
    --org-role-current-border: #2a623d;
    --org-role-current-text: #a3d9b1;
    --org-role-completed-bg: #1a3a5f;
    --org-role-completed-border: #2a5885;
    --org-role-completed-text: #9cc3e5;
}
```

## Mobile Responsiveness

The organization chart is responsive and adapts to different screen sizes:

```css
/* Responsive styles */
@media (max-width: 768px) {
    .org-chart {
        padding: 10px;
        max-height: 300px;
    }
    
    .org-role {
        padding: 6px 8px;
        font-size: 0.8rem;
    }
    
    .level-roles {
        gap: 6px;
    }
}

@media (max-width: 480px) {
    .org-chart {
        padding: 8px;
        max-height: 250px;
    }
    
    .org-role {
        padding: 4px 6px;
        font-size: 0.7rem;
    }
    
    .level-roles {
        gap: 4px;
    }
}
```

This organization chart system provides a visual representation of the corporate hierarchy and the player's progression path, enhancing the immersive experience of the Corporate Prompt Master game.
