{% extends 'base/layout.html' %}
{% load static %}
{% load crispy_forms_tags %}

{% block title %}Flag Answer - {{ assistant.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Flag This Answer</h1>
            <p class="text-muted mb-0">
                Help improve {{ assistant.name }} by flagging answers that need community input
            </p>
        </div>
        <div class="col-auto">
            <a href="{% url 'assistants:assistant_chat_by_id' company.id assistant.id %}" class="btn btn-light">
                <i class="bi bi-arrow-left me-2"></i>
                Back to Assistant
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Original Question & Answer</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label fw-bold">Question:</label>
                        <div class="p-3 bg-light rounded">{{ question }}</div>
                    </div>
                    <div>
                        <label class="form-label fw-bold">Current Answer:</label>
                        <div class="p-3 bg-light rounded">{{ answer }}</div>
                    </div>
                </div>
            </div>

            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">Why is this answer unsatisfactory?</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        <input type="hidden" name="question" value="{{ question }}">
                        <input type="hidden" name="answer" value="{{ answer }}">

                        <div class="mb-3">
                            {{ form.reason|as_crispy_field }}
                        </div>

                        {% if is_anonymous %}
                        <div class="mb-3">
                            {{ form.email|as_crispy_field }}
                            <div class="form-text text-info">
                                <i class="bi bi-info-circle me-1"></i>
                                Providing your email allows you to receive notifications when community members add context.
                            </div>
                        </div>
                        {% endif %}

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <button type="submit" class="btn btn-warning">
                                <i class="bi bi-flag me-2"></i>
                                Submit Flag
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">What Happens Next?</h5>
                </div>
                <div class="card-body">
                    <ol class="ps-3">
                        <li class="mb-2">Your flagged question will be visible to the community</li>
                        <li class="mb-2">Community members can add context to improve answers</li>
                        <li class="mb-2">You'll be notified when new context is added</li>
                        <li class="mb-2">The assistant will use the new context to provide better answers</li>
                    </ol>
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-people-fill me-2"></i>
                        Community members will see this in the Assist tab and can contribute their knowledge!
                    </div>
                </div>
            </div>

            <div class="card shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">Community Collaboration</h5>
                </div>
                <div class="card-body">
                    <p>After flagging, you can also:</p>
                    <ul class="ps-3">
                        <li class="mb-2">Visit the Assist tab to add your own context</li>
                        <li class="mb-2">Share the question with knowledgeable community members</li>
                        <li class="mb-2">Check back later to see new contributions</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
