/**
 * Force White Hearts in Dark Mode
 * This script ensures all heart icons are white by default in dark mode
 */

document.addEventListener('DOMContentLoaded', function() {
    // Function to force heart icons to be white in dark mode
    function forceWhiteHearts() {
        // Check if we're in dark mode
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        
        if (isDarkMode) {
            console.log('Dark mode detected, forcing heart icons to be white');
            
            // Get all like buttons
            const likeButtons = document.querySelectorAll('.like-button, .btn-like, .btn-favorite, .favorite-button');
            
            likeButtons.forEach(button => {
                // Skip liked buttons (they should be pink/red)
                if (button.classList.contains('text-danger')) {
                    return;
                }
                
                // Force SVG icons to be white
                const svgIcon = button.querySelector('svg');
                if (svgIcon) {
                    svgIcon.style.fill = '#ffffff';
                    svgIcon.style.color = '#ffffff';
                    
                    // Also force all paths to be white
                    const paths = svgIcon.querySelectorAll('path');
                    paths.forEach(path => {
                        path.style.fill = '#ffffff';
                    });
                }
                
                // Force Bootstrap icons to be white
                const bsIcon = button.querySelector('i');
                if (bsIcon) {
                    bsIcon.style.color = '#ffffff';
                }
            });
        }
    }
    
    // Run immediately
    forceWhiteHearts();
    
    // Also run when theme changes
    document.addEventListener('themeChanged', forceWhiteHearts);
    
    // Run periodically to catch any dynamically added buttons
    setInterval(forceWhiteHearts, 1000);
});
