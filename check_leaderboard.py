import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from corporate.models import Company, Leaderboard, LeaderboardEntry

# Check all companies
companies = Company.objects.all()
print(f"Total companies: {companies.count()}")

for company in companies:
    print(f"\nCompany: {company.name}")

    # Check leaderboards for this company
    leaderboards = Leaderboard.objects.filter(company=company, is_global=False)
    print(f"  Leaderboards: {leaderboards.count()}")

    for leaderboard in leaderboards:
        print(f"  - {leaderboard.name} ({leaderboard.time_period})")

        # Check entries for this leaderboard
        entries = LeaderboardEntry.objects.filter(leaderboard=leaderboard)
        print(f"    Entries: {entries.count()}")

        for entry in entries:
            print(f"    - {entry.user.username}: {entry.score} points, {entry.highest_role}")

# If no entries found, print a message
if not Leaderboard.objects.filter(is_global=False).exists():
    print("\nNo company leaderboards found in the database.")
elif not LeaderboardEntry.objects.exists():
    print("\nNo leaderboard entries found in the database.")
