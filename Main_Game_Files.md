# Main Game Files Documentation

This document provides detailed information about the core files that power the Corporate Prompt Master game.

## Core Python Files

### context_aware_game.py

The main Flask application that serves as the backend for the game.

**Key Functions:**
- `index()`: Serves the main game interface
- `start_game()`: Initializes or resets the game state
- `submit_prompt()`: Processes player submissions and evaluates responses
- `get_game_state()`: Returns the current game state to the client
- `generate_preview()`: Generates a preview of the player's response

**Game State Management:**
```python
game_state = {
    "current_role": "applicant",
    "performance_score": 0,
    "challenges_completed": 0,
    "role_challenges_completed": 0,
    "messages": [],
    "game_completed": False,
    "current_manager": "hr",
    "current_task": None,
    "completed_roles": []
}
```

**Routes:**
- `/`: Serves the main game interface
- `/start_game`: Initializes/resets the game state
- `/submit_prompt`: Processes player submissions
- `/get_game_state`: Returns the current game state
- `/generate_preview`: Generates response previews

### game_state_manager.py

Manages the game's state transitions and progression logic.

**Key Functions:**
- `check_for_promotion(game_state, role_progression)`: Determines if a player should be promoted
- `process_task_completion(game_state, evaluation_results, points_earned)`: Updates game state when a task is completed
- `process_task_failure(game_state)`: Handles failed task attempts
- `get_next_task(game_state, tasks_by_role)`: Determines the next task for the player
- `update_role_average_score(game_state, score)`: Updates the player's average score

### role_tasks.py

Defines tasks for each role in the game.

**Key Functions:**
- `get_tasks_for_role(role, tasks)`: Returns tasks for a specific role
- `generate_role_progression_html(current_role, completed_roles)`: Creates HTML for the role progression visualization

**Task Structure:**
```python
{
    "id": "task_id",
    "manager": "manager_id",
    "description": "Detailed task description...",
    "response_template": "Template for the expected response...",
    "feedback": {
        "good": "Positive feedback for excellent work...",
        "okay": "Moderate feedback with improvement suggestions...",
        "bad": "Critical feedback with guidance for improvement..."
    }
}
```

### all_role_tasks.py

Contains comprehensive task definitions for all roles in the company hierarchy.

**Key Features:**
- Defines tasks for all roles from applicant to executive level
- Maps roles to their appropriate managers
- Provides fallback task generation for undefined roles
- Ensures consistent task structure across the game

**Manager Mapping:**
```python
MANAGER_MAPPING = {
    "sales_manager": "vp_marketing",
    "advertising_manager": "vp_marketing",
    "service_manager": "vp_operations",
    "production_manager": "vp_operations",
    "facilities_manager": "vp_operations",
    "accounts_receivable_manager": "vp_finance",
    "accounts_payable_manager": "vp_finance",
    "hr_coordinator": "hr",
    "hr_manager": "hr_director"
}
```

### enhanced_evaluation.py

Evaluates player responses using LLM-powered analysis.

**Key Functions:**
- `evaluate_response_with_enhanced_llm(user_response, task_id, task_description)`: Evaluates responses using an LLM
- `calculate_score(evaluation_results)`: Converts evaluation metrics into a numerical score
- `generate_improvement_feedback(evaluation_results)`: Creates specific improvement suggestions
- `parse_evaluation_response(evaluation_text)`: Extracts structured data from LLM responses

**Evaluation Criteria:**
- Completeness: Does the response address all requirements?
- Quality: Is the response well-written and structured?
- Professionalism: Is the tone appropriate?
- Relevance: Does the response directly address the task?

### guideline_evaluation.py

Evaluates responses against predefined guidelines.

**Key Functions:**
- `evaluate_response_with_guidelines(user_response, task_id)`: Evaluates against structured guidelines
- `get_guidelines_for_task(task_id)`: Retrieves guidelines for a specific task
- `format_guidelines_for_prompt(guidelines)`: Formats guidelines for the LLM prompt
- `fallback_evaluation(user_response)`: Provides basic evaluation when LLM evaluation fails

### llm_feedback_generator.py

Generates personalized feedback from managers.

**Key Functions:**
- `generate_manager_feedback(player_response, task_id, manager_name, performance_metrics)`: Creates personalized feedback
- `call_llm_api(prompt, max_retries)`: Handles communication with the LLM API

### llm_response_generator_openai_format.py

Handles communication with LLM APIs in a provider-agnostic way.

**Key Functions:**
- `generate_response_with_llm(prompt, purpose, max_retries)`: Generates responses using an LLM
- `retry_with_exponential_backoff(func, max_retries)`: Implements retry logic for API calls

### generate_org_chart.py

Creates a visual representation of the company hierarchy.

**Key Functions:**
- `generate_org_chart_html(current_role, completed_roles)`: Generates HTML for the organization chart
- Defines the organizational structure with multiple levels
- Highlights the player's current position and completed roles

## Core Frontend Files

### context_aware_index.html

The main HTML file that defines the game's user interface.

**Key Components:**
- Left sidebar with player information and game stats
- Central chat area for interactions with characters
- Right sidebar with organization chart and role progression
- Modal dialogs for previews and celebrations
- Dark/light mode toggle

### context_aware_styles.css

The main CSS file that styles the game interface.

**Key Features:**
- Responsive layout using Flexbox
- Color schemes for light and dark modes
- Styling for the chat interface, sidebars, and modals
- Typography and spacing rules

### context_aware_game.js

The main JavaScript file that handles client-side game logic.

**Key Functions:**
- `startGame()`: Initializes the game
- `submitPrompt()`: Sends player responses to the server
- `updateGameState()`: Updates the UI based on game state
- `displayMessage()`: Adds messages to the chat interface
- `showPreviewModal()`: Displays the response preview modal

### org-chart.js and org-chart-styles.css

Implements the interactive organization chart.

**Key Features:**
- Visual representation of the corporate hierarchy
- Highlighting of current and completed roles
- Zoom and pan functionality
- Responsive design for different screen sizes

### role_progression_updated.css

Styles the role progression visualization.

**Key Features:**
- Visual representation of the player's career path
- Highlighting of current, completed, and future roles
- Responsive design for different screen sizes

## Supporting Files

### dark-light-mode.css

Implements the dark mode styling.

**Key Features:**
- CSS variables for color schemes
- Dark mode overrides for all UI components
- Special handling for charts and visualizations

### celebration.js and celebration.css

Implements celebration effects for achievements.

**Key Features:**
- Confetti animation for promotions
- Modal dialog with congratulatory messages
- Sound effects for celebrations

### responsive-messages.js and responsive-preview.css

Handles responsive behavior for the chat interface.

**Key Features:**
- Adapts message display for different screen sizes
- Ensures proper scrolling and layout on mobile devices
- Handles touch interactions

### chart-hover-zoom.js and chart-hover-zoom.css

Implements zoom functionality for charts.

**Key Features:**
- Zoom on hover for organization chart
- Pan and zoom controls
- Touch support for mobile devices

This documentation provides an overview of the main files in the Corporate Prompt Master game. Each file plays a specific role in creating an engaging, interactive experience that simulates career progression in a corporate environment.
