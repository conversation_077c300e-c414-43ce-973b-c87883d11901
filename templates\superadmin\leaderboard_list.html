{% extends 'superadmin/base_superadmin.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block superadmin_content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-6 col-lg-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {% trans "Total Leaderboards" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_leaderboards }}</div>
                            <div class="small text-muted mt-2">
                                <div>{% trans "Global:" %} {{ global_leaderboards }}</div>
                                <div>{% trans "Company:" %} {{ company_leaderboards }}</div>
                                <div>{% trans "Active:" %} {{ active_leaderboards }}</div>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-trophy fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6 col-lg-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {% trans "Total Entries" %}</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_entries }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="bi bi-list-ol fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-12 col-lg-6">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                        {% trans "Top Companies by Entries" %}</div>
                    <div class="table-responsive mt-2">
                        <table class="table table-sm mb-0">
                            <thead>
                                <tr>
                                    <th>{% trans "Company" %}</th>
                                    <th>{% trans "Entries" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for company in top_companies %}
                                <tr>
                                    <td>
                                        <a href="{% url 'superadmin:company_detail' pk=company.id %}">
                                            {{ company.name }}
                                        </a>
                                    </td>
                                    <td>
                                        {{ company.entry_count }}
                                        <div class="mt-1 d-flex flex-wrap gap-1">
                                            <a href="?company={{ company.id }}&time_period=all_time" class="badge bg-primary text-decoration-none">{% trans "All" %}</a>
                                            <a href="?company={{ company.id }}&time_period=monthly" class="badge bg-success text-decoration-none">{% trans "M" %}</a>
                                            <a href="?company={{ company.id }}&time_period=weekly" class="badge bg-info text-decoration-none">{% trans "W" %}</a>
                                            <a href="?company={{ company.id }}&time_period=daily" class="badge bg-warning text-decoration-none">{% trans "D" %}</a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="2">{% trans "No companies with entries yet" %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Filter Leaderboards" %}</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="company" class="form-label">{% trans "Company" %}</label>
                    <select name="company" id="company" class="form-select">
                        <option value="">{% trans "All Companies" %}</option>
                        {% for company in companies %}
                        <option value="{{ company.id }}" {% if filter_company == company.id|stringformat:"i" %}selected{% endif %}>
                            {{ company.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="time_period" class="form-label">{% trans "Time Period" %}</label>
                    <select name="time_period" id="time_period" class="form-select">
                        <option value="">{% trans "All Time Periods" %}</option>
                        {% for value, label in time_periods %}
                        <option value="{{ value }}" {% if filter_time_period == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="q" class="form-label">{% trans "Search" %}</label>
                    <input type="text" class="form-control" id="q" name="q" placeholder="{% trans 'Search by name or company' %}" value="{{ filter_q }}">
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">{% trans "Filter" %}</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Leaderboards Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">{% trans "Leaderboards" %}</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>{% trans "ID" %}</th>
                            <th>{% trans "Name" %}</th>
                            <th>{% trans "Company" %}</th>
                            <th>{% trans "Available Time Periods" %}</th>
                            <th>{% trans "Entries" %}</th>
                            <th>{% trans "Created" %}</th>
                            <th>{% trans "Actions" %}</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leaderboard in leaderboards %}
                        <tr>
                            <td>{{ leaderboard.id }}</td>
                            <td>
                                {% if filter_time_period %}
                                {{ leaderboard.name }}
                                {% else %}
                                {{ leaderboard.company.name }} Leaderboard
                                {% endif %}
                            </td>
                            <td>{{ leaderboard.company.name }}</td>
                            <td>
                                {% with company_id=leaderboard.company.id %}
                                <div class="d-flex flex-wrap gap-1">
                                    {% if filter_time_period %}
                                    <span class="badge bg-primary">
                                        {% if leaderboard.time_period == 'all_time' %}
                                        {% trans "All Time" %}
                                        {% elif leaderboard.time_period == 'monthly' %}
                                        {% trans "Monthly" %}
                                        {% elif leaderboard.time_period == 'weekly' %}
                                        {% trans "Weekly" %}
                                        {% elif leaderboard.time_period == 'daily' %}
                                        {% trans "Daily" %}
                                        {% else %}
                                        {{ leaderboard.time_period }}
                                        {% endif %}
                                    </span>
                                    {% else %}
                                    <a href="?company={{ company_id }}&time_period=all_time" class="badge bg-primary text-decoration-none">{% trans "All Time" %}</a>
                                    <a href="?company={{ company_id }}&time_period=monthly" class="badge bg-success text-decoration-none">{% trans "Monthly" %}</a>
                                    <a href="?company={{ company_id }}&time_period=weekly" class="badge bg-info text-decoration-none">{% trans "Weekly" %}</a>
                                    <a href="?company={{ company_id }}&time_period=daily" class="badge bg-warning text-decoration-none">{% trans "Daily" %}</a>
                                    {% endif %}
                                </div>
                                {% endwith %}
                            </td>
                            <td>{{ leaderboard.entry_count }}</td>
                            <td>{{ leaderboard.created_at|date:"Y-m-d" }}</td>
                            <td>
                                {% if filter_time_period %}
                                <a href="{% url 'superadmin:leaderboard_detail' leaderboard.id %}" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> {% trans "View" %}
                                </a>
                                {% else %}
                                <div class="btn-group">
                                    <a href="{% url 'superadmin:leaderboard_detail' leaderboard.id %}" class="btn btn-sm btn-info">
                                        <i class="bi bi-eye"></i> {% trans "View" %}
                                    </a>
                                    <button type="button" class="btn btn-sm btn-info dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false">
                                        <span class="visually-hidden">Toggle Dropdown</span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="?company={{ leaderboard.company.id }}&time_period=all_time">{% trans "All Time" %}</a></li>
                                        <li><a class="dropdown-item" href="?company={{ leaderboard.company.id }}&time_period=monthly">{% trans "Monthly" %}</a></li>
                                        <li><a class="dropdown-item" href="?company={{ leaderboard.company.id }}&time_period=weekly">{% trans "Weekly" %}</a></li>
                                        <li><a class="dropdown-item" href="?company={{ leaderboard.company.id }}&time_period=daily">{% trans "Daily" %}</a></li>
                                    </ul>
                                </div>
                                {% endif %}
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">{% trans "No leaderboards found." %}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_time_period %}&time_period={{ filter_time_period }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_time_period %}&time_period={{ filter_time_period }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_time_period %}&time_period={{ filter_time_period }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_time_period %}&time_period={{ filter_time_period }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if filter_company %}&company={{ filter_company }}{% endif %}{% if filter_time_period %}&time_period={{ filter_time_period }}{% endif %}{% if filter_q %}&q={{ filter_q }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock superadmin_content %}