from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db.models import Q
from accounts.models import TeamInvitation
from datetime import timedelta
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Manage team invitations - clean up expired invitations and send reminders'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clean',
            action='store_true',
            help='Clean up expired invitations',
        )
        parser.add_argument(
            '--remind',
            action='store_true',
            help='Send reminders for pending invitations',
        )
        parser.add_argument(
            '--days',
            type=int,
            default=3,
            help='Number of days before expiration to send reminder (default: 3)',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )

    def handle(self, *args, **options):
        now = timezone.now()
        clean = options['clean']
        remind = options['remind']
        days = options['days']
        dry_run = options['dry_run']

        if not (clean or remind):
            raise CommandError("Please specify at least one action: --clean or --remind")

        # Clean up expired invitations
        if clean:
            expired = TeamInvitation.objects.filter(
                Q(expires_at__lt=now, status='pending') |
                Q(created_at__lt=now - timedelta(days=30), status__in=['declined', 'expired'])
            )
            
            count = expired.count()
            self.stdout.write(f"Found {count} invitations to clean up")
            
            if not dry_run and count > 0:
                # Delete old declined/expired invitations
                old_deleted = expired.filter(status__in=['declined', 'expired']).delete()[0]
                # Mark pending as expired
                newly_expired = expired.filter(status='pending').update(status='expired')
                
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Cleaned up {old_deleted} old invitations and marked {newly_expired} as expired"
                    )
                )
            elif count > 0:
                self.stdout.write(
                    self.style.WARNING("Dry run - no changes made")
                )

        # Send reminders
        if remind:
            reminder_date = now + timedelta(days=days)
            to_remind = TeamInvitation.objects.filter(
                status='pending',
                expires_at__date=reminder_date.date(),
                # Don't remind if already reminded in last 24 hours
                modified_at__lt=now - timedelta(days=1)
            )
            
            count = to_remind.count()
            self.stdout.write(f"Found {count} invitations needing reminders")
            
            if not dry_run and count > 0:
                for invitation in to_remind:
                    try:
                        invitation.send_invitation()
                        invitation.metadata['reminder_sent'] = now.isoformat()
                        invitation.save(update_fields=['metadata', 'modified_at'])
                        self.stdout.write(f"Sent reminder for {invitation}")
                    except Exception as e:
                        logger.error(f"Failed to send reminder for {invitation}: {e}")
                        self.stderr.write(
                            self.style.ERROR(f"Failed to send reminder for {invitation}: {e}")
                        )
                
                self.stdout.write(
                    self.style.SUCCESS(f"Sent {count} reminders")
                )
            elif count > 0:
                self.stdout.write(
                    self.style.WARNING("Dry run - no reminders sent")
                )

        # Summary
        total_pending = TeamInvitation.objects.filter(status='pending').count()
        self.stdout.write("\nSummary:")
        self.stdout.write(f"Total pending invitations: {total_pending}")
        self.stdout.write(
            f"Invitations expiring in next week: "
            f"{TeamInvitation.objects.filter(status='pending', expires_at__lt=now + timedelta(days=7)).count()}"
        )
