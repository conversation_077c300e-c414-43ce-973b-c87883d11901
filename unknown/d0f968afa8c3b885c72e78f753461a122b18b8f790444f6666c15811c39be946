/**
 * Direct TinyMCE initialization
 * This is a simplified script to initialize TinyMCE directly
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log("Direct TinyMCE initialization script loaded");
    
    // Initialize TinyMCE after a short delay
    setTimeout(initTinyMCE, 500);
    
    // Add click handler to contributions tab
    var contributionsTab = document.getElementById('contributions-tab');
    if (contributionsTab) {
        contributionsTab.addEventListener('click', function() {
            console.log("Contributions tab clicked");
            setTimeout(initTinyMCE, 100);
        });
    }
    
    // Function to initialize TinyMCE
    function initTinyMCE() {
        console.log("Initializing TinyMCE directly");
        
        // Check if TinyMCE is already loaded
        if (typeof tinymce === 'undefined') {
            console.error("TinyMCE not loaded!");
            return;
        }
        
        // Remove any existing editor instances
        if (tinymce.get('id_text_content')) {
            tinymce.get('id_text_content').remove();
        }
        
        // Initialize TinyMCE
        tinymce.init({
            selector: '#id_text_content',
            height: 300,
            menubar: false,
            plugins: [
                'advlist autolink lists link image charmap print preview anchor',
                'searchreplace visualblocks code fullscreen',
                'insertdatetime media table paste code help wordcount'
            ],
            toolbar: 'undo redo | formatselect | bold italic backcolor | ' +
                    'alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:14px }',
            setup: function (editor) {
                editor.on('change', function () { 
                    editor.save(); 
                });
            },
            init_instance_callback: function(editor) {
                console.log("TinyMCE initialized successfully");
                
                // Force visibility of the editor container
                var editorContainer = editor.getContainer();
                if (editorContainer) {
                    editorContainer.style.visibility = 'visible';
                    editorContainer.style.display = 'block';
                }
                
                // Force visibility of all TinyMCE elements
                document.querySelectorAll('.tox-tinymce, .tox-toolbar__primary, .tox-toolbar-overlord, .tox-edit-area, .tox-edit-area__iframe').forEach(function(el) {
                    el.style.visibility = 'visible';
                    el.style.display = el.tagName === 'DIV' ? 'block' : 'flex';
                });
            }
        });
    }
});
