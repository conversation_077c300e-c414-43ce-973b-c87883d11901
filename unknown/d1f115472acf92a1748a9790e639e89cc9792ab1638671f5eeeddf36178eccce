/**
 * Theme Switcher - Disabled
 * Dark mode is now the only theme, so theme switching is no longer needed
 */

document.addEventListener('DOMContentLoaded', function() {
    // Always use dark mode
    const theme = 'dark';

    // Apply dark mode
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);

    // Remove any existing theme toggle buttons
    const themeToggleBtn = document.querySelector('.theme-toggle-btn');
    if (themeToggleBtn) {
        themeToggleBtn.remove();
    }
});
