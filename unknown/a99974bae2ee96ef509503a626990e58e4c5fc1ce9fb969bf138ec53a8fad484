/* Moderation Dashboard Styles */

/* Custom badge colors */
.bg-restricted-subtle {
    background-color: rgba(220, 53, 69, 0.1);
}
.text-restricted {
    color: #dc3545;
}

.bg-new-member-subtle {
    background-color: rgba(108, 117, 125, 0.1);
}
.text-new-member {
    color: #6c757d;
}

.bg-contributor-subtle {
    background-color: rgba(13, 110, 253, 0.1);
}
.text-contributor {
    color: #0d6efd;
}

.bg-trusted-member-subtle {
    background-color: rgba(25, 135, 84, 0.1);
}
.text-trusted-member {
    color: #198754;
}

.bg-expert-subtle {
    background-color: rgba(111, 66, 193, 0.1);
}
.text-expert {
    color: #6f42c1;
}

.bg-community-leader-subtle {
    background-color: rgba(255, 193, 7, 0.1);
}
.text-community-leader {
    color: #ffc107;
}

/* Report status badges */
.bg-pending-subtle {
    background-color: rgba(255, 193, 7, 0.1);
}
.text-pending {
    color: #ffc107;
}

.bg-approved-subtle {
    background-color: rgba(25, 135, 84, 0.1);
}
.text-approved {
    color: #198754;
}

.bg-rejected-subtle {
    background-color: rgba(108, 117, 125, 0.1);
}
.text-rejected {
    color: #6c757d;
}

.bg-removed-subtle {
    background-color: rgba(220, 53, 69, 0.1);
}
.text-removed {
    color: #dc3545;
}

/* Report type badges */
.bg-spam-subtle {
    background-color: rgba(255, 193, 7, 0.1);
}
.text-spam {
    color: #ffc107;
}

.bg-inappropriate-subtle {
    background-color: rgba(220, 53, 69, 0.1);
}
.text-inappropriate {
    color: #dc3545;
}

.bg-offensive-subtle {
    background-color: rgba(111, 66, 193, 0.1);
}
.text-offensive {
    color: #6f42c1;
}

.bg-misinformation-subtle {
    background-color: rgba(23, 162, 184, 0.1);
}
.text-misinformation {
    color: #17a2b8;
}

.bg-other-subtle {
    background-color: rgba(108, 117, 125, 0.1);
}
.text-other {
    color: #6c757d;
}

/* Action type badges */
.bg-approve-subtle {
    background-color: rgba(25, 135, 84, 0.1);
}
.text-approve {
    color: #198754;
}

.bg-reject-subtle {
    background-color: rgba(255, 193, 7, 0.1);
}
.text-reject {
    color: #ffc107;
}

.bg-remove-subtle {
    background-color: rgba(220, 53, 69, 0.1);
}
.text-remove {
    color: #dc3545;
}

.bg-warn-subtle {
    background-color: rgba(255, 193, 7, 0.1);
}
.text-warn {
    color: #ffc107;
}

.bg-ban-subtle {
    background-color: rgba(220, 53, 69, 0.1);
}
.text-ban {
    color: #dc3545;
}

.bg-unban-subtle {
    background-color: rgba(25, 135, 84, 0.1);
}
.text-unban {
    color: #198754;
}

.bg-adjust-reputation-subtle {
    background-color: rgba(13, 110, 253, 0.1);
}
.text-adjust-reputation {
    color: #0d6efd;
}

/* Card hover effects */
.card {
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

/* Table styles */
.table th {
    font-weight: 600;
    color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Button styles */
.btn {
    font-weight: 500;
}

/* Modal styles */
.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* Chart container styles */
canvas {
    max-width: 100%;
}
