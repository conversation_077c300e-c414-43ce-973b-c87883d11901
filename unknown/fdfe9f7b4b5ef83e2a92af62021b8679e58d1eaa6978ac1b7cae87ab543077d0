from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse, HttpResponseForbidden
from django.contrib import messages
from django.db.models import Count, Avg, Q
from django.utils import timezone
from django.utils.crypto import get_random_string
from django.core.paginator import Paginator
import json
import qrcode
from io import BytesIO
import base64
from datetime import timedelta

from .models import (
    CompanyGameSettings, CompanyCourse, CourseTask, CourseCompletion, GameSession
)
from corporate.models import Company, Leaderboard, LeaderboardEntry
from .forms import (
    CompanyRegistrationForm, CompanyGameSettingsForm, CompanyCourseForm,
    CourseTaskForm, TeamInvitationForm
)


@login_required
def company_create(request):
    """View for creating a new company"""
    if request.method == 'POST':
        form = CompanyRegistrationForm(request.POST)
        if form.is_valid():
            company = form.save(user=request.user)
            messages.success(request, f"Company '{company.name}' created successfully!")
            return redirect('game:company_dashboard', company_slug=company.slug)
    else:
        form = CompanyRegistrationForm()

    return render(request, 'game/company/create.html', {'form': form})


@login_required
def company_dashboard(request, company_slug):
    """Company dashboard view"""
    company = get_object_or_404(Company, slug=company_slug)

    # Check if user is owner or member
    if request.user != company.owner:
        return HttpResponseForbidden("You don't have permission to access this dashboard.")

    # Get game sessions for this company
    game_sessions = GameSession.objects.filter(company=company)

    # Get leaderboard
    leaderboard = Leaderboard.objects.filter(company=company, time_period='all_time', is_global=False).first()
    if not leaderboard:
        # Create default leaderboard if it doesn't exist
        leaderboard = Leaderboard.objects.create(
            company=company,
            name=f"{company.name} Leaderboard",
            time_period='all_time',
            is_global=False
        )

    # Get top performers
    top_performers = LeaderboardEntry.objects.filter(leaderboard=leaderboard).select_related('user')[:10]

    # Get recent game sessions
    recent_sessions = game_sessions.order_by('-created_at')[:10]

    # Get company courses
    courses = CompanyCourse.objects.filter(company=company)

    # Get company stats
    stats = {
        'total_sessions': game_sessions.count(),
        'active_sessions': game_sessions.filter(game_completed=False).count(),
        'completed_sessions': game_sessions.filter(game_completed=True).count(),
        'average_score': game_sessions.aggregate(Avg('performance_score'))['performance_score__avg'] or 0,
        'total_courses': courses.count(),
    }

    context = {
        'company': company,
        'stats': stats,
        'top_performers': top_performers,
        'recent_sessions': recent_sessions,
        'courses': courses,
    }

    return render(request, 'game/company/dashboard.html', context)


@login_required
def company_settings(request, company_slug):
    """View for updating company game settings"""
    company = get_object_or_404(Company, slug=company_slug)

    # Check if user is owner
    if request.user != company.owner:
        return HttpResponseForbidden("You don't have permission to modify company settings.")

    # Get or create settings
    settings, created = CompanyGameSettings.objects.get_or_create(company=company)

    if request.method == 'POST':
        form = CompanyGameSettingsForm(request.POST, instance=settings)
        if form.is_valid():
            form.save()
            messages.success(request, "Company settings updated successfully!")
            return redirect('game:company_settings', company_slug=company.slug)
    else:
        form = CompanyGameSettingsForm(instance=settings)

    context = {
        'company': company,
        'form': form,
    }

    return render(request, 'game/company/settings.html', context)


@login_required
def company_team(request, company_slug):
    """View for managing company team members"""
    company = get_object_or_404(Company, slug=company_slug)

    # Check if user is owner
    if request.user != company.owner:
        return HttpResponseForbidden("You don't have permission to manage team members.")

    # Handle team invitation form
    if request.method == 'POST':
        invite_form = TeamInvitationForm(request.POST)
        if invite_form.is_valid():
            emails = invite_form.cleaned_data['emails']
            message = invite_form.cleaned_data.get('message', '')

            # Generate invitation links
            invitation_links = []
            for email in emails:
                # Generate a unique token
                token = get_random_string(32)

                # TODO: Send invitation email

                # Store invitation in session for now
                if 'invitations' not in request.session:
                    request.session['invitations'] = {}

                request.session['invitations'][token] = {
                    'email': email,
                    'company_id': company.id,
                    'expires_at': (timezone.now() + timedelta(days=7)).isoformat(),
                }

                # Create invitation link
                invitation_links.append({
                    'email': email,
                    'link': f"/game/join/{token}/",
                })

            messages.success(request, f"Sent {len(emails)} invitation(s).")

            context = {
                'company': company,
                'invitation_links': invitation_links,
            }

            return render(request, 'game/company/invitation_links.html', context)
    else:
        invite_form = TeamInvitationForm()

    # Get team members (for now, just the owner)
    team_members = [{'user': company.owner, 'role': 'Owner'}]

    # Get game sessions by team members
    game_sessions = GameSession.objects.filter(
        company=company,
        user=company.owner
    ).order_by('-created_at')

    context = {
        'company': company,
        'team_members': team_members,
        'invite_form': invite_form,
        'game_sessions': game_sessions,
    }

    return render(request, 'game/company/team.html', context)


@login_required
def company_courses(request, company_slug):
    """View for managing company courses"""
    company = get_object_or_404(Company, slug=company_slug)

    # Check if user is owner
    if request.user != company.owner:
        return HttpResponseForbidden("You don't have permission to manage courses.")

    # Handle course creation form
    if request.method == 'POST':
        form = CompanyCourseForm(request.POST)
        if form.is_valid():
            course = form.save(commit=False)
            course.company = company
            course.save()
            messages.success(request, f"Course '{course.name}' created successfully!")
            return redirect('game:company_course_detail', company_slug=company.slug, course_id=course.id)
    else:
        form = CompanyCourseForm()

    # Get all courses for this company
    courses = CompanyCourse.objects.filter(company=company).order_by('-created_at')

    context = {
        'company': company,
        'courses': courses,
        'form': form,
    }

    return render(request, 'game/company/courses.html', context)


@login_required
def company_course_detail(request, company_slug, course_id):
    """View for viewing and editing a specific course"""
    company = get_object_or_404(Company, slug=company_slug)
    course = get_object_or_404(CompanyCourse, id=course_id, company=company)

    # Check if user is owner
    if request.user != company.owner:
        return HttpResponseForbidden("You don't have permission to view this course.")

    # Handle task creation form
    if request.method == 'POST':
        task_form = CourseTaskForm(request.POST)
        if task_form.is_valid():
            task = task_form.save(commit=False)
            task.course = course
            task.save()
            messages.success(request, f"Task '{task.title}' added successfully!")
            return redirect('game:company_course_detail', company_slug=company.slug, course_id=course.id)
    else:
        task_form = CourseTaskForm()

    # Get all tasks for this course
    tasks = CourseTask.objects.filter(course=course).order_by('role', 'order')

    # Group tasks by role
    tasks_by_role = {}
    for task in tasks:
        if task.role not in tasks_by_role:
            tasks_by_role[task.role] = []
        tasks_by_role[task.role].append(task)

    context = {
        'company': company,
        'course': course,
        'tasks_by_role': tasks_by_role,
        'task_form': task_form,
    }

    return render(request, 'game/company/course_detail.html', context)


@login_required
def company_leaderboard(request, company_slug):
    """View for company leaderboard"""
    company = get_object_or_404(Company, slug=company_slug)

    # Get leaderboard
    leaderboard = Leaderboard.objects.filter(company=company, time_period='all_time', is_global=False).first()
    if not leaderboard:
        # Create default leaderboard if it doesn't exist
        leaderboard = Leaderboard.objects.create(
            company=company,
            name=f"{company.name} Leaderboard",
            time_period='all_time',
            is_global=False
        )

    # Get filter parameters
    game_id = request.GET.get('game', '')
    team = request.GET.get('team', '')

    # Build filter query
    filter_query = {'leaderboard': leaderboard}

    if game_id:
        filter_query['game_id'] = game_id

    if team:
        # Find game sessions for this team
        team_user_ids = GameSession.objects.filter(
            company=company,
            team=team
        ).values_list('user_id', flat=True).distinct()

        filter_query['user_id__in'] = team_user_ids

    # Get entries with pagination
    entries = LeaderboardEntry.objects.filter(**filter_query).select_related('user')

    # Get available games for filtering
    available_games = LeaderboardEntry.objects.filter(
        leaderboard=leaderboard
    ).exclude(
        game_id=''
    ).values_list('game_id', 'game_name').distinct()

    # Get available teams for filtering
    available_teams = GameSession.objects.filter(
        company=company
    ).exclude(
        team=''
    ).values_list('team', flat=True).distinct()

    # Get current user's rank and score
    user_entry = None
    user_rank = None

    if request.user.is_authenticated:
        try:
            # Get the user's entry
            user_filter = filter_query.copy()
            user_filter['user'] = request.user
            user_entry = LeaderboardEntry.objects.get(**user_filter)

            # Calculate the user's rank
            user_rank = entries.filter(score__gt=user_entry.score).count() + 1
        except LeaderboardEntry.DoesNotExist:
            pass

    # Paginate the entries
    paginator = Paginator(entries, 20)  # 20 entries per page
    page_number = request.GET.get('page', 1)
    page_obj = paginator.get_page(page_number)

    context = {
        'company': company,
        'leaderboard': leaderboard,
        'page_obj': page_obj,
        'available_games': available_games,
        'available_teams': available_teams,
        'selected_game': game_id,
        'selected_team': team,
        'user_entry': user_entry,
        'user_rank': user_rank,
    }

    return render(request, 'game/company/leaderboard.html', context)


def company_home(request, company_slug):
    """
    Company home page view
    """
    company = get_object_or_404(Company, slug=company_slug)

    # Store company ID in session
    request.session['active_company_id'] = company.id

    # Get company settings
    settings, created = CompanyGameSettings.objects.get_or_create(company=company)

    # Check if user can access the company
    is_anonymous = not request.user.is_authenticated
    can_access = True

    if settings.require_login and is_anonymous:
        can_access = False

    if not settings.is_public and is_anonymous:
        can_access = False

    # Get company stats
    stats = {
        'total_players': GameSession.objects.filter(company=company).values('user').distinct().count(),
        'active_players': GameSession.objects.filter(
            company=company,
            updated_at__gte=timezone.now() - timedelta(days=7)
        ).values('user').distinct().count(),
        'total_completions': GameSession.objects.filter(company=company, game_completed=True).count(),
        'average_score': GameSession.objects.filter(
            company=company,
            performance_score__gt=0
        ).aggregate(Avg('performance_score'))['performance_score__avg'] or 0
    }

    # Get top performers
    top_performers = LeaderboardEntry.objects.filter(
        leaderboard__company=company,
        leaderboard__time_period='all_time',
        leaderboard__is_global=False
    ).select_related('user').order_by('-score')[:5]

    # Get available courses
    courses = CompanyCourse.objects.filter(company=company, is_active=True)

    # Get teams
    teams = GameSession.objects.filter(company=company).exclude(team='').values_list('team', flat=True).distinct()

    return render(request, 'game/company/home.html', {
        'company': company,
        'settings': settings,
        'can_access': can_access,
        'stats': stats,
        'top_performers': top_performers,
        'courses': courses,
        'teams': teams,
        'is_anonymous': is_anonymous
    })


def generate_company_qr(company):
    """Generate a QR code for company registration"""
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )

    # Create URL for joining the company
    join_url = f"/game/company/{company.slug}/join/"

    qr.add_data(join_url)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")

    # Convert to base64 for embedding in HTML
    buffer = BytesIO()
    img.save(buffer, format="PNG")
    img_str = base64.b64encode(buffer.getvalue()).decode()

    return f"data:image/png;base64,{img_str}"
