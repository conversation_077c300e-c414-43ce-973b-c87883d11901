{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Community Contexts - {{ assistant.name }}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/community-contexts.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header with navigation -->
    <div class="d-flex align-items-center mb-4">
        <div class="me-auto">
            <h1 class="h3 mb-0">Community Contexts</h1>
            <p class="text-muted mb-0">
                Knowledge contributed by the community for {{ assistant.name }}
            </p>
        </div>
        <div class="d-flex">
            <a href="{% url 'assistants:add_context' assistant.company.id assistant.id %}" class="btn btn-primary me-2">
                <i class="bi bi-plus-circle me-1"></i> Add Context
            </a>
            <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-outline-secondary">
                <i class="bi bi-chat-dots me-1"></i> Chat
            </a>
        </div>
    </div>

    <!-- Filter and Sort Bar -->
    <div class="card mb-4 border-0 shadow-sm">
        <div class="card-body p-3">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <span class="text-muted">Filter by:</span>
                    </div>
                    <div class="me-3">
                        <select class="form-select form-select-sm" id="user" name="user">
                            <option value="">All Users</option>
                            {% for u in users %}
                                <option value="{{ u.id }}" {% if request.GET.user == u.id|stringformat:"s" %}selected{% endif %}>
                                    {{ u.username }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="me-3">
                        <span class="text-muted">Sort by:</span>
                    </div>
                    <div class="me-3">
                        <select class="form-select form-select-sm" id="sort" name="sort">
                            <option value="">Most Recent</option>
                            <option value="upvotes" {% if sort_by == 'upvotes' %}selected{% endif %}>Most Upvoted</option>
                            <option value="usage" {% if sort_by == 'usage' %}selected{% endif %}>Most Used</option>
                        </select>
                    </div>
                </div>
                <div>
                    <button type="button" class="btn btn-sm btn-primary" id="filterBtn">
                        <i class="bi bi-funnel"></i> Apply
                    </button>
                    <a href="{% url 'assistants:contexts' assistant.company.id assistant.id %}" class="btn btn-sm btn-outline-secondary ms-2">
                        <i class="bi bi-x-circle"></i> Clear
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Context List -->
    <div class="contexts-list">
        {% for context in contexts %}
            <div class="card mb-3 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="d-flex align-items-center">
                            {% if context.created_by.profile.avatar %}
                                <img src="{{ context.created_by.profile.avatar.url }}" alt="{{ context.created_by.username }}" class="rounded-circle me-2" width="32" height="32">
                            {% else %}
                                <div class="bg-secondary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px;">
                                    <i class="bi bi-person-fill"></i>
                                </div>
                            {% endif %}
                            <div>
                                <span class="fw-bold">{{ context.created_by.get_full_name|default:context.created_by.username }}</span>
                                <small class="text-muted d-block">Added {{ context.created_at|date:"M d, Y" }}</small>
                            </div>
                        </div>
                        <div>
                            <span class="badge bg-primary me-1"><i class="bi bi-lightning-charge"></i> {{ context.times_used }} uses</span>
                            <span class="badge bg-success"><i class="bi bi-hand-thumbs-up"></i> <span class="upvote-count">{{ context.upvote_count }}</span> upvotes</span>
                            {% if context.answer_upvote_count %}
                            <span class="badge bg-info"><i class="bi bi-chat-dots"></i> {{ context.answer_upvote_count }} from answers</span>
                            {% endif %}
                        </div>
                    </div>

                    <h5 class="card-title">{{ context.title|default:"Untitled Context" }}</h5>
                    <div class="card-text mb-3 context-content" data-context-id="{{ context.id }}">
                        <div class="content-preview">{{ context.text_content|safe|truncatewords_html:50 }}</div>
                        <div class="content-full" style="display: none;">{{ context.text_content|safe }}</div>
                    </div>
                    <div class="d-flex justify-content-between mb-3">
                        <div>
                            <button class="btn btn-sm {% if context.id in upvoted_contexts %}btn-success{% else %}btn-outline-success{% endif %} upvote-btn"
                                    data-context-id="{{ context.id }}"
                                    data-url="{% url 'assistants:upvote_context' assistant.company.id assistant.id context.id %}">
                                <i class="bi bi-hand-thumbs-up me-1"></i>
                                <span class="upvote-text">{% if context.id in upvoted_contexts %}Upvoted{% else %}Upvote{% endif %}</span>
                            </button>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-link text-decoration-none show-more-btn" data-context-id="{{ context.id }}">
                                <i class="bi bi-chevron-down me-1"></i> Show more
                            </button>
                            {% if context.created_by == request.user %}
                                <a href="{% url 'assistants:edit_context' assistant.company.id assistant.id context.id %}" class="btn btn-sm btn-outline-secondary ms-2">
                                    <i class="bi bi-pencil me-1"></i> Edit
                                </a>
                                <form method="post" action="{% url 'assistants:delete_context' assistant.company.id assistant.id context.id %}" class="d-inline" onsubmit="return confirm('Are you sure you want to delete this context? This action cannot be undone.')">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-sm btn-outline-danger ms-2">
                                        <i class="bi bi-trash me-1"></i> Delete
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                    </div>

                    {% if context.images.exists %}
                        <div class="mb-3">
                            {% for img in context.images.all %}
                                <img src="{{ img.image.url }}" alt="{{ img.caption|default:'Context image' }}" class="img-fluid rounded mb-2" style="max-height: 300px;">
                            {% endfor %}
                        </div>
                    {% endif %}

                    {% if context.keywords %}
                        <div class="mb-2">
                            {% for keyword in context.keywords %}
                                <a href="?keyword={{ keyword }}" class="badge bg-light text-dark me-1 text-decoration-none">{{ keyword }}</a>
                            {% endfor %}
                        </div>
                    {% endif %}


                </div>
            </div>
        {% empty %}
            <div class="alert alert-info">
                <i class="bi bi-info-circle-fill me-2"></i>
                No contexts found. Be the first to contribute knowledge!
            </div>
        {% endfor %}
    </div>

    <!-- Simple Pagination -->
    {% if contexts.has_other_pages %}
        <div class="d-flex justify-content-center mt-4">
            <div class="btn-group">
                {% if contexts.has_previous %}
                    <a href="?page={{ contexts.previous_page_number }}{% if request.GET.keyword %}&keyword={{ request.GET.keyword }}{% endif %}{% if request.GET.user %}&user={{ request.GET.user }}{% endif %}" class="btn btn-outline-secondary">
                        <i class="bi bi-chevron-left"></i> Previous
                    </a>
                {% else %}
                    <button class="btn btn-outline-secondary" disabled>
                        <i class="bi bi-chevron-left"></i> Previous
                    </button>
                {% endif %}

                {% if contexts.has_next %}
                    <a href="?page={{ contexts.next_page_number }}{% if request.GET.keyword %}&keyword={{ request.GET.keyword }}{% endif %}{% if request.GET.user %}&user={{ request.GET.user }}{% endif %}" class="btn btn-outline-secondary">
                        Next <i class="bi bi-chevron-right"></i>
                    </a>
                {% else %}
                    <button class="btn btn-outline-secondary" disabled>
                        Next <i class="bi bi-chevron-right"></i>
                    </button>
                {% endif %}
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fix for the filter form
    const userSelect = document.getElementById('user');
    const sortSelect = document.getElementById('sort');
    const filterBtn = document.getElementById('filterBtn');

    if (userSelect && filterBtn) {
        // Create a hidden form
        const form = document.createElement('form');
        form.method = 'get';
        form.id = 'filterForm';
        document.body.appendChild(form);

        // Add event listener to the filter button
        filterBtn.addEventListener('click', function() {
            // Get the selected values
            const userValue = userSelect.value;
            const sortValue = sortSelect.value;

            // Create a hidden input for the user
            if (userValue) {
                const userInput = document.createElement('input');
                userInput.type = 'hidden';
                userInput.name = 'user';
                userInput.value = userValue;
                form.appendChild(userInput);
            }

            // Create a hidden input for the sort
            if (sortValue) {
                const sortInput = document.createElement('input');
                sortInput.type = 'hidden';
                sortInput.name = 'sort';
                sortInput.value = sortValue;
                form.appendChild(sortInput);
            }

            // Submit the form
            form.submit();
        });
    }

    // Show more/less functionality
    const showMoreButtons = document.querySelectorAll('.show-more-btn');

    showMoreButtons.forEach(button => {
        button.addEventListener('click', function() {
            const contextId = this.getAttribute('data-context-id');
            const contentContainer = document.querySelector(`.context-content[data-context-id="${contextId}"]`);
            const preview = contentContainer.querySelector('.content-preview');
            const fullContent = contentContainer.querySelector('.content-full');

            if (preview.style.display !== 'none') {
                // Show full content
                preview.style.display = 'none';
                fullContent.style.display = 'block';
                this.innerHTML = '<i class="bi bi-chevron-up me-1"></i> Show less';
            } else {
                // Show preview
                preview.style.display = 'block';
                fullContent.style.display = 'none';
                this.innerHTML = '<i class="bi bi-chevron-down me-1"></i> Show more';

                // Scroll back to the top of the card
                const card = this.closest('.card');
                card.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });

    // Upvote functionality
    const upvoteButtons = document.querySelectorAll('.upvote-btn');

    upvoteButtons.forEach(button => {
        button.addEventListener('click', function() {
            const contextId = this.getAttribute('data-context-id');
            const url = this.getAttribute('data-url');
            const upvoteText = this.querySelector('.upvote-text');
            const upvoteCountElement = this.closest('.card').querySelector('.upvote-count');
            const upvoteCount = parseInt(upvoteCountElement.textContent);

            // Get CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

            // Send AJAX request to upvote
            fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update the button appearance
                    if (data.upvote_status === 'added') {
                        button.classList.remove('btn-outline-success');
                        button.classList.add('btn-success');
                        upvoteText.textContent = 'Upvoted';
                    } else {
                        button.classList.remove('btn-success');
                        button.classList.add('btn-outline-success');
                        upvoteText.textContent = 'Upvote';
                    }

                    // Update the upvote count
                    upvoteCountElement.textContent = data.upvote_count;
                }
            })
            .catch(error => {
                console.error('Error upvoting context:', error);
            });
        });
    });
});
</script>
{% endblock %}