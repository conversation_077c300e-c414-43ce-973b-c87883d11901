/**
 * Chat Dark Mode Handler
 * Ensures dark mode is properly applied to the chat interface
 */

document.addEventListener('DOMContentLoaded', function() {
    // Check if dark mode is active
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';

    // Apply dark mode styles to chat elements
    applyDarkModeToChat(isDarkMode);

    // Listen for theme changes
    document.addEventListener('themeChanged', function(e) {
        const isDarkMode = e.detail.theme === 'dark';
        applyDarkModeToChat(isDarkMode);
    });

    // Function to apply dark mode to chat elements
    function applyDarkModeToChat(isDarkMode) {
        // Get all chat elements
        const chatContainer = document.querySelector('.chat-container, .general-chat-container');
        const chatBox = document.querySelector('.chat-box, .general-chat-box, #chat-box');
        const userMessages = document.querySelectorAll('.user-message .message-content');
        const assistantMessages = document.querySelectorAll('.assistant-message .message-content');
        const navContentBubbles = document.querySelectorAll('.nav-content-bubble .message-content');
        const chatInput = document.getElementById('chat-input') || document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const sidebar = document.getElementById('chat-sidebar');
        const assistantHeader = document.querySelector('.general-assistant-header');

        if (isDarkMode) {
            // Apply dark mode styles
            if (chatContainer) {
                chatContainer.style.backgroundColor = 'var(--dark-bg-tertiary)';
                chatContainer.style.background = 'var(--dark-bg-tertiary)';
                chatContainer.style.borderColor = 'var(--dark-border)';
                chatContainer.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.25)';
            }

            if (chatBox) {
                chatBox.style.backgroundColor = 'var(--dark-bg-secondary)';
                chatBox.style.background = 'var(--dark-bg-secondary)';
                chatBox.style.borderColor = 'var(--dark-border)';
            }

            userMessages.forEach(function(el) {
                el.style.background = 'linear-gradient(145deg, #0066ff, #0055cc)';
                el.style.color = '#ffffff';
                el.style.boxShadow = '0 5px 15px rgba(0, 102, 255, 0.3)';
                el.style.borderRadius = '18px 18px 4px 18px';
                el.style.border = 'none';

                // Add shine effect if not already present
                if (!el.querySelector('.shine-effect')) {
                    const shine = document.createElement('div');
                    shine.className = 'shine-effect';
                    shine.style.position = 'absolute';
                    shine.style.top = '0';
                    shine.style.left = '-50%';
                    shine.style.width = '200%';
                    shine.style.height = '100%';
                    shine.style.background = 'linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0) 100%)';
                    shine.style.transform = 'skewX(-20deg)';
                    shine.style.pointerEvents = 'none';

                    // Only add if element has position relative
                    if (window.getComputedStyle(el).position === 'static') {
                        el.style.position = 'relative';
                    }

                    el.appendChild(shine);
                }
            });

            assistantMessages.forEach(function(el) {
                el.style.background = 'linear-gradient(145deg, #2a2a2a, #222222)';
                el.style.color = '#ffffff';
                el.style.borderColor = '#333333';
                el.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2)';
                el.style.borderRadius = '18px 18px 18px 4px';
            });

            navContentBubbles.forEach(function(el) {
                el.style.background = 'linear-gradient(145deg, #252525, #1e1e1e)';
                el.style.color = '#ffffff';
                el.style.borderColor = '#333333';
                el.style.borderLeftColor = '#0077ff';
                el.style.borderLeftWidth = '4px';
                el.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05)';
                el.style.borderRadius = '8px';

                // Add glow effect to left border
                if (!el.querySelector('.border-glow')) {
                    const glow = document.createElement('div');
                    glow.className = 'border-glow';
                    glow.style.position = 'absolute';
                    glow.style.top = '0';
                    glow.style.left = '0';
                    glow.style.width = '4px';
                    glow.style.height = '100%';
                    glow.style.background = '#0077ff';
                    glow.style.boxShadow = '0 0 15px 2px rgba(0, 119, 255, 0.5)';
                    glow.style.opacity = '0.8';
                    glow.style.pointerEvents = 'none';
                    glow.style.borderRadius = '4px 0 0 4px';

                    // Only add if element has position relative
                    if (window.getComputedStyle(el).position === 'static') {
                        el.style.position = 'relative';
                    }

                    el.appendChild(glow);
                }
            });

            if (chatInput) {
                chatInput.style.backgroundColor = '#1a1a1a';
                chatInput.style.color = '#ffffff';
                chatInput.style.borderColor = '#333333';
                chatInput.style.borderRadius = '24px';
                chatInput.style.padding = '0.75rem 1.25rem';
                chatInput.style.boxShadow = 'inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05)';
            }

            // Style chat form if it exists
            const chatForm = document.getElementById('chat-form');
            if (chatForm) {
                chatForm.style.background = 'linear-gradient(to bottom, #1a1a1a, #151515)';
                chatForm.style.borderTop = '1px solid #333333';
                chatForm.style.padding = '1rem';
                chatForm.style.boxShadow = '0 -5px 15px rgba(0, 0, 0, 0.1)';
            }

            if (sendButton) {
                sendButton.style.background = 'linear-gradient(to bottom, #0077ff, #0055cc)';
                sendButton.style.border = 'none';
                sendButton.style.color = 'white';
                sendButton.style.borderRadius = '50%';
                sendButton.style.width = '46px';
                sendButton.style.height = '46px';
                sendButton.style.display = 'flex';
                sendButton.style.alignItems = 'center';
                sendButton.style.justifyContent = 'center';
                sendButton.style.boxShadow = '0 4px 10px rgba(0, 102, 255, 0.3)';
                sendButton.style.marginLeft = '0.5rem';
            }

            if (sidebar) {
                sidebar.style.background = 'linear-gradient(to bottom, #1a1a1a, #121212)';
                sidebar.style.borderColor = '#333333';
                sidebar.style.boxShadow = '0 5px 25px rgba(0, 0, 0, 0.5), inset -5px 0 15px rgba(0, 0, 0, 0.3)';

                // Style sidebar navigation items
                const sidebarNavItems = sidebar.querySelectorAll('.list-group-item');
                sidebarNavItems.forEach(item => {
                    item.style.background = 'transparent';
                    item.style.color = '#cccccc';
                    item.style.borderColor = '#333333';
                    item.style.transition = 'all 0.3s ease';
                    item.style.borderLeft = '3px solid transparent';
                    item.style.margin = '2px 0';

                    // Add hover event
                    item.addEventListener('mouseenter', function() {
                        if (!this.classList.contains('active')) {
                            this.style.background = 'rgba(255, 255, 255, 0.05)';
                            this.style.color = '#ffffff';
                            this.style.borderLeft = '3px solid #0077ff';
                        }
                    });

                    // Add leave event
                    item.addEventListener('mouseleave', function() {
                        if (!this.classList.contains('active')) {
                            this.style.background = 'transparent';
                            this.style.color = '#cccccc';
                            this.style.borderLeft = '3px solid transparent';
                        }
                    });

                    // Style active items
                    if (item.classList.contains('active')) {
                        item.style.background = 'linear-gradient(to right, rgba(0, 102, 255, 0.2), transparent)';
                        item.style.color = '#ffffff';
                        item.style.borderLeft = '3px solid #0077ff';
                        item.style.textShadow = '0 0 5px rgba(0, 102, 255, 0.5)';
                    }
                });
            }

            // Style the assistant header
            if (assistantHeader) {
                assistantHeader.style.background = 'linear-gradient(135deg, #1e1e1e 0%, #**********%)';
                assistantHeader.style.backgroundColor = '#1e1e1e';
                assistantHeader.style.borderBottom = '1px solid #333333';
                assistantHeader.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';

                // Force the header to be visible in dark mode
                assistantHeader.style.display = 'block';

                // Style header headings
                const companyName = assistantHeader.querySelector('h4');
                if (companyName) {
                    companyName.style.color = '#0066ff';
                    companyName.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.2)';
                }

                const assistantName = assistantHeader.querySelector('h5');
                if (assistantName) {
                    assistantName.style.color = '#ffffff';
                    assistantName.style.textShadow = '0 1px 1px rgba(0, 0, 0, 0.1)';
                }

                // Style all buttons in the header
                const allHeaderButtons = assistantHeader.querySelectorAll('button, .btn, .btn-outline-secondary, .btn-outline-primary, .btn-sm, .like-button, .rate-assistant-btn');
                allHeaderButtons.forEach(button => {
                    button.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                    button.style.borderColor = '#333333';
                    button.style.color = '#ffffff';
                    button.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';

                    // Style icons inside buttons
                    const icons = button.querySelectorAll('i, .bi');
                    icons.forEach(icon => {
                        icon.style.color = '#ffffff';
                        icon.style.textShadow = '0 0 5px rgba(0, 102, 255, 0.3)';
                    });

                    // Special handling for like button with heart icon
                    if (button.classList.contains('like-button')) {
                        const heartIcon = button.querySelector('.bi-heart-fill');
                        if (heartIcon) {
                            heartIcon.style.color = '#ff3366';
                            heartIcon.style.textShadow = '0 0 5px rgba(255, 51, 102, 0.5)';
                        }
                    }

                    // Add hover event
                    button.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = 'rgba(255, 255, 255, 0.1)';
                        this.style.borderColor = '#0066ff';
                        this.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.3)';
                        this.style.transform = 'translateY(-1px)';
                    });

                    // Add leave event
                    button.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
                        this.style.borderColor = '#333333';
                        this.style.boxShadow = '0 2px 5px rgba(0, 0, 0, 0.2)';
                        this.style.transform = 'none';
                    });
                });

                // Style horizontal rule
                const hr = assistantHeader.querySelector('hr');
                if (hr) {
                    hr.style.borderColor = '#333333';
                    hr.style.opacity = '0.5';
                }
            }

            // Apply dark mode to code blocks with syntax highlighting
            document.querySelectorAll('.message-content pre').forEach(function(el) {
                el.style.backgroundColor = '#1a1a1a';
                el.style.color = '#e6e6e6';
                el.style.border = '1px solid #333333';
                el.style.borderRadius = '8px';
                el.style.padding = '1rem';
                el.style.margin = '1rem 0';
                el.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.2)';
                el.style.overflowX = 'auto';
            });

            document.querySelectorAll('.message-content code').forEach(function(el) {
                // Skip code elements inside pre (already styled)
                if (el.parentElement.tagName !== 'PRE') {
                    el.style.backgroundColor = '#252525';
                    el.style.color = '#e6e6e6';
                    el.style.border = '1px solid #333333';
                    el.style.borderRadius = '4px';
                    el.style.padding = '0.2rem 0.4rem';
                    el.style.fontFamily = "'Consolas', 'Monaco', 'Courier New', monospace";
                    el.style.fontSize = '0.9em';
                }
            });

            // Apply dark mode to tables with modern styling
            document.querySelectorAll('.message-content table').forEach(function(el) {
                el.style.borderCollapse = 'collapse';
                el.style.width = '100%';
                el.style.margin = '1rem 0';
                el.style.border = '1px solid #333333';
                el.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.2)';
                el.style.borderRadius = '8px';
                el.style.overflow = 'hidden';
            });

            document.querySelectorAll('.message-content th').forEach(function(el) {
                el.style.backgroundColor = '#252525';
                el.style.color = '#ffffff';
                el.style.padding = '0.75rem 1rem';
                el.style.borderBottom = '2px solid #0077ff';
                el.style.textAlign = 'left';
                el.style.fontWeight = 'bold';
            });

            document.querySelectorAll('.message-content td').forEach(function(el) {
                el.style.padding = '0.75rem 1rem';
                el.style.borderBottom = '1px solid #333333';
                el.style.borderTop = 'none';
                el.style.borderLeft = 'none';
                el.style.borderRight = 'none';
            });

            // Zebra striping for table rows
            document.querySelectorAll('.message-content tr:nth-child(even)').forEach(function(el) {
                el.style.backgroundColor = 'rgba(255, 255, 255, 0.05)';
            });

            // Apply dark mode to blockquotes with modern styling
            document.querySelectorAll('.message-content blockquote').forEach(function(el) {
                el.style.borderLeft = '4px solid #0077ff';
                el.style.backgroundColor = '#1e1e1e';
                el.style.color = '#cccccc';
                el.style.padding = '1rem 1.25rem';
                el.style.margin = '1rem 0';
                el.style.borderRadius = '0 8px 8px 0';
                el.style.boxShadow = '0 4px 10px rgba(0, 0, 0, 0.1)';
                el.style.fontStyle = 'italic';
            });

            // Apply dark mode to links with hover effects
            document.querySelectorAll('.message-content a').forEach(function(el) {
                el.style.color = '#0088ff';
                el.style.textDecoration = 'none';
                el.style.borderBottom = '1px dotted #0088ff';
                el.style.transition = 'all 0.2s ease';

                // Add hover event
                el.addEventListener('mouseenter', function() {
                    this.style.color = '#00aaff';
                    this.style.borderBottom = '1px solid #00aaff';
                    this.style.textShadow = '0 0 8px rgba(0, 170, 255, 0.5)';
                });

                // Add leave event
                el.addEventListener('mouseleave', function() {
                    this.style.color = '#0088ff';
                    this.style.borderBottom = '1px dotted #0088ff';
                    this.style.textShadow = 'none';
                });
            });

            // Apply dark mode to images with modern styling
            document.querySelectorAll('.message-content img').forEach(function(el) {
                el.style.maxWidth = '100%';
                el.style.border = '1px solid #333333';
                el.style.borderRadius = '8px';
                el.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.3)';
                el.style.transition = 'all 0.3s ease';

                // Add hover effect
                el.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.01)';
                    this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.4)';
                });

                // Add leave effect
                el.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                    this.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.3)';
                });
            });

            // Apply dark mode to headings with modern styling
            document.querySelectorAll('.message-content h1, .message-content h2, .message-content h3, .message-content h4, .message-content h5, .message-content h6').forEach(function(el) {
                el.style.color = '#ffffff';
                el.style.fontWeight = '600';
                el.style.marginTop = '1.5rem';
                el.style.marginBottom = '1rem';
                el.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.3)';

                // Add subtle accent line under headings
                if (el.tagName === 'H1' || el.tagName === 'H2') {
                    el.style.borderBottom = '1px solid #333333';
                    el.style.paddingBottom = '0.5rem';
                }

                // Add subtle accent color to h1 and h2
                if (el.tagName === 'H1') {
                    el.style.color = '#0088ff';
                }
            });

            // Apply dark mode to horizontal rules with modern styling
            document.querySelectorAll('.message-content hr').forEach(function(el) {
                el.style.border = 'none';
                el.style.height = '1px';
                el.style.background = 'linear-gradient(to right, transparent, #333333, transparent)';
                el.style.margin = '2rem 0';
            });
        } else {
            // Reset styles for light mode
            if (chatContainer) {
                chatContainer.style.backgroundColor = '#ffffff';
                chatContainer.style.background = '#ffffff';
                chatContainer.style.borderColor = '#e0e0e0';
                chatContainer.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.1)';
            }

            if (chatBox) {
                chatBox.style.backgroundColor = '#f9f9f9';
                chatBox.style.background = '#f9f9f9';
                chatBox.style.borderColor = '#f0f0f0';
            }

            userMessages.forEach(function(el) {
                el.style.backgroundColor = '#3b7dd8';
                el.style.background = '#3b7dd8';
                el.style.color = '#ffffff';
                el.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
            });

            assistantMessages.forEach(function(el) {
                el.style.backgroundColor = '#ffffff';
                el.style.background = '#ffffff';
                el.style.color = '#333333';
                el.style.borderColor = 'rgba(0, 0, 0, 0.05)';
                el.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.03)';
            });

            navContentBubbles.forEach(function(el) {
                el.style.backgroundColor = '#ffffff';
                el.style.background = '#ffffff';
                el.style.color = '#333333';
                el.style.borderColor = 'rgba(0, 0, 0, 0.05)';
                el.style.borderLeftColor = '#0d6efd';
            });

            if (chatInput) {
                chatInput.style.backgroundColor = '#ffffff';
                chatInput.style.color = '#333333';
                chatInput.style.borderColor = '#d0d0d0';
            }

            if (sendButton) {
                sendButton.style.backgroundColor = '#3b7dd8';
                sendButton.style.borderColor = '#3b7dd8';
            }

            if (sidebar) {
                sidebar.style.backgroundColor = '#ffffff';
                sidebar.style.borderColor = 'rgba(0, 0, 0, 0.05)';
                sidebar.style.boxShadow = '0 5px 15px rgba(0, 0, 0, 0.1)';
            }

            // Reset assistant header styling
            if (assistantHeader) {
                assistantHeader.style.background = 'linear-gradient(135deg, #ffffff 0%, var(--primary-light) 100%)';
                assistantHeader.style.backgroundColor = '#ffffff';
                assistantHeader.style.borderBottom = '1px solid rgba(0, 0, 0, 0.08)';
                assistantHeader.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.05)';

                // Ensure the header is visible in light mode if it was hidden
                if (assistantHeader.style.display === 'none') {
                    assistantHeader.style.display = 'block';
                }

                // Reset header headings
                const companyName = assistantHeader.querySelector('h4');
                if (companyName) {
                    companyName.style.color = '#0052cc';
                    companyName.style.textShadow = '0 1px 2px rgba(0, 0, 0, 0.1)';
                }

                const assistantName = assistantHeader.querySelector('h5');
                if (assistantName) {
                    assistantName.style.color = '#333333';
                    assistantName.style.textShadow = '0 1px 1px rgba(0, 0, 0, 0.05)';
                }

                // Reset all buttons in the header
                const allHeaderButtons = assistantHeader.querySelectorAll('button, .btn, .btn-outline-secondary, .btn-outline-primary, .btn-sm, .like-button, .rate-assistant-btn');
                allHeaderButtons.forEach(button => {
                    button.style.backgroundColor = '';
                    button.style.borderColor = '';
                    button.style.color = '';
                    button.style.boxShadow = '';
                    button.style.transform = '';

                    // Reset icons inside buttons
                    const icons = button.querySelectorAll('i, .bi');
                    icons.forEach(icon => {
                        icon.style.color = '';
                        icon.style.textShadow = '';
                    });

                    // Special handling for like button with heart icon
                    if (button.classList.contains('like-button')) {
                        const heartIcon = button.querySelector('.bi-heart-fill');
                        if (heartIcon) {
                            heartIcon.style.color = '#ff3366';
                            heartIcon.style.textShadow = '';
                        }
                    }

                    // Remove event listeners by cloning and replacing
                    const newButton = button.cloneNode(true);
                    button.parentNode.replaceChild(newButton, button);
                });

                // Reset horizontal rule
                const hr = assistantHeader.querySelector('hr');
                if (hr) {
                    hr.style.borderColor = 'rgba(0, 0, 0, 0.1)';
                    hr.style.opacity = '0.25';
                }
            }

            // Reset code blocks
            document.querySelectorAll('.message-content pre, .message-content code').forEach(function(el) {
                el.style.backgroundColor = '#f8f9fa';
                el.style.color = '#333333';
                el.style.borderColor = '#e0e0e0';
            });

            // Reset tables
            document.querySelectorAll('.message-content table').forEach(function(el) {
                el.style.borderColor = '#e0e0e0';
            });

            document.querySelectorAll('.message-content th, .message-content td').forEach(function(el) {
                el.style.borderColor = '#e0e0e0';
            });

            document.querySelectorAll('.message-content th').forEach(function(el) {
                el.style.backgroundColor = '#f8f9fa';
            });

            // Reset blockquotes
            document.querySelectorAll('.message-content blockquote').forEach(function(el) {
                el.style.borderLeftColor = '#e0e0e0';
                el.style.backgroundColor = '#f8f9fa';
                el.style.color = '#666666';
            });

            // Reset links
            document.querySelectorAll('.message-content a').forEach(function(el) {
                el.style.color = '#0d6efd';
            });

            // Reset images
            document.querySelectorAll('.message-content img').forEach(function(el) {
                el.style.borderColor = '#e0e0e0';
                el.style.backgroundColor = '#ffffff';
            });

            // Reset headings
            document.querySelectorAll('.message-content h1, .message-content h2, .message-content h3, .message-content h4, .message-content h5, .message-content h6').forEach(function(el) {
                el.style.color = '#333333';
            });

            // Reset horizontal rules
            document.querySelectorAll('.message-content hr').forEach(function(el) {
                el.style.borderColor = '#e0e0e0';
            });
        }
    }

    // Set up a mutation observer to apply styles to new messages
    const chatBox = document.getElementById('chat-box');
    if (chatBox) {
        // Use a debounced version to prevent excessive calls
        let timeout;
        const observer = new MutationObserver(function() {
            // Clear any existing timeout
            if (timeout) {
                clearTimeout(timeout);
            }

            // Set a new timeout to run the function after a delay
            timeout = setTimeout(function() {
                const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
                applyDarkModeToChat(isDarkMode);
            }, 500);
        });

        observer.observe(chatBox, { childList: true, subtree: false });
    }
});
