"""
Custom middleware for the accounts app.
"""
from django.utils.deprecation import MiddlewareMixin
from django.contrib.auth.models import User
from accounts.models import Company
from guardian.shortcuts import get_perms_for_model, get_objects_for_user
import logging
import sys

# Configure more verbose logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)

class ImpersonatePermissionsMiddleware(MiddlewareMixin):
    """
    Middleware to ensure that when a superuser impersonates a company owner,
    they get all the necessary permissions.

    This middleware should be placed after the django-impersonate middleware.
    """

    def process_request(self, request):
        """Process each request to ensure proper permissions during impersonation."""
        logger.debug("ImpersonatePermissionsMiddleware.process_request called")

        # Debug request attributes
        logger.debug(f"Request path: {request.path}")
        logger.debug(f"Request method: {request.method}")
        logger.debug(f"Request user: {getattr(request, 'user', 'No user')}")

        # Check if this is an impersonation session
        is_impersonating = hasattr(request, 'is_impersonate') and request.is_impersonate
        logger.debug(f"Is impersonation session: {is_impersonating}")

        if not is_impersonating:
            logger.debug("Not an impersonation session, returning None")
            return None

        # Debug impersonation attributes
        logger.debug(f"request.is_impersonate: {request.is_impersonate}")
        logger.debug(f"Has real_user attribute: {hasattr(request, 'real_user')}")
        logger.debug(f"Has user attribute: {hasattr(request, 'user')}")

        # Only proceed if we have both the real user and the impersonated user
        if not hasattr(request, 'real_user') or not hasattr(request, 'user'):
            logger.warning("Missing real_user or user attribute, returning None")
            return None

        # Debug user details
        logger.debug(f"Real user: {request.real_user.username} (ID: {request.real_user.id})")
        logger.debug(f"Impersonated user: {request.user.username} (ID: {request.user.id})")
        logger.debug(f"Real user is superuser: {request.real_user.is_superuser}")

        # Only proceed if the real user is a superuser
        if not request.real_user.is_superuser:
            logger.warning("Real user is not a superuser, returning None")
            return None

        # Get the impersonated user
        impersonated_user = request.user

        # Check if the impersonated user is a company owner
        companies = Company.objects.filter(owner=impersonated_user)
        logger.debug(f"Companies owned by impersonated user: {companies.count()}")

        if not companies.exists():
            # Not a company owner, no special handling needed
            logger.debug("Impersonated user is not a company owner, returning None")
            return None

        # Log the impersonation for debugging
        logger.info(f"Superuser {request.real_user.username} is impersonating company owner {impersonated_user.username}")

        # For each company owned by the impersonated user, ensure they have all owner permissions
        for company in companies:
            # Get all possible permissions for the Company model
            company_perms = get_perms_for_model(Company)

            # Log the company and available permissions
            logger.info(f"Ensuring permissions for company: {company.name} (ID: {company.id})")
            logger.debug(f"Available permissions: {[p.codename for p in company_perms]}")

            # Check what permissions the user currently has
            current_perms = get_objects_for_user(impersonated_user, 'accounts.change_company_settings', Company)
            logger.debug(f"Current permissions: {current_perms}")

            # If needed, you could force-assign permissions here using django-guardian
            # This would be a more invasive approach and should be used with caution

        # Add a flag to the request to indicate that impersonation UI should be shown
        request.show_impersonation_ui = True
        logger.debug("Added show_impersonation_ui=True flag to request")

        # Continue with the request
        logger.debug("Impersonation middleware processing complete")
        return None
