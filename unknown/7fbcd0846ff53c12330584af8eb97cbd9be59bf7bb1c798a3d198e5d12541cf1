<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Media Directories</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">
</head>
<body>
    <div class="container py-4">
        <h1 class="h2 mb-4">Check Media Directories</h1>
        
        <div class="card mb-4">
            <div class="card-body">
                <h5>Media Directory Status</h5>
                <div id="status-container">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p>Checking media directories...</p>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-body">
                <h5>Fix Logo Paths</h5>
                <p>This will scan the media directory for logo files and ensure they're in the correct location.</p>
                <button id="fix-button" class="btn btn-primary">Fix Logo Paths</button>
                <div id="fix-result" class="mt-3"></div>
            </div>
        </div>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const statusContainer = document.getElementById('status-container');
        const fixButton = document.getElementById('fix-button');
        const fixResult = document.getElementById('fix-result');
        
        // Check media directories on page load
        fetch('{% url "assistants:check_media_dirs" %}')
            .then(response => response.json())
            .then(data => {
                console.log('Directory check results:', data);
                
                let html = '<h6>Directory Status:</h6><ul>';
                for (const [key, value] of Object.entries(data.directories)) {
                    html += `<li><strong>${key}:</strong> ${value}</li>`;
                }
                html += '</ul>';
                
                statusContainer.innerHTML = html;
            })
            .catch(error => {
                console.error('Error:', error);
                statusContainer.innerHTML = `
                    <div class="alert alert-danger">
                        Error checking directories: ${error.message || 'Unknown error'}
                    </div>
                `;
            });
        
        // Fix logo paths when button is clicked
        fixButton.addEventListener('click', function() {
            fixResult.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p>Fixing logo paths...</p>
            `;
            
            fetch('{% url "assistants:check_media_dirs" %}?fix=true')
                .then(response => response.json())
                .then(data => {
                    console.log('Fix results:', data);
                    
                    let html = '<h6>Fix Results:</h6>';
                    if (data.fix_results) {
                        html += `
                            <ul>
                                <li><strong>Scanned:</strong> ${data.fix_results.scanned} files</li>
                                <li><strong>Moved:</strong> ${data.fix_results.moved} files</li>
                                <li><strong>Errors:</strong> ${data.fix_results.errors} files</li>
                            </ul>
                        `;
                        
                        if (data.fix_results.details && data.fix_results.details.length > 0) {
                            html += '<h6>Details:</h6><ul>';
                            for (const detail of data.fix_results.details) {
                                html += `<li><strong>Source:</strong> ${detail.source}<br><strong>Target:</strong> ${detail.target}<br><strong>Status:</strong> ${detail.status}</li>`;
                            }
                            html += '</ul>';
                        }
                    } else {
                        html += '<p>No fix was performed.</p>';
                    }
                    
                    fixResult.innerHTML = html;
                })
                .catch(error => {
                    console.error('Error:', error);
                    fixResult.innerHTML = `
                        <div class="alert alert-danger">
                            Error fixing logo paths: ${error.message || 'Unknown error'}
                        </div>
                    `;
                });
        });
    });
    </script>
</body>
</html>
