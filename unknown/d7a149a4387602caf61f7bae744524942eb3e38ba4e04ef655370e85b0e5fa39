/**
 * Category Badge Styling
 * Improves contrast and readability of assistant category badges
 */

/* Target the assistant type badge with blue background and blue text */
.badge.bg-primary.bg-opacity-10.text-primary,
.badge.bg-primary.bg-opacity-10.text-primary.tag-badge,
span.badge.bg-primary.bg-opacity-10.text-primary {
    /* Change to a solid, vibrant blue background with white text for better contrast */
    background-color: #0052cc !important; /* Darker blue for better contrast */
    background-image: none !important; /* Remove any gradient */
    color: #ffffff !important; /* White text for maximum readability */
    font-weight: 700 !important; /* Bolder text for better visibility */
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2) !important; /* Subtle text shadow for depth */
    box-shadow: 0 2px 4px rgba(0, 82, 204, 0.3) !important; /* Enhanced shadow with matching color */
    border: none !important; /* Remove any border */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
    opacity: 1 !important; /* Ensure full opacity */
    padding: 0.35em 0.65em !important; /* Slightly more padding */
}

/* Tag badges (light background with dark text) */
.badge.bg-light.text-dark.border.tag-badge,
span.badge.bg-light.text-dark.border {
    background-color: #d3d4d5 !important; /* Darker light gray for better contrast */
    color: #212529 !important; /* Dark text for maximum readability */
    border-color: #adb5bd !important; /* Darker border for better visibility */
    font-weight: 700 !important; /* Bolder text for better visibility */
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.5) !important; /* Light text shadow for depth */
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important; /* Enhanced shadow */
    letter-spacing: 0.02em !important; /* Slightly increased letter spacing */
}
