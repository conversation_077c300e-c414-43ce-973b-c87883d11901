{% extends 'corporate/base.html' %}

{% block title %}Edit Profile - Corporate Prompt Master{% endblock %}

{% block content %}
<div class="row justify-content-center mb-4">
    <div class="col-md-8">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2><i class="fas fa-user-edit me-2"></i>Edit Profile</h2>
            <a href="{% url 'corporate:corporate_dashboard' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i> Back to Dashboard
            </a>
        </div>

        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">Your Profile Information</h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        {% for error in form.non_field_errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="row mb-4">
                        <div class="col-md-3 text-center">
                            <div class="profile-image-container mb-3">
                                {% if user.profile.avatar %}
                                <img src="{{ user.profile.avatar.url }}" alt="Profile Picture" class="img-fluid rounded-circle profile-image" id="profile-preview">
                                {% else %}
                                <div class="avatar-placeholder rounded-circle text-white d-flex align-items-center justify-content-center" style="width: 150px; height: 150px; margin: 0 auto;">
                                    <span style="font-size: 3rem;">{{ user.username|first|upper }}</span>
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.avatar.id_for_label }}" class="form-label">Profile Picture</label>
                                {{ form.avatar }}
                                {% if form.avatar.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.avatar.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">Upload a new profile picture (optional)</div>
                            </div>
                        </div>

                        <div class="col-md-9">
                            <h5 class="mb-3">Personal Information</h5>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label">First Name</label>
                                    {{ form.first_name }}
                                    {% if form.first_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.first_name.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                    {{ form.last_name }}
                                    {% if form.last_name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.last_name.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.email.id_for_label }}" class="form-label">Email Address</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <h5 class="mb-3 mt-4">Company Information</h5>

                            <div class="mb-3">
                                <label class="form-label">Company</label>
                                <input type="text" class="form-control" value="{{ company.name }}" disabled>
                                <div class="form-text">You cannot change your company affiliation</div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="{{ form.job_title.id_for_label }}" class="form-label">Job Title</label>
                                    {{ form.job_title }}
                                    {% if form.job_title.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.job_title.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.department.id_for_label }}" class="form-label">Department</label>
                                    {{ form.department }}
                                    {% if form.department.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.department.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="{{ form.employee_id.id_for_label }}" class="form-label">Employee ID</label>
                                {{ form.employee_id }}
                                {% if form.employee_id.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.employee_id.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Role</label>
                                <input type="text" class="form-control" value="{% if corporate_profile.is_company_admin %}Administrator{% else %}Member{% endif %}" disabled>
                                <div class="form-text">Your role can only be changed by a company administrator</div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'corporate:corporate_dashboard' %}" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Preview uploaded profile image
        const avatarInput = document.getElementById('{{ form.avatar.id_for_label }}');
        const profilePreview = document.getElementById('profile-preview');

        if (avatarInput && profilePreview) {
            avatarInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();

                    reader.onload = function(e) {
                        // Create image if it doesn't exist
                        if (!profilePreview) {
                            const avatarPlaceholder = document.querySelector('.avatar-placeholder');
                            if (avatarPlaceholder) {
                                avatarPlaceholder.style.display = 'none';

                                const newImg = document.createElement('img');
                                newImg.id = 'profile-preview';
                                newImg.className = 'img-fluid rounded-circle profile-image';
                                newImg.alt = 'Profile Picture';

                                avatarPlaceholder.parentNode.appendChild(newImg);
                                profilePreview = document.getElementById('profile-preview');
                            }
                        }

                        // Update image source
                        if (profilePreview) {
                            profilePreview.src = e.target.result;
                        }
                    };

                    reader.readAsDataURL(this.files[0]);
                }
            });
        }
    });
</script>
{% endblock %}
