{% extends "superadmin/base_superadmin.html" %}
{% load i18n %}

{% block title %}{{ title }} - {% trans "Superadmin" %}{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{% url 'superadmin:dashboard' %}">{% trans "Dashboard" %}</a></li>
<li class="breadcrumb-item active" aria-current="page">{% trans "Companies" %}</li>
{% endblock %}

{% block superadmin_content %}
<!-- Filter Form -->
<div class="d-flex align-items-center mb-4">
    <div class="input-group me-2" style="max-width: 300px;">
        <input type="text" class="form-control" name="q" placeholder="{% trans 'Company name...' %}" value="{{ filter_q|default:'' }}">
        <button class="btn btn-primary" type="button" id="searchBtn">
            <i class="bi bi-search"></i>
        </button>
    </div>

    <select class="form-select me-2" style="max-width: 150px;" id="entityTypeFilter">
        <option value="" {% if not filter_entity_type %}selected{% endif %}>{% trans "Any Type" %}</option>
        <option value="company" {% if filter_entity_type == 'company' %}selected{% endif %}>{% trans "Company" %}</option>
        <option value="community" {% if filter_entity_type == 'community' %}selected{% endif %}>{% trans "Community" %}</option>
    </select>

    <select class="form-select me-2" style="max-width: 150px;" id="statusFilter">
        <option value="" {% if not filter_status %}selected{% endif %}>{% trans "Any Status" %}</option>
        <option value="active" {% if filter_status == 'active' %}selected{% endif %}>{% trans "Active" %}</option>
        <option value="inactive" {% if filter_status == 'inactive' %}selected{% endif %}>{% trans "Inactive" %}</option>
    </select>

    <select class="form-select me-2" style="max-width: 150px;" id="featuredFilter">
        <option value="" {% if not filter_featured %}selected{% endif %}>{% trans "Any Featured" %}</option>
        <option value="true" {% if filter_featured == 'true' %}selected{% endif %}>{% trans "Featured" %}</option>
        <option value="false" {% if filter_featured == 'false' %}selected{% endif %}>{% trans "Not Featured" %}</option>
    </select>

    <div class="ms-auto d-flex">
        <button type="button" class="btn btn-outline-light me-2" id="clearFilters">
            <i class="bi bi-x-circle"></i> {% trans "Clear Filters" %}
        </button>
        <button type="button" class="btn btn-primary" id="applyFilters">
            <i class="bi bi-funnel"></i> {% trans "Apply Filters" %}
        </button>
    </div>
</div>


{% if companies %}
<!-- Companies Table -->
<div class="superadmin-card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-building me-2"></i>{% trans "Companies" %}</h5>
        <span class="badge rounded-pill" style="background: var(--admin-gradient-primary); box-shadow: 0 2px 5px rgba(67, 97, 238, 0.3);">
            {{ companies|length }} {% if is_paginated %}<small>({% trans "filtered" %})</small>{% endif %}
        </span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="superadmin-table table">
                <thead>
                    <tr>
                        <th>{% trans "Name" %}</th>
                        <th>{% trans "Owner" %}</th>
                        <th>{% trans "Tier" %}</th>
                        <th>{% trans "Tier Expiry" %}</th>
                        <th>{% trans "Status" %}</th>
                        <th>{% trans "Featured" %}</th>
                        <th>{% trans "Pending Requests" %}</th>
                        <th>{% trans "Created" %}</th>
                        <th>{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody>
                    {% for company in companies %}
                    <tr data-company-pk="{{ company.pk }}">
                        <td class="fw-bold">{{ company.name }}</td>
                        <td>{{ company.owner.get_username }}</td>
                        <td>
                            <span class="status-badge {% if company.tier == company.TIER_GOLD %}status-badge-gold{% elif company.tier == company.TIER_PREMIUM %}status-badge-premium{% else %}status-badge-standard{% endif %}">
                                {{ company.get_tier_display }}
                            </span>
                        </td>
                        <td class="editable-field" data-field-name="tier_expiry_date">
                            <span>{% if company.tier_expiry_date %}{{ company.tier_expiry_date|date:"Y-m-d" }}{% else %}N/A{% endif %}</span>
                            <input type="date" class="form-control form-control-sm" value="{% if company.tier_expiry_date %}{{ company.tier_expiry_date|date:"Y-m-d" }}{% endif %}">
                        </td>
                        <td>
                            <span class="status-badge {% if company.is_active %}status-badge-active{% else %}status-badge-inactive{% endif %}">
                                {% if company.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge {% if company.is_featured %}status-badge-featured{% else %}status-badge-standard{% endif %}">
                                {% if company.is_featured %}{% trans "Yes" %}{% else %}{% trans "No" %}{% endif %}
                                {% if company.featured_expiry_date %}<small>({{ company.featured_expiry_date|date:"Y-m-d" }})</small>{% endif %}
                            </span>
                        </td>
                        <td>
                            {% if company.tier_change_pending %}
                                <span class="status-badge status-badge-pending">
                                    {% trans "Tier" %}: {{ company.get_requested_tier_display }}
                                </span>
                            {% endif %}

                            {% if company.featured_request_pending %}
                                <span class="status-badge status-badge-pending">
                                    {% trans "Featured" %}
                                    {% if company.requested_featured_duration %}
                                        ({{ company.get_requested_featured_duration_display }})
                                    {% endif %}
                                </span>
                            {% endif %}

                            {% if not company.tier_change_pending and not company.featured_request_pending %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>{{ company.created_at|date:"Y-m-d" }}</td>
                        <td>
                            <div class="table-actions d-flex justify-content-end">
                                <!-- Status Toggle -->
                                <form action="{% url 'superadmin:company_activate_toggle' company.pk %}" method="post" class="d-inline-block">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-deactivate{% if not company.is_active %} active{% endif %}" title="{% if company.is_active %}{% trans 'Deactivate' %}{% else %}{% trans 'Activate' %}{% endif %}">
                                        <i class="bi bi-{% if company.is_active %}x-circle{% else %}check-circle{% endif %}"></i>
                                    </button>
                                </form>

                                <!-- Tier Actions -->
                                {% if company.tier_change_pending %}
                                <div class="dropdown d-inline-block">
                                    <button class="btn btn-admin-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-arrow-up-circle"></i> {% trans "Tier Request" %}
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <form action="{% url 'superadmin:company_approve_tier' company.pk %}" method="post">
                                                {% csrf_token %}
                                                <button type="submit" class="dropdown-item text-success">
                                                    <i class="bi bi-check-circle"></i> {% trans "Approve" %}
                                                </button>
                                            </form>
                                        </li>
                                        <li>
                                            <form action="{% url 'superadmin:company_reject_tier' company.pk %}" method="post">
                                                {% csrf_token %}
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="bi bi-x-circle"></i> {% trans "Reject" %}
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                                {% endif %}

                                <!-- Featured Actions -->
                                {% if company.featured_request_pending %}
                                <div class="dropdown d-inline-block">
                                    <button class="btn btn-admin-info btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-star"></i> {% trans "Feature Request" %}
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <form action="{% url 'superadmin:company_approve_featured' company.pk %}" method="post">
                                                {% csrf_token %}
                                                <button type="submit" class="dropdown-item text-success">
                                                    <i class="bi bi-check-circle"></i> {% trans "Approve" %}
                                                </button>
                                            </form>
                                        </li>
                                        <li>
                                            <form action="{% url 'superadmin:company_reject_featured' company.pk %}" method="post">
                                                {% csrf_token %}
                                                <button type="submit" class="dropdown-item text-danger">
                                                    <i class="bi bi-x-circle"></i> {% trans "Reject" %}
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                                {% else %}
                                <form action="{% url 'superadmin:company_featured_toggle' company.pk %}" method="post" class="d-inline-block">
                                    {% csrf_token %}
                                    <button type="submit" class="btn btn-feature{% if company.is_featured %} active{% endif %}" title="{% if company.is_featured %}{% trans 'Unfeature' %}{% else %}{% trans 'Feature' %}{% endif %}">
                                        <i class="bi bi-star{% if company.is_featured %}-fill{% endif %}"></i>
                                    </button>
                                </form>
                                {% endif %}

                                <!-- More Actions -->
                                <div class="dropdown d-inline-block">
                                    <button class="btn btn-admin-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="bi bi-three-dots"></i> {% trans "More" %}
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <form action="{% url 'superadmin:company_impersonate' company.owner.pk %}" method="post">
                                                {% csrf_token %}
                                                <button type="submit" class="dropdown-item">
                                                    <i class="bi bi-person-badge"></i> {% trans "Impersonate Owner" %}
                                                </button>
                                            </form>
                                        </li>
                                        {% if company.tier != company.TIER_STANDARD %}
                                        <li>
                                            <form action="{% url 'superadmin:company_set_standard_tier' company.pk %}" method="post">
                                                {% csrf_token %}
                                                <button type="submit" class="dropdown-item">
                                                    <i class="bi bi-arrow-down-circle"></i> {% trans "Set Standard Tier" %}
                                                </button>
                                            </form>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Pagination -->
{% if is_paginated %}
<div class="superadmin-pagination">
    {% include "base/pagination.html" with page_obj=page_obj %}
</div>
{% endif %}

{% else %}
<div class="superadmin-card">
    <div class="card-body text-center py-5">
        <div class="empty-state">
            <i class="bi bi-building fs-1 mb-3 d-block" style="color: rgba(67, 97, 238, 0.3);"></i>
            <h5 class="mb-3">{% trans "No companies found" %}</h5>
            <p class="text-muted mb-4">{% trans "No companies match your search criteria." %}</p>
            <a href="{% url 'superadmin:company_list' %}" class="btn btn-admin-primary">
                <i class="bi bi-arrow-repeat me-2"></i> {% trans "Clear Filters" %}
            </a>
        </div>
    </div>
</div>
{% endif %}

{% endblock superadmin_content %}

{% block page_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality
    const searchInput = document.querySelector('.input-group input');
    const entityTypeFilter = document.getElementById('entityTypeFilter');
    const statusFilter = document.getElementById('statusFilter');
    const featuredFilter = document.getElementById('featuredFilter');
    const clearFiltersBtn = document.getElementById('clearFilters');
    const applyFiltersBtn = document.getElementById('applyFilters');
    const searchBtn = document.getElementById('searchBtn');

    // Apply filters
    const applyFilters = function() {
        const params = new URLSearchParams();

        if (searchInput.value) {
            params.append('q', searchInput.value);
        }

        if (entityTypeFilter.selectedIndex > 0) {
            params.append('entity_type', entityTypeFilter.value);
        }

        if (statusFilter.selectedIndex > 0) {
            params.append('status', statusFilter.value);
        }

        if (featuredFilter.selectedIndex > 0) {
            params.append('featured', featuredFilter.value);
        }

        window.location.href = `${window.location.pathname}?${params.toString()}`;
    };

    applyFiltersBtn.addEventListener('click', applyFilters);
    searchBtn.addEventListener('click', applyFilters);

    // Search on Enter key
    searchInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            applyFilters();
        }
    });

    // Clear filters
    clearFiltersBtn.addEventListener('click', function() {
        window.location.href = window.location.pathname;
    });

    // Initialize editable fields
    const editableFields = document.querySelectorAll('.editable-field');

    editableFields.forEach(field => {
        const displaySpan = field.querySelector('span');
        const inputField = field.querySelector('input');
        const companyPk = field.closest('tr').dataset.companyPk;
        const fieldName = field.dataset.fieldName;

        if (!displaySpan || !inputField) return;

        // Store original value for potential reversion
        field.dataset.originalValue = displaySpan.textContent;

        // Double-click to edit
        field.addEventListener('dblclick', () => {
            field.classList.add('editing');
            inputField.focus();
        });

        // Function to save changes
        const saveChanges = () => {
            const newValue = inputField.value; // YYYY-MM-DD format or empty string

            // Update display (optimistically)
            field.classList.remove('editing');
            displaySpan.textContent = newValue ? newValue : 'N/A';

            // Prepare data for AJAX request
            const data = {
                field: fieldName,
                value: newValue
            };

            // Get CSRF token using the helper function from layout.html
            const csrfToken = getCsrfToken();

            // Send Fetch request
            fetch(`/superadmin/companies/${companyPk}/update-expiry/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(result => {
                if (result.status === 'success') {
                    // Show success indicator
                    field.classList.add('bg-success-subtle');
                    setTimeout(() => field.classList.remove('bg-success-subtle'), 1500);

                    // Show toast notification
                    const toast = document.createElement('div');
                    toast.className = 'position-fixed bottom-0 end-0 p-3';
                    toast.style.zIndex = '5';
                    toast.innerHTML = `
                        <div class="toast align-items-center text-white bg-success border-0" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="d-flex">
                                <div class="toast-body">
                                    <i class="bi bi-check-circle me-2"></i> ${fieldName} updated successfully
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    const toastEl = new bootstrap.Toast(toast.querySelector('.toast'));
                    toastEl.show();

                    // Remove toast after it's hidden
                    toast.addEventListener('hidden.bs.toast', () => {
                        document.body.removeChild(toast);
                    });
                } else {
                    console.error(`Error updating ${fieldName}:`, result.message);

                    // Revert to original value
                    displaySpan.textContent = field.dataset.originalValue;

                    // Show error toast
                    const toast = document.createElement('div');
                    toast.className = 'position-fixed bottom-0 end-0 p-3';
                    toast.style.zIndex = '5';
                    toast.innerHTML = `
                        <div class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
                            <div class="d-flex">
                                <div class="toast-body">
                                    <i class="bi bi-exclamation-triangle me-2"></i> Error: ${result.message}
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                        </div>
                    `;
                    document.body.appendChild(toast);
                    const toastEl = new bootstrap.Toast(toast.querySelector('.toast'));
                    toastEl.show();

                    // Remove toast after it's hidden
                    toast.addEventListener('hidden.bs.toast', () => {
                        document.body.removeChild(toast);
                    });
                }
            })
            .catch(error => {
                console.error('Network error:', error);

                // Revert to original value
                displaySpan.textContent = field.dataset.originalValue;

                // Show error notification
                const toast = document.createElement('div');
                toast.className = 'position-fixed bottom-0 end-0 p-3';
                toast.style.zIndex = '5';
                toast.innerHTML = `
                    <div class="toast align-items-center text-white bg-danger border-0" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="bi bi-wifi-off me-2"></i> Network error occurred
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                `;
                document.body.appendChild(toast);
                const toastEl = new bootstrap.Toast(toast.querySelector('.toast'));
                toastEl.show();

                // Remove toast after it's hidden
                toast.addEventListener('hidden.bs.toast', () => {
                    document.body.removeChild(toast);
                });
            });
        };

        // Save on Enter key press
        inputField.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveChanges();
            } else if (e.key === 'Escape') {
                // Cancel editing
                field.classList.remove('editing');
                inputField.value = inputField.defaultValue || '';
            }
        });

        // Save on blur
        inputField.addEventListener('blur', saveChanges);
    });
});
</script>
{% endblock %}

{% block extra_js %}
{{ block.super }}
{% endblock %}
