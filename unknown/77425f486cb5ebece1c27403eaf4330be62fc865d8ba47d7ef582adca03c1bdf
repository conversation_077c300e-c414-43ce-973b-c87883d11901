/**
 * Enhanced Dark Mode CSS
 * A more professional and eye-friendly dark theme with better contrast
 */

/* Dark Mode Variables */
[data-theme="dark"] {
  /* Base Colors - Deep, rich dark tones for a more dramatic look */
  --dark-bg-primary: #121212; /* Main background - near black for dramatic effect */
  --dark-bg-secondary: #1e1e1e; /* Secondary background - very dark gray */
  --dark-bg-tertiary: #252525; /* Tertiary background - for cards, containers */
  --dark-bg-elevated: #2c2c2c; /* Elevated elements - for hover states, active items */

  /* Text Colors - High contrast for better readability */
  --dark-text-primary: #ffffff; /* Primary text - pure white for maximum contrast */
  --dark-text-secondary: #cccccc; /* Secondary text - light gray but still very readable */
  --dark-text-muted: #999999; /* Muted text - for less important elements */

  /* Accent Colors - Vibrant and eye-catching */
  --dark-accent-primary: #0066ff; /* Primary accent - vibrant blue */
  --dark-accent-secondary: #00cccc; /* Secondary accent - bright teal */
  --dark-accent-tertiary: #9933ff; /* Tertiary accent - bright purple */

  /* UI Elements */
  --dark-border: #333333; /* Border color - darker for more contrast */
  --dark-shadow: rgba(0, 0, 0, 0.5); /* Shadow - more pronounced for depth */
  --dark-shadow-light: rgba(0, 0, 0, 0.3); /* Lighter shadow but still visible */

  /* Message Colors */
  --dark-user-message-bg: #0055cc; /* User message - deeper vibrant blue */
  --dark-user-message-text: #ffffff; /* User message text - white */
  --dark-assistant-message-bg: #2a2a2a; /* Assistant message - dark but distinct */
  --dark-assistant-message-text: #ffffff; /* Assistant message text - white for better contrast */

  /* Status Colors */
  --dark-success: #00cc66; /* Success - bright green */
  --dark-warning: #ffcc00; /* Warning - bright yellow */
  --dark-danger: #ff3333; /* Danger - bright red */
  --dark-info: #0099ff; /* Info - bright blue */

  /* Override existing variables */
  --ef-background: var(--dark-bg-primary);
  --ef-background-alt: var(--dark-bg-secondary);
  --ef-surface: var(--dark-bg-tertiary);
  --ef-surface-alt: var(--dark-bg-elevated);

  --ef-text-primary: var(--dark-text-primary);
  --ef-text-secondary: var(--dark-text-secondary);
  --ef-text-muted: var(--dark-text-muted);

  --ef-primary: var(--dark-accent-primary);
  --ef-primary-light: var(--dark-bg-elevated);
  --ef-primary-dark: var(--dark-accent-secondary);

  --ef-border: var(--dark-border);
  --ef-shadow: var(--dark-shadow-light);
  --ef-shadow-stronger: var(--dark-shadow);

  --ef-card-bg: var(--dark-bg-tertiary);
  --ef-card-border: var(--dark-border);

  --ef-sidebar-bg: var(--dark-bg-secondary);
  --ef-sidebar-border: var(--dark-border);

  --ef-user-message-bg: var(--dark-user-message-bg);
  --ef-user-message-text: var(--dark-user-message-text);
  --ef-assistant-message-bg: var(--dark-assistant-message-bg);
  --ef-assistant-message-text: var(--dark-assistant-message-text);

  /* Bootstrap overrides */
  --bs-body-bg: var(--dark-bg-primary);
  --bs-body-color: var(--dark-text-primary);
  --bs-primary: var(--dark-accent-primary);
  --bs-secondary: var(--dark-bg-elevated);
  --bs-success: var(--dark-success);
  --bs-info: var(--dark-info);
  --bs-warning: var(--dark-warning);
  --bs-danger: var(--dark-danger);
  --bs-light: var(--dark-bg-secondary);
  --bs-dark: var(--dark-bg-primary);
  --bs-primary-rgb: 74, 125, 255;
  --bs-secondary-rgb: 50, 59, 78;
  --bs-border-color: var(--dark-border);
}

/* Apply dark mode styles to core elements */
[data-theme="dark"] body {
  background-color: var(--dark-bg-primary);
  background: var(--dark-bg-primary);
  color: var(--dark-text-primary);
}

/* Navbar styling - more dramatic with gradient */
[data-theme="dark"] .navbar {
  background: linear-gradient(to right, #121212, #1a1a1a, #121212);
  border-bottom: 1px solid #333333;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .navbar-light .navbar-brand,
[data-theme="dark"] .navbar-light .navbar-nav .nav-link {
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

[data-theme="dark"] .navbar-light .navbar-brand {
  font-weight: 600;
}

[data-theme="dark"] .navbar-light .navbar-nav .nav-link:hover,
[data-theme="dark"] .navbar-light .navbar-nav .nav-link:focus {
  color: var(--dark-accent-primary);
  text-shadow: 0 0 8px rgba(0, 102, 255, 0.5);
}

[data-theme="dark"] .navbar-toggler {
  border-color: #333333;
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .navbar-toggler-icon {
  filter: invert(1) brightness(1.5);
}

/* Sidebar styling - with gradient and glow effect */
[data-theme="dark"] .sidebar {
  background: linear-gradient(to bottom, #1a1a1a, #121212);
  border-right: 1px solid #333333;
  box-shadow: inset -5px 0 15px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .sidebar .nav-link {
  color: #cccccc;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  margin-bottom: 2px;
}

[data-theme="dark"] .sidebar .nav-link:hover {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.05);
  border-left: 3px solid var(--dark-accent-primary);
  padding-left: calc(1rem - 3px);
}

[data-theme="dark"] .sidebar .nav-link.active {
  color: #ffffff;
  background: linear-gradient(to right, rgba(0, 102, 255, 0.2), transparent);
  border-left: 3px solid var(--dark-accent-primary);
  padding-left: calc(1rem - 3px);
  text-shadow: 0 0 5px rgba(0, 102, 255, 0.5);
}

/* Card styling - with subtle gradient and enhanced shadows */
[data-theme="dark"] .card {
  background: linear-gradient(145deg, #1e1e1e, #252525);
  border: 1px solid #333333;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  overflow: hidden;
}

[data-theme="dark"] .card-header {
  background: linear-gradient(to right, #1a1a1a, #222222);
  border-bottom: 1px solid #333333;
  padding: 1rem 1.25rem;
}

[data-theme="dark"] .card-footer {
  background: linear-gradient(to right, #1a1a1a, #222222);
  border-top: 1px solid #333333;
  padding: 1rem 1.25rem;
}

[data-theme="dark"] .card-title {
  color: #ffffff;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Button styling - with gradients, glows and modern effects */
[data-theme="dark"] .btn-primary {
  background: linear-gradient(to bottom, #0077ff, #0055cc);
  border: none;
  box-shadow: 0 4px 10px rgba(0, 102, 255, 0.3), 0 0 0 1px rgba(0, 102, 255, 0.5);
  color: white;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  border-radius: 6px;
}

[data-theme="dark"] .btn-primary:hover {
  background: linear-gradient(to bottom, #0088ff, #0066dd);
  box-shadow: 0 6px 15px rgba(0, 102, 255, 0.4), 0 0 0 1px rgba(0, 102, 255, 0.6);
  transform: translateY(-1px);
}

[data-theme="dark"] .btn-primary:active {
  background: linear-gradient(to bottom, #0055cc, #0044aa);
  box-shadow: 0 2px 5px rgba(0, 102, 255, 0.3), 0 0 0 1px rgba(0, 102, 255, 0.6);
  transform: translateY(1px);
}

[data-theme="dark"] .btn-outline-primary {
  background-color: transparent;
  color: #0077ff;
  border: 1px solid #0077ff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  text-shadow: 0 0 5px rgba(0, 102, 255, 0.3);
  transition: all 0.3s ease;
  border-radius: 6px;
}

[data-theme="dark"] .btn-outline-primary:hover {
  background-color: rgba(0, 102, 255, 0.1);
  border-color: #0088ff;
  box-shadow: 0 4px 10px rgba(0, 102, 255, 0.2), 0 0 0 1px rgba(0, 102, 255, 0.5);
  color: #0088ff;
  transform: translateY(-1px);
}

[data-theme="dark"] .btn-outline-primary:active {
  background-color: rgba(0, 102, 255, 0.2);
  box-shadow: 0 2px 5px rgba(0, 102, 255, 0.2);
  transform: translateY(1px);
}

[data-theme="dark"] .btn-secondary {
  background: linear-gradient(to bottom, #333333, #222222);
  border: none;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
  border-radius: 6px;
}

[data-theme="dark"] .btn-secondary:hover {
  background: linear-gradient(to bottom, #3a3a3a, #2a2a2a);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.15);
  transform: translateY(-1px);
}

[data-theme="dark"] .btn-secondary:active {
  background: linear-gradient(to bottom, #222222, #1a1a1a);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
  transform: translateY(1px);
}

/* Form controls - with subtle glow effects and modern styling */
[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
  background-color: #1a1a1a;
  border: 1px solid #333333;
  color: #ffffff;
  border-radius: 6px;
  padding: 0.6rem 0.75rem;
  transition: all 0.3s ease;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
  background-color: #1a1a1a;
  border-color: #0077ff;
  box-shadow: 0 0 0 3px rgba(0, 119, 255, 0.25), inset 0 1px 3px rgba(0, 0, 0, 0.2);
  color: #ffffff;
  outline: none;
}

[data-theme="dark"] .form-control:hover,
[data-theme="dark"] .form-select:hover {
  border-color: #444444;
}

[data-theme="dark"] .form-control::placeholder {
  color: #777777;
  opacity: 1;
}

/* Input groups */
[data-theme="dark"] .input-group-text {
  background-color: #252525;
  border-color: #333333;
  color: #cccccc;
}

/* Table styling */
[data-theme="dark"] .table {
  color: var(--dark-text-primary);
  border-color: var(--dark-border);
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) > * {
  background-color: rgba(255, 255, 255, 0.05);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .table-hover > tbody > tr:hover > * {
  background-color: rgba(255, 255, 255, 0.075);
  color: var(--dark-text-primary);
}

/* List group styling */
[data-theme="dark"] .list-group-item {
  background-color: var(--dark-bg-tertiary);
  border-color: var(--dark-border);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .list-group-item.active {
  background-color: var(--dark-accent-primary);
  border-color: var(--dark-accent-primary);
}

[data-theme="dark"] .list-group-item-action:hover {
  background-color: var(--dark-bg-elevated);
  color: var(--dark-text-primary);
}

/* Modal styling */
[data-theme="dark"] .modal-content {
  background-color: var(--dark-bg-tertiary);
  border: 1px solid var(--dark-border);
}

[data-theme="dark"] .modal-header,
[data-theme="dark"] .modal-footer {
  border-color: var(--dark-border);
}

[data-theme="dark"] .btn-close {
  filter: invert(1) grayscale(100%) brightness(200%);
}

/* Dropdown styling */
[data-theme="dark"] .dropdown-menu {
  background-color: var(--dark-bg-tertiary);
  border: 1px solid var(--dark-border);
}

[data-theme="dark"] .dropdown-item {
  color: var(--dark-text-primary);
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
  background-color: var(--dark-bg-elevated);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .dropdown-divider {
  border-color: var(--dark-border);
}

/* Alert styling */
[data-theme="dark"] .alert {
  border: 1px solid transparent;
}

[data-theme="dark"] .alert-primary {
  background-color: rgba(74, 125, 255, 0.15);
  border-color: rgba(74, 125, 255, 0.4);
  color: #a3bfff;
}

[data-theme="dark"] .alert-secondary {
  background-color: rgba(160, 174, 192, 0.15);
  border-color: rgba(160, 174, 192, 0.4);
  color: #a0aec0;
}

[data-theme="dark"] .alert-success {
  background-color: rgba(56, 161, 105, 0.15);
  border-color: rgba(56, 161, 105, 0.4);
  color: #68d391;
}

[data-theme="dark"] .alert-danger {
  background-color: rgba(229, 62, 62, 0.15);
  border-color: rgba(229, 62, 62, 0.4);
  color: #fc8181;
}

[data-theme="dark"] .alert-warning {
  background-color: rgba(214, 158, 46, 0.15);
  border-color: rgba(214, 158, 46, 0.4);
  color: #fbd38d;
}

[data-theme="dark"] .alert-info {
  background-color: rgba(49, 130, 206, 0.15);
  border-color: rgba(49, 130, 206, 0.4);
  color: #63b3ed;
}

/* Badge styling */
[data-theme="dark"] .badge.bg-primary {
  background-color: var(--dark-accent-primary) !important;
}

[data-theme="dark"] .badge.bg-secondary {
  background-color: var(--dark-bg-elevated) !important;
  color: var(--dark-text-primary) !important;
}

[data-theme="dark"] .badge.bg-success {
  background-color: var(--dark-success) !important;
}

[data-theme="dark"] .badge.bg-danger {
  background-color: var(--dark-danger) !important;
}

[data-theme="dark"] .badge.bg-warning {
  background-color: var(--dark-warning) !important;
  color: var(--dark-bg-primary) !important;
}

[data-theme="dark"] .badge.bg-info {
  background-color: var(--dark-info) !important;
}

[data-theme="dark"] .badge.bg-light {
  background-color: var(--dark-bg-secondary) !important;
  color: var(--dark-text-primary) !important;
}

[data-theme="dark"] .badge.bg-dark {
  background-color: var(--dark-bg-primary) !important;
  color: var(--dark-text-primary) !important;
}

/* Progress bar styling */
[data-theme="dark"] .progress {
  background-color: var(--dark-bg-secondary);
}

/* Pagination styling */
[data-theme="dark"] .pagination {
  --bs-pagination-bg: var(--dark-bg-tertiary);
  --bs-pagination-border-color: var(--dark-border);
  --bs-pagination-color: var(--dark-text-primary);
  --bs-pagination-hover-bg: var(--dark-bg-elevated);
  --bs-pagination-hover-color: var(--dark-text-primary);
  --bs-pagination-focus-bg: var(--dark-bg-elevated);
  --bs-pagination-focus-color: var(--dark-text-primary);
  --bs-pagination-active-bg: var(--dark-accent-primary);
  --bs-pagination-active-border-color: var(--dark-accent-primary);
}

/* Tooltip styling */
[data-theme="dark"] .tooltip .tooltip-inner {
  background-color: var(--dark-bg-elevated);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .tooltip .tooltip-arrow::before {
  border-top-color: var(--dark-bg-elevated);
}

/* Popover styling */
[data-theme="dark"] .popover {
  background-color: var(--dark-bg-tertiary);
  border: 1px solid var(--dark-border);
}

[data-theme="dark"] .popover-header {
  background-color: var(--dark-bg-secondary);
  border-bottom: 1px solid var(--dark-border);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .popover-body {
  color: var(--dark-text-primary);
}

/* Toast styling */
[data-theme="dark"] .toast {
  background-color: var(--dark-bg-tertiary);
  border: 1px solid var(--dark-border);
}

[data-theme="dark"] .toast-header {
  background-color: var(--dark-bg-secondary);
  border-bottom: 1px solid var(--dark-border);
  color: var(--dark-text-primary);
}

/* Scrollbar styling */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--dark-bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background-color: var(--dark-bg-elevated);
  border-radius: 6px;
  border: 3px solid var(--dark-bg-secondary);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background-color: var(--dark-border);
}

/* Chat-specific styling */
[data-theme="dark"] .chat-container,
[data-theme="dark"] .general-chat-container {
  background-color: var(--dark-bg-tertiary) !important;
  background: var(--dark-bg-tertiary) !important;
  border: 1px solid var(--dark-border) !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25) !important;
}

[data-theme="dark"] .chat-box,
[data-theme="dark"] .general-chat-box,
[data-theme="dark"] #chat-box {
  background-color: var(--dark-bg-secondary) !important;
  background: var(--dark-bg-secondary) !important;
  border: 1px solid var(--dark-border) !important;
}

[data-theme="dark"] .user-message .message-content {
  background-color: var(--dark-user-message-bg) !important;
  background: var(--dark-user-message-bg) !important;
  color: var(--dark-user-message-text) !important;
  border: none !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .assistant-message .message-content {
  background-color: var(--dark-assistant-message-bg) !important;
  background: var(--dark-assistant-message-bg) !important;
  color: var(--dark-assistant-message-text) !important;
  border: 1px solid var(--dark-border) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1) !important;
}

[data-theme="dark"] .nav-content-bubble .message-content {
  background-color: var(--dark-bg-tertiary) !important;
  background: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
  border: 1px solid var(--dark-border) !important;
  border-left: 3px solid var(--dark-accent-primary) !important;
}

/* Input field styling */
[data-theme="dark"] #chat-input {
  background-color: var(--dark-bg-secondary);
  color: var(--dark-text-primary);
  border: 1px solid var(--dark-border);
}

[data-theme="dark"] #chat-input:focus {
  border-color: var(--dark-accent-primary);
  box-shadow: 0 0 0 0.25rem rgba(74, 125, 255, 0.25);
}

/* Send button styling */
[data-theme="dark"] #send-button {
  background-color: var(--dark-accent-primary);
  border-color: var(--dark-accent-primary);
}

[data-theme="dark"] #send-button:hover {
  background-color: #3a6ae0;
  border-color: #3a6ae0;
}

/* Theme toggle button styling */
[data-theme="dark"] .theme-toggle-btn {
  background-color: var(--dark-accent-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 0 3px rgba(74, 125, 255, 0.3);
}

[data-theme="dark"] .theme-toggle-btn:hover {
  background-color: #3a6ae0;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4), 0 0 0 4px rgba(74, 125, 255, 0.4);
}

/* TinyMCE editor styling */
[data-theme="dark"] .tox .tox-editor-container {
  background-color: var(--dark-bg-secondary);
}

[data-theme="dark"] .tox .tox-edit-area__iframe {
  background-color: var(--dark-bg-secondary);
}

[data-theme="dark"] .tox .tox-toolbar,
[data-theme="dark"] .tox .tox-toolbar__primary,
[data-theme="dark"] .tox .tox-toolbar__overflow {
  background-color: var(--dark-bg-tertiary);
  border-color: var(--dark-border);
}

[data-theme="dark"] .tox .tox-tbtn {
  color: var(--dark-text-primary);
}

[data-theme="dark"] .tox .tox-tbtn:hover {
  background-color: var(--dark-bg-elevated);
}

[data-theme="dark"] .tox .tox-statusbar {
  background-color: var(--dark-bg-tertiary);
  border-color: var(--dark-border);
  color: var(--dark-text-secondary);
}

/* Glass-look styling for header buttons */
[data-theme="dark"] .glass-button {
  background: rgba(74, 125, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(74, 125, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .glass-button:hover {
  background: rgba(74, 125, 255, 0.3);
  border: 1px solid rgba(74, 125, 255, 0.4);
}

/* Enhanced header text styling */
[data-theme="dark"] .assistant-header h1,
[data-theme="dark"] .assistant-header h2,
[data-theme="dark"] .assistant-header h3 {
  color: var(--dark-text-primary);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Directory cards */
[data-theme="dark"] .directory-card {
  background-color: var(--dark-bg-tertiary);
  border: 1px solid var(--dark-border);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .directory-card:hover {
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
}

[data-theme="dark"] .directory-card .card-title {
  color: var(--dark-text-primary);
}

[data-theme="dark"] .directory-card .card-text {
  color: var(--dark-text-secondary);
}

/* Ensure all icons are visible and have transparent backgrounds */
[data-theme="dark"] .bi,
[data-theme="dark"] i.bi,
[data-theme="dark"] i.fa,
[data-theme="dark"] i.fas,
[data-theme="dark"] i.far,
[data-theme="dark"] i.fab,
[data-theme="dark"] button i,
[data-theme="dark"] .btn i {
  color: inherit;
  background-color: transparent !important;
  background: none !important;
  box-shadow: none !important;
}

/* Fix for any hardcoded colors in SVGs */
[data-theme="dark"] svg {
  filter: brightness(0.8) contrast(1.2);
}

/* Ensure links are visible */
[data-theme="dark"] a {
  color: var(--dark-accent-primary);
}

[data-theme="dark"] a:hover {
  color: #6a93ff;
}

/* Fix for any hardcoded background colors */
[data-theme="dark"] [style*="background-color: white"],
[data-theme="dark"] [style*="background-color: #fff"],
[data-theme="dark"] [style*="background-color: rgb(255, 255, 255)"],
[data-theme="dark"] [style*="background-color: rgba(255, 255, 255"] {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
}

/* Fix for any hardcoded text colors */
[data-theme="dark"] [style*="color: black"],
[data-theme="dark"] [style*="color: #000"],
[data-theme="dark"] [style*="color: rgb(0, 0, 0)"],
[data-theme="dark"] [style*="color: rgba(0, 0, 0"] {
  color: var(--dark-text-primary) !important;
}
