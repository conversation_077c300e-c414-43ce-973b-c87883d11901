{% extends 'base/layout.html' %}
{% load static %}
{% load account_tags %}
{% load permission_tags %}
{% load crispy_forms_tags %}

{% block title %}{{ active_company.name }} - Community Dashboard{% endblock %}

{% block extra_css %}
<link href="{% static 'css/facebook-style.css' %}" rel="stylesheet">
{{ context_form.media.css }}
{% endblock %}

{% block extra_head %}
<!-- Load TinyMCE word limit script -->
<script src="{% static 'js/tinymce-word-limit.js' %}"></script>
{% endblock %}

{% block body_class %}facebook-style{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Welcome Section -->
    <div class="row align-items-center mb-4">
        <div class="col d-flex align-items-center">
            {% if active_company.info.logo and active_company.info.logo.url %}
                <img src="{{ active_company.info.logo.url }}"
                     alt="{{ active_company.name }} Logo"
                     class="rounded-circle me-3"
                     style="width: 40px; height: 40px; object-fit: cover;">
            {% endif %}
            <div>
                <h1 class="h3 mb-0">Welcome to {{ active_company.name }}</h1>
                <p class="text-muted mb-0">
                    Community Dashboard
                </p>
            </div>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                {% check_perm user 'accounts.change_company_settings' company as can_edit_settings %}
                {% if can_edit_settings %}
                    <a href="{% url 'accounts:community_settings' company.id %}" class="btn btn-light">
                        <i class="bi bi-gear"></i> Settings
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Community Assistant Section -->
    <div class="row g-3 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center mb-4">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="bi bi-people-fill text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h4 mb-1">Your Community Assistant</h3>
                            <p class="text-muted mb-0">Help your community by contributing knowledge</p>
                        </div>
                        {% for assistant in community_assistants %}
                            <div class="ms-auto">
                                <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}" class="btn btn-success me-2">
                                    <i class="bi bi-chat-dots me-2"></i> Chat Now
                                </a>
                                <a href="{% url 'assistants:community_dashboard' company.id assistant.id %}" class="btn btn-outline-primary">
                                    <i class="bi bi-people me-2"></i> Social Dashboard
                                </a>
                            </div>
                        {% endfor %}
                    </div>

                    <div class="alert alert-info mb-4">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="bi bi-info-circle-fill fs-4"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="alert-heading">How it works</h5>
                                <p>Your community assistant learns from contributions made by community members. The more context you add, the smarter it becomes!</p>
                            </div>
                        </div>
                    </div>

                    <!-- Add New Context Section -->
                    <div class="card bg-light border-0 mb-4">
                        <div class="card-body p-4">
                            <h4 class="h5 mb-3">Add New Knowledge</h4>
                            {% for assistant in community_assistants %}
                                <form action="{% url 'assistants:add_context' company.id assistant.id %}" method="post" class="mb-3" enctype="multipart/form-data" id="knowledge-form">
                                    {% csrf_token %}
                                    {{ context_form|crispy }}
                                    <div class="word-counter mt-1 text-end">
                                        <small class="text-muted" id="word-count">0</small>
                                        <small class="text-muted"> / 500 words</small>
                                    </div>
                                    <div class="alert alert-info mt-3 mb-3">
                                        <i class="bi bi-info-circle me-2"></i>
                                        <small>Keep your contribution under 500 words. Shorter, focused knowledge is more effective.</small>
                                    </div>
                                    <div class="d-grid mt-3">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-2"></i> Add Knowledge
                                        </button>
                                    </div>
                                </form>
                            {% empty %}
                                <div class="alert alert-warning">
                                    <p class="mb-0">No community assistant available. Please contact support.</p>
                                </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Recent Contributions -->
                    <h4 class="h5 mb-3">Recent Contributions</h4>
                    <div class="list-group mb-4">
                        {% if recent_contexts %}
                            {% for context in recent_contexts %}
                                <div class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">{{ context.title }}</h5>
                                        <small class="text-muted">{{ context.created_at|timesince }} ago</small>
                                    </div>
                                    <div class="mb-1">{{ context.text_content|safe|truncatewords_html:20 }}</div>
                                    <small class="text-muted">Added by {{ context.created_by.get_full_name|default:context.created_by.username }}</small>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="list-group-item text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-journal-text h3 mb-2"></i>
                                    <p>No contributions yet. Be the first to add knowledge!</p>
                                </div>
                            </div>
                        {% endif %}
                    </div>

                    <!-- Flagged Questions -->
                    <h4 class="h5 mb-3">Questions Needing Better Answers</h4>
                    <div class="list-group">
                        {% if flagged_questions %}
                            {% for question in flagged_questions %}
                                <div class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1">{{ question.question_text|truncatechars:100 }}</h5>
                                        <small class="text-muted">{{ question.created_at|timesince }} ago</small>
                                    </div>
                                    <p class="mb-1">{{ question.reason|truncatechars:150 }}</p>
                                    <div class="mt-2">
                                        <a href="{% url 'assistants:flagged_question_detail' company.id community_assistants.0.id question.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-pencil me-1"></i> Add Knowledge
                                        </a>
                                    </div>
                                </div>
                            {% endfor %}
                            {% if community_assistants.0 %}
                                <div class="list-group-item bg-light text-center">
                                    <a href="{% url 'assistants:flagged_questions' company.id community_assistants.0.id %}" class="btn btn-link text-decoration-none">
                                        View All Flagged Questions
                                    </a>
                                </div>
                            {% endif %}
                        {% else %}
                            <div class="list-group-item text-center py-4">
                                <div class="text-muted">
                                    <i class="bi bi-flag h3 mb-2"></i>
                                    <p>No flagged questions at the moment.</p>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Community Stats -->
    <div class="row g-3">
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 p-3 rounded">
                                <i class="bi bi-people text-primary"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Community Members</h3>
                            <h4 class="h2 mb-0">{{ total_members }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 p-3 rounded">
                                <i class="bi bi-journal-text text-success"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Knowledge Contributions</h3>
                            <h4 class="h2 mb-0">{{ total_contexts }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card h-100 border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 p-3 rounded">
                                <i class="bi bi-chat-dots text-info"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h3 class="h6 text-muted mb-0">Assistant Interactions</h3>
                            <h4 class="h2 mb-0">{{ dashboard_analytics.total_assistant_interactions|default:"0" }}</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
{{ context_form.media.js }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[title]');
    tooltips.forEach(el => {
        new bootstrap.Tooltip(el);
    });

    // Initialize word count limit
    if (typeof initWordCountLimit === 'function') {
        initWordCountLimit('id_text_content', 'word-count');
    }

    // Add form submit handler to prevent submission if over limit
    const form = document.getElementById('knowledge-form');
    if (form) {
        form.addEventListener('submit', function(e) {
            if (typeof countTinyMCEWords === 'function') {
                const wordCount = countTinyMCEWords('id_text_content');
                if (wordCount > 500) {
                    e.preventDefault();
                    alert('Your content exceeds the 500 word limit. Please shorten your text to submit.');
                    return false;
                }
            }
        });
    }
});
</script>
{% endblock %}
