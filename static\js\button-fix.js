/**
 * Button Fix JavaScript
 * This script fixes issues with buttons not working in the superadmin interface
 * by using event delegation and ensuring proper event handling.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Button Fix: Initializing button fixes for superadmin');
    
    // Use event delegation for all button clicks
    document.body.addEventListener('click', function(event) {
        // Find the closest button or element with a button role
        const button = event.target.closest('button, [role="button"], .btn, .nav-button, a.btn-info, a.btn-primary, a.btn-secondary, a.btn-success, a.btn-warning, a.btn-danger');
        
        if (!button) return; // Not a button click
        
        console.log('Button Fix: Button clicked:', button.id || button.className);
        
        // If it's a link with href, let the browser handle it
        if (button.tagName === 'A' && button.hasAttribute('href')) {
            // Just ensure the link is followed
            const href = button.getAttribute('href');
            if (href && href !== '#' && !href.startsWith('javascript:')) {
                console.log('Button Fix: Following link:', href);
                // Let the default action happen
                return true;
            }
        }
    });
    
    // Fix z-index issues that might be making buttons unclickable
    fixZIndexIssues();
    
    // Ensure buttons are properly styled and visible
    fixButtonStyling();
    
    // Set up a MutationObserver to fix buttons that are added dynamically
    const observer = new MutationObserver(function(mutations) {
        fixZIndexIssues();
        fixButtonStyling();
    });
    
    // Start observing the document
    observer.observe(document.body, { 
        childList: true, 
        subtree: true,
        attributes: true,
        attributeFilter: ['style', 'class']
    });
    
    console.log('Button Fix: Initialization complete');
});

/**
 * Fix z-index issues that might be making buttons unclickable
 */
function fixZIndexIssues() {
    // Fix z-index for buttons that might be hidden behind other elements
    const buttons = document.querySelectorAll('button, .btn, a.btn-info, a.btn-primary, a.btn-secondary, a.btn-success, a.btn-warning, a.btn-danger');
    buttons.forEach(button => {
        // Ensure button has proper z-index
        button.style.position = button.style.position || 'relative';
        button.style.zIndex = '100';
    });
}

/**
 * Fix button styling to ensure they are properly visible and clickable
 */
function fixButtonStyling() {
    // Fix styling for all buttons
    const allButtons = document.querySelectorAll('button, .btn, a.btn-info, a.btn-primary, a.btn-secondary, a.btn-success, a.btn-warning, a.btn-danger');
    allButtons.forEach(button => {
        // Ensure buttons have proper pointer cursor
        button.style.cursor = 'pointer';
        
        // Ensure buttons have proper pointer events
        button.style.pointerEvents = 'auto';
        
        // Ensure buttons are not hidden
        button.style.visibility = 'visible';
        button.style.display = button.style.display || 'inline-block';
        button.style.opacity = '1';
    });
}
