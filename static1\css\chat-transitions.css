/**
 * Chat Transitions CSS
 * Ensures smooth transitions between light and dark mode for chat elements
 */

/* Add transitions to all message content elements */
.message-content,
.assistant-message .message-content,
.user-message .message-content,
.nav-content-bubble .message-content,
div.message.assistant-message.mb-3 span.message-content,
div.message.assistant-message span.message-content,
div.message.user-message.mb-3 span.message-content,
div.message.user-message span.message-content,
.message.assistant-message .message-content,
.message.user-message .message-content,
span.message-content.assistant-message,
span.message-content.user-message {
    transition: background 0.3s ease,
                background-color 0.3s ease,
                color 0.3s ease,
                border 0.3s ease,
                border-color 0.3s ease,
                box-shadow 0.3s ease !important;
}

/* Ensure transitions work for both theme modes */
[data-theme="dark"] .message-content,
[data-theme="dark"] .assistant-message .message-content,
[data-theme="dark"] .user-message .message-content,
[data-theme="dark"] .nav-content-bubble .message-content {
    transition: background 0.3s ease,
                background-color 0.3s ease,
                color 0.3s ease,
                border 0.3s ease,
                border-color 0.3s ease,
                box-shadow 0.3s ease !important;
}

[data-theme="light"] .message-content,
[data-theme="light"] .assistant-message .message-content,
[data-theme="light"] .user-message .message-content,
[data-theme="light"] .nav-content-bubble .message-content {
    transition: background 0.3s ease,
                background-color 0.3s ease,
                color 0.3s ease,
                border 0.3s ease,
                border-color 0.3s ease,
                box-shadow 0.3s ease !important;
}

/* Ensure transitions are disabled when the no-transition class is present */
.no-transition .message-content,
.no-transition .assistant-message .message-content,
.no-transition .user-message .message-content,
.no-transition .nav-content-bubble .message-content {
    transition: none !important;
}
