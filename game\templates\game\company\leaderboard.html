{% extends 'game/company/base.html' %}

{% block title %}Leaderboard - {{ company.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h2">Company Leaderboard</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                {{ leaderboard.time_period|title }} <span class="caret"></span>
            </button>
            <ul class="dropdown-menu dropdown-menu-end">
                <li><a class="dropdown-item {% if leaderboard.time_period == 'all_time' %}active{% endif %}" href="?period=all_time">All Time</a></li>
                <li><a class="dropdown-item {% if leaderboard.time_period == 'monthly' %}active{% endif %}" href="?period=monthly">Monthly</a></li>
                <li><a class="dropdown-item {% if leaderboard.time_period == 'weekly' %}active{% endif %}" href="?period=weekly">Weekly</a></li>
                <li><a class="dropdown-item {% if leaderboard.time_period == 'daily' %}active{% endif %}" href="?period=daily">Daily</a></li>
            </ul>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">Filters</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <!-- Game Filter -->
                {% if available_games %}
                <div class="col-md-6 mb-3">
                    <label class="form-label">Game</label>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{% url 'game:company_leaderboard' company_slug=company.slug %}{% if selected_team %}?team={{ selected_team }}{% endif %}"
                           class="btn btn-sm {% if not selected_game %}btn-primary{% else %}btn-outline-primary{% endif %}">
                            All Games
                        </a>
                        {% for game_id, game_name in available_games %}
                        <a href="{% url 'game:company_leaderboard' company_slug=company.slug %}?game={{ game_id }}{% if selected_team %}&team={{ selected_team }}{% endif %}"
                           class="btn btn-sm {% if selected_game == game_id %}btn-primary{% else %}btn-outline-primary{% endif %}">
                            {{ game_name }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

                <!-- Team Filter -->
                {% if available_teams %}
                <div class="col-md-6 mb-3">
                    <label class="form-label">Team</label>
                    <div class="d-flex flex-wrap gap-2">
                        <a href="{% url 'game:company_leaderboard' company_slug=company.slug %}{% if selected_game %}?game={{ selected_game }}{% endif %}"
                           class="btn btn-sm {% if not selected_team %}btn-primary{% else %}btn-outline-primary{% endif %}">
                            All Teams
                        </a>
                        {% for team_name in available_teams %}
                        <a href="{% url 'game:company_leaderboard' company_slug=company.slug %}?{% if selected_game %}game={{ selected_game }}&{% endif %}team={{ team_name }}"
                           class="btn btn-sm {% if selected_team == team_name %}btn-primary{% else %}btn-outline-primary{% endif %}">
                            {{ team_name }}
                        </a>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Leaderboard Card -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
                {{ leaderboard.name }}
                {% if selected_game %}
                    <span class="badge bg-primary ms-2">
                        {% for game_id, game_name in available_games %}
                            {% if game_id == selected_game %}{{ game_name }}{% endif %}
                        {% endfor %}
                    </span>
                {% endif %}
                {% if selected_team %}
                    <span class="badge bg-info ms-2">Team: {{ selected_team }}</span>
                {% endif %}
            </h5>
            {% if user_rank %}
            <span class="badge bg-success">Your Rank: #{{ user_rank }}</span>
            {% endif %}
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="5%">#</th>
                            <th width="40%">Player</th>
                            <th width="15%">Score</th>
                            <th width="25%">Highest Role</th>
                            <th width="15%">Last Updated</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for entry in page_obj %}
                        <tr {% if entry.user == request.user %}class="table-primary"{% endif %}>
                            <td>{{ page_obj.start_index|add:forloop.counter0 }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar me-3">
                                        {% if entry.user.profile.avatar %}
                                        <img src="{{ entry.user.profile.avatar.url }}" alt="Avatar" class="rounded-circle" width="40">
                                        {% else %}
                                        <div class="avatar-placeholder rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                            {{ entry.user.username|first|upper }}
                                        </div>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ entry.user.get_full_name|default:entry.user.username }}</h6>
                                        <small class="text-muted">{{ entry.user.email }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-primary rounded-pill">{{ entry.score }}</span>
                            </td>
                            <td>
                                {% if entry.highest_role %}
                                <span class="badge bg-info">{{ entry.highest_role|title }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <small class="text-muted">{{ entry.timestamp|date:"M d, Y" }}</small>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center py-4">
                                <p class="mb-0 text-muted">No entries yet. Start playing to appear on the leaderboard!</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
        <div class="card-footer">
            <nav aria-label="Leaderboard pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.period %}&period={{ request.GET.period }}{% endif %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.period %}&period={{ request.GET.period }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for i in page_obj.paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                        <li class="page-item"><a class="page-link" href="?page={{ i }}{% if request.GET.period %}&period={{ request.GET.period }}{% endif %}">{{ i }}</a></li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.period %}&period={{ request.GET.period }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.period %}&period={{ request.GET.period }}{% endif %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>

</div>
{% endblock %}
