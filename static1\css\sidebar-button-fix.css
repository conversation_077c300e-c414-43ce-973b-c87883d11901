/**
 * Sidebar Button Fix CSS
 * Fixes issues with buttons in the sidebar not being clickable
 */

/* Ensure navigation buttons are clickable */
#chat-sidebar .list-group-item,
#chat-sidebar .nav-button,
#chat-sidebar button,
#chat-sidebar .btn {
    position: relative !important;
    z-index: 1061 !important;
    cursor: pointer !important;
    pointer-events: auto !important;
    overflow: visible !important;
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
}

/* Add hover effect to navigation buttons */
#chat-sidebar .list-group-item:hover,
#chat-sidebar .nav-button:hover {
    background-color: rgba(0, 123, 255, 0.1) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.2s ease !important;
}

/* Active state for navigation buttons */
#chat-sidebar .list-group-item.active,
#chat-sidebar .nav-button.active {
    background-color: rgba(0, 123, 255, 0.2) !important;
    color: #0066ff !important;
    font-weight: 500 !important;
    border-left: 3px solid #0066ff !important;
}

/* Ensure sidebar has proper z-index */
#chat-sidebar {
    z-index: 1060 !important;
    position: fixed !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* Ensure sidebar toggle button is clickable */
#sidebar-toggle-inside,
#test-sidebar-toggle,
#test-sidebar-toggle-mobile {
    position: relative !important;
    z-index: 1062 !important;
    cursor: pointer !important;
    pointer-events: auto !important;
}

/* Dark mode specific styles */
[data-theme="dark"] #chat-sidebar .list-group-item:hover,
[data-theme="dark"] #chat-sidebar .nav-button:hover {
    background-color: rgba(0, 123, 255, 0.15) !important;
}

[data-theme="dark"] #chat-sidebar .list-group-item.active,
[data-theme="dark"] #chat-sidebar .nav-button.active {
    background-color: rgba(0, 123, 255, 0.25) !important;
    color: #0088ff !important;
    border-left: 3px solid #0088ff !important;
}

/* Ensure sidebar overlay is properly positioned */
#sidebar-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 1059 !important; /* Just below sidebar */
    display: none !important;
    opacity: 0 !important;
    transition: opacity 0.3s ease !important;
    pointer-events: none !important;
}

#sidebar-overlay.active {
    display: block !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}
