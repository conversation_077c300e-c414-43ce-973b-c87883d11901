# Role and Task System

This document explains the role and task system in the Corporate Prompt Master game, detailing how roles are defined, tasks are assigned, and progression is managed.

## Role Hierarchy

The game features a comprehensive corporate hierarchy with multiple departments and levels:

### Executive Level
- Board/Shareholders
- CEO
- COO

### Vice President Level
- VP of Marketing
- VP of Operations
- VP of Finance
- HR Director

### Manager Level
- Sales Manager
- Advertising/Research Manager
- Service Manager
- Production Manager
- Facilities Manager
- Accounts Receivable Manager
- Accounts Payable Manager
- HR Manager

### Associate Level
- Junior Assistant
- Sales Associate
- Marketing Associate
- Service Associate
- Production Associate
- Facilities Associate
- Accounts Receivable Associate
- Accounts Payable Associate
- HR Coordinator

### Entry Level
- Applicant (starting role)

## Role Definitions

Roles are defined in the `ROLE_PROGRESSION` dictionary in `context_aware_game.py`:

```python
ROLE_PROGRESSION = {
    "applicant": {
        "display_name": "Applicant",
        "next_role": "junior_assistant",
        "challenges_required": 1,
        "promotion_message": "Congratulations! You've been hired as a Junior Assistant!"
    },
    "junior_assistant": {
        "display_name": "Junior Assistant",
        "next_role": "sales_associate",
        "challenges_required": 3,
        "promotion_message": "Congratulations! You've been promoted to Sales Associate!"
    },
    # Additional roles...
}
```

Each role definition includes:
- `display_name`: The formatted name shown in the UI
- `next_role`: The role the player will advance to upon completion
- `challenges_required`: Number of tasks that must be completed to advance
- `promotion_message`: The message displayed when the player is promoted

## Task System

Tasks are defined in `role_tasks.py` and `all_role_tasks.py`. Each task is associated with a specific role and manager.

### Task Structure

```python
{
    "id": "cover_letter",
    "manager": "hr",
    "description": "Write a cover letter applying for the Junior Assistant position...",
    "response_template": "# Cover Letter\n\n[Your Name]\n[Your Address]...",
    "feedback": {
        "good": "Excellent cover letter! Your qualifications are clearly presented...",
        "okay": "Good effort on your cover letter. Consider highlighting...",
        "bad": "Your cover letter needs significant improvement..."
    }
}
```

Each task includes:
- `id`: A unique identifier for the task
- `manager`: The manager responsible for the task
- `description`: A detailed description of what the player needs to do
- `response_template`: A template to guide the player's response
- `feedback`: Feedback templates for different performance levels

### Task Assignment

Tasks are assigned based on the player's current role:

```python
def get_tasks_for_role(role, tasks):
    """Get the tasks for a specific role."""
    role_tasks = {
        "applicant": [
            # Applicant role has 1 task (cover letter)
            tasks[0]
        ],
        "junior_assistant": [
            # Junior Assistant role has 3 tasks
            tasks[1], tasks[2], tasks[3]
        ],
        # Additional role tasks...
    }
    
    # Return tasks for the requested role
    if role in role_tasks:
        return role_tasks[role]
    else:
        # Create generic tasks for roles not explicitly defined
        # ...
```

For roles not explicitly defined, the system generates generic tasks:

```python
# Create 3 generic tasks for this role
return [
    {
        "id": f"task_{i+1}_for_{role}",
        "manager": manager,
        "description": f"Complete task {i+1} for the {role_display} role...",
        "response_template": f"A comprehensive response addressing all requirements...",
        "feedback": {
            "good": f"Excellent work on this {role_display} task!...",
            "okay": f"Good effort on this {role_display} task...",
            "bad": f"This response for the {role_display} task needs improvement..."
        }
    } for i in range(3)
]
```

## Manager System

Each role is associated with specific managers who assign tasks and provide feedback:

```python
def get_manager_for_role(role):
    """Determine the appropriate manager for a role."""
    if role in ["junior_assistant"]:
        return "hr"
    elif role in ["sales_associate", "marketing_associate", "sales_manager", "advertising_manager"]:
        return "vp_marketing"
    elif role in ["service_associate", "production_associate", "facilities_associate",
                 "service_manager", "production_manager", "facilities_manager"]:
        return "vp_operations"
    # Additional manager assignments...
```

The manager mapping ensures that tasks are assigned by appropriate supervisors based on the corporate hierarchy.

## Progression System

The progression system is implemented in `game_state_manager.py`:

```python
def check_for_promotion(game_state, role_progression):
    """Check if the player should be promoted to the next role."""
    current_role = game_state.get("current_role")
    role_info = role_progression.get(current_role, {})
    challenges_required = role_info.get("challenges_required", 3)
    
    # Check if player has completed enough challenges
    if game_state.get("role_challenges_completed", 0) >= challenges_required:
        next_role = role_info.get("next_role")
        promotion_message = role_info.get("promotion_message", 
                                         f"Congratulations! You've been promoted to {next_role}!")
        
        # Add current role to completed roles
        if current_role not in game_state.get("completed_roles", []):
            if "completed_roles" not in game_state:
                game_state["completed_roles"] = []
            game_state["completed_roles"].append(current_role)
        
        # Update game state for new role
        game_state["current_role"] = next_role
        game_state["role_challenges_completed"] = 0
        
        # Determine the new manager based on the role
        game_state["current_manager"] = get_manager_for_role(next_role)
        
        # Return promotion status and message
        return True, promotion_message
    
    return False, ""
```

The promotion process:
1. Checks if the player has completed the required number of tasks
2. Updates the current role to the next role in the progression
3. Resets the role-specific challenge counter
4. Updates the current manager based on the new role
5. Adds the previous role to the completed roles list
6. Returns a promotion status and message

## Task Completion

When a task is completed, the game updates the role progression state:

```python
def process_task_completion(game_state, evaluation_results, points_earned):
    """Process the completion of a task."""
    # Update performance score
    game_state["performance_score"] += points_earned
    
    # Update challenges completed
    game_state["challenges_completed"] += 1
    game_state["role_challenges_completed"] += 1
    
    # Update role average score
    update_role_average_score(game_state, evaluation_results["overall_score"])
    
    # Update mastery level
    game_state["mastery_level"] = get_mastery_level(game_state.get("role_average_score", 0))
    
    # Additional processing...
```

## Visual Representation

The role progression is visually represented in two ways:

### 1. Organization Chart

The organization chart shows the entire corporate hierarchy with the player's current position highlighted:

```python
def generate_org_chart_html(current_role, completed_roles=None):
    """Generate HTML for the organization chart."""
    if completed_roles is None:
        completed_roles = []
    
    # Define the organizational structure for the chart
    org_structure = [
        # Executive Level
        {
            "level": "Executive",
            "roles": ["shareholders", "ceo", "coo"]
        },
        # VP Level
        {
            "level": "Vice Presidents",
            "roles": ["vp_marketing", "vp_operations", "vp_finance", "hr_director"]
        },
        # Additional levels...
    ]
    
    # Build HTML with appropriate styling for each role
    # ...
```

### 2. Role Progression Display

The role progression display shows the player's advancement path with completed, current, and future roles:

```python
def generate_role_progression_html(current_role, completed_roles=None):
    """Generate HTML for the role progression visualization."""
    if completed_roles is None:
        completed_roles = []
    
    # Define the progression path
    progression_path = [
        "applicant",
        "junior_assistant",
        "sales_associate",
        "marketing_associate",
        # Additional roles...
    ]
    
    # Build HTML with appropriate styling for each role
    # ...
```

## Task Definitions

Tasks are defined in JSON files in the `roles` directory. Each role has its own JSON file with task definitions:

```json
{
  "tasks": [
    {
      "id": "onboarding_checklist",
      "manager": "hr",
      "description": "Create a comprehensive onboarding checklist for new employees...",
      "response_template": "# New Employee Onboarding Checklist\n\n## Before First Day...",
      "feedback": {
        "good": "Excellent onboarding checklist! You've covered all the essential elements...",
        "okay": "Good effort on the onboarding checklist. Consider adding more details...",
        "bad": "Your onboarding checklist needs significant improvement..."
      }
    },
    // Additional tasks...
  ]
}
```

These task definitions are loaded and processed by the `all_role_tasks.py` module to create a comprehensive task dictionary for the game.

This role and task system creates a compelling game loop that motivates players to complete tasks and advance through the corporate hierarchy, simulating a career path from entry-level applicant to executive positions.
