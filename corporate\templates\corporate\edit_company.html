{% extends 'corporate/base.html' %}

{% block title %}Edit Company - {{ company.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <h2 class="mb-3">Edit Company Details</h2>
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>{{ company.name }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}

                        <div class="row">
                            <!-- Company Logo -->
                            <div class="col-md-4 text-center mb-4">
                                {% if company.logo %}
                                <img src="{{ company.logo.url }}" alt="{{ company.name }} Logo" class="img-fluid company-logo mb-3" style="max-height: 150px;">
                                {% else %}
                                <div class="company-logo-placeholder mb-3">
                                    <i class="fas fa-building fa-5x"></i>
                                </div>
                                {% endif %}

                                <div class="mb-3">
                                    <label for="{{ form.logo.id_for_label }}" class="form-label">Company Logo</label>
                                    {{ form.logo }}
                                    {% if form.logo.errors %}
                                    <div class="text-danger">
                                        {{ form.logo.errors }}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">Upload a new logo for your company (optional)</div>
                                </div>
                            </div>

                            <!-- Company Details -->
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">Company Name</label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                    <div class="text-danger">
                                        {{ form.name.errors }}
                                    </div>
                                    {% endif %}
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.description.id_for_label }}" class="form-label">Company Description</label>
                                    {{ form.description }}
                                    {% if form.description.errors %}
                                    <div class="text-danger">
                                        {{ form.description.errors }}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">Provide a brief description of your company</div>
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                    {{ form.phone_number }}
                                    {% if form.phone_number.errors %}
                                    <div class="text-danger">
                                        {{ form.phone_number.errors }}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">{{ form.phone_number.help_text }}</div>
                                </div>

                                <div class="mb-3 form-check">
                                    {{ form.allow_leaderboard }}
                                    <label for="{{ form.allow_leaderboard.id_for_label }}" class="form-check-label">
                                        Allow employees to appear on leaderboards
                                    </label>
                                    {% if form.allow_leaderboard.errors %}
                                    <div class="text-danger">
                                        {{ form.allow_leaderboard.errors }}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">If checked, employees from your company will appear on leaderboards</div>
                                </div>
                            </div>
                        </div>

                        <div class="text-end mt-3">
                            <a href="{% url 'corporate:corporate_dashboard' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Company Status Information -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card shadow">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Company Status</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Owner:</strong> {{ company.owner.get_full_name|default:company.owner.username }}</p>
                            <p><strong>Created:</strong> {{ company.created_at|date:"F j, Y" }}</p>
                        </div>
                        <div class="col-md-6">
                            <p>
                                <strong>Status:</strong>
                                {% if company.is_active %}
                                <span class="badge bg-success">Active</span>
                                {% elif company.pending_approval %}
                                <span class="badge bg-warning">Pending Approval</span>
                                {% else %}
                                <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </p>
                            {% if company.approved_at %}
                            <p><strong>Approved:</strong> {{ company.approved_at|date:"F j, Y" }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
