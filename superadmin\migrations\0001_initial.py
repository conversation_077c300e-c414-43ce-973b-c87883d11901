# Generated by Django 5.2 on 2025-05-21 16:06

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="SiteConfiguration",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "help_url",
                    models.URLField(
                        blank=True,
                        help_text="The URL for the 'Help' button in the site header.",
                        null=True,
                    ),
                ),
                (
                    "site_title",
                    models.<PERSON>r<PERSON><PERSON>(
                        default="Corporate Prompt Master",
                        help_text="The title of the site used in SEO meta tags.",
                        max_length=100,
                    ),
                ),
                (
                    "site_description",
                    models.TextField(
                        default="AI-powered game for corporate training and prompt engineering skills",
                        help_text="The description of the site used in SEO meta tags.",
                        max_length=300,
                    ),
                ),
                (
                    "site_keywords",
                    models.CharField(
                        default="corporate training, AI game, prompt engineering, corporate learning",
                        help_text="Comma-separated keywords for SEO.",
                        max_length=200,
                    ),
                ),
                (
                    "google_analytics_id",
                    models.CharField(
                        blank=True,
                        help_text="Google Analytics tracking ID (e.g., UA-XXXXXXXX-X or G-XXXXXXXXXX).",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "google_verification_tag",
                    models.CharField(
                        blank=True,
                        help_text="Google Search Console verification tag.",
                        max_length=100,
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name_plural": "Site Configuration",
            },
        ),
    ]
