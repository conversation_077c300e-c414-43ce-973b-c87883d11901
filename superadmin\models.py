from django.db import models
from django.core.exceptions import ValidationError

class SiteConfiguration(models.Model):
    """
    Singleton model to store site-wide configuration settings.
    """
    help_url = models.URLField(
        max_length=200,
        blank=True,
        null=True,
        help_text="The URL for the 'Help' button in the site header."
    )

    # SEO Settings
    site_title = models.CharField(
        max_length=100,
        default="Corporate Prompt Master",
        help_text="The title of the site used in SEO meta tags."
    )
    site_description = models.TextField(
        max_length=300,
        default="AI-powered game for corporate training and prompt engineering skills",
        help_text="The description of the site used in SEO meta tags."
    )
    site_keywords = models.CharField(
        max_length=200,
        default="corporate training, AI game, prompt engineering, corporate learning",
        help_text="Comma-separated keywords for SEO."
    )
    google_analytics_id = models.Char<PERSON>ield(
        max_length=50,
        blank=True,
        null=True,
        help_text="Google Analytics tracking ID (e.g., UA-XXXXXXXX-X or G-XXXXXXXXXX)."
    )
    google_verification_tag = models.CharField(
        max_length=100,
        blank=True,
        null=True,
        help_text="Google Search Console verification tag."
    )

    def save(self, *args, **kwargs):
        if not self.pk and SiteConfiguration.objects.exists():
            # If an instance already exists and we are trying to create a new one,
            # raise an error.
            raise ValidationError('There can be only one SiteConfiguration instance')
        return super(SiteConfiguration, self).save(*args, **kwargs)

    def __str__(self):
        return "Site Configuration"

    class Meta:
        verbose_name_plural = "Site Configuration"
