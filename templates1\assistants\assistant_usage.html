{% extends "assistants/base.html" %}

{% block title %}Assistant Usage{% endblock %}

{% block page_header %}
    <h1 class="h2">Usage Statistics for {{ assistant.name }}</h1>
{% endblock %}

{% block main_content %}
<div class="row g-4 mb-4">
    <!-- Monthly Interactions -->
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title text-muted text-uppercase small">Interactions (This Month)</h5>
                <p class="card-text display-6">{{ usage.monthly_interactions|default:"0" }}</p>
            </div>
        </div>
    </div>

    <!-- Monthly Tokens -->
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title text-muted text-uppercase small">Tokens Used (This Month)</h5>
                <p class="card-text display-6">{{ usage.monthly_tokens|default:"0" }}</p>
            </div>
        </div>
    </div>

    <!-- Estimated Cost -->
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-body">
                <h5 class="card-title text-muted text-uppercase small">Estimated Cost (This Month)</h5>
                <p class="card-text display-6">${{ usage.estimated_cost|floatformat:2 }}</p>
                <p class="card-text small text-muted">Based on model: {{ assistant.get_model_display }}</p>
            </div>
        </div>
    </div>
</div>

<div class="alert alert-info" role="alert">
    Usage statistics are calculated for the current calendar month.
</div>
{% endblock %}
