{# This partial expects 'response' dictionary and 'interaction_id' in context #}
{% load rating_tags %} {# Load rating tags #}

{% if response and response.status == 'success' and response.content %}
<div class="message assistant-message">
    {{ response.content|safe }} {# Display the actual content, marked as safe HTML #}
    {# Removed per-message rating block #}

    <div class="message-actions mt-2">
        <button type="button" class="btn btn-sm btn-outline-success upvote-answer-btn"
                data-interaction-id="{{ interaction.id }}">
            <i class="bi bi-hand-thumbs-up me-1"></i> Helpful
        </button>

        {% if assistant.assistant_type == 'community' %}
        <button type="button" class="btn btn-sm btn-outline-warning flag-answer-btn ms-2"
                data-question="{{ interaction.user_input }}"
                data-answer="{{ response.content|striptags }}">
            <i class="bi bi-flag me-1"></i> Flag Answer
        </button>
        {% endif %}
    </div>

    {# Display context sources if available #}
    {% if response.used_contexts %}
    <div class="message-sources mt-2 small text-muted">
        Sources:
        {% for context_source in response.used_contexts %}
        <a href="#" class="context-link ms-2" data-context-id="{{ context_source.id }}" title="View context: {{ context_source.title }}">
            <i class="bi bi-file-text"></i> [{{ forloop.counter }}]
        </a>
        {% endfor %}
        <div class="mt-1">
            <small class="text-success">Community knowledge helped answer this question! Thank you to our contributors.</small>
        </div>
    </div>
    {% endif %}
</div>

{# Context Modal Structure #}
<div class="modal fade" id="contextModal" tabindex="-1" aria-labelledby="contextModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg modal-dialog-scrollable">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="contextModalLabel">Context Source</h5>
        <div class="ms-auto me-3">
          <span class="badge bg-primary me-1"><i class="bi bi-lightning-charge"></i> <span id="context-usage">0</span> uses</span>
          <span class="badge bg-success"><i class="bi bi-hand-thumbs-up"></i> <span id="context-upvotes">0</span> upvotes</span>
          <span class="badge bg-info" id="context-answer-upvotes-badge" style="display: none;"><i class="bi bi-chat-dots"></i> <span id="context-answer-upvotes">0</span> from answers</span>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body" id="contextModalBody">
        Loading context...
      </div>
      <div class="modal-footer d-flex justify-content-between">
        <div>
          <button type="button" class="btn btn-outline-success" id="upvote-context-btn" data-context-id="" data-url="">
            <i class="bi bi-hand-thumbs-up me-1"></i> Upvote This Knowledge
          </button>
        </div>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div>

{# Flag Answer Modal for Anonymous Users #}
<div class="modal fade" id="flagAnswerModal" tabindex="-1" aria-labelledby="flagAnswerModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="flagAnswerModalLabel">Flag This Answer</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p>Help us improve by flagging answers that need improvement.</p>
        <form id="flag-answer-form">
          <input type="hidden" id="flag-question" name="question" value="">
          <input type="hidden" id="flag-answer" name="answer" value="">
          <div class="mb-3">
            <label for="flag-reason" class="form-label">Why is this answer unsatisfactory?</label>
            <textarea class="form-control" id="flag-reason" name="reason" rows="3" placeholder="Please explain why this answer needs improvement"></textarea>
          </div>
          <div class="mb-3">
            <label for="flag-email" class="form-label">Email (Optional)</label>
            <input type="email" class="form-control" id="flag-email" name="email" placeholder="<EMAIL>">
            <div class="form-text">Provide your email to receive notifications when this question gets better context.</div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-primary" id="submit-flag-btn">Submit Flag</button>
      </div>
    </div>
  </div>
</div>
{% elif response and response.error %}
<div class="message assistant-message text-danger"> {# Style errors differently #}
    Error: {{ response.error|escape }}
</div>
{% else %}
<div class="message assistant-message text-danger">
    Error: Received an empty or invalid response from the assistant.
</div>
{% endif %}
