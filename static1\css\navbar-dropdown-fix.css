/**
 * Navbar Dropdown Fix
 * Fixes the issue where the header expands instead of just showing the dropdown
 */

/* Ensure dropdown menus appear correctly without expanding the header */
.navbar .dropdown-menu {
  position: absolute !important;
  float: none !important;
  margin-top: 0.5rem !important;
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  z-index: 1060 !important; /* Higher z-index to ensure it appears above other elements */
  overflow: visible !important;
  transform: none !important;
  max-height: calc(100vh - 100px) !important;
  overflow-y: auto !important;
}

/* Ensure dropdown toggle behaves correctly */
.navbar .dropdown-toggle {
  cursor: pointer !important;
  position: relative !important;
  z-index: 1050 !important;
}

/* Fix for user dropdown specifically */
.navbar .nav-item.dropdown {
  position: relative !important;
}

/* Ensure dropdown menu doesn't affect navbar layout */
.navbar-collapse {
  flex-basis: auto !important;
}

/* Fix for mobile view */
@media (max-width: 991.98px) {
  /* Ensure dropdown menus appear within the collapsed navbar on mobile */
  .navbar-collapse .dropdown-menu {
    position: static !important;
    float: none !important;
    width: 100% !important;
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
    background-color: rgba(0, 0, 0, 0.02) !important;
    border: none !important;
    box-shadow: none !important;
    max-height: none !important;
  }

  /* Ensure dropdown toggle works correctly on mobile */
  .navbar-collapse .dropdown-toggle::after {
    float: right !important;
    margin-top: 0.5rem !important;
  }

  /* Ensure dropdown items are properly styled on mobile */
  .navbar-collapse .dropdown-item {
    padding: 0.75rem 1.5rem !important;
    white-space: normal !important;
  }
}

/* Dark mode fixes */
[data-theme="dark"] .navbar .dropdown-menu {
  background-color: #1a1a1a !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3) !important;
}

[data-theme="dark"] .navbar .dropdown-item {
  color: #cccccc !important;
}

[data-theme="dark"] .navbar .dropdown-item:hover {
  background-color: #252525 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .navbar .dropdown-divider {
  border-color: #333333 !important;
}

/* Fix for mobile dark mode */
@media (max-width: 991.98px) {
  [data-theme="dark"] .navbar-collapse .dropdown-menu {
    background-color: rgba(255, 255, 255, 0.05) !important;
  }
}

/* Fix for dropdown toggle animation */
.navbar .dropdown-toggle::after {
  transition: transform 0.2s ease !important;
}

.navbar .dropdown-toggle[aria-expanded="true"]::after {
  transform: rotate(180deg) !important;
}

/* Fix for dropdown menu animation */
.navbar .dropdown-menu {
  transition: opacity 0.2s ease, transform 0.2s ease !important;
  transform: translateY(-10px) !important;
  opacity: 0 !important;
  display: block !important;
  visibility: hidden !important;
}

.navbar .dropdown-menu.show {
  transform: translateY(0) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* Fix for dropdown menu positioning */
.navbar .dropdown-menu-end {
  right: 0 !important;
  left: auto !important;
}
