# Generated by Django 5.2 on 2025-05-21 16:06

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("corporate", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name="AnonymousPlayerSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "cleanup_enabled",
                    models.BooleanField(
                        default=True,
                        help_text="If enabled, anonymous player data will be cleaned up automatically",
                    ),
                ),
                (
                    "cleanup_hours",
                    models.PositiveIntegerField(
                        default=24,
                        help_text="Number of hours after which anonymous player data will be deleted",
                    ),
                ),
                (
                    "last_cleanup",
                    models.DateTimeField(
                        blank=True,
                        help_text="Timestamp of the last cleanup operation",
                        null=True,
                    ),
                ),
            ],
            options={
                "verbose_name": "Anonymous Player Settings",
                "verbose_name_plural": "Anonymous Player Settings",
            },
        ),
        migrations.CreateModel(
            name="CompanyCourse",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("is_active", models.BooleanField(default=True)),
                (
                    "is_public",
                    models.BooleanField(
                        default=False,
                        help_text="If true, visible to all company members",
                    ),
                ),
                (
                    "teams",
                    models.TextField(
                        blank=True,
                        help_text="Comma-separated list of teams with access to this course",
                    ),
                ),
                (
                    "start_role",
                    models.CharField(
                        default="applicant",
                        help_text="Starting role for this course",
                        max_length=50,
                    ),
                ),
                (
                    "max_role",
                    models.CharField(
                        blank=True,
                        help_text="Maximum achievable role (leave blank for no limit)",
                        max_length=50,
                    ),
                ),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="game_courses",
                        to="corporate.company",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CompanyGameSettings",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "is_public",
                    models.BooleanField(
                        default=True,
                        help_text="If true, the game is visible to non-company members",
                    ),
                ),
                (
                    "require_login",
                    models.BooleanField(
                        default=False,
                        help_text="If true, users must be logged in to play",
                    ),
                ),
                (
                    "show_leaderboard",
                    models.BooleanField(
                        default=True, help_text="If true, show company leaderboard"
                    ),
                ),
                (
                    "show_in_global_leaderboard",
                    models.BooleanField(
                        default=True,
                        help_text="If true, company members appear in global leaderboard",
                    ),
                ),
                (
                    "custom_welcome_message",
                    models.TextField(
                        blank=True,
                        help_text="Custom welcome message for company members",
                    ),
                ),
                (
                    "custom_completion_message",
                    models.TextField(
                        blank=True,
                        help_text="Custom completion message for company members",
                    ),
                ),
                (
                    "help_url",
                    models.URLField(
                        blank=True,
                        help_text="URL for the help button in the game header",
                        max_length=500,
                        null=True,
                    ),
                ),
                (
                    "use_company_branding",
                    models.BooleanField(
                        default=False,
                        help_text="If true, use company logo and colors in game",
                    ),
                ),
                (
                    "primary_color",
                    models.CharField(
                        blank=True,
                        help_text="Primary color for game UI (hex code)",
                        max_length=7,
                    ),
                ),
                (
                    "company",
                    models.OneToOneField(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="game_settings",
                        to="corporate.company",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CourseTask",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("title", models.CharField(max_length=100)),
                ("description", models.TextField()),
                (
                    "role",
                    models.CharField(
                        help_text="Role this task belongs to", max_length=50
                    ),
                ),
                (
                    "task_id",
                    models.CharField(
                        help_text="Unique identifier for this task",
                        max_length=50,
                        unique=True,
                    ),
                ),
                (
                    "order",
                    models.IntegerField(default=0, help_text="Order within the role"),
                ),
                (
                    "challenge_text",
                    models.TextField(
                        help_text="The challenge text presented to the user"
                    ),
                ),
                (
                    "success_criteria",
                    models.TextField(help_text="Criteria for successful completion"),
                ),
                (
                    "course",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="tasks",
                        to="game.companycourse",
                    ),
                ),
            ],
            options={
                "ordering": ["role", "order"],
            },
        ),
        migrations.CreateModel(
            name="GameSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("session_id", models.CharField(blank=True, max_length=100, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "team",
                    models.CharField(
                        blank=True,
                        help_text="Team or department within the company",
                        max_length=100,
                    ),
                ),
                ("current_role", models.CharField(default="applicant", max_length=50)),
                ("performance_score", models.IntegerField(default=0)),
                ("challenges_completed", models.IntegerField(default=0)),
                ("role_challenges_completed", models.IntegerField(default=0)),
                ("game_completed", models.BooleanField(default=False)),
                ("current_manager", models.CharField(default="hr", max_length=50)),
                (
                    "current_task",
                    models.CharField(default="cover_letter", max_length=50),
                ),
                ("completed_roles", models.TextField(default="[]")),
                ("first_task_pending", models.BooleanField(default=True)),
                ("next_task_pending", models.BooleanField(default=False)),
                (
                    "company",
                    models.ForeignKey(
                        blank=True,
                        help_text="The company this game session belongs to",
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="game_sessions",
                        to="corporate.company",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="Message",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("message_id", models.CharField(max_length=100)),
                ("sender", models.CharField(max_length=50)),
                ("text", models.TextField()),
                ("html", models.TextField()),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("is_challenge", models.BooleanField(default=False)),
                ("is_markdown", models.BooleanField(default=True)),
                (
                    "is_promotion",
                    models.BooleanField(blank=True, default=False, null=True),
                ),
                ("task_id", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "game_session",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="messages",
                        to="game.gamesession",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="CourseCompletion",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                ("started_at", models.DateTimeField(auto_now_add=True)),
                ("completed_at", models.DateTimeField(blank=True, null=True)),
                ("current_role", models.CharField(default="applicant", max_length=50)),
                ("current_task", models.CharField(blank=True, max_length=50)),
                (
                    "completed_tasks",
                    models.TextField(
                        default="[]", help_text="JSON list of completed task IDs"
                    ),
                ),
                ("score", models.IntegerField(default=0)),
                ("is_completed", models.BooleanField(default=False)),
                (
                    "course",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="completions",
                        to="game.companycourse",
                    ),
                ),
                (
                    "user",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="course_completions",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    "game_session",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to="game.gamesession",
                    ),
                ),
            ],
            options={
                "unique_together": {("user", "course")},
            },
        ),
    ]
