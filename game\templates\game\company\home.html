{% extends 'game/company/base.html' %}

{% block title %}{{ company.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h2">{{ company.name }}</h1>
            <p class="text-muted mb-0">Welcome to the Corporate Prompt Master game</p>
        </div>
        {% if not is_anonymous %}
        <a href="{% url 'game:index' %}?company={{ company.slug }}" class="btn btn-primary">
            <i class="bi bi-controller"></i> Play Game
        </a>
        {% endif %}
    </div>
    
    {% if not can_access %}
    <!-- Access Restricted -->
    <div class="card mb-4">
        <div class="card-body text-center py-5">
            <div class="mb-4">
                <i class="bi bi-lock" style="font-size: 3rem;"></i>
            </div>
            <h4>Access Restricted</h4>
            <p class="text-muted">This company game is restricted to authorized users only.</p>
            {% if settings.require_login %}
            <p>Please log in to access this game.</p>
            <div class="mt-3">
                <a href="/accounts/login/" class="btn btn-primary me-2">Log In</a>
                <a href="/accounts/register/" class="btn btn-outline-primary">Register</a>
            </div>
            {% else %}
            <p>You don't have permission to access this company's game.</p>
            {% endif %}
        </div>
    </div>
    {% else %}
    
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="icon">
                    <i class="bi bi-people"></i>
                </div>
                <div class="value">{{ stats.total_players }}</div>
                <div class="label">Total Players</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="icon">
                    <i class="bi bi-person-check"></i>
                </div>
                <div class="value">{{ stats.active_players }}</div>
                <div class="label">Active Players</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="icon">
                    <i class="bi bi-check-circle"></i>
                </div>
                <div class="value">{{ stats.total_completions }}</div>
                <div class="label">Completions</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card stats-card">
                <div class="icon">
                    <i class="bi bi-star"></i>
                </div>
                <div class="value">{{ stats.average_score|floatformat:1 }}</div>
                <div class="label">Average Score</div>
            </div>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="row">
        <!-- Top Performers -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Top Performers</h5>
                    {% if settings.show_leaderboard %}
                    <a href="{% url 'game:company_leaderboard' company_slug=company.slug %}" class="btn btn-sm btn-outline-primary">View All</a>
                    {% endif %}
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for entry in top_performers %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <span class="badge bg-secondary me-2">{{ forloop.counter }}</span>
                                {{ entry.user.username }}
                                {% if entry.highest_role %}
                                <span class="badge bg-info ms-2">{{ entry.highest_role }}</span>
                                {% endif %}
                            </div>
                            <span class="badge bg-primary rounded-pill">{{ entry.score }} pts</span>
                        </div>
                        {% empty %}
                        <div class="list-group-item text-center py-3">
                            <p class="mb-0 text-muted">No entries yet. Start playing to appear on the leaderboard!</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Available Courses -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Available Courses</h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for course in courses %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ course.name }}</h6>
                                    <p class="mb-0 small text-muted">
                                        {% if course.description %}
                                        {{ course.description|truncatechars:100 }}
                                        {% else %}
                                        No description provided.
                                        {% endif %}
                                    </p>
                                </div>
                                <a href="#" class="btn btn-sm btn-outline-success">
                                    <i class="bi bi-play-fill"></i> Play
                                </a>
                            </div>
                        </div>
                        {% empty %}
                        <div class="list-group-item text-center py-3">
                            <p class="mb-0 text-muted">No courses available yet.</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Teams -->
    {% if teams %}
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">Teams</h5>
        </div>
        <div class="card-body">
            <div class="d-flex flex-wrap gap-2">
                {% for team_name in teams %}
                <a href="{% url 'game:index' %}?company={{ company.slug }}&team={{ team_name }}" class="btn btn-outline-primary">
                    {{ team_name }}
                </a>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- About Company -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">About {{ company.name }}</h5>
        </div>
        <div class="card-body">
            <p>
                Welcome to the Corporate Prompt Master game for {{ company.name }}. 
                Test your prompt engineering skills and compete with your colleagues!
            </p>
            
            <div class="d-grid gap-2 col-md-6 mx-auto mt-4">
                <a href="{% url 'game:index' %}?company={{ company.slug }}" class="btn btn-lg btn-primary">
                    <i class="bi bi-controller"></i> Start Playing
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}
