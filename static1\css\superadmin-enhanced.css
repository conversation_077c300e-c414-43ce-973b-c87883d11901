/* Enhanced Superadmin Styles */

:root {
  /* Modern color palette */
  --admin-primary: #4361ee;
  --admin-primary-dark: #3a56d4;
  --admin-secondary: #6c757d;
  --admin-success: #2ecc71;
  --admin-info: #3498db;
  --admin-warning: #f39c12;
  --admin-danger: #e74c3c;
  --admin-light: #f8f9fa;
  --admin-dark: #343a40;
  --admin-gray-100: #f8f9fa;
  --admin-gray-200: #e9ecef;
  --admin-gray-300: #dee2e6;
  --admin-gray-800: #343a40;
  --admin-gray-900: #212529;

  /* Glass effect colors */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);

  /* Layout */
  --admin-sidebar-width: 260px;
  --admin-header-height: 70px;

  /* Gradients */
  --admin-gradient-primary: linear-gradient(135deg, var(--admin-primary) 0%, var(--admin-primary-dark) 100%);
  --admin-gradient-success: linear-gradient(135deg, var(--admin-success) 0%, #27ae60 100%);
  --admin-gradient-info: linear-gradient(135deg, var(--admin-info) 0%, #2980b9 100%);
  --admin-gradient-warning: linear-gradient(135deg, var(--admin-warning) 0%, #e67e22 100%);
  --admin-gradient-danger: linear-gradient(135deg, var(--admin-danger) 0%, #c0392b 100%);
}

/* Body and general styles */
.superadmin-body {
  background-color: #1a1a2e;
  color: #fff;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Sidebar Styles */
.superadmin-sidebar {
  background: rgba(26, 26, 46, 0.8);
  backdrop-filter: blur(10px);
  min-height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  width: var(--admin-sidebar-width);
  z-index: 1030;
  box-shadow: var(--glass-shadow);
  border-right: 1px solid var(--glass-border);
  transition: all 0.3s ease;
  padding-top: calc(var(--admin-header-height) + 3rem);
  margin-top: 56px; /* Height of the unified header */
}

.superadmin-sidebar .sidebar-brand {
  height: 4.5rem;
  text-decoration: none;
  font-size: 1.3rem;
  font-weight: 700;
  padding: 1.5rem 1.5rem;
  text-align: left;
  letter-spacing: 0.05rem;
  z-index: 1;
  color: white;
  display: flex;
  align-items: center;
  margin-top: 3rem;
}

.superadmin-sidebar .sidebar-brand i {
  font-size: 1.8rem;
  margin-right: 0.75rem;
  color: var(--admin-primary);
}

.superadmin-sidebar .nav-item {
  position: relative;
  margin: 0.25rem 0.75rem;
}

.superadmin-sidebar .nav-item .nav-link {
  display: flex;
  align-items: center;
  padding: 0.85rem 1.25rem;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
  border-radius: 10px;
  transition: all 0.2s ease;
}

.superadmin-sidebar .nav-item .nav-link:hover {
  color: white;
  background-color: rgba(67, 97, 238, 0.15);
}

.superadmin-sidebar .nav-item .nav-link.active {
  color: white;
  font-weight: 600;
  background-color: rgba(67, 97, 238, 0.25);
  box-shadow: 0 4px 20px rgba(67, 97, 238, 0.2);
}

.superadmin-sidebar .nav-item .nav-link i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
  width: 1.5rem;
  text-align: center;
}

.superadmin-sidebar .sidebar-divider {
  margin: 1.25rem 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.superadmin-sidebar .sidebar-heading {
  padding: 0 1.25rem;
  font-weight: 600;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.5);
  text-transform: uppercase;
  letter-spacing: 0.05rem;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

/* Main Content Styles */
.superadmin-layout {
  display: flex;
  min-height: calc(100vh - 56px); /* Subtract unified header height */
  margin-top: 0;
}

.superadmin-content {
  margin-left: var(--admin-sidebar-width);
  transition: all 0.3s ease;
  min-height: calc(100vh - 56px); /* Subtract unified header height */
  width: 100%;
  background-color: #121212;
  background-image:
    radial-gradient(at 47% 33%, rgba(67, 97, 238, 0.1) 0, transparent 59%),
    radial-gradient(at 82% 65%, rgba(46, 204, 113, 0.1) 0, transparent 55%);
}

.superadmin-container {
  padding: 1.5rem;
  background-color: #121212;
  min-height: calc(100vh - 56px);
}

/* Header */
.superadmin-header {
  height: var(--admin-header-height);
  background: rgba(26, 26, 46, 0.7);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  padding: 0 1.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Ensure no borders on any breadcrumb elements */
nav[aria-label="breadcrumb"] {
  border: none !important;
  background-color: transparent !important;
  background: transparent !important;
}

/* Override any breadcrumb background colors from other stylesheets */
.breadcrumb,
.breadcrumb ol,
.breadcrumb li,
.breadcrumb-item,
ol.breadcrumb,
.breadcrumb-item::before,
.breadcrumb-item::after {
  background-color: transparent !important;
  --bs-breadcrumb-bg: transparent !important;
  --ef-background-alt: transparent !important;
  background: transparent !important;
  border: none !important;
  border-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Force transparent background on breadcrumb */
.breadcrumb {
  background-color: transparent !important;
  background: transparent !important;
}

/* Custom superadmin breadcrumb styles */
.superadmin-breadcrumb-nav,
.superadmin-breadcrumb {
  background-color: transparent !important;
  background: transparent !important;
  border: none !important;
  border-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
  --ef-background-alt: transparent !important;
  --bs-breadcrumb-bg: transparent !important;
}

.superadmin-header h1 {
  margin-bottom: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
}

.superadmin-header .breadcrumb,
.superadmin-header nav[aria-label="breadcrumb"],
.superadmin-header ol.breadcrumb {
  margin-bottom: 0;
  background-color: transparent !important;
  background: transparent !important;
  padding: 0;
  --bs-breadcrumb-bg: transparent !important;
  --ef-background-alt: transparent !important;
  border: none !important;
  border-color: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

.superadmin-header .breadcrumb-item {
  font-size: 0.85rem;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  border: none !important;
  border-color: transparent !important;
  box-shadow: none !important;
}

.superadmin-header .breadcrumb-item.active {
  color: var(--admin-primary);
  font-weight: 600;
  background-color: rgba(67, 97, 238, 0.1);
}

.superadmin-header .breadcrumb-item a {
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
}

.superadmin-header .breadcrumb-item a:hover {
  color: white;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.superadmin-header .breadcrumb-item + .breadcrumb-item::before {
  content: "\F285"; /* Bootstrap Icons chevron-right */
  font-family: "bootstrap-icons";
  color: rgba(255, 255, 255, 0.4);
  margin: 0 0.25rem;
}

/* Dashboard Cards */
.superadmin-card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
  height: 100%;
  overflow: hidden;
}

.superadmin-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.superadmin-card .card-header {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding: 1.2rem 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
}

.dashboard-actions-card .card-header,
.dashboard-system-card .card-header {
  background: linear-gradient(90deg, rgba(67, 97, 238, 0.15) 0%, rgba(30, 30, 45, 0.5) 100%);
}

.superadmin-card .card-header h5 {
  margin-bottom: 0;
  font-weight: 600;
  color: white;
  font-size: 1.1rem;
}

.superadmin-card .card-body {
  flex: 1 1 auto;
  padding: 1.5rem;
}

.superadmin-card .card-icon {
  width: 50px;
  height: 50px;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  color: #fff;
  background: var(--admin-gradient-primary);
  box-shadow: 0 8px 16px rgba(67, 97, 238, 0.3);
  margin-right: 1rem;
}

.superadmin-card.card-primary .card-icon {
  background: var(--admin-gradient-primary);
  box-shadow: 0 8px 16px rgba(67, 97, 238, 0.3);
}

.superadmin-card.card-success .card-icon {
  background: var(--admin-gradient-success);
  box-shadow: 0 8px 16px rgba(46, 204, 113, 0.3);
}

.superadmin-card.card-info .card-icon {
  background: var(--admin-gradient-info);
  box-shadow: 0 8px 16px rgba(52, 152, 219, 0.3);
}

.superadmin-card.card-warning .card-icon {
  background: var(--admin-gradient-warning);
  box-shadow: 0 8px 16px rgba(243, 156, 18, 0.3);
}

.superadmin-card.card-danger .card-icon {
  background: var(--admin-gradient-danger);
  box-shadow: 0 8px 16px rgba(231, 76, 60, 0.3);
}

.superadmin-card .card-stat {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 0.25rem;
  line-height: 1;
  display: flex;
  align-items: baseline;
}

.superadmin-card .card-stat small {
  font-size: 1rem;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  margin-left: 0.5rem;
}

.superadmin-card.card-primary {
  border-left: 4px solid var(--admin-primary);
}

.superadmin-card.card-success {
  border-left: 4px solid var(--admin-success);
}

.superadmin-card.card-info {
  border-left: 4px solid var(--admin-info);
}

.superadmin-card.card-warning {
  border-left: 4px solid var(--admin-warning);
}

.superadmin-card.card-danger {
  border-left: 4px solid var(--admin-danger);
}

/* Button Styles */
.btn-admin {
  font-weight: 700;
  font-size: 0.9rem;
  border-radius: 10px;
  padding: 0.6rem 1.5rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.03em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-admin::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s;
}

.btn-admin:hover::before {
  opacity: 1;
}

.btn-admin-primary {
  background: linear-gradient(135deg, #4361ee 0%, #3a56d4 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(67, 97, 238, 0.4), 0 0 0 1px rgba(67, 97, 238, 0.5);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 700;
  letter-spacing: 0.02em;
}

.btn-admin-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(67, 97, 238, 0.5), 0 0 0 1px rgba(67, 97, 238, 0.6), 0 0 20px rgba(67, 97, 238, 0.4);
  color: white;
}

.btn-admin-success {
  background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(46, 204, 113, 0.4), 0 0 0 1px rgba(46, 204, 113, 0.5);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 700;
  letter-spacing: 0.02em;
}

.btn-admin-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(46, 204, 113, 0.5), 0 0 0 1px rgba(46, 204, 113, 0.6), 0 0 20px rgba(46, 204, 113, 0.4);
  color: white;
}

.btn-admin-info {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4), 0 0 0 1px rgba(52, 152, 219, 0.5);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 700;
  letter-spacing: 0.02em;
}

.btn-admin-info:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.5), 0 0 0 1px rgba(52, 152, 219, 0.6), 0 0 20px rgba(52, 152, 219, 0.4);
  color: white;
}

.btn-admin-warning {
  background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(243, 156, 18, 0.4), 0 0 0 1px rgba(243, 156, 18, 0.5);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  font-weight: 700;
  letter-spacing: 0.02em;
}

.btn-admin-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(243, 156, 18, 0.5), 0 0 0 1px rgba(243, 156, 18, 0.6), 0 0 20px rgba(243, 156, 18, 0.4);
  color: white;
}

.btn-admin-danger {
  background: var(--admin-gradient-danger);
  color: white;
  box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.btn-admin-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
  color: white;
}

.btn-admin-light {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.btn-admin-light:hover {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon i {
  margin-right: 0.5rem;
  font-size: 1.2rem;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.4));
}

.btn-sm.btn-icon i {
  font-size: 1.1rem;
  margin-right: 0.4rem;
  filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.5));
}

.btn-admin-primary i {
  color: rgba(255, 255, 255, 1);
}

.btn-admin-info i {
  color: rgba(255, 255, 255, 1);
}

.btn-admin-warning i {
  color: rgba(255, 255, 255, 1);
}

.btn-admin-success i {
  color: rgba(255, 255, 255, 1);
}

/* Dashboard Enhanced Styles */
.dashboard-welcome-card {
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.15) 0%, rgba(46, 204, 113, 0.15) 100%);
  border: none;
  overflow: hidden;
  position: relative;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.dashboard-welcome-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.05) 0%, rgba(46, 204, 113, 0.05) 100%);
  transform: translateX(-100%);
  animation: welcome-shine 3s infinite;
}

@keyframes welcome-shine {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
  100% { transform: translateX(100%); }
}

.welcome-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 15px;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.8) 0%, rgba(67, 97, 238, 0.6) 100%);
  box-shadow: 0 10px 20px rgba(67, 97, 238, 0.3);
  margin-right: 1.5rem;
}

.welcome-icon i {
  font-size: 2rem;
  color: white;
}

/* Dashboard Stat Cards */
.dashboard-stat-card {
  border: none;
  overflow: hidden;
  position: relative;
  transition: all 0.3s ease;
  border-radius: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05);
  background: rgba(30, 30, 45, 0.6);
  backdrop-filter: blur(10px);
}

.card-primary {
  background: linear-gradient(135deg, rgba(30, 30, 45, 0.7) 0%, rgba(30, 30, 45, 0.8) 100%);
  border-left: 4px solid rgba(67, 97, 238, 0.8);
}

.card-info {
  background: linear-gradient(135deg, rgba(30, 30, 45, 0.7) 0%, rgba(30, 30, 45, 0.8) 100%);
  border-left: 4px solid rgba(52, 152, 219, 0.8);
}

.card-warning {
  background: linear-gradient(135deg, rgba(30, 30, 45, 0.7) 0%, rgba(30, 30, 45, 0.8) 100%);
  border-left: 4px solid rgba(243, 156, 18, 0.8);
}

.card-success {
  background: linear-gradient(135deg, rgba(30, 30, 45, 0.7) 0%, rgba(30, 30, 45, 0.8) 100%);
  border-left: 4px solid rgba(46, 204, 113, 0.8);
}

.dashboard-stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.card-primary:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(67, 97, 238, 0.2);
}

.card-info:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(52, 152, 219, 0.2);
}

.card-warning:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(243, 156, 18, 0.2);
}

.card-success:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3), 0 0 20px rgba(46, 204, 113, 0.2);
}

.stat-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.stat-card-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  margin-right: 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
}

.card-primary .stat-card-icon {
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.9) 0%, rgba(67, 97, 238, 0.7) 100%);
  box-shadow: 0 5px 15px rgba(67, 97, 238, 0.4), 0 0 0 1px rgba(67, 97, 238, 0.5), 0 0 20px rgba(67, 97, 238, 0.3);
  border: 1px solid rgba(67, 97, 238, 0.6);
}

.card-info .stat-card-icon {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.9) 0%, rgba(52, 152, 219, 0.7) 100%);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4), 0 0 0 1px rgba(52, 152, 219, 0.5), 0 0 20px rgba(52, 152, 219, 0.3);
  border: 1px solid rgba(52, 152, 219, 0.6);
}

.card-warning .stat-card-icon {
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.9) 0%, rgba(243, 156, 18, 0.7) 100%);
  box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4), 0 0 0 1px rgba(243, 156, 18, 0.5), 0 0 20px rgba(243, 156, 18, 0.3);
  border: 1px solid rgba(243, 156, 18, 0.6);
}

.card-success .stat-card-icon {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.9) 0%, rgba(46, 204, 113, 0.7) 100%);
  box-shadow: 0 5px 15px rgba(46, 204, 113, 0.4), 0 0 0 1px rgba(46, 204, 113, 0.5), 0 0 20px rgba(46, 204, 113, 0.3);
  border: 1px solid rgba(46, 204, 113, 0.6);
}

.stat-card-icon i {
  font-size: 1.5rem;
  color: white;
  filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.2));
}

.stat-card-title {
  font-size: 0.7rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05rem;
  color: rgba(255, 255, 255, 0.85);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.stat-card-value {
  font-size: 3rem;
  font-weight: 700;
  color: white;
  margin: 0.5rem 0;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.stat-card-label {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 1.5rem;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.stat-card-action {
  margin-top: auto;
}

.stat-card-action .btn {
  border-width: 2px;
  font-weight: 700;
  padding: 0.5rem 1rem;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.stat-card-action .btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s;
  z-index: -1;
}

.stat-card-action .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.25), 0 0 5px rgba(255, 255, 255, 0.1);
}

.stat-card-action .btn:hover::after {
  opacity: 1;
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.85rem;
  border-radius: 8px;
  font-weight: 800;
  letter-spacing: 0.03em;
  min-width: 160px;
  text-align: center;
  justify-content: center;
  color: #ffffff !important;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5) !important;
}

/* Enhanced button styles for card actions */
.card-primary .stat-card-action .btn {
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.9) 0%, rgba(67, 97, 238, 0.8) 100%);
  border: 1px solid rgba(67, 97, 238, 0.6);
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3), 0 0 0 1px rgba(67, 97, 238, 0.5);
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.card-primary .stat-card-action .btn:hover {
  background: linear-gradient(135deg, rgba(67, 97, 238, 1) 0%, rgba(67, 97, 238, 0.9) 100%);
  box-shadow: 0 6px 15px rgba(67, 97, 238, 0.4), 0 0 0 1px rgba(67, 97, 238, 0.7), 0 0 20px rgba(67, 97, 238, 0.3);
  color: #ffffff;
}

.card-info .stat-card-action .btn {
  background: linear-gradient(135deg, rgba(52, 152, 219, 0.9) 0%, rgba(52, 152, 219, 0.8) 100%);
  border: 1px solid rgba(52, 152, 219, 0.6);
  box-shadow: 0 4px 10px rgba(52, 152, 219, 0.3), 0 0 0 1px rgba(52, 152, 219, 0.5);
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.card-info .stat-card-action .btn:hover {
  background: linear-gradient(135deg, rgba(52, 152, 219, 1) 0%, rgba(52, 152, 219, 0.9) 100%);
  box-shadow: 0 6px 15px rgba(52, 152, 219, 0.4), 0 0 0 1px rgba(52, 152, 219, 0.7), 0 0 20px rgba(52, 152, 219, 0.3);
  color: #ffffff;
}

.card-warning .stat-card-action .btn {
  background: linear-gradient(135deg, rgba(243, 156, 18, 0.9) 0%, rgba(243, 156, 18, 0.8) 100%);
  border: 1px solid rgba(243, 156, 18, 0.6);
  box-shadow: 0 4px 10px rgba(243, 156, 18, 0.3), 0 0 0 1px rgba(243, 156, 18, 0.5);
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.card-warning .stat-card-action .btn:hover {
  background: linear-gradient(135deg, rgba(243, 156, 18, 1) 0%, rgba(243, 156, 18, 0.9) 100%);
  box-shadow: 0 6px 15px rgba(243, 156, 18, 0.4), 0 0 0 1px rgba(243, 156, 18, 0.7), 0 0 20px rgba(243, 156, 18, 0.3);
  color: #ffffff;
}

.card-success .stat-card-action .btn {
  background: linear-gradient(135deg, rgba(46, 204, 113, 0.9) 0%, rgba(46, 204, 113, 0.8) 100%);
  border: 1px solid rgba(46, 204, 113, 0.6);
  box-shadow: 0 4px 10px rgba(46, 204, 113, 0.3), 0 0 0 1px rgba(46, 204, 113, 0.5);
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.card-success .stat-card-action .btn:hover {
  background: linear-gradient(135deg, rgba(46, 204, 113, 1) 0%, rgba(46, 204, 113, 0.9) 100%);
  box-shadow: 0 6px 15px rgba(46, 204, 113, 0.4), 0 0 0 1px rgba(46, 204, 113, 0.7), 0 0 20px rgba(46, 204, 113, 0.3);
  color: #ffffff;
}

/* Quick Actions Section */
.dashboard-actions-card {
  border: none;
  background: rgba(30, 30, 45, 0.5);
  border-radius: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.dashboard-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  border-radius: 15px;
  background: rgba(67, 97, 238, 0.15);
  backdrop-filter: blur(5px);
  border: 1px solid rgba(67, 97, 238, 0.3);
  color: white;
  text-decoration: none;
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(67, 97, 238, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dashboard-action-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(67, 97, 238, 0.3) 0%, rgba(67, 97, 238, 0) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dashboard-action-btn:hover {
  transform: translateY(-5px);
  background: rgba(67, 97, 238, 0.25);
  box-shadow: 0 10px 30px rgba(67, 97, 238, 0.3), 0 0 0 1px rgba(67, 97, 238, 0.4), 0 0 20px rgba(67, 97, 238, 0.2);
  color: white;
  border-color: rgba(67, 97, 238, 0.5);
}

.dashboard-action-btn:hover::after {
  opacity: 1;
}

.action-icon {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
  color: rgba(67, 97, 238, 1);
  filter: drop-shadow(0 0 8px rgba(67, 97, 238, 0.5));
}

.dashboard-action-btn:hover .action-icon {
  transform: scale(1.1);
  color: rgba(67, 97, 238, 1);
  filter: drop-shadow(0 0 12px rgba(67, 97, 238, 0.7));
}

.action-text {
  font-weight: 700;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.02em;
}

/* System Information Section */
.dashboard-system-card {
  border: none;
  background: rgba(30, 30, 45, 0.5);
  border-radius: 20px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.system-info-section {
  margin-bottom: 1.5rem;
}

.system-info-title {
  font-size: 1rem;
  font-weight: 600;
  color: rgba(67, 97, 238, 0.9);
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.system-info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.info-label {
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

.info-value {
  color: white;
  font-weight: 600;
}

/* System Information */
.system-info-table {
  width: 100%;
  color: rgba(255, 255, 255, 0.8);
}

.system-info-table th {
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.system-info-table td {
  font-weight: 600;
  padding: 0.75rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.35rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  min-width: 70px;
  justify-content: center;
}

.status-badge-active {
  color: #fff;
  background: var(--admin-gradient-success);
}

.status-badge-inactive {
  color: #fff;
  background: var(--admin-gradient-danger);
}

.status-badge-pending {
  color: #fff;
  background: var(--admin-gradient-warning);
}

.status-badge-featured {
  color: #fff;
  background: var(--admin-gradient-info);
}

.status-badge-gold {
  color: #000;
  background: linear-gradient(135deg, #ffd700 0%, #ffcc00 100%);
  box-shadow: 0 2px 5px rgba(255, 215, 0, 0.3);
}

.status-badge-premium {
  color: #fff;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  box-shadow: 0 2px 5px rgba(52, 152, 219, 0.3);
}

.status-badge-standard {
  color: rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Styles */
@media (max-width: 991.98px) {
  .superadmin-sidebar {
    width: 100%;
    height: auto;
    position: relative;
    min-height: auto;
  }

  .superadmin-content {
    margin-left: 0;
  }

  .superadmin-sidebar .sidebar-brand {
    height: auto;
    padding: 1rem;
  }

  .superadmin-sidebar .nav {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
  }

  .superadmin-sidebar .nav-item {
    width: auto;
  }

  .superadmin-sidebar .nav-item .nav-link {
    padding: 0.5rem 1rem;
  }

  .superadmin-sidebar .sidebar-divider {
    display: none;
  }

  .superadmin-sidebar .sidebar-heading {
    display: none;
  }
}

@media (max-width: 767.98px) {
  .superadmin-card .card-stat {
    font-size: 1.5rem;
  }

  .superadmin-card .card-icon {
    font-size: 1.25rem;
    width: 40px;
    height: 40px;
  }

  .quick-actions .btn {
    margin-bottom: 1rem;
  }
}

/* Toggle Sidebar Button */
.sidebar-toggle {
  position: absolute;
  top: calc(var(--admin-header-height) + 0.75rem);
  right: 1rem;
  z-index: 1050;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--admin-gradient-primary);
  color: white;
  border: none;
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  transform: rotate(90deg);
  box-shadow: 0 6px 15px rgba(67, 97, 238, 0.4);
}

.sidebar-toggle i {
  font-size: 1.25rem;
}

/* Table Styles */
.superadmin-table {
  width: 100%;
  margin-bottom: 0;
  color: white;
  border-collapse: separate;
  border-spacing: 0;
}

.superadmin-table th {
  padding: 1rem;
  vertical-align: middle;
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.8rem;
  letter-spacing: 0.05rem;
  color: rgba(255, 255, 255, 0.7);
}

.superadmin-table td {
  padding: 0.75rem 1rem;
  vertical-align: middle;
  border-top: 1px solid rgba(255, 255, 255, 0.05);
  color: white;
}

.superadmin-table tbody tr {
  transition: all 0.2s ease;
}

.superadmin-table tbody tr:hover {
  background-color: rgba(67, 97, 238, 0.1);
}

.superadmin-table .text-muted {
  color: rgba(255, 255, 255, 0.5) !important;
}

/* Table Action Buttons */
.table-actions .btn {
  width: 38px;
  height: 38px;
  padding: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  margin-right: 0.25rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  transition: all 0.2s ease;
}

.table-actions .btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-2px);
}

.table-actions .btn i {
  font-size: 1rem;
}

.table-actions .btn-feature,
.table-actions .btn-deactivate {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.table-actions .btn-feature i {
  color: rgba(255, 255, 255, 0.7);
}

.table-actions .btn-feature.active i {
  color: var(--admin-info);
}

.table-actions .btn-deactivate i {
  color: rgba(255, 255, 255, 0.7);
}

.table-actions .btn-deactivate.active i {
  color: var(--admin-success);
}

.table-actions .dropdown-menu {
  background: rgba(26, 26, 46, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  padding: 0.5rem;
}

.table-actions .dropdown-item {
  color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  padding: 0.5rem 1rem;
  transition: all 0.2s ease;
}

.table-actions .dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.table-actions .dropdown-item i {
  margin-right: 0.5rem;
}

.table-actions .text-success {
  color: var(--admin-success) !important;
}

.table-actions .text-danger {
  color: var(--admin-danger) !important;
}

/* Form Styles */
.form-control, .form-select {
  background-color: rgb(37, 37, 37);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 10px;
  padding: 0.6rem 1rem;
}

.form-control:focus, .form-select:focus {
  background-color: rgb(45, 45, 45);
  border-color: rgba(67, 97, 238, 0.5);
  color: white;
  box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.form-control::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}

.form-label {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.input-group-text {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

.form-check-input {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-check-input:checked {
  background-color: var(--admin-primary);
  border-color: var(--admin-primary);
}

.form-check-label {
  color: rgba(255, 255, 255, 0.8);
}

/* Pagination */
.superadmin-pagination {
  margin-top: 1.5rem;
}

.superadmin-pagination .pagination {
  justify-content: center;
}

.superadmin-pagination .page-link {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  margin: 0 0.25rem;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.superadmin-pagination .page-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

.superadmin-pagination .page-item.active .page-link {
  background: var(--admin-gradient-primary);
  border-color: var(--admin-primary);
  color: white;
  box-shadow: 0 4px 10px rgba(67, 97, 238, 0.3);
}

.superadmin-pagination .page-item.disabled .page-link {
  background: rgba(255, 255, 255, 0.02);
  color: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.05);
}

/* Editable Fields */
.editable-field {
  position: relative;
  cursor: pointer;
}

.editable-field:hover {
  background-color: rgba(67, 97, 238, 0.1);
  border-radius: 8px;
}

.editable-field span {
  display: block;
  padding: 0.25rem 0.5rem;
}

.editable-field input {
  display: none;
  width: 100%;
  padding: 0.25rem 0.5rem;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(67, 97, 238, 0.5);
  border-radius: 8px;
  color: white;
}

.editable-field.editing span {
  display: none;
}

.editable-field.editing input {
  display: block;
}

.editable-field.bg-success-subtle {
  background-color: rgba(46, 204, 113, 0.15) !important;
  transition: background-color 0.5s ease;
}

/* Empty State */
.empty-state {
  padding: 2rem 1rem;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  color: rgba(67, 97, 238, 0.3);
}

.empty-state h5 {
  font-weight: 600;
  color: white;
  margin-bottom: 1rem;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 1.5rem;
}

.empty-state .btn {
  min-width: 180px;
}
