/* Dark Table Override - Forces dark background for tables in game list */

/* Force dark background for all tables */
.table {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* Force dark background for table rows */
.table tbody tr {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* Force dark background for table cells */
.table tbody td {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* Force dark background for table headers */
.table thead th {
    background-color: #0d6efd !important;
    color: #ffffff !important;
}

/* Force dark background for card bodies containing tables */
.card-body {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* Force dark background for cards */
.card {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
}

/* Ensure text in cards is visible */
.card * {
    color: #ffffff !important;
}

/* Keep the header blue */
.card-header.bg-primary {
    background-color: #0d6efd !important;
    color: #ffffff !important;
}

/* Ensure badges remain visible */
.badge.bg-primary {
    background-color: #0d6efd !important;
    color: #ffffff !important;
}

.badge.bg-success {
    background-color: #198754 !important;
    color: #ffffff !important;
}

/* Ensure buttons remain visible */
.btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
}

.btn-outline-danger {
    color: #dc3545 !important;
    border-color: #dc3545 !important;
}

.btn-outline-danger:hover {
    background-color: #dc3545 !important;
    color: #ffffff !important;
}

/* Force dark background for table-responsive */
.table-responsive {
    background-color: #1e1e1e !important;
}

/* Force dark background for card footers */
.card-footer {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
}

/* Force dark background for list groups */
.list-group-item {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Force dark background for alerts */
.alert {
    background-color: #252525 !important;
    border-color: #444444 !important;
}

.alert-info {
    background-color: #0d3b66 !important;
    color: #ffffff !important;
}

.alert-warning {
    background-color: #664d03 !important;
    color: #ffffff !important;
}

/* Force dark background for modals */
.modal-content {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
}

.modal-header, .modal-footer {
    background-color: #252525 !important;
    border-color: #444444 !important;
}

/* Ensure text in alerts is visible */
.alert * {
    color: #ffffff !important;
}

/* Ensure links in alerts are visible */
.alert-link {
    color: #8bb9fe !important;
    text-decoration: underline !important;
}

/* Force dark background for main container */
.container, .container-fluid {
    background-color: #121212 !important;
}

/* Force dark background for main content */
main.container {
    background-color: #121212 !important;
}

/* Force dark background for body */
body {
    background-color: #121212 !important;
}
