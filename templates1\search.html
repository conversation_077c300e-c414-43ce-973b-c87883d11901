{% extends 'base/layout.html' %}
{% load static %}
{% load account_tags %}

{% block title %}Search Results - 24seven{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/directory.css' %}">
<style>
    /* Custom search results styling */
    .search-results-container {
        margin-top: 1.5rem;
    }

    /* Override the list-group-item styling for search results */
    .search-results-container .list-group-item {
        background-color: rgba(230, 240, 255, 0.9); /* Light blue background */
        background-image: linear-gradient(135deg, rgba(230, 240, 255, 0.9) 0%, rgba(230, 240, 255, 0.5) 100%); /* Gradient that fades to half intensity */
        border-radius: 1rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(0, 0, 0, 0.08);
    }

    .search-results-container .list-group-item:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }

    /* Logo container sizing for search results */
    .search-results-container .logo-container {
        height: 120px;
        width: 120px;
        min-height: 120px;
        min-width: 120px;
        max-height: 120px;
        max-width: 120px;
        margin: 0 auto; /* Center the logo container */
    }

    /* Ensure icons fit properly within the container */
    .search-results-container .logo-container .logo-placeholder i {
        font-size: 80px; /* Smaller size to fit better */
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
    }

    /* Improve text readability */
    .search-results-container h5 {
        font-size: 1.25rem;
        color: #0a4b9c;
    }

    .search-results-container p {
        font-size: 1rem;
        color: #333;
    }

    /* Badge styling */
    .tag-badge {
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
        margin-right: 0.5rem;
        border-radius: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- Search Header -->
    <div class="mb-4">
        <form method="get" action="{% url 'search' %}" class="row g-3">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text bg-transparent">
                        <i class="bi bi-search"></i>
                    </span>
                    <input type="search" name="q" class="form-control form-control-lg"
                           placeholder="Search for anything..." value="{{ query|default:'' }}"
                           autofocus>
                </div>
            </div>
            <div class="col-md-4 d-flex flex-wrap align-items-center">
                <!-- Radio buttons for search type -->
                <div class="form-check form-check-inline me-2">
                    <input class="form-check-input" type="radio" name="type" id="searchTypeAssistant" value="assistant" {% if search_type == 'assistant' or not search_type %}checked{% endif %}>
                    <label class="form-check-label" for="searchTypeAssistant">Assistants</label>
                </div>
                <div class="form-check form-check-inline me-2">
                    <input class="form-check-input" type="radio" name="type" id="searchTypeCommunity" value="community" {% if search_type == 'community' %}checked{% endif %}>
                    <label class="form-check-label" for="searchTypeCommunity">Community</label>
                </div>
                <div class="form-check form-check-inline me-2">
                    <input class="form-check-input" type="radio" name="type" id="searchTypeCompany" value="company" {% if search_type == 'company' %}checked{% endif %}>
                    <label class="form-check-label" for="searchTypeCompany">Companies</label>
                </div>
                <button type="submit" class="btn btn-primary ms-auto mt-2 mt-md-0">Search</button> {# Added explicit search button #}
            </div>
        </form>
    </div>

    {% if query %}
        <!-- Results Summary -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h4 class="mb-0">Search Results for "{{ query }}"</h4>
                <p class="text-muted mb-0">
                    Searching for: <span class="fw-bold">{% if is_company_search %}Companies{% elif is_community_search %}Community Assistants{% else %}Assistants{% endif %}</span>.
                    Found {{ page_obj.paginator.count }} result{{ page_obj.paginator.count|pluralize }}.
                </p>
            </div>
            <!-- Optional: Add sorting dropdown later if needed -->
            <!--
            <div class="btn-group">
                <button type="button" class="btn btn-outline-secondary dropdown-toggle"
                        data-bs-toggle="dropdown">
                    Sort by: Relevance
                </button>
                <ul class="dropdown-menu dropdown-menu-end">
                    <li><button class="dropdown-item active">Relevance</button></li>
                    <li><button class="dropdown-item">Date</button></li>
                    <li><button class="dropdown-item">Name</button></li>
                </ul>
            </div>
            -->
        </div>

        <!-- Results List -->
        <div class="list-group mb-4 search-results-container">
            {% for item in results %}
                {% if is_company_search %}
                    <!-- Display Company Result with Enhanced Styling -->
                    <div class="list-group-item position-relative directory-card">
                        <div class="row g-4 pt-3" style="height: 100%;">
                            <a href="{% url 'accounts:public_company_detail' slug=item.slug %}" class="directory-item-link-wrapper col-md-10 row g-4 me-0 text-decoration-none">
                                <!-- Column 1: Logo with Fallback -->
                                <div class="col-md-3 d-flex justify-content-center align-items-center">
                                    <div class="logo-container">
                                        {% if item.info.logo %}
                                            <img src="{{ item.info.logo.url }}" alt="{{ item.name }} logo">
                                        {% else %}
                                            <div class="logo-placeholder">
                                                <i class="bi bi-building-fill"></i>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Column 2: Company Info -->
                                <div class="col-md-9">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-2 fw-bold">{{ item.name }}</h5>
                                        <small class="text-muted">{{ item.created_at|timesince }} ago</small>
                                    </div>
                                    <p class="mb-2 fs-6">{{ item.info.description|truncatechars:200 }}</p>
                                    {% if item.info.website %}
                                        <small class="text-muted d-block mb-2">
                                            <i class="bi bi-globe me-1"></i>{{ item.info.website }}
                                        </small>
                                    {% endif %}
                                    {% if item.info.industry %}
                                        <small class="badge bg-light text-dark border tag-badge">
                                            <i class="bi bi-building me-1"></i>{{ item.info.industry }}
                                        </small>
                                    {% endif %}
                                </div>
                            </a>
                        </div>
                    </div>
                {% elif is_community_search %}
                    <!-- Display Community Assistant Result with Enhanced Styling -->
                    <div class="list-group-item position-relative directory-card">
                        <div class="row g-4 pt-3" style="height: 100%;">
                            <a href="{% url 'assistants:assistant_chat' slug=item.slug %}" class="directory-item-link-wrapper col-md-10 row g-4 me-0 text-decoration-none">
                                <!-- Column 1: Logo with Fallback -->
                                <div class="col-md-3 d-flex justify-content-center align-items-center">
                                    <div class="logo-container">
                                        {% with logo_url=item.get_logo_url %}
                                            {% if logo_url %}
                                                <img src="{{ logo_url }}" alt="{{ item.name }} logo">
                                            {% else %}
                                                <div class="logo-placeholder">
                                                    <i class="bi bi-robot"></i>
                                                </div>
                                            {% endif %}
                                        {% endwith %}
                                    </div>
                                </div>

                                <!-- Column 2: Assistant Info -->
                                <div class="col-md-9">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-2 fw-bold">{{ item.name }}</h5>
                                        <small class="text-muted">{{ item.created_at|timesince }} ago</small>
                                    </div>
                                    <p class="mb-2 fs-6">{{ item.description|truncatechars:200 }}</p>
                                    <small class="text-muted d-block mb-2">
                                        <i class="bi bi-building me-1"></i>By {{ item.company.name }}
                                    </small>
                                    <div class="mb-2">
                                        <span class="badge bg-secondary tag-badge community-badge">Community</span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                {% else %}
                    <!-- Display Regular Assistant Result with Enhanced Styling -->
                    <div class="list-group-item position-relative directory-card">
                        <div class="row g-4 pt-3" style="height: 100%;">
                            <a href="{% url 'assistants:assistant_chat' slug=item.slug %}" class="directory-item-link-wrapper col-md-10 row g-4 me-0 text-decoration-none">
                                <!-- Column 1: Logo with Fallback -->
                                <div class="col-md-3 d-flex justify-content-center align-items-center">
                                    <div class="logo-container">
                                        {% with logo_url=item.get_logo_url %}
                                            {% if logo_url %}
                                                <img src="{{ logo_url }}" alt="{{ item.name }} logo">
                                            {% else %}
                                                <div class="logo-placeholder">
                                                    <i class="bi bi-robot"></i>
                                                </div>
                                            {% endif %}
                                        {% endwith %}
                                    </div>
                                </div>

                                <!-- Column 2: Assistant Info -->
                                <div class="col-md-9">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-2 fw-bold">{{ item.name }}</h5>
                                        <small class="text-muted">{{ item.created_at|timesince }} ago</small>
                                    </div>
                                    <p class="mb-2 fs-6">{{ item.description|truncatechars:200 }}</p>
                                    <small class="text-muted d-block mb-2">
                                        <i class="bi bi-building me-1"></i>By {{ item.company.name }}
                                    </small>
                                    <div class="mb-2">
                                        <span class="badge bg-primary bg-opacity-10 text-primary tag-badge">{{ item.get_assistant_type_display }}</span>
                                    </div>
                                </div>
                            </a>
                        </div>
                    </div>
                {% endif %}
            {% empty %}
                <!-- No results found message moved here -->
            {% endfor %}
        </div>

        {% if not results %}
            <div class="text-center py-5">
                <div class="mb-3">
                    <i class="bi bi-search h1 text-muted"></i>
                </div>
                <h4>No Results Found</h4>
                <p class="text-muted">
                    We couldn't find anything matching your search.
                    Try different keywords or check your spelling.
                </p>
            </div>
        {% endif %}
    {% else %}
        <!-- Initial Search State -->
        <div class="text-center py-5">
            <div class="mb-3">
                <i class="bi bi-search h1 text-primary"></i>
            </div>
            <h4>Search Across Your Company</h4>
            <p class="text-muted">
                Find AI assistants, content, and team members quickly.
            </p>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Update sort order
    document.querySelectorAll('[data-sort]').forEach(button => {
        button.addEventListener('click', function() {
            const sort = this.getAttribute('data-sort');
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('sort', sort);
            window.location.href = currentUrl.toString();
        });
    });
});
</script>
{% endblock %}
