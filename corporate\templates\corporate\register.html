{% extends 'corporate/base.html' %}

{% block title %}Register - Corporate Prompt Master{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-user-plus me-2"></i>Corporate Registration</h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}

                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3">Company Information</h5>

                            <div class="mb-3">
                                <label for="{{ company_form.name.id_for_label }}" class="form-label">Company Name</label>
                                {{ company_form.name }}
                                {% if company_form.name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in company_form.name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ company_form.description.id_for_label }}" class="form-label">Company Description</label>
                                {{ company_form.description }}
                                {% if company_form.description.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in company_form.description.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ company_form.phone_number.id_for_label }}" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                {{ company_form.phone_number }}
                                {% if company_form.phone_number.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in company_form.phone_number.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                {% if company_form.phone_number.help_text %}
                                <div class="form-text">{{ company_form.phone_number.help_text }}</div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ company_form.logo.id_for_label }}" class="form-label">Company Logo</label>
                                {{ company_form.logo }}
                                {% if company_form.logo.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in company_form.logo.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <div class="form-text">Optional: Upload your company logo</div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <h5 class="mb-3">Admin User Information</h5>

                            <div class="mb-3">
                                <label for="{{ user_form.first_name.id_for_label }}" class="form-label">First Name</label>
                                {{ user_form.first_name }}
                                {% if user_form.first_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in user_form.first_name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ user_form.last_name.id_for_label }}" class="form-label">Last Name</label>
                                {{ user_form.last_name }}
                                {% if user_form.last_name.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in user_form.last_name.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ user_form.email.id_for_label }}" class="form-label">Email Address</label>
                                {{ user_form.email }}
                                {% if user_form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in user_form.email.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ user_form.username.id_for_label }}" class="form-label">Username</label>
                                {{ user_form.username }}
                                {% if user_form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in user_form.username.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ user_form.password1.id_for_label }}" class="form-label">Password</label>
                                {{ user_form.password1 }}
                                {% if user_form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in user_form.password1.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="{{ user_form.password2.id_for_label }}" class="form-label">Confirm Password</label>
                                {{ user_form.password2 }}
                                {% if user_form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in user_form.password2.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>Register Company
                        </button>
                    </div>
                </form>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">Already have an account? <a href="{% url 'corporate:corporate_login' %}">Login here</a></p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
