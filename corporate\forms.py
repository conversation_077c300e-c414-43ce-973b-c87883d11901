from django import forms
from django.contrib.auth.forms import UserCreationForm, AuthenticationForm
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils import timezone
from .models import Company, CorporateUser, CompanyInvitation, RegistrationLink


class CorporateLoginForm(AuthenticationForm):
    """
    Custom login form for corporate users with improved styling.
    """
    username = forms.CharField(
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Username or Email'})
    )
    password = forms.CharField(
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'Password'})
    )

    remember_me = forms.BooleanField(
        required=False,
        initial=True,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

    class Meta:
        model = User
        fields = ['username', 'password', 'remember_me']


class CompanyRegistrationForm(forms.ModelForm):
    """
    Form for registering a new company.
    """
    name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Company Name'})
    )
    description = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'placeholder': 'Company Description', 'rows': 3})
    )
    phone_number = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': '+256 7XX XXXXXX', 'type': 'tel'}),
        help_text="Required. Format: +256 7XX XXXXXX (Ugandan format)"
    )
    logo = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={'class': 'form-control'})
    )

    class Meta:
        model = Company
        fields = ['name', 'description', 'phone_number', 'logo']


class CorporateUserRegistrationForm(UserCreationForm):
    """
    Form for registering a new corporate user.
    """
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'First Name',
            'autocomplete': 'given-name'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Last Name',
            'autocomplete': 'family-name'
        })
    )
    email = forms.EmailField(
        max_length=254,
        required=True,
        widget=forms.EmailInput(attrs={
            'class': 'form-control',
            'placeholder': 'Email Address',
            'autocomplete': 'email'
        })
    )
    username = forms.CharField(
        max_length=150,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Username',
            'autocomplete': 'username'
        })
    )
    password1 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Password',
            'autocomplete': 'new-password'
        })
    )
    password2 = forms.CharField(
        widget=forms.PasswordInput(attrs={
            'class': 'form-control',
            'placeholder': 'Confirm Password',
            'autocomplete': 'new-password'
        })
    )

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'username', 'email', 'password1', 'password2']

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError("This email is already in use.")
        return email


class CorporateProfileForm(forms.ModelForm):
    """
    Form for corporate user profile information.
    """
    # User model fields
    first_name = forms.CharField(
        max_length=30,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'First Name'})
    )
    last_name = forms.CharField(
        max_length=30,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Last Name'})
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Email Address'})
    )

    # CorporateUser model fields
    job_title = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Job Title'})
    )
    department = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Department'})
    )
    employee_id = forms.CharField(
        max_length=50,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Employee ID'})
    )

    # Profile picture field
    avatar = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={'class': 'form-control', 'accept': 'image/*'})
    )

    class Meta:
        model = CorporateUser
        fields = ['job_title', 'department', 'employee_id']

    def __init__(self, *args, **kwargs):
        instance = kwargs.get('instance')
        if instance:
            # Initialize with user data
            initial = kwargs.get('initial', {})
            initial.update({
                'first_name': instance.user.first_name,
                'last_name': instance.user.last_name,
                'email': instance.user.email,
            })

            # Try to get avatar from UserProfile if it exists
            try:
                if hasattr(instance.user, 'profile') and instance.user.profile.avatar:
                    initial['avatar'] = instance.user.profile.avatar
            except:
                pass

            kwargs['initial'] = initial

        super().__init__(*args, **kwargs)

    def save(self, commit=True):
        corporate_profile = super().save(commit=False)

        # Update User model fields
        user = corporate_profile.user
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        user.email = self.cleaned_data['email']

        if commit:
            user.save()
            corporate_profile.save()

            # Handle avatar upload - create or update UserProfile
            avatar = self.cleaned_data.get('avatar')
            if avatar:
                from accounts.models import UserProfile
                profile, created = UserProfile.objects.get_or_create(user=user)
                profile.avatar = avatar
                profile.save()

        return corporate_profile


class CorporateUserInviteForm(forms.ModelForm):
    """
    Form for inviting a single new user to a company.
    """
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Email Address'})
    )
    job_title = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Job Title'})
    )
    department = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Department'})
    )
    is_admin = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )
    message = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'placeholder': 'Optional message', 'rows': 3})
    )

    class Meta:
        model = CompanyInvitation
        fields = ['email', 'job_title', 'department', 'is_admin', 'message']

    def clean_email(self):
        email = self.cleaned_data.get('email')
        company = self.instance.company if self.instance and self.instance.pk else None

        # Check if a user with this email already exists and is already in the company
        try:
            user = User.objects.get(email=email)
            if company and CorporateUser.objects.filter(user=user, company=company).exists():
                raise ValidationError("This user is already a member of your company.")
        except User.DoesNotExist:
            pass

        # Check if there's already a pending invitation for this email
        if company and CompanyInvitation.objects.filter(
            email=email,
            company=company,
            status=CompanyInvitation.STATUS_PENDING
        ).exists():
            raise ValidationError("There is already a pending invitation for this email.")

        return email

    def save(self, company, invited_by, commit=True):
        invitation = super().save(commit=False)
        invitation.company = company
        invitation.invited_by = invited_by
        invitation.expires_at = timezone.now() + timezone.timedelta(days=7)

        if commit:
            invitation.save()

        return invitation


class BulkInvitationForm(forms.Form):
    """
    Form for inviting multiple users to a company at once.
    """
    emails = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Enter email addresses, one per line'
        }),
        help_text="Enter one email address per line."
    )
    is_admin = forms.BooleanField(
        required=False,
        initial=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text="Make all invited users company administrators."
    )
    message = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Optional message to include in the invitation email'
        }),
        help_text="This message will be included in the invitation email."
    )

    def clean_emails(self):
        emails_raw = self.cleaned_data.get('emails', '')
        emails = [email.strip() for email in emails_raw.split('\n') if email.strip()]

        if not emails:
            raise ValidationError("Please enter at least one email address.")

        # Validate each email
        invalid_emails = []
        for email in emails:
            try:
                forms.EmailField().clean(email)
            except ValidationError:
                invalid_emails.append(email)

        if invalid_emails:
            raise ValidationError(f"Invalid email addresses: {', '.join(invalid_emails)}")

        return emails

    def save(self, company, invited_by):
        """Create invitations for all email addresses."""
        emails = self.cleaned_data.get('emails', [])
        is_admin = self.cleaned_data.get('is_admin', False)
        message = self.cleaned_data.get('message', '')

        invitations = []
        for email in emails:
            # Skip if user is already a member
            try:
                user = User.objects.get(email=email)
                if CorporateUser.objects.filter(user=user, company=company).exists():
                    continue
            except User.DoesNotExist:
                pass

            # Skip if pending invitation exists
            if CompanyInvitation.objects.filter(
                email=email,
                company=company,
                status=CompanyInvitation.STATUS_PENDING
            ).exists():
                continue

            # Create invitation
            invitation = CompanyInvitation(
                company=company,
                email=email,
                invited_by=invited_by,
                is_admin=is_admin,
                message=message,
                expires_at=timezone.now() + timezone.timedelta(days=7)
            )
            invitation.save()
            invitations.append(invitation)

        return invitations


class RegistrationLinkForm(forms.ModelForm):
    """
    Form for creating registration links.
    """
    intended_role = forms.ChoiceField(
        choices=[
            ('admin', 'Administrator'),
            ('member', 'Member'),
        ],
        initial='member',
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    approval_type = forms.ChoiceField(
        choices=[
            ('auto', 'Automatic Activation'),
            ('admin', 'Requires Admin Approval'),
        ],
        initial='auto',
        widget=forms.Select(attrs={'class': 'form-select'}),
        help_text="Choose whether users joining via this link need admin approval or are automatically activated."
    )
    expires_at = forms.DateTimeField(
        required=False,
        widget=forms.DateTimeInput(attrs={
            'class': 'form-control',
            'type': 'datetime-local'
        }),
        help_text="Optional expiration date. Leave blank for no expiration."
    )
    max_uses = forms.IntegerField(
        required=False,
        min_value=1,
        widget=forms.NumberInput(attrs={'class': 'form-control'}),
        help_text="Maximum number of times this link can be used. Leave blank for unlimited uses."
    )
    notes = forms.CharField(
        required=False,
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Optional notes about this link'}),
        help_text="Internal notes about the purpose of this link (not shown to users)."
    )

    class Meta:
        model = RegistrationLink
        fields = ['intended_role', 'approval_type', 'expires_at', 'max_uses', 'notes']

    def save(self, company, created_by, commit=True):
        link = super().save(commit=False)
        link.company = company
        link.created_by = created_by

        if commit:
            link.save()

        return link


class CompanyEditForm(forms.ModelForm):
    """
    Form for editing company details.
    """
    name = forms.CharField(
        max_length=255,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Company Name'})
    )
    description = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'placeholder': 'Company Description', 'rows': 4})
    )
    phone_number = forms.CharField(
        max_length=20,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': '+256 7XX XXXXXX', 'type': 'tel'}),
        help_text="Required. Format: +256 7XX XXXXXX (Ugandan format)"
    )
    logo = forms.ImageField(
        required=False,
        widget=forms.FileInput(attrs={'class': 'form-control'})
    )
    allow_leaderboard = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        help_text="Allow employees to appear on leaderboards"
    )

    class Meta:
        model = Company
        fields = ['name', 'description', 'phone_number', 'logo', 'allow_leaderboard']
