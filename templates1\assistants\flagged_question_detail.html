{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Flagged Question - {{ assistant.name }}{% endblock %}

{% block content %}
<div class="container py-4">
    <!-- Header -->
    <div class="row align-items-center mb-4">
        <div class="col">
            <h1 class="h3 mb-0">Flagged Question</h1>
            <p class="text-muted mb-0">
                Viewing details for a flagged question in {{ assistant.name }}
            </p>
        </div>
        <div class="col-auto">
            <a href="{% url 'assistants:add_context_to_flagged' assistant.company.id assistant.id flagged.id %}" class="btn btn-primary">
                <i class="bi bi-plus-circle me-2"></i>
                Add Context
            </a>
            {% if not flagged.is_resolved %}
                {% if is_anonymous %}
                    <a href="{% url 'assistants:resolve_flagged' assistant.company.id assistant.id flagged.id %}?tracking_id={{ tracking_id }}" class="btn btn-success ms-2">
                        <i class="bi bi-check-circle me-2"></i>
                        Mark Resolved
                    </a>
                {% elif request.user == flagged.user or request.user == assistant.company.owner %}
                    <a href="{% url 'assistants:resolve_flagged' assistant.company.id assistant.id flagged.id %}" class="btn btn-success ms-2">
                        <i class="bi bi-check-circle me-2"></i>
                        Mark Resolved
                    </a>
                {% endif %}
            {% endif %}
            <a href="{% url 'assistants:flagged_questions' assistant.company.id assistant.id %}" class="btn btn-light ms-2">
                <i class="bi bi-arrow-left me-2"></i>
                Back
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Question and Answer -->
            <div class="card shadow-sm mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Question & Answer</h5>
                    <span class="badge {% if flagged.is_resolved %}bg-success{% else %}bg-warning text-dark{% endif %}">
                        {{ flagged.is_resolved|yesno:"Resolved,Unresolved" }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="mb-4">
                        <h6 class="fw-bold">Question:</h6>
                        <div class="p-3 bg-light rounded">{{ flagged.question }}</div>
                    </div>
                    <div class="mb-4">
                        <h6 class="fw-bold">Original Answer:</h6>
                        <div class="p-3 bg-light rounded">{{ flagged.original_answer }}</div>
                    </div>
                    {% if flagged.reason %}
                        <div>
                            <h6 class="fw-bold">Reason for Flagging:</h6>
                            <div class="p-3 bg-light rounded">{{ flagged.reason }}</div>
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer text-muted">
                    <small>
                        Flagged {% if flagged.is_anonymous %}anonymously{% else %}by {{ flagged.user.username }}{% endif %}
                        on {{ flagged.created_at|date:"F j, Y, g:i a" }}
                    </small>
                    {% if flagged.is_resolved and flagged.resolved_at %}
                        <br>
                        <small>Resolved on {{ flagged.resolved_at|date:"F j, Y, g:i a" }}</small>
                    {% endif %}
                    {% if is_anonymous %}
                        <div class="mt-2 alert alert-info py-2">
                            <small>
                                <i class="bi bi-info-circle me-1"></i>
                                Your tracking ID: <strong>{{ tracking_id }}</strong>
                                <br>
                                Save this ID to check on your flagged question later.
                            </small>
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Related Contexts -->
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Related Contexts</h5>
                    <a href="{% url 'assistants:add_context_to_flagged' assistant.company.id assistant.id flagged.id %}" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add Context
                    </a>
                </div>
                <div class="card-body">
                    {% for context in related_contexts %}
                        <div class="card mb-3">
                            <div class="card-body">
                                <h5 class="card-title mb-2">{{ context.title|default:"Untitled Context" }}</h5>
                                <div class="card-text">{{ context.text_content|safe }}</div>

                                {% if context.images.exists %}
                                    <div class="mt-3">
                                        {% for img in context.images.all %}
                                            <img src="{{ img.image.url }}" alt="{{ img.caption|default:'Context image' }}" class="img-thumbnail me-2 mb-2" style="max-width: 100px; max-height: 100px;">
                                        {% endfor %}
                                    </div>
                                {% endif %}

                                {% if context.keywords %}
                                    <div class="mt-3">
                                        {% for keyword in context.keywords %}
                                            <span class="badge bg-light text-dark me-1">{{ keyword }}</span>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                            <div class="card-footer text-muted">
                                <small>Added by {{ context.created_by.username }} on {{ context.created_at|date:"M d, Y" }}</small>
                            </div>
                        </div>
                    {% empty %}
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            No contexts have been added for this flagged question yet. Be the first to contribute!
                        </div>
                    {% endfor %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            {% if not flagged.is_anonymous %}
            <!-- Flagged By -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Flagged By</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        {% if flagged.user.profile.avatar %}
                            <img src="{{ flagged.user.profile.avatar.url }}" alt="{{ flagged.user.username }}" class="rounded-circle me-3" style="width: 48px; height: 48px; object-fit: cover;">
                        {% else %}
                            <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                                <i class="bi bi-person"></i>
                            </div>
                        {% endif %}
                        <div>
                            <h6 class="mb-0">{{ flagged.user.username }}</h6>
                            <small class="text-muted">Member since {{ flagged.user.date_joined|date:"M Y" }}</small>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <!-- Anonymous Flag -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Anonymous Flag</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center me-3" style="width: 48px; height: 48px;">
                            <i class="bi bi-incognito"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">Anonymous User</h6>
                            <small class="text-muted">Flagged on {{ flagged.created_at|date:"M d, Y" }}</small>
                        </div>
                    </div>
                    {% if flagged.email %}
                    <div class="mt-3 small text-muted">
                        <i class="bi bi-envelope me-1"></i> Email notifications enabled
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}

            <!-- Notifications -->
            <div class="card shadow-sm mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Notifications</h5>
                </div>
                <div class="card-body">
                    {% for notification in notifications %}
                        <div class="d-flex align-items-start mb-3 {% if not notification.is_read %}bg-light p-2 rounded{% endif %}">
                            <div class="flex-shrink-0">
                                <i class="bi bi-bell-fill {% if not notification.is_read %}text-primary{% else %}text-muted{% endif %} me-2"></i>
                            </div>
                            <div>
                                <p class="mb-1">{{ notification.context.created_by.username }} added "{{ notification.context.title|default:"Untitled Context" }}"</p>
                                <small class="text-muted">{{ notification.created_at|date:"M d, Y g:i a" }}</small>
                            </div>
                        </div>
                    {% empty %}
                        <p class="text-muted mb-0">No notifications for this flagged question.</p>
                    {% endfor %}
                </div>
            </div>

            <!-- How It Works -->
            <div class="card shadow-sm">
                <div class="card-header">
                    <h5 class="card-title mb-0">How It Works</h5>
                </div>
                <div class="card-body">
                    <ol class="ps-3">
                        <li class="mb-2">Community members add context to improve answers</li>
                        <li class="mb-2">The assistant uses this context for future responses</li>
                        <li class="mb-2">The person who flagged the question gets notified</li>
                        <li class="mb-2">Once satisfied, the question can be marked as resolved</li>
                    </ol>
                    <div class="alert alert-info mt-3">
                        <i class="bi bi-lightbulb-fill me-2"></i>
                        The more specific and relevant the context, the better the assistant's answers will be!
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
