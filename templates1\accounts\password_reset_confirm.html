{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Set New Password - 24seven{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="form-container mt-4">
                <h2 class="text-center mb-4">Set New Password</h2>

                {% if validlink %}
                    {% if form.errors %}
                        <div class="alert alert-danger">
                            <h5>Please correct the following errors:</h5>
                            <ul class="mb-0">
                                {% for field, errors in form.errors.items %}
                                    {% for error in errors %}
                                        <li>{{ field|title }}: {{ error }}</li>
                                    {% endfor %}
                                {% endfor %}
                            </ul>
                        </div>
                    {% endif %}

                    <p class="text-muted mb-4">
                        Please enter your new password twice so we can verify you typed it in correctly.
                    </p>

                    <form method="post">
                        {% csrf_token %}

                        <div class="mb-3">
                            <label for="id_new_password1" class="form-label">New Password</label>
                            <input type="password" name="new_password1" id="id_new_password1" class="form-control" required>
                            {% if form.new_password1.help_text %}
                                <small class="form-text text-muted">
                                    <ul class="mb-0">
                                        {% for help_text in form.new_password1.help_text.split('\n') %}
                                            <li>{{ help_text }}</li>
                                        {% endfor %}
                                    </ul>
                                </small>
                            {% endif %}
                        </div>

                        <div class="mb-3">
                            <label for="id_new_password2" class="form-label">Confirm Password</label>
                            <input type="password" name="new_password2" id="id_new_password2" class="form-control" required>
                            {% if form.new_password2.help_text %}
                                <small class="form-text text-muted">
                                    {{ form.new_password2.help_text }}
                                </small>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                    </form>
                {% else %}
                    <div class="alert alert-danger">
                        <h5 class="alert-heading">Invalid Password Reset Link</h5>
                        <p class="mb-0">
                            The password reset link was invalid, possibly because it has already been used or has expired.
                            Please request a new password reset.
                        </p>
                    </div>

                    <div class="d-grid gap-2">
                        <a href="{% url 'accounts:password_reset' %}" class="btn btn-primary">Request New Reset Link</a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
