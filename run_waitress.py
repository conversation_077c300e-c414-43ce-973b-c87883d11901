"""
<PERSON><PERSON><PERSON> to run the Django application with Waitress on Windows.
This replaces the Passenger deployment method.

For more information on Waitress, see:
https://docs.pylonsproject.org/projects/waitress/en/latest/
"""

import os
import multiprocessing
from waitress import serve
from prompt_game.wsgi import application

# Set environment variables
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')

# Configuration
host = '0.0.0.0'
port = 8000
threads = multiprocessing.cpu_count() * 2

print(f"Starting Waitress server on {host}:{port} with {threads} threads")
serve(application, host=host, port=port, threads=threads)
