{% if user_sessions %}
<div class="card shadow mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0"><i class="fas fa-history me-2"></i>Your Game Progress</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Last Played</th>
                        <th>Current Role</th>
                        <th>Score</th>
                        <th>Status</th>
                        <th>Action</th>
                    </tr>
                </thead>
                <tbody>
                    {% for session in user_sessions %}
                    <tr>
                        <td>{{ session.updated_at|date:"M d, Y H:i" }}</td>
                        <td>{{ session.current_role|title }}</td>
                        <td>{{ session.performance_score }}</td>
                        <td>
                            {% if session.game_completed %}
                            <span class="badge bg-success">Completed</span>
                            {% else %}
                            <span class="badge bg-primary">In Progress</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="{% url 'game:index' %}{% if company %}?company={{ company.slug }}{% endif %}" class="btn btn-sm btn-primary">
                                    {% if session.game_completed %}
                                    Play Again
                                    {% else %}
                                    Continue
                                    {% endif %}
                                </a>
                                <button type="button" class="btn btn-sm btn-outline-danger restart-game-btn"
                                        data-session-id="{{ session.id }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#restartGameModal">
                                    <i class="fas fa-redo-alt"></i> Restart
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endif %}
