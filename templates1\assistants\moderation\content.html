{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Content Moderation - {{ assistant.name }}{% endblock %}

{% block extra_css %}
<link href="{% static 'css/moderation-dashboard.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">Content Moderation</h1>
                <div>
                    <a href="{% url 'assistants:moderation_dashboard' company.id assistant.id %}" class="btn btn-outline-primary me-2">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>
            <p class="text-muted">Review and moderate reported content for {{ assistant.name }}</p>
        </div>
    </div>

    <!-- Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-5">
                            <label for="status" class="form-label">Filter by Status</label>
                            <select class="form-select" id="status" name="status" onchange="this.form.submit()">
                                <option value="">All Statuses</option>
                                <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>Pending Review</option>
                                <option value="approved" {% if status_filter == 'approved' %}selected{% endif %}>Approved</option>
                                <option value="rejected" {% if status_filter == 'rejected' %}selected{% endif %}>Rejected</option>
                                <option value="removed" {% if status_filter == 'removed' %}selected{% endif %}>Content Removed</option>
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label for="type" class="form-label">Filter by Report Type</label>
                            <select class="form-select" id="type" name="type" onchange="this.form.submit()">
                                <option value="">All Types</option>
                                <option value="spam" {% if type_filter == 'spam' %}selected{% endif %}>Spam</option>
                                <option value="inappropriate" {% if type_filter == 'inappropriate' %}selected{% endif %}>Inappropriate Content</option>
                                <option value="offensive" {% if type_filter == 'offensive' %}selected{% endif %}>Offensive Language</option>
                                <option value="misinformation" {% if type_filter == 'misinformation' %}selected{% endif %}>Misinformation</option>
                                <option value="other" {% if type_filter == 'other' %}selected{% endif %}>Other</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label d-block">&nbsp;</label>
                            <a href="{% url 'assistants:moderation_content' company.id assistant.id %}" class="btn btn-outline-secondary w-100">
                                <i class="bi bi-x-circle"></i> Clear Filters
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Reports Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Reported Content</h5>
                </div>
                <div class="card-body">
                    {% if reports %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Type</th>
                                        <th>Status</th>
                                        <th>Reported By</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for report in reports %}
                                        <tr>
                                            <td>{{ report.id }}</td>
                                            <td>
                                                <span class="badge bg-{{ report.report_type|lower|slugify }}-subtle text-{{ report.report_type|lower|slugify }}">
                                                    {{ report.get_report_type_display }}
                                                </span>
                                            </td>
                                            <td>
                                                <span class="badge bg-{{ report.status|lower|slugify }}-subtle text-{{ report.status|lower|slugify }}">
                                                    {{ report.get_status_display }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if report.reported_by %}
                                                    {{ report.reported_by.username }}
                                                {% else %}
                                                    Anonymous
                                                {% endif %}
                                            </td>
                                            <td>{{ report.created_at|date:"M d, Y" }}</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-primary view-report-btn" data-bs-toggle="modal" data-bs-target="#viewReportModal" 
                                                    data-report-id="{{ report.id }}"
                                                    data-report-type="{{ report.get_report_type_display }}"
                                                    data-report-reason="{{ report.reason }}"
                                                    data-report-status="{{ report.status }}"
                                                    data-report-date="{{ report.created_at|date:'M d, Y H:i' }}"
                                                    data-reported-by="{% if report.reported_by %}{{ report.reported_by.username }}{% else %}Anonymous{% endif %}">
                                                    <i class="bi bi-eye"></i> View
                                                </button>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center my-4">No reported content found matching your criteria.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- View Report Modal -->
<div class="modal fade" id="viewReportModal" tabindex="-1" aria-labelledby="viewReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="viewReportModalLabel">Report Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6>Report Information</h6>
                        <table class="table table-sm">
                            <tr>
                                <th>Report ID:</th>
                                <td id="reportId"></td>
                            </tr>
                            <tr>
                                <th>Type:</th>
                                <td id="reportType"></td>
                            </tr>
                            <tr>
                                <th>Status:</th>
                                <td id="reportStatus"></td>
                            </tr>
                            <tr>
                                <th>Reported By:</th>
                                <td id="reportedBy"></td>
                            </tr>
                            <tr>
                                <th>Date:</th>
                                <td id="reportDate"></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Reason for Report</h6>
                        <div class="p-3 bg-light rounded" id="reportReason" style="min-height: 100px;"></div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <h6>Reported Content</h6>
                        <div class="p-3 bg-light rounded" id="reportedContent" style="min-height: 150px;">
                            <!-- Content will be loaded dynamically -->
                            <div class="text-center">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading content...</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <h6>Take Action</h6>
                        <div class="d-flex gap-2">
                            <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}" class="me-2">
                                {% csrf_token %}
                                <input type="hidden" name="action_type" value="approve">
                                <input type="hidden" name="report_id" id="approveReportId">
                                <input type="hidden" name="next" value="{{ request.path }}">
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-check-circle"></i> Approve Content
                                </button>
                            </form>
                            
                            <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}" class="me-2">
                                {% csrf_token %}
                                <input type="hidden" name="action_type" value="reject">
                                <input type="hidden" name="report_id" id="rejectReportId">
                                <input type="hidden" name="next" value="{{ request.path }}">
                                <button type="submit" class="btn btn-warning">
                                    <i class="bi bi-x-circle"></i> Reject Report
                                </button>
                            </form>
                            
                            <form method="post" action="{% url 'assistants:moderation_action' company.id assistant.id %}">
                                {% csrf_token %}
                                <input type="hidden" name="action_type" value="remove">
                                <input type="hidden" name="report_id" id="removeReportId">
                                <input type="hidden" name="next" value="{{ request.path }}">
                                <button type="submit" class="btn btn-danger">
                                    <i class="bi bi-trash"></i> Remove Content
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/moderation-dashboard.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // View Report Modal
        const viewReportModal = document.getElementById('viewReportModal');
        if (viewReportModal) {
            viewReportModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const reportId = button.getAttribute('data-report-id');
                const reportType = button.getAttribute('data-report-type');
                const reportReason = button.getAttribute('data-report-reason');
                const reportStatus = button.getAttribute('data-report-status');
                const reportDate = button.getAttribute('data-report-date');
                const reportedBy = button.getAttribute('data-reported-by');
                
                document.getElementById('reportId').textContent = reportId;
                document.getElementById('reportType').textContent = reportType;
                document.getElementById('reportStatus').textContent = reportStatus.charAt(0).toUpperCase() + reportStatus.slice(1);
                document.getElementById('reportedBy').textContent = reportedBy;
                document.getElementById('reportDate').textContent = reportDate;
                document.getElementById('reportReason').textContent = reportReason;
                
                // Set form values
                document.getElementById('approveReportId').value = reportId;
                document.getElementById('rejectReportId').value = reportId;
                document.getElementById('removeReportId').value = reportId;
                
                // Load reported content
                // This would typically be an AJAX call to fetch the content
                // For now, we'll just simulate it
                setTimeout(() => {
                    document.getElementById('reportedContent').innerHTML = 
                        '<div class="alert alert-info">Content would be loaded here via AJAX in a real implementation.</div>';
                }, 1000);
            });
        }
    });
</script>
{% endblock %}
