/* Hover Header Styles */

.hover-header {
    position: fixed;
    top: -80px; /* Start off-screen */
    left: 0;
    right: 0;
    height: 80px;
    background: linear-gradient(135deg, #4a86e8 0%, #7b1fa2 100%);
    color: white;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: top 0.3s ease-in-out;
    opacity: 0.95;
    padding: 0 20px;
    text-align: center;
}

.hover-header.visible {
    top: 0;
}

.hover-header-content {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 1200px;
}

.hover-header-logo {
    font-size: 1.8rem;
    font-weight: bold;
    margin-right: 15px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
}

.hover-header-logo-icon {
    font-size: 2rem;
    margin-right: 10px;
}

.hover-header-subtitle {
    font-size: 1rem;
    opacity: 0.9;
    font-style: italic;
    margin-left: 15px;
    padding-left: 15px;
    border-left: 1px solid rgba(255, 255, 255, 0.3);
}

.hover-header-actions {
    margin-left: auto;
    display: flex;
    gap: 10px;
}

.hover-header-button {
    background-color: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.hover-header-button:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.hover-header-button-icon {
    margin-right: 5px;
}

/* Dark mode adjustments */
html.dark-mode .hover-header,
body.dark-mode .hover-header {
    background: linear-gradient(135deg, #1a237e 0%, #4a148c 100%);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4);
    color: #ffffff; /* Ensure text is white for maximum contrast */
}

/* Ensure all text elements in the header have good contrast in dark mode */
html.dark-mode .hover-header-logo,
body.dark-mode .hover-header-logo,
html.dark-mode .hover-header-subtitle,
body.dark-mode .hover-header-subtitle {
    color: #ffffff;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5); /* Enhanced shadow for better readability */
}

/* Improve button contrast in dark mode */
html.dark-mode .hover-header-button,
body.dark-mode .hover-header-button {
    background-color: rgba(255, 255, 255, 0.25); /* Slightly more opaque for better contrast */
    border: 1px solid rgba(255, 255, 255, 0.4); /* Brighter border */
    color: #ffffff;
    font-weight: 500; /* Slightly bolder text */
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3); /* Text shadow for better readability */
}

html.dark-mode .hover-header-button:hover,
body.dark-mode .hover-header-button:hover {
    background-color: rgba(255, 255, 255, 0.35); /* Brighter on hover */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); /* Add shadow on hover */
}

/* Mobile adjustments */
@media (max-width: 768px) {
    .hover-header {
        height: 60px;
    }

    .hover-header-logo {
        font-size: 1.4rem;
    }

    .hover-header-subtitle {
        display: none;
    }

    .hover-header-actions {
        gap: 5px;
    }

    .hover-header-button {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
}

/* Animation for the header when it appears */
@keyframes headerFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 0.95;
        transform: translateY(0);
    }
}

.hover-header.visible {
    animation: headerFadeIn 0.3s ease-out;
}
