from django.core.management.base import BaseCommand
from corporate.models import LeaderboardEntry, GameAccess
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fix leaderboard entries with empty game_id values'

    def handle(self, *args, **options):
        # Get all entries with empty game_id
        empty_entries = LeaderboardEntry.objects.filter(game_id='')
        count = empty_entries.count()
        
        self.stdout.write(f"Found {count} leaderboard entries with empty game_id")
        
        if count == 0:
            self.stdout.write(self.style.SUCCESS("No entries to fix!"))
            return
        
        # Get the default game access
        default_game = GameAccess.objects.filter(game_id='prompt_master').first()
        
        if not default_game:
            self.stdout.write(self.style.ERROR("Default game 'prompt_master' not found!"))
            return
            
        default_game_id = default_game.game_id
        default_game_name = default_game.game_name
        
        self.stdout.write(f"Using default game: {default_game_id} - {default_game_name}")
        
        # Update all entries with empty game_id
        updated = empty_entries.update(
            game_id=default_game_id,
            game_name=default_game_name
        )
        
        self.stdout.write(self.style.SUCCESS(f"Successfully updated {updated} leaderboard entries"))
