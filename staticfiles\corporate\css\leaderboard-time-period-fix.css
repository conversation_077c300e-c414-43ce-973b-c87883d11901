/* 
 * Leaderboard Time Period Fix CSS
 * Makes time period text dark when the background turns white (active state)
 */

/* Make text dark when button is active (white background) */
.btn-outline-light.active {
    color: #212529 !important; /* Dark text color */
    text-shadow: none !important; /* Remove any text shadow */
    font-weight: bold !important; /* Make text bold for better visibility */
}

/* Ensure hover state also has dark text */
.btn-outline-light:hover {
    color: #212529 !important;
}

/* Ensure focus state also has dark text */
.btn-outline-light:focus {
    color: #212529 !important;
}
