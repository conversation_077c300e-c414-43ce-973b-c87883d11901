/**
 * CSS to hide the right toggle button in mobile and tablet modes
 * This file contains specific rules to ensure the right toggle button is hidden
 */

/* Hide the right sidebar toggle button on tablet and mobile */
@media (max-width: 1024px) {
    /* Target the right toggle button with high specificity */
    .right-sidebar-toggle,
    button.right-sidebar-toggle,
    div.right-sidebar-toggle,
    #right-sidebar-toggle,
    [class*="right-toggle"],
    [class*="toggle-right"],
    [id*="right-toggle"],
    [id*="toggle-right"],
    [aria-label*="right sidebar"],
    [aria-label*="Right sidebar"],
    .right-toggle,
    #right-toggle,
    .toggle-right,
    #toggle-right {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        position: absolute !important;
        left: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
    }
}

/* Additional rule for mobile view */
@media (max-width: 768px) {
    /* Target the right toggle button with even higher specificity */
    html body .right-sidebar-toggle,
    html body button.right-sidebar-toggle,
    html body div.right-sidebar-toggle,
    html body #right-sidebar-toggle,
    html body [class*="right-toggle"],
    html body [class*="toggle-right"],
    html body [id*="right-toggle"],
    html body [id*="toggle-right"],
    html body [aria-label*="right sidebar"],
    html body [aria-label*="Right sidebar"],
    html body .right-toggle,
    html body #right-toggle,
    html body .toggle-right,
    html body #toggle-right {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        position: absolute !important;
        left: -9999px !important;
        width: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
    }
}

/* Target any element with a class containing "right" and "toggle" */
@media (max-width: 1024px) {
    [class*="right"][class*="toggle"],
    [class*="toggle"][class*="right"],
    [id*="right"][id*="toggle"],
    [id*="toggle"][id*="right"] {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Target specific button that toggles the right sidebar */
@media (max-width: 1024px) {
    button[onclick*="right"],
    button[data-target*="right"],
    a[onclick*="right"],
    a[data-target*="right"],
    div[onclick*="right"],
    div[data-target*="right"],
    span[onclick*="right"],
    span[data-target*="right"] {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Target by position (assuming the toggle is positioned on the right side) */
@media (max-width: 1024px) {
    button[style*="right:"],
    a[style*="right:"],
    div[style*="right:"],
    span[style*="right:"] {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Hide any toggle button positioned on the right edge of the screen */
@media (max-width: 1024px) {
    .toggle-button[style*="right"],
    button.toggle[style*="right"],
    .sidebar-toggle[style*="right"] {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Hide any fixed-position elements on the right side that might be toggle buttons */
@media (max-width: 1024px) {
    button[style*="position: fixed"][style*="right"],
    a[style*="position: fixed"][style*="right"],
    div[style*="position: fixed"][style*="right"],
    span[style*="position: fixed"][style*="right"] {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Target by icon content (common icons used for toggle buttons) */
@media (max-width: 1024px) {
    button:has(i.fa-chevron-right),
    button:has(i.fa-chevron-left),
    button:has(i.fa-angle-right),
    button:has(i.fa-angle-left),
    button:has(i.fa-caret-right),
    button:has(i.fa-caret-left) {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Hide any element with a toggle icon that's positioned on the right */
@media (max-width: 1024px) {
    [style*="right"] i.fa-bars,
    [style*="right"] i.fa-navicon,
    [style*="right"] i.fa-reorder,
    [style*="right"] i.fa-ellipsis-v,
    [style*="right"] i.fa-ellipsis-h {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Hide any toggle button with a right arrow icon */
@media (max-width: 1024px) {
    button:has(> i[class*="arrow"]),
    button:has(> i[class*="chevron"]),
    button:has(> i[class*="angle"]),
    button:has(> i[class*="caret"]) {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Hide any element containing text about right sidebar */
@media (max-width: 1024px) {
    [class*="toggle"]:contains("right"),
    [id*="toggle"]:contains("right"),
    button:contains("right sidebar"),
    a:contains("right sidebar"),
    div:contains("right sidebar"),
    span:contains("right sidebar") {
        display: none !important;
        visibility: hidden !important;
    }
}

/* Hide any element with a data attribute related to right sidebar */
@media (max-width: 1024px) {
    [data-sidebar="right"],
    [data-target*="right-sidebar"],
    [data-toggle*="right-sidebar"],
    [data-controls*="right-sidebar"] {
        display: none !important;
        visibility: hidden !important;
    }
}
