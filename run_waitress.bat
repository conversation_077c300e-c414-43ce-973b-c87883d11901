@echo off
REM Script to run the Django application with Waitress on Windows
REM This replaces the Passenger deployment method

REM Activate virtual environment if it exists
if exist venv\Scripts\activate.bat (
    call venv\Scripts\activate.bat
)

REM Install or update dependencies
pip install -r requirements.txt

REM Collect static files
python manage.py collectstatic --noinput

REM Apply migrations
python manage.py migrate

REM Start Waitress
python run_waitress.py
