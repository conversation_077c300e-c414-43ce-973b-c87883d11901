/* Corporate Styles for Prompt Master Platform */

/* Light/Dark Mode Variables */
:root {
    --primary-color: #4a86e8;
    --primary-dark: #3a76d8;
    --primary-light: #6a96f8;
    --secondary-color: #34a853;
    --secondary-dark: #2a8a43;
    --secondary-light: #44b863;
    --background-color: #f8f9fa;
    --card-bg: #ffffff;
    --text-color: #333333;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --input-bg: #ffffff;
    --input-border: #ced4da;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --footer-bg: #f8f9fa;
}

/* Dark Mode */
.dark-mode {
    --primary-color: #5c94f0;
    --primary-dark: #4a86e8;
    --primary-light: #6ca2ff;
    --secondary-color: #44b863;
    --secondary-dark: #34a853;
    --secondary-light: #54c873;
    --background-color: #121212;
    --card-bg: #1e1e1e;
    --text-color: #e0e0e0;
    --text-muted: #aaaaaa;
    --border-color: #333333;
    --input-bg: #2a2a2a;
    --input-border: #444444;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --footer-bg: #1e1e1e;
}

/* Base Styles */
body {
    background-color: var(--background-color);
    color: var(--text-color);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    transition: background-color 0.3s, color 0.3s;
}

/* Navbar */
.navbar-dark {
    background-color: var(--primary-color) !important;
}

.navbar-brand {
    font-weight: bold;
}

/* Cards */
.card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    box-shadow: 0 4px 6px var(--shadow-color);
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px var(--shadow-color);
}

.card-header {
    border-bottom-color: var(--border-color);
}

.card-footer {
    border-top-color: var(--border-color);
    background-color: var(--card-bg);
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* Forms */
.form-control {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);
}

.form-control:focus {
    background-color: var(--input-bg);
    border-color: var(--primary-color);
    color: var(--text-color);
    box-shadow: 0 0 0 0.25rem rgba(74, 134, 232, 0.25);
}

/* Tables */
.table {
    color: var(--text-color);
}

.table-hover tbody tr:hover {
    background-color: rgba(74, 134, 232, 0.1);
}

.table-primary {
    background-color: rgba(74, 134, 232, 0.2);
}

/* Footer */
.footer {
    background-color: var(--footer-bg) !important;
    border-top: 1px solid var(--border-color);
}

/* Stat Cards */
.stat-card {
    text-align: center;
    padding: 15px;
    border-radius: 5px;
    background-color: rgba(74, 134, 232, 0.1);
    margin-bottom: 15px;
}

.stat-card-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-color);
}

.stat-card-label {
    font-size: 0.9rem;
    color: var(--text-muted);
}

/* Company Logo */
.company-logo {
    max-height: 100px;
    max-width: 100%;
}

.company-logo-placeholder {
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
}

/* Certificate Styles */
.certificate-preview {
    border: 1px solid var(--border-color);
    padding: 20px;
    background-color: var(--background-color);
    border-radius: 5px;
    margin-bottom: 20px;
}

.certificate-inner {
    background-color: var(--card-bg);
    border: 10px solid var(--primary-color);
    padding: 20px;
    text-align: center;
}

.certificate-header h3 {
    color: var(--primary-color);
    font-size: 24px;
    margin-bottom: 20px;
    text-transform: uppercase;
}

.certificate-name {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--primary-color);
    display: inline-block;
    padding-bottom: 5px;
}

.certificate-text, .certificate-role, .certificate-score, .certificate-date, .certificate-verification {
    margin-bottom: 10px;
}

.certificate-role, .certificate-score {
    font-weight: bold;
    color: var(--primary-color);
}

/* Game Card */
.game-icon {
    margin: 20px 0;
}

/* Leaderboard */
.nav-tabs .nav-link {
    color: var(--text-color);
}

.nav-tabs .nav-link.active {
    background-color: var(--card-bg);
    color: var(--primary-color);
    border-color: var(--border-color) var(--border-color) var(--card-bg);
}

/* Alerts */
.alert {
    border: none;
    box-shadow: 0 2px 4px var(--shadow-color);
}

/* Badges */
.badge.bg-light {
    color: var(--text-color) !important;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-title {
        font-size: 1.25rem;
    }
    
    .stat-card-value {
        font-size: 1.5rem;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    .certificate-inner {
        padding: 10px;
    }
    
    .certificate-header h3 {
        font-size: 20px;
    }
    
    .certificate-name {
        font-size: 16px;
    }
}

/* Dark Mode Specific Overrides */
.dark-mode .dropdown-menu {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

.dark-mode .dropdown-item {
    color: var(--text-color);
}

.dark-mode .dropdown-item:hover {
    background-color: rgba(74, 134, 232, 0.1);
}

.dark-mode .modal-content {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

.dark-mode .close {
    color: var(--text-color);
}

.dark-mode .text-muted {
    color: var(--text-muted) !important;
}

.dark-mode .list-group-item {
    background-color: var(--card-bg);
    border-color: var(--border-color);
    color: var(--text-color);
}

.dark-mode .bg-light {
    background-color: var(--card-bg) !important;
}

.dark-mode .table {
    color: var(--text-color);
}

.dark-mode .table td, .dark-mode .table th {
    border-color: var(--border-color);
}

.dark-mode .form-check-input {
    background-color: var(--input-bg);
    border-color: var(--input-border);
}

.dark-mode .form-check-input:checked {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}
