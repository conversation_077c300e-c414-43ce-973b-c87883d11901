"""
<PERSON><PERSON>t to update all leaderboard entries with the correct game status.
"""
import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from django.contrib.auth.models import User
from game.models import GameSession
from corporate.models import LeaderboardEntry

def update_game_status():
    """
    Update all leaderboard entries with the correct game status.
    """
    print("Updating game status for all leaderboard entries...")
    
    # Get all leaderboard entries
    entries = LeaderboardEntry.objects.all()
    print(f"Found {entries.count()} leaderboard entries")
    
    updated_count = 0
    skipped_count = 0
    
    for entry in entries:
        try:
            # Get the most recent game session for this user
            latest_session = GameSession.objects.filter(user=entry.user).order_by('-updated_at').first()
            
            if latest_session:
                # Determine the game status
                if latest_session.game_completed:
                    game_status = "Completed"
                else:
                    game_status = "In Progress"
                
                # Update the leaderboard entry
                entry.game_status = game_status
                entry.save()
                
                print(f"Updated entry for {entry.user.username}: {game_status}")
                updated_count += 1
            else:
                print(f"No game session found for {entry.user.username}, skipping")
                skipped_count += 1
        except Exception as e:
            print(f"Error updating entry for {entry.user.username}: {e}")
            skipped_count += 1
    
    print(f"Update complete. Updated {updated_count} entries, skipped {skipped_count} entries.")

if __name__ == "__main__":
    update_game_status()
