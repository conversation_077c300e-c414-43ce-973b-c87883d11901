"""
Management command to create missing permissions in the database.
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from django.apps import apps
from accounts.permissions import OWNER_PERMS_COMPANY

class Command(BaseCommand):
    help = 'Creates missing permissions in the database.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation of assistant permissions even if they already exist',
        )

    def handle(self, *args, **options):
        force = options.get('force', False)
        self.stdout.write("Checking for missing permissions...")

        # Get all existing permissions
        existing_perms = {}
        for perm in Permission.objects.all():
            key = f"{perm.content_type.app_label}.{perm.codename}"
            existing_perms[key] = perm

        self.stdout.write(f"Found {len(existing_perms)} existing permissions")

        # Check each permission in OWNER_PERMS_COMPANY
        missing_perms = []
        for perm_string in OWNER_PERMS_COMPANY:
            if perm_string not in existing_perms:
                missing_perms.append(perm_string)

        self.stdout.write(f"Found {len(missing_perms)} missing permissions")

        # Create missing permissions
        for perm_string in missing_perms:
            try:
                app_label, codename = perm_string.split('.')

                # Try to find a model in the app to associate with the permission
                app_models = apps.get_app_config(app_label).get_models()
                if not app_models:
                    self.stdout.write(self.style.ERROR(f"No models found in app '{app_label}'"))
                    continue

                # Use the first model in the app (or a more specific one if needed)
                model = app_models[0]
                content_type = ContentType.objects.get_for_model(model)

                # Create a readable name for the permission
                name = ' '.join(word.capitalize() for word in codename.split('_'))

                # Create the permission
                permission = Permission.objects.create(
                    codename=codename,
                    name=name,
                    content_type=content_type
                )

                self.stdout.write(self.style.SUCCESS(f"Created permission '{perm_string}'"))

            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error creating permission '{perm_string}': {e}"))

        # Specifically create assistant permissions if they don't exist or if force=True
        assistant_permissions = [
            ('add_assistant', 'Can add assistant'),
            ('change_assistant', 'Can change assistant'),
            ('delete_assistant', 'Can delete assistant'),
            ('view_assistant_usage', 'Can view assistant usage'),
            ('view_assistant_analytics', 'Can view assistant analytics'),
            ('access_all_private', 'Can access all private assistants'),
            ('create_assistant_token', 'Can create assistant token'),
        ]

        try:
            # Get the Assistant model content type
            assistant_models = apps.get_app_config('assistants').get_models()
            if not assistant_models:
                self.stdout.write(self.style.ERROR("No models found in 'assistants' app"))
            else:
                # Find the Assistant model
                assistant_model = None
                for model in assistant_models:
                    if model.__name__.lower() == 'assistant':
                        assistant_model = model
                        break

                if not assistant_model:
                    self.stdout.write(self.style.ERROR("Assistant model not found in 'assistants' app"))
                else:
                    content_type = ContentType.objects.get_for_model(assistant_model)

                    # Create each permission
                    for codename, name in assistant_permissions:
                        perm_string = f"assistants.{codename}"
                        if force or perm_string not in existing_perms:
                            try:
                                permission, created = Permission.objects.get_or_create(
                                    codename=codename,
                                    content_type=content_type,
                                    defaults={'name': name}
                                )

                                if created:
                                    self.stdout.write(self.style.SUCCESS(f"Created permission '{perm_string}'"))
                                else:
                                    self.stdout.write(f"Permission '{perm_string}' already exists")
                            except Exception as e:
                                self.stdout.write(self.style.ERROR(f"Error creating permission '{perm_string}': {e}"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error creating assistant permissions: {e}"))

        self.stdout.write(self.style.SUCCESS("Done!"))
