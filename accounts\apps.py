from django.apps import AppConfig
from django.db.models.signals import post_migrate

def create_default_permissions(sender, **kwargs):
    """Create/update default groups and permissions after migrations."""
    from django.contrib.auth.models import Permission, Group
    from django.contrib.contenttypes.models import ContentType
    # Removed unused import: from django.db import models

    # --- Delete Old/Unwanted Groups ---
    # Ensure the groups we *don't* want are removed
    unwanted_group_names = ['Administrator', 'Member', 'Viewer']
    deleted_count, _ = Group.objects.filter(name__in=unwanted_group_names).delete()
    if deleted_count > 0:
        print(f"Deleted {deleted_count} old/unwanted groups: {unwanted_group_names}")

    # Get content types
    from .models import Company

    # Try to import Assistant model, but handle the case where it doesn't exist
    assistant_content_type = None
    try:
        from assistants.models import Assistant
        try:
            assistant_content_type = ContentType.objects.get_for_model(Assistant)
        except ContentType.DoesNotExist:
            print("Warning: ContentType for assistants.Assistant not found. Skipping assistant permission setup.")
    except ImportError:
        print("Warning: assistants app not found. Skipping assistant permission setup.")

    try:
        company_content_type = ContentType.objects.get_for_model(Company)
    except ContentType.DoesNotExist:
        print("Warning: ContentType for accounts.Company not found. Skipping permission setup.")
        return

    # --- Define and Create Custom Company Permissions ---
    custom_company_permissions = [
        ('can_manage_team', 'Can manage team members'),
        # ('can_manage_assistants', 'Can manage AI assistants'), # Commented out: Prefer specific perms
        ('can_upload_content', 'Can upload content'),
        ('can_view_analytics', 'Can view company analytics'),
        ('can_change_settings', 'Can change company settings'),
        ('can_view_billing', 'Can view billing information'),
        # Add the new permission defined in models.py Meta
        ('manage_company_assistants', 'Can manage assistants within the company'),
    ] # Add the closing bracket here
    company_perms_qs = [] # Add this line which was missing in the previous SEARCH block
    for codename, name in custom_company_permissions:
        perm, _ = Permission.objects.get_or_create(
            codename=codename,
            name=name,
            content_type=company_content_type,
        )
        company_perms_qs.append(perm)
    # Use the list of created/fetched permission objects directly
    company_permissions = Permission.objects.filter(id__in=[p.id for p in company_perms_qs])
    # Remove the corrupted line below if it exists
    # company_permissons = Pemission.objs.fiter(id__in=[p.id for p in compan_perms_qs])

    # --- Get the specific permission for managing assistants ---
    manage_assistants_perm = None
    try:
        manage_assistants_perm = Permission.objects.get(
            content_type=company_content_type,
            codename='manage_company_assistants'
        )
    except Permission.DoesNotExist:
        print("Warning: Permission 'accounts.manage_company_assistants' not found. Ensure migrations are run.")

    # --- Create/Update Default Groups ---
    admin_group, created_admin = Group.objects.get_or_create(name='Company Administrators')
    member_group, created_member = Group.objects.get_or_create(name='Company Members')
    guest_group, created_guest = Group.objects.get_or_create(name='Company Guests')

    # --- Define Member Permissions ---
    member_perms_codenames = ['can_upload_content', 'can_view_analytics']
    # Filter from the already fetched company_permissions queryset
    member_company_permissions = company_permissions.filter(codename__in=member_perms_codenames)
    member_permissions_set = set(member_company_permissions)
    # Add the new company-level assistant management permission if it exists
    if manage_assistants_perm:
        member_permissions_set.add(manage_assistants_perm)
    # Assign the combined set to the member group
    member_group.permissions.set(list(member_permissions_set))

    # --- Define Guest Permissions ---
    guest_perms_codenames = ['can_view_analytics']
    # Filter from the already fetched company_permissions queryset
    guest_permissions = company_permissions.filter(codename__in=guest_perms_codenames)
    guest_group.permissions.set(guest_permissions)

    # --- Define Admin Permissions (Combine all relevant permissions) ---
    # Start with all defined company permissions
    admin_permissions_set = set(company_permissions)
    # Add member permissions (which now might include manage_company_assistants)
    admin_permissions_set.update(member_permissions_set) # Use the updated member set
    # Add guest permissions
    admin_permissions_set.update(guest_permissions)
    # Ensure the manage_company_assistants perm is included directly if it exists
    if manage_assistants_perm:
        admin_permissions_set.add(manage_assistants_perm)

    # Assign the combined set to the admin group
    admin_group.permissions.set(list(admin_permissions_set))

    print("Default groups and permissions configuration applied.") # Updated confirmation message

class AccountsConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'accounts'
    verbose_name = 'Account Management'

    def ready(self):
        """
        Initialize app and connect signals.
        """
        # Import and connect signals
        import accounts.signals

        # Connect post_migrate signal to create permissions
        post_migrate.connect(create_default_permissions, sender=self)

        # Register activity logger
        from django.contrib.auth.models import User
        from django.db.models.signals import post_save, post_delete

        def log_user_activity(sender, instance, created=None, **kwargs):
            """Log user-related activity."""
            from .models import ActivityLog

            if created is not None:  # post_save signal
                action = 'created' if created else 'updated'
            else:  # post_delete signal
                action = 'deleted'

            if hasattr(instance, 'company'):
                company = instance.company
            else:
                # Try to get company from context if available
                from django.core.cache import cache
                company = cache.get('current_company')

            if company:
                ActivityLog.objects.create(
                    company=company,
                    user=instance if isinstance(instance, User) else None,
                    action=action,
                    target=str(instance),
                    target_type=instance.__class__.__name__
                )

        # Connect activity logging signals
        post_save.connect(log_user_activity, sender=User)
        post_delete.connect(log_user_activity, sender=User)
