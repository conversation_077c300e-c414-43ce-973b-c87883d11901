"""
Debug views for the accounts app.
"""
import json
import logging
import sys
from django.http import JsonResponse
from django.views.decorators.http import require_GET
from django.contrib.auth.decorators import login_required

# Configure logging
logger = logging.getLogger(__name__)
handler = logging.StreamHandler(sys.stdout)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.DEBUG)

@require_GET
@login_required
def debug_impersonation(request):
    """
    Debug view to check impersonation state.
    """
    data = {
        'is_authenticated': request.user.is_authenticated,
        'user_id': request.user.id,
        'username': request.user.username,
        'is_superuser': request.user.is_superuser,
        'is_staff': request.user.is_staff,
        'is_impersonate': getattr(request, 'is_impersonate', False),
        'has_real_user': hasattr(request, 'real_user'),
        'show_impersonation_ui': getattr(request, 'show_impersonation_ui', False),
        'request_path': request.path,
        'request_method': request.method,
    }
    
    # Add real_user info if available
    if hasattr(request, 'real_user'):
        data['real_user'] = {
            'id': request.real_user.id,
            'username': request.real_user.username,
            'is_superuser': request.real_user.is_superuser,
            'is_staff': request.real_user.is_staff,
        }
    
    # Add session info
    data['session'] = {
        'session_key': request.session.session_key,
        'session_items': {k: str(v) for k, v in request.session.items()},
    }
    
    # Add request headers
    data['headers'] = {k: v for k, v in request.headers.items()}
    
    return JsonResponse(data, json_dumps_params={'indent': 2})
