{% extends 'corporate/base.html' %}

{% block title %}{{ target_user.get_full_name|default:target_user.username }} Progress - {{ company.name }}{% endblock %}

{% block extra_css %}
{% include 'corporate/user_progress_extra.html' %}
{% include 'corporate/user_progress_detail_extra.html' %}
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2>User Progress Details</h2>
            <a href="{% url 'corporate:user_progress' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>Back to All Users
            </a>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>{{ target_user.get_full_name|default:target_user.username }}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>User Information</h6>
                        <table class="table" style="background-color: #1e1e1e !important;">
                            <tr style="background-color: #1e1e1e !important;">
                                <th style="background-color: #1e1e1e !important;">Username:</th>
                                <td style="background-color: #1e1e1e !important;">{{ target_user.username }}</td>
                            </tr>
                            <tr style="background-color: #1e1e1e !important;">
                                <th style="background-color: #1e1e1e !important;">Full Name:</th>
                                <td style="background-color: #1e1e1e !important;">{{ target_user.get_full_name|default:"Not provided" }}</td>
                            </tr>
                            <tr style="background-color: #1e1e1e !important;">
                                <th style="background-color: #1e1e1e !important;">Email:</th>
                                <td style="background-color: #1e1e1e !important;">{{ target_user.email }}</td>
                            </tr>
                            <tr style="background-color: #1e1e1e !important;">
                                <th style="background-color: #1e1e1e !important;">Job Title:</th>
                                <td style="background-color: #1e1e1e !important;">{{ target_user.corporate_profile.job_title|default:"Not specified" }}</td>
                            </tr>
                            <tr style="background-color: #1e1e1e !important;">
                                <th style="background-color: #1e1e1e !important;">Role:</th>
                                <td style="background-color: #1e1e1e !important;">
                                    {% if target_user.corporate_profile.is_company_admin %}
                                    <span class="badge bg-danger">Admin</span>
                                    {% else %}
                                    <span class="badge bg-secondary">Member</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr style="background-color: #1e1e1e !important;">
                                <th style="background-color: #1e1e1e !important;">Joined:</th>
                                <td style="background-color: #1e1e1e !important;">{{ target_user.date_joined|date:"F j, Y" }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Progress Summary</h6>
                        <div class="row">
                            <div class="col-6 mb-3">
                                <div class="card bg-light" style="background-color: #1e1e1e !important; border-color: #333333 !important;">
                                    <div class="card-body text-center" style="background-color: #1e1e1e !important;">
                                        <h3 class="mb-0" style="color: #ffffff !important;">{{ user_sessions.count }}</h3>
                                        <p class="text-muted mb-0" style="color: #cccccc !important;">Total Sessions</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="card bg-light" style="background-color: #1e1e1e !important; border-color: #333333 !important;">
                                    <div class="card-body text-center" style="background-color: #1e1e1e !important;">
                                        <h3 class="mb-0" style="color: #ffffff !important;">{{ user_sessions.filter.game_completed.count }}</h3>
                                        <p class="text-muted mb-0" style="color: #cccccc !important;">Completed Games</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="card bg-light" style="background-color: #1e1e1e !important; border-color: #333333 !important;">
                                    <div class="card-body text-center" style="background-color: #1e1e1e !important;">
                                        <h3 class="mb-0" style="color: #ffffff !important;">{{ user_certificates.count }}</h3>
                                        <p class="text-muted mb-0" style="color: #cccccc !important;">Certificates</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-6 mb-3">
                                <div class="card bg-light" style="background-color: #1e1e1e !important; border-color: #333333 !important;">
                                    <div class="card-body text-center" style="background-color: #1e1e1e !important;">
                                        <h3 class="mb-0" style="color: #ffffff !important;">{{ user_sessions.first.performance_score|default:"0" }}</h3>
                                        <p class="text-muted mb-0" style="color: #cccccc !important;">Highest Score</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card shadow mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-gamepad me-2"></i>Game Sessions</h5>
            </div>
            <div class="card-body">
                {% if user_sessions %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Current Role</th>
                                <th>Score</th>
                                <th>Challenges Completed</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in user_sessions %}
                            <tr>
                                <td>{{ session.updated_at|date:"M d, Y H:i" }}</td>
                                <td>{{ session.current_role|title }}</td>
                                <td>{{ session.performance_score }}</td>
                                <td>{{ session.challenges_completed }}</td>
                                <td>
                                    {% if session.game_completed %}
                                    <span class="badge bg-success">Completed</span>
                                    {% else %}
                                    <span class="badge bg-primary">In Progress</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <a href="{% url 'game:index' %}{% if company %}?company={{ company.slug }}{% endif %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>View
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No game sessions found for this user.
                </div>
                {% endif %}
            </div>
        </div>

        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-certificate me-2"></i>Certificates</h5>
            </div>
            <div class="card-body">
                {% if user_certificates %}
                <div class="row">
                    {% for certificate in user_certificates %}
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body text-center">
                                <div class="certificate-icon mb-3">
                                    <i class="fas fa-certificate fa-4x text-primary"></i>
                                </div>
                                <h5 class="card-title">{{ certificate.title }}</h5>
                                <p class="card-text text-muted">Issued on {{ certificate.issue_date|date:"F j, Y" }}</p>
                                <a href="{% url 'corporate:download_certificate' certificate_id=certificate.id %}" class="btn btn-primary">
                                    <i class="fas fa-download me-1"></i>Download
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No certificates found for this user.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
