from django.test import TestCase
from django.template import Template, Context
from django.contrib.auth.models import User
from django.utils import timezone
from accounts.models import Company
import hashlib
from datetime import timedelta

class AccountTagsTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )
    
    def render_template(self, template_string, context=None):
        """Helper method to render template strings with the given context"""
        template = Template(
            '{% load account_tags %}' + template_string
        )
        return template.render(Context(context or {}))
    
    def test_md5_filter(self):
        """Test MD5 hash generation filter"""
        email = '<EMAIL>'
        expected = hashlib.md5(email.lower().encode('utf-8')).hexdigest()
        rendered = self.render_template(
            "{{ email|md5 }}",
            {'email': email}
        )
        self.assertEqual(rendered, expected)
    
    def test_company_role_filter(self):
        """Test company role filter"""
        # Test owner role
        rendered = self.render_template(
            "{{ user|company_role:company }}",
            {'user': self.user, 'company': self.company}
        )
        self.assertEqual(rendered, 'owner')
        
        # Test member role
        member = User.objects.create_user(
            username='member',
            email='<EMAIL>',
            password='member123'
        )
        self.company.members.add(member)
        rendered = self.render_template(
            "{{ user|company_role:company }}",
            {'user': member, 'company': self.company}
        )
        self.assertEqual(rendered, 'member')
        
        # Test non-member
        non_member = User.objects.create_user(
            username='nonmember',
            email='<EMAIL>',
            password='nonmember123'
        )
        rendered = self.render_template(
            "{{ user|company_role:company }}",
            {'user': non_member, 'company': self.company}
        )
        self.assertEqual(rendered, '')
    
    def test_gravatar_url_tag(self):
        """Test Gravatar URL generation tag"""
        email = '<EMAIL>'
        email_hash = hashlib.md5(email.lower().encode('utf-8')).hexdigest()
        
        # Test default size
        rendered = self.render_template(
            "{% gravatar_url email %}",
            {'email': email}
        )
        self.assertIn(email_hash, rendered)
        self.assertIn('s=40', rendered)
        
        # Test custom size
        rendered = self.render_template(
            "{% gravatar_url email 80 %}",
            {'email': email}
        )
        self.assertIn('s=80', rendered)
        
        # Test custom default image
        rendered = self.render_template(
            "{% gravatar_url email 40 'retro' %}",
            {'email': email}
        )
        self.assertIn('d=retro', rendered)
    
    def test_initials_filter(self):
        """Test initials generation filter"""
        # Test full name
        rendered = self.render_template(
            "{{ name|initials }}",
            {'name': 'John Doe'}
        )
        self.assertEqual(rendered, 'JD')
        
        # Test single name
        rendered = self.render_template(
            "{{ name|initials }}",
            {'name': 'John'}
        )
        self.assertEqual(rendered, 'J')
        
        # Test empty string
        rendered = self.render_template(
            "{{ name|initials }}",
            {'name': ''}
        )
        self.assertEqual(rendered, '?')
    
    def test_format_activity_date_filter(self):
        """Test activity date formatting filter"""
        now = timezone.now()
        
        # Test same day
        timestamp = now - timedelta(hours=2)
        rendered = self.render_template(
            "{{ timestamp|format_activity_date }}",
            {'timestamp': timestamp}
        )
        self.assertIn(':', rendered)  # Should show time
        
        # Test within week
        timestamp = now - timedelta(days=3)
        rendered = self.render_template(
            "{{ timestamp|format_activity_date }}",
            {'timestamp': timestamp}
        )
        self.assertIn('ago', rendered)  # Should show "X days ago"
        
        # Test older date
        timestamp = now - timedelta(days=30)
        rendered = self.render_template(
            "{{ timestamp|format_activity_date }}",
            {'timestamp': timestamp}
        )
        self.assertNotIn('ago', rendered)  # Should show full date
    
    def test_company_member_card_tag(self):
        """Test company member card inclusion tag"""
        rendered = self.render_template(
            "{% company_member_card member company %}",
            {'member': self.user, 'company': self.company}
        )
        # Check required elements
        self.assertIn(self.user.username, rendered)
        self.assertIn('Owner', rendered)
        self.assertIn(self.user.email, rendered)
        self.assertIn('Member since', rendered)
    
    def test_activity_item_tag(self):
        """Test activity item inclusion tag"""
        activity = {
            'title': 'Test Activity',
            'description': 'Test Description',
            'timestamp': timezone.now(),
            'icon': 'bi-star',
            'link_url': '/test/',
            'link_text': 'View Details',
            'related_data': {
                'Status': 'Complete',
                'Type': 'Test'
            }
        }
        rendered = self.render_template(
            "{% activity_item activity %}",
            {'activity': activity}
        )
        # Check required elements
        self.assertIn(activity['title'], rendered)
        self.assertIn(activity['description'], rendered)
        self.assertIn(activity['icon'], rendered)
        self.assertIn(activity['link_url'], rendered)
        self.assertIn(activity['link_text'], rendered)
        self.assertIn('Status', rendered)
        self.assertIn('Complete', rendered)
