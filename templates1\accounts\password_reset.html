{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Reset Password - 24seven{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="form-container mt-4">
                <h2 class="text-center mb-4">Reset Password</h2>

                {% if form.errors %}
                    <div class="alert alert-danger">
                        <h5>Please correct the following errors:</h5>
                        <ul class="mb-0">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <li>{{ field|title }}: {{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                {% endif %}

                <p class="text-muted mb-4">
                    Forgotten your password? Enter your email address below, and we'll send you instructions
                    for setting a new one.
                </p>

                <form method="post">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="id_email" class="form-label">Email Address</label>
                        <input type="email" name="email" id="id_email" class="form-control" required>
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Send Reset Link</button>
                    </div>
                </form>

                <hr>

                <div class="text-center">
                    <p>Remember your password?</p>
                    <a href="{% url 'accounts:login' %}" class="btn btn-outline-primary">Back to Login</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
