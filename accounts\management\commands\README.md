# Custom Management Commands

This directory contains custom Django management commands for the Company Assistant project.

## Available Commands

### `toggle_debug`
Toggle Django's DEBUG setting on/off for development purposes.

```bash
python manage.py toggle_debug
```

Options:
- None

Example:
```bash
$ python manage.py toggle_debug
Debug mode is now: True
```

### `test_setup`
Create test data for development and testing purposes.

```bash
python manage.py test_setup [options]
```

Options:
- `--cleanup`: Remove all test data instead of creating it
- `--force`: Force creation of test data even if it already exists

Example:
```bash
# Create test data
$ python manage.py test_setup

# Clean up test data
$ python manage.py test_setup --cleanup

# Force recreation of test data
$ python manage.py test_setup --force
```

Created test data includes:
- Test user with username 'test_user'
- Test company owned by the test user
- Default AI assistant for the test company
- Sample content and settings

## Writing New Commands

To create a new management command:

1. Create a new Python file in this directory with the command name (e.g., `mycommand.py`)
2. Extend `BaseCommand` from Django's management framework
3. Implement the required `handle()` method
4. Add documentation in this README

Example:
```python
from django.core.management.base import BaseCommand

class Command(BaseCommand):
    help = 'Description of what the command does'

    def add_arguments(self, parser):
        parser.add_argument('--option', type=str, help='Description of option')

    def handle(self, *args, **options):
        # Command implementation here
        self.stdout.write(self.style.SUCCESS('Command completed'))
```

## Testing Commands

Commands should be tested in `accounts/tests/test_commands.py`. Each command should have:
- Basic functionality tests
- Option/argument tests
- Error handling tests
- Output verification tests

Example test:
```python
from io import StringIO
from django.core.management import call_command
from django.test import TestCase

class CommandTests(TestCase):
    def setUp(self):
        self.out = StringIO()
        self.err = StringIO()

    def test_mycommand(self):
        call_command('mycommand', stdout=self.out)
        self.assertIn('Expected output', self.out.getvalue())
