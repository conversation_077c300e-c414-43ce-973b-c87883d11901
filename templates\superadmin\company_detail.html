{% extends 'superadmin/base_superadmin.html' %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block superadmin_content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ title }}</h1>
        <a href="{% url 'superadmin:company_list' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">
            <i class="bi bi-arrow-left"></i> {% trans "Back to Companies" %}
        </a>
    </div>

    <!-- Company Info Card -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "Company Information" %}</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 30%">{% trans "ID" %}</th>
                                <td>{{ company.id }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Name" %}</th>
                                <td>{{ company.name }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Owner" %}</th>
                                <td>
                                    {% if company.owner %}
                                    <a href="{% url 'superadmin:user_detail' company.owner.id %}">
                                        {{ company.owner.username }}
                                    </a>
                                    {% else %}
                                    {% trans "No owner assigned" %}
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>{% trans "Status" %}</th>
                                <td>
                                    {% if company.pending_approval %}
                                    <span class="badge bg-warning text-dark">{% trans "Pending Approval" %}</span>
                                    {% elif company.is_active %}
                                    <span class="badge bg-success">{% trans "Active" %}</span>
                                    {% else %}
                                    <span class="badge bg-danger">{% trans "Inactive" %}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% if company.approved_at %}
                            <tr>
                                <th>{% trans "Approved At" %}</th>
                                <td>{{ company.approved_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Approved By" %}</th>
                                <td>{{ company.approved_by.username|default:"Unknown" }}</td>
                            </tr>
                            {% endif %}
                            <tr>
                                <th>{% trans "Created" %}</th>
                                <td>{{ company.created_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Last Updated" %}</th>
                                <td>{{ company.updated_at|date:"Y-m-d H:i" }}</td>
                            </tr>
                            <tr>
                                <th>{% trans "Description" %}</th>
                                <td>{{ company.description|default:"No description provided"|linebreaks }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="mt-3">
                        <form action="{% url 'superadmin:company_activate_toggle' company.pk %}" method="post" class="d-inline-block">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-sm
                                {% if company.pending_approval %}btn-outline-success
                                {% elif company.is_active %}btn-outline-danger
                                {% else %}btn-outline-success{% endif %}">
                                {% if company.pending_approval %}{% trans "Approve" %}
                                {% elif company.is_active %}{% trans "Deactivate" %}
                                {% else %}{% trans "Activate" %}{% endif %}
                            </button>
                        </form>
                        {% if company.owner %}
                        <form action="{% url 'superadmin:company_impersonate' company.owner.pk %}" method="post" class="d-inline-block ms-2">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-sm btn-outline-primary">
                                {% trans "Impersonate Owner" %}
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{% trans "Company Statistics" %}</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-4">
                            <div class="card border-left-primary h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                {% trans "Users" %}</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ user_count }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-people fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-left-success h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                                {% trans "Leaderboards" %}</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ leaderboard_count }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-trophy fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4 mb-4">
                            <div class="card border-left-info h-100 py-2">
                                <div class="card-body">
                                    <div class="row no-gutters align-items-center">
                                        <div class="col mr-2">
                                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                                {% trans "Game Sessions" %}</div>
                                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ game_session_count }}</div>
                                        </div>
                                        <div class="col-auto">
                                            <i class="bi bi-controller fa-2x text-gray-300"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if top_users %}
                    <div class="mt-4">
                        <h6 class="font-weight-bold">{% trans "Top Users by Score" %}</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>{% trans "User" %}</th>
                                        <th>{% trans "Total Score" %}</th>
                                        <th>{% trans "Entries" %}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for user in top_users %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'superadmin:user_detail' user.id %}">
                                                {{ user.username }}
                                            </a>
                                        </td>
                                        <td>{{ user.total_score }}</td>
                                        <td>{{ user.entry_count }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tabs for Users, Leaderboards, and Game Sessions -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <ul class="nav nav-tabs card-header-tabs" id="companyTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="users-tab" data-bs-toggle="tab" data-bs-target="#users" type="button" role="tab" aria-controls="users" aria-selected="true">
                        {% trans "Users" %} ({{ user_count }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="leaderboards-tab" data-bs-toggle="tab" data-bs-target="#leaderboards" type="button" role="tab" aria-controls="leaderboards" aria-selected="false">
                        {% trans "Leaderboards" %} ({{ leaderboard_count }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="sessions-tab" data-bs-toggle="tab" data-bs-target="#sessions" type="button" role="tab" aria-controls="sessions" aria-selected="false">
                        {% trans "Game Sessions" %} ({{ game_session_count }})
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body">
            <div class="tab-content" id="companyTabsContent">
                <!-- Users Tab -->
                <div class="tab-pane fade show active" id="users" role="tabpanel" aria-labelledby="users-tab">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>{% trans "Username" %}</th>
                                    <th>{% trans "Email" %}</th>
                                    <th>{% trans "Job Title" %}</th>
                                    <th>{% trans "Admin" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in company_users %}
                                <tr>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.email }}</td>
                                    <td>{{ user.corporate_profile.job_title|default:"-" }}</td>
                                    <td>
                                        {% if user.corporate_profile.is_company_admin %}
                                        <span class="badge bg-success">{% trans "Yes" %}</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{% trans "No" %}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{% url 'superadmin:user_detail' user.id %}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> {% trans "View" %}
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">{% trans "No users found for this company." %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Leaderboards Tab -->
                <div class="tab-pane fade" id="leaderboards" role="tabpanel" aria-labelledby="leaderboards-tab">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>{% trans "Name" %}</th>
                                    <th>{% trans "Time Period" %}</th>
                                    <th>{% trans "Entries" %}</th>
                                    <th>{% trans "Created" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leaderboard in leaderboards %}
                                <tr>
                                    <td>{{ leaderboard.name }}</td>
                                    <td>{{ leaderboard.get_time_period_display }}</td>
                                    <td>{{ leaderboard.entry_count }}</td>
                                    <td>{{ leaderboard.created_at|date:"Y-m-d" }}</td>
                                    <td>
                                        <a href="{% url 'superadmin:leaderboard_detail' leaderboard.id %}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> {% trans "View" %}
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">{% trans "No leaderboards found for this company." %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Game Sessions Tab -->
                <div class="tab-pane fade" id="sessions" role="tabpanel" aria-labelledby="sessions-tab">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>{% trans "User" %}</th>
                                    <th>{% trans "Score" %}</th>
                                    <th>{% trans "Role" %}</th>
                                    <th>{% trans "Created" %}</th>
                                    <th>{% trans "Actions" %}</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in game_sessions %}
                                <tr>
                                    <td>{{ session.user.username }}</td>
                                    <td>{{ session.performance_score }}</td>
                                    <td>{{ session.current_role|default:"-"|title }}</td>
                                    <td>{{ session.created_at|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <a href="{% url 'superadmin:game_session_detail' session.id %}" class="btn btn-sm btn-info">
                                            <i class="bi bi-eye"></i> {% trans "View" %}
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center">{% trans "No game sessions found for this company." %}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock superadmin_content %}
