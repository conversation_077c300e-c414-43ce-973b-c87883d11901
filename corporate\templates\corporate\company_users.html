{% extends 'corporate/base.html' %}

{% block title %}Manage Users - {{ company.name }}{% endblock %}

{% block extra_css %}
{% include 'corporate/company_users_extra.html' %}
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2 class="mb-3" style="color: #ffffff !important; font-weight: bold !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Manage Company Users</h2>

        {% if pending_users %}
        <div class="card shadow mb-4 border-warning">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center" style="padding: 15px !important;">
                <h5 class="mb-0" style="font-weight: bold !important; font-size: 18px !important;">
                    <i class="fas fa-user-clock me-2"></i>Pending Approval Requests
                    <span class="badge rounded-pill bg-danger ms-2">{{ pending_users|length }}</span>
                </h5>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-dark" data-bs-toggle="collapse" data-bs-target="#pendingUsersCollapse" aria-expanded="true">
                        <i class="fas fa-chevron-down"></i>
                    </button>
                </div>
            </div>
            <div class="collapse show" id="pendingUsersCollapse">
                <div class="card-body">
                    <div class="alert alert-info mb-3">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>These users have registered using invitation links that require admin approval. Review and approve or reject their requests.</span>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead style="background-color: #ffc107 !important;">
                                <tr>
                                    <th style="color: #212529 !important; font-weight: bold !important; font-size: 16px !important;">User</th>
                                    <th style="color: #212529 !important; font-weight: bold !important; font-size: 16px !important;">Email</th>
                                    <th style="color: #212529 !important; font-weight: bold !important; font-size: 16px !important;">Requested Role</th>
                                    <th style="color: #212529 !important; font-weight: bold !important; font-size: 16px !important;">Registration Date</th>
                                    <th style="color: #212529 !important; font-weight: bold !important; font-size: 16px !important;">Actions</th>
                                </tr>
                            </thead>
                        <tbody>
                            {% for corp_user in pending_users %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-2">
                                            {% if corp_user.user.profile.avatar %}
                                            <img src="{{ corp_user.user.profile.avatar.url }}" alt="Avatar" class="rounded-circle" width="40">
                                            {% else %}
                                            <div class="avatar-placeholder rounded-circle text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: #ffc107; color: #212529; font-weight: bold; font-size: 16px;">
                                                {{ corp_user.user.username|first|upper }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="user-name" style="color: #ffffff !important; font-weight: bold !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">{{ corp_user.user.get_full_name|default:corp_user.user.username }}</h6>
                                            <small class="user-handle" style="color: #ffffff !important;">@{{ corp_user.user.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td style="color: #ffffff !important;">{{ corp_user.user.email }}</td>
                                <td>
                                    {% if corp_user.is_company_admin %}
                                    <span class="badge bg-danger" style="border: 1px solid rgba(255, 255, 255, 0.3) !important; color: #ffffff !important; font-weight: bold !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">Admin</span>
                                    {% else %}
                                    <span class="badge bg-secondary" style="border: 1px solid rgba(255, 255, 255, 0.3) !important; color: #ffffff !important; font-weight: bold !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">Member</span>
                                    {% endif %}
                                </td>
                                <td style="color: #ffffff !important;">{{ corp_user.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <div class="btn-group">
                                        <form method="post" action="{% url 'corporate:company_users' %}" class="me-1">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="approve_user">
                                            <input type="hidden" name="user_id" value="{{ corp_user.user.id }}">
                                            <button type="submit" class="btn btn-sm btn-success">
                                                <i class="fas fa-check me-1"></i> Approve
                                            </button>
                                        </form>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#rejectUserModal{{ corp_user.user.id }}">
                                            <i class="fas fa-times me-1"></i> Reject
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <!-- Reject User Modal -->
                            <div class="modal fade" id="rejectUserModal{{ corp_user.user.id }}" tabindex="-1" aria-labelledby="rejectUserModalLabel{{ corp_user.user.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="rejectUserModalLabel{{ corp_user.user.id }}">Reject User Request</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to reject <strong style="color: #ffffff !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">{{ corp_user.user.get_full_name|default:corp_user.user.username }}</strong>'s request to join {{ company.name }}?</p>
                                            <p class="text-danger">This action cannot be undone.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <form method="post" action="{% url 'corporate:company_users' %}">
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="reject_user">
                                                <input type="hidden" name="user_id" value="{{ corp_user.user.id }}">
                                                <button type="submit" class="btn btn-danger">Reject Request</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center" style="background-color: #0d6efd !important; padding: 15px !important;">
                <h5 class="mb-0" style="color: #ffffff !important; font-weight: bold !important; font-size: 18px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;"><i class="fas fa-users me-2"></i>{{ company.name }} Team</h5>
                <div>
                    <a href="{% url 'corporate:company_invitations' %}" class="btn btn-light btn-sm me-2" style="font-weight: bold !important; color: #0d6efd !important;">
                        <i class="fas fa-envelope me-1"></i> Manage Invitations
                        {% if pending_users_count > 0 %}
                        <span class="badge rounded-pill bg-danger">{{ pending_users_count }}</span>
                        {% endif %}
                    </a>
                    <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#inviteUserModal" style="font-weight: bold !important; color: #0d6efd !important;">
                        <i class="fas fa-user-plus me-1"></i> Invite User
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if company_users %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead style="background-color: #0d6efd !important;">
                            <tr>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">User</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Email</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Job Title</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Role</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Joined</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for corp_user in company_users %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-2">
                                            {% if corp_user.user.profile.avatar %}
                                            <img src="{{ corp_user.user.profile.avatar.url }}" alt="Avatar" class="rounded-circle" width="40">
                                            {% else %}
                                            <div class="avatar-placeholder rounded-circle text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: #0d6efd; color: white; font-weight: bold; font-size: 16px;">
                                                {{ corp_user.user.username|first|upper }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="user-name" style="color: #ffffff !important; font-weight: bold !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">{{ corp_user.user.get_full_name|default:corp_user.user.username }}</h6>
                                            <small class="user-handle" style="color: #ffffff !important;">@{{ corp_user.user.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td style="color: #ffffff !important;">{{ corp_user.user.email }}</td>
                                <td>
                                    {% if corp_user.job_title %}
                                        <span style="color: #ffffff !important;">{{ corp_user.job_title }}</span>
                                    {% else %}
                                        <span style="color: #ffffff !important; font-style: italic;">Not specified</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if corp_user.is_company_admin %}
                                    <span class="badge bg-danger" style="border: 1px solid rgba(255, 255, 255, 0.3) !important; color: #ffffff !important; font-weight: bold !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">Admin</span>
                                    {% else %}
                                    <span class="badge bg-secondary" style="border: 1px solid rgba(255, 255, 255, 0.3) !important; color: #ffffff !important; font-weight: bold !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">Member</span>
                                    {% endif %}
                                </td>
                                <td style="color: #ffffff !important;">{{ corp_user.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{% url 'corporate:user_progress_detail' user_id=corp_user.user.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                        {% if corp_user.user != request.user %}
                                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#removeUserModal{{ corp_user.user.id }}">
                                            <i class="fas fa-user-minus"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>

                            <!-- Remove User Modal -->
                            <div class="modal fade" id="removeUserModal{{ corp_user.user.id }}" tabindex="-1" aria-labelledby="removeUserModalLabel{{ corp_user.user.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="removeUserModalLabel{{ corp_user.user.id }}">Remove User</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to remove <strong style="color: #ffffff !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">{{ corp_user.user.get_full_name|default:corp_user.user.username }}</strong> from {{ company.name }}?</p>
                                            <p class="text-danger">This action cannot be undone.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <form method="post" action="{% url 'corporate:company_users' %}">
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="remove_user">
                                                <input type="hidden" name="user_id" value="{{ corp_user.user.id }}">
                                                <button type="submit" class="btn btn-danger">Remove User</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No users have been added to this company yet.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Invite User Modal -->
<div class="modal fade" id="inviteUserModal" tabindex="-1" aria-labelledby="inviteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="invite_user">
                <div class="modal-header">
                    <h5 class="modal-title" id="inviteUserModalLabel">Invite User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="{{ invite_form.emails.id_for_label }}" class="form-label">Email Addresses</label>
                        {{ invite_form.emails }}
                        <div class="form-text">{{ invite_form.emails.help_text }}</div>
                        {% if invite_form.emails.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in invite_form.emails.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="{{ invite_form.message.id_for_label }}" class="form-label">Personal Message (Optional)</label>
                        {{ invite_form.message }}
                        <div class="form-text">{{ invite_form.message.help_text }}</div>
                        {% if invite_form.message.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in invite_form.message.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Invitations</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form elements
        const emailsTextarea = document.getElementById('{{ invite_form.emails.id_for_label }}');
        if (emailsTextarea) {
            emailsTextarea.classList.add('form-control');
            emailsTextarea.placeholder = 'Enter email addresses, one per line';
        }

        const messageTextarea = document.getElementById('{{ invite_form.message.id_for_label }}');
        if (messageTextarea) {
            messageTextarea.classList.add('form-control');
            messageTextarea.placeholder = 'Optional message to include in the invitation';
        }
    });
</script>
{% endblock %}
