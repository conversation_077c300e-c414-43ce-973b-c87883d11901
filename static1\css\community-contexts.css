/* Context Links Styling */
.context-links {
    border-top: 1px solid var(--bs-border-color);
    margin-top: 0.75rem;
    padding-top: 0.75rem;
}

.context-links-header {
    display: flex;
    align-items: center;
    color: var(--bs-secondary);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.context-links-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.context-link {
    display: inline-flex;
    align-items: center;
    color: var(--bs-primary);
    text-decoration: none;
    font-size: 0.9rem;
    padding: 0.25rem 0.5rem;
    background-color: rgba(13, 110, 253, 0.1);
    border-radius: 0.25rem;
    transition: all 0.2s ease;
}

.context-link:hover {
    color: var(--bs-primary);
    background-color: rgba(13, 110, 253, 0.15);
    text-decoration: none;
    transform: translateY(-1px);
}

/* Context Modal Styling */
.context-content {
    font-size: 0.95rem;
    line-height: 1.6;
    white-space: pre-wrap;
    padding: 1rem;
    background-color: var(--bs-light);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.context-metadata {
    display: flex;
    align-items: center;
    margin-left: 1rem;
    padding: 0.25rem 0.5rem;
    background-color: var(--bs-light);
    border-radius: 0.25rem;
    font-size: 0.85rem;
    white-space: nowrap;
}

.context-metadata i {
    margin-right: 0.25rem;
    color: var(--bs-secondary);
}

/* Modal Customization */
#contextModal .modal-header {
    border-bottom-color: rgba(0, 0, 0, 0.05);
    background-color: rgba(248, 249, 250, 0.5);
}

#contextModal .modal-footer {
    border-top-color: rgba(0, 0, 0, 0.05);
    background-color: rgba(248, 249, 250, 0.5);
}

#contextModal .modal-content {
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

#contextModal .modal-body {
    padding: 1.5rem;
    background-color: #ffffff;
}

/* Loading State */
#contextModal .loading-state {
    text-align: center;
    padding: 2rem;
}

#contextModal .loading-state .spinner-border {
    width: 3rem;
    height: 3rem;
    margin-bottom: 1rem;
}

#contextModal .loading-state p {
    color: var(--bs-secondary);
    margin: 0;
}
