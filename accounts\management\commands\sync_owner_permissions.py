from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission
from guardian.shortcuts import assign_perm
from accounts.models import Company
from accounts.permissions import OWNER_PERMS_COMPANY

class Command(BaseCommand):
    help = 'Ensures all company owners have the permissions defined in OWNER_PERMS_COMPANY.'

    def handle(self, *args, **options):
        self.stdout.write("Starting to sync owner permissions...")
        companies_processed = 0
        permissions_assigned = 0

        for company in Company.objects.select_related('owner').all():
            owner = company.owner
            if not owner:
                self.stdout.write(self.style.WARNING(f"Company '{company.name}' (ID: {company.id}) has no owner. Skipping."))
                continue

            companies_processed += 1
            self.stdout.write(f"Processing Company: '{company.name}' (Owner: {owner.username})")

            for perm_codename in OWNER_PERMS_COMPANY:
                try:
                    # Check if the user already has the permission for this object
                    # Note: has_perm checks *both* direct assignment and group permissions.
                    # For this command, we specifically want to ensure direct assignment.
                    # assign_perm handles duplicates gracefully (doesn't create extra entries).
                    assign_perm(perm_codename, owner, company)
                    # Optional: Add more verbose logging if needed to see *which* permissions were newly assigned vs already present.
                    # For simplicity, we just call assign_perm.
                    permissions_assigned += 1 # Count attempts, not just new assignments for simplicity here.

                except Permission.DoesNotExist:
                    self.stdout.write(self.style.ERROR(f"  Permission '{perm_codename}' not found in the system. Skipping assignment."))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"  Error assigning perm '{perm_codename}' to owner '{owner.username}' for company '{company.name}': {e}"))

            self.stdout.write(self.style.SUCCESS(f"  Finished processing permissions for '{company.name}'."))

        self.stdout.write(self.style.SUCCESS(
            f"\nPermissions sync complete. Processed {companies_processed} companies. "
            f"Attempted to assign/verify {permissions_assigned} permissions in total."
        ))
