/**
 * Assistant Dark Mode CSS
 * Specific dark mode styling for the assistant chat interface
 */

/* Dark mode styles for the assistant chat interface */
[data-theme="dark"] .assistant-container {
  background-color: var(--dark-bg-primary);
  color: var(--dark-text-primary);
}

/* Header styling */
[data-theme="dark"] .assistant-header,
[data-theme="dark"] .general-assistant-header,
[data-theme="dark"] div.general-assistant-header,
[data-theme="dark"] div.assistant-header {
  background: linear-gradient(135deg, #1e1e1e 0%, #252525 100%) !important;
  background-color: var(--dark-bg-secondary) !important;
  border-bottom: 1px solid var(--dark-border) !important;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .assistant-header h1,
[data-theme="dark"] .assistant-header h2,
[data-theme="dark"] .assistant-header h3,
[data-theme="dark"] .general-assistant-header h1,
[data-theme="dark"] .general-assistant-header h2,
[data-theme="dark"] .general-assistant-header h3 {
  color: var(--dark-text-primary) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

/* General Assistant Header specific styling */
[data-theme="dark"] .general-assistant-header h4 {
  font-weight: 700 !important;
  color: var(--dark-accent-primary) !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
  letter-spacing: 0.02em !important;
  font-size: 1.5rem !important;
  margin-bottom: 0.25rem !important;
  transform: translateZ(0) !important;
  transition: all 0.2s ease !important;
}

[data-theme="dark"] .general-assistant-header h5 {
  font-weight: 600 !important;
  color: var(--dark-text-primary) !important;
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.1) !important;
  letter-spacing: 0.01em !important;
  font-size: 1.2rem !important;
  margin-bottom: 0 !important;
  transform: translateZ(0) !important;
  transition: all 0.2s ease !important;
}

[data-theme="dark"] .general-assistant-header h5.text-muted {
  color: var(--dark-text-secondary) !important;
}

/* Hover effects for headings in dark mode */
[data-theme="dark"] .general-assistant-header h4:hover,
[data-theme="dark"] .general-assistant-header h5:hover {
  transform: translateY(-1px) translateZ(0) !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
}

/* Rating and buttons in the header */
[data-theme="dark"] .general-assistant-header .btn-outline-secondary {
  background-color: rgba(255, 255, 255, 0.05) !important;
  border-color: var(--dark-border) !important;
  color: var(--dark-text-primary) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease !important;
}

[data-theme="dark"] .general-assistant-header .btn-outline-secondary:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: var(--dark-accent-primary) !important;
  color: var(--dark-text-primary) !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3) !important;
  transform: translateY(-1px) !important;
}

/* Horizontal rule in the header */
[data-theme="dark"] .general-assistant-header hr {
  border-color: var(--dark-border) !important;
  opacity: 0.5 !important;
}

/* Glass-look styling for header buttons */
[data-theme="dark"] .glass-button {
  background: rgba(74, 125, 255, 0.2);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(74, 125, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .glass-button:hover {
  background: rgba(74, 125, 255, 0.3);
  border: 1px solid rgba(74, 125, 255, 0.4);
}

/* Sidebar styling */
[data-theme="dark"] .sidebar-container {
  background-color: var(--dark-bg-secondary);
  border-right: 1px solid var(--dark-border);
}

[data-theme="dark"] .sidebar-header {
  background-color: var(--dark-bg-tertiary);
  border-bottom: 1px solid var(--dark-border);
}

[data-theme="dark"] .sidebar-title {
  color: var(--dark-text-primary);
}

[data-theme="dark"] .sidebar-toggle {
  color: var(--dark-text-primary);
  background-color: var(--dark-bg-elevated);
  border: 1px solid var(--dark-border);
}

[data-theme="dark"] .sidebar-toggle:hover {
  background-color: var(--dark-accent-primary);
}

/* Navigation items */
[data-theme="dark"] .list-group-item {
  background-color: var(--dark-bg-tertiary);
  border-color: var(--dark-border);
  color: var(--dark-text-primary);
}

[data-theme="dark"] .list-group-item:hover {
  background-color: var(--dark-bg-elevated);
}

[data-theme="dark"] .list-group-item.active {
  background-color: var(--dark-accent-primary);
  border-color: var(--dark-accent-primary);
  color: white;
}

/* Chat container - with dramatic styling */
[data-theme="dark"] .chat-container,
[data-theme="dark"] .general-chat-container {
  background: linear-gradient(145deg, #1a1a1a, #151515) !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

/* Chat box */
[data-theme="dark"] .chat-box,
[data-theme="dark"] .general-chat-box,
[data-theme="dark"] #chat-box {
  background: #121212 !important;
  border: none !important;
  box-shadow: inset 0 2px 10px rgba(0, 0, 0, 0.2) !important;
  padding: 1.5rem !important;
}

/* User messages - with gradient and glow effect */
[data-theme="dark"] .user-message .message-content {
  background: linear-gradient(145deg, #0066ff, #0055cc) !important;
  color: #ffffff !important;
  border: none !important;
  box-shadow: 0 5px 15px rgba(0, 102, 255, 0.3) !important;
  border-radius: 18px 18px 4px 18px !important;
  padding: 1rem 1.25rem !important;
  font-weight: 500 !important;
  letter-spacing: 0.01em !important;
  position: relative !important;
  overflow: hidden !important;
  transform: translateZ(0) !important;
}

/* Add subtle shine effect to user messages */
[data-theme="dark"] .user-message .message-content::after {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: -50% !important;
  width: 200% !important;
  height: 100% !important;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0) 100%) !important;
  transform: skewX(-20deg) !important;
  pointer-events: none !important;
}

/* Assistant messages - with subtle gradient and modern styling */
[data-theme="dark"] .assistant-message .message-content {
  background: linear-gradient(145deg, #2a2a2a, #222222) !important;
  color: #ffffff !important;
  border: 1px solid #333333 !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  border-radius: 18px 18px 18px 4px !important;
  padding: 1rem 1.25rem !important;
  font-weight: 400 !important;
  letter-spacing: 0.01em !important;
  position: relative !important;
  overflow: hidden !important;
  transform: translateZ(0) !important;
}

/* Navigation content bubbles - with gradient and glow effect */
[data-theme="dark"] .nav-content-bubble .message-content {
  background: linear-gradient(145deg, #252525, #1e1e1e) !important;
  color: #ffffff !important;
  border: 1px solid #333333 !important;
  border-left: 4px solid #0077ff !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  border-radius: 8px !important;
  padding: 1.25rem !important;
  margin-bottom: 1.5rem !important;
  position: relative !important;
  overflow: hidden !important;
}

/* Add subtle glow to navigation bubbles */
[data-theme="dark"] .nav-content-bubble .message-content::before {
  content: "" !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 4px !important;
  height: 100% !important;
  background: #0077ff !important;
  box-shadow: 0 0 15px 2px rgba(0, 119, 255, 0.5) !important;
  opacity: 0.8 !important;
  pointer-events: none !important;
}

/* Chat input - with modern styling and glow effect */
[data-theme="dark"] #chat-input,
[data-theme="dark"] #message-input {
  background-color: #1a1a1a !important;
  color: #ffffff !important;
  border: 1px solid #333333 !important;
  border-radius: 24px !important;
  padding: 0.75rem 1.25rem !important;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 0 0 1px rgba(255, 255, 255, 0.05) !important;
  transition: all 0.3s ease !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
}

[data-theme="dark"] #chat-input:focus,
[data-theme="dark"] #message-input:focus {
  border-color: #0077ff !important;
  box-shadow: 0 0 0 3px rgba(0, 119, 255, 0.25), inset 0 1px 3px rgba(0, 0, 0, 0.2) !important;
  outline: none !important;
}

/* Chat form styling */
[data-theme="dark"] #chat-form {
  background: linear-gradient(to bottom, #1a1a1a, #151515) !important;
  border-top: 1px solid #333333 !important;
  padding: 1rem !important;
  box-shadow: 0 -5px 15px rgba(0, 0, 0, 0.1) !important;
}

/* Send button - with gradient and glow effect */
[data-theme="dark"] #send-button {
  background: linear-gradient(to bottom, #0077ff, #0055cc) !important;
  border: none !important;
  color: white !important;
  border-radius: 50% !important;
  width: 46px !important;
  height: 46px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 4px 10px rgba(0, 102, 255, 0.3) !important;
  transition: all 0.3s ease !important;
  margin-left: 0.5rem !important;
}

[data-theme="dark"] #send-button:hover {
  background: linear-gradient(to bottom, #0088ff, #0066dd) !important;
  box-shadow: 0 6px 15px rgba(0, 102, 255, 0.4) !important;
  transform: translateY(-2px) !important;
}

[data-theme="dark"] #send-button:active {
  background: linear-gradient(to bottom, #0055cc, #0044aa) !important;
  box-shadow: 0 2px 5px rgba(0, 102, 255, 0.3) !important;
  transform: translateY(1px) !important;
}

/* Suggested questions - with glass morphism effect */
[data-theme="dark"] .suggested-question {
  background: rgba(30, 30, 30, 0.7) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border-radius: 20px !important;
  padding: 0.6rem 1.2rem !important;
  margin: 0.5rem 0.25rem !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
  transition: all 0.3s ease !important;
  font-size: 0.9rem !important;
}

[data-theme="dark"] .suggested-question:hover {
  background: rgba(0, 102, 255, 0.2) !important;
  border-color: rgba(0, 119, 255, 0.5) !important;
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(0, 119, 255, 0.3) !important;
  transform: translateY(-2px) !important;
}

[data-theme="dark"] .suggested-question:active {
  transform: translateY(1px) !important;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2) !important;
}

/* Typing indicator - with modern animation */
[data-theme="dark"] .typing-indicator {
  background-color: rgba(30, 30, 30, 0.7) !important;
  backdrop-filter: blur(5px) !important;
  -webkit-backdrop-filter: blur(5px) !important;
  border-radius: 20px !important;
  padding: 0.5rem 1rem !important;
  display: inline-flex !important;
  align-items: center !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
}

[data-theme="dark"] .typing-indicator span {
  background-color: #0077ff !important;
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
  margin: 0 2px !important;
  display: inline-block !important;
  animation: typing-bounce 1.4s infinite ease-in-out both !important;
}

[data-theme="dark"] .typing-indicator span:nth-child(1) {
  animation-delay: -0.32s !important;
}

[data-theme="dark"] .typing-indicator span:nth-child(2) {
  animation-delay: -0.16s !important;
}

@keyframes typing-bounce {
  0%, 80%, 100% {
    transform: scale(0.6);
    opacity: 0.6;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Code blocks in messages - with syntax highlighting colors */
[data-theme="dark"] .message-content pre {
  background-color: #1a1a1a !important;
  color: #e6e6e6 !important;
  border: 1px solid #333333 !important;
  border-radius: 8px !important;
  padding: 1rem !important;
  margin: 1rem 0 !important;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2) !important;
  overflow-x: auto !important;
}

[data-theme="dark"] .message-content code {
  background-color: #252525 !important;
  color: #e6e6e6 !important;
  border: 1px solid #333333 !important;
  border-radius: 4px !important;
  padding: 0.2rem 0.4rem !important;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace !important;
  font-size: 0.9em !important;
}

/* Syntax highlighting for code blocks */
[data-theme="dark"] .message-content pre .keyword,
[data-theme="dark"] .message-content code .keyword {
  color: #ff79c6 !important; /* Pink */
}

[data-theme="dark"] .message-content pre .string,
[data-theme="dark"] .message-content code .string {
  color: #f1fa8c !important; /* Yellow */
}

[data-theme="dark"] .message-content pre .comment,
[data-theme="dark"] .message-content code .comment {
  color: #6272a4 !important; /* Blue-gray */
}

[data-theme="dark"] .message-content pre .function,
[data-theme="dark"] .message-content code .function {
  color: #50fa7b !important; /* Green */
}

[data-theme="dark"] .message-content pre .number,
[data-theme="dark"] .message-content code .number {
  color: #bd93f9 !important; /* Purple */
}

/* Tables in messages */
[data-theme="dark"] .message-content table {
  border-color: var(--dark-border);
}

[data-theme="dark"] .message-content th,
[data-theme="dark"] .message-content td {
  border-color: var(--dark-border);
}

[data-theme="dark"] .message-content th {
  background-color: var(--dark-bg-tertiary);
}

[data-theme="dark"] .message-content tr:nth-child(even) {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Links in messages */
[data-theme="dark"] .message-content a {
  color: var(--dark-accent-primary);
}

[data-theme="dark"] .message-content a:hover {
  color: #6a93ff;
}

/* Blockquotes in messages */
[data-theme="dark"] .message-content blockquote {
  border-left: 4px solid var(--dark-border);
  background-color: var(--dark-bg-tertiary);
  color: var(--dark-text-secondary);
}

/* Lists in messages */
[data-theme="dark"] .message-content ul,
[data-theme="dark"] .message-content ol {
  color: var(--dark-text-primary);
}

/* Images in messages */
[data-theme="dark"] .message-content img {
  border: 1px solid var(--dark-border);
  background-color: var(--dark-bg-tertiary);
}

/* Headings in messages */
[data-theme="dark"] .message-content h1,
[data-theme="dark"] .message-content h2,
[data-theme="dark"] .message-content h3,
[data-theme="dark"] .message-content h4,
[data-theme="dark"] .message-content h5,
[data-theme="dark"] .message-content h6 {
  color: var(--dark-text-primary);
}

/* Horizontal rule in messages */
[data-theme="dark"] .message-content hr {
  border-color: var(--dark-border);
}

/* Ensure all icons are visible and have transparent backgrounds */
[data-theme="dark"] .bi,
[data-theme="dark"] i.bi,
[data-theme="dark"] i.fa,
[data-theme="dark"] i.fas,
[data-theme="dark"] i.far,
[data-theme="dark"] i.fab,
[data-theme="dark"] button i,
[data-theme="dark"] .btn i {
  color: inherit;
  background-color: transparent !important;
  background: none !important;
  box-shadow: none !important;
}

/* Fix for any hardcoded colors in SVGs */
[data-theme="dark"] svg {
  filter: brightness(0.8) contrast(1.2);
}

/* Ensure links are visible */
[data-theme="dark"] a {
  color: var(--dark-accent-primary);
}

[data-theme="dark"] a:hover {
  color: #6a93ff;
}

/* Fix for any hardcoded background colors */
[data-theme="dark"] [style*="background-color: white"],
[data-theme="dark"] [style*="background-color: #fff"],
[data-theme="dark"] [style*="background-color: rgb(255, 255, 255)"],
[data-theme="dark"] [style*="background-color: rgba(255, 255, 255"] {
  background-color: var(--dark-bg-tertiary) !important;
  color: var(--dark-text-primary) !important;
}

/* Fix for any hardcoded text colors */
[data-theme="dark"] [style*="color: black"],
[data-theme="dark"] [style*="color: #000"],
[data-theme="dark"] [style*="color: rgb(0, 0, 0)"],
[data-theme="dark"] [style*="color: rgba(0, 0, 0"] {
  color: var(--dark-text-primary) !important;
}
