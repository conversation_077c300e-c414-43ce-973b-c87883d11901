from django.test import TestCase
from django.contrib.auth.models import User
from django.core.files.uploadedfile import SimpleUploadedFile
from django.utils import timezone
from ..models import Company, CompanyInvitation
import uuid
import os

class CompanyTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )
    
    def test_company_creation(self):
        """Test that a company is created correctly"""
        self.assertEqual(self.company.name, 'Test Company')
        self.assertEqual(self.company.owner, self.user)
        self.assertTrue(isinstance(self.company.uuid, uuid.UUID))
        self.assertTrue(self.company.qr_code)
        self.assertTrue(os.path.exists(self.company.qr_code.path))
    
    def test_company_str(self):
        """Test the string representation of Company"""
        self.assertEqual(str(self.company), 'Test Company')
    
    def test_company_user_mixin(self):
        """Test the CompanyUserMixin functionality"""
        self.assertTrue(self.user.has_company)
        self.assertEqual(self.user.current_company, self.company)
        self.assertTrue(self.user.is_company_owner(self.company))
        self.assertEqual(self.user.get_company_role(self.company), 'owner')
        
        # Test with another user as member
        member = User.objects.create_user(
            username='member',
            email='<EMAIL>',
            password='member123'
        )
        self.company.members.add(member)
        self.assertTrue(member.has_company)
        self.assertTrue(member.is_company_member(self.company))
        self.assertEqual(member.get_company_role(self.company), 'member')
    
    def test_company_delete(self):
        """Test that company deletion cleans up files"""
        qr_code_path = self.company.qr_code.path
        self.company.delete()
        self.assertFalse(os.path.exists(qr_code_path))
    
    def tearDown(self):
        # Clean up any generated files
        if self.company.qr_code and os.path.exists(self.company.qr_code.path):
            os.remove(self.company.qr_code.path)

class CompanyInvitationTests(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        self.company = Company.objects.create(
            name='Test Company',
            owner=self.user
        )
        self.invitation = CompanyInvitation.objects.create(
            company=self.company,
            email='<EMAIL>'
        )
    
    def test_invitation_creation(self):
        """Test that an invitation is created correctly"""
        self.assertEqual(self.invitation.company, self.company)
        self.assertEqual(self.invitation.email, '<EMAIL>')
        self.assertFalse(self.invitation.accepted)
        self.assertIsNone(self.invitation.accepted_at)
        self.assertTrue(isinstance(self.invitation.token, uuid.UUID))
    
    def test_invitation_acceptance(self):
        """Test invitation acceptance process"""
        self.invitation.accepted = True
        self.invitation.accepted_at = timezone.now()
        self.invitation.save()
        
        self.assertTrue(self.invitation.accepted)
        self.assertIsNotNone(self.invitation.accepted_at)
    
    def test_invitation_str(self):
        """Test the string representation of CompanyInvitation"""
        expected = f"{self.company.name} - {self.invitation.email}"
        self.assertEqual(str(self.invitation), expected)
    
    def test_unique_invitation(self):
        """Test that duplicate invitations are not allowed"""
        with self.assertRaises(Exception):
            CompanyInvitation.objects.create(
                company=self.company,
                email='<EMAIL>'
            )
