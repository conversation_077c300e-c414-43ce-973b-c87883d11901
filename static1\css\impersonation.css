/* Impersonation styles */
.impersonation-indicator,
#impersonation-indicator,
#impersonation-alert {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    width: 100% !important;
    background-color: #dc3545 !important;
    color: white !important;
    text-align: center !important;
    padding: 8px !important;
    z-index: 9999 !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3) !important;
    font-weight: bold !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.impersonation-indicator a,
#impersonation-indicator a,
#impersonation-alert a {
    color: white !important;
    text-decoration: none !important;
    margin: 0 10px !important;
}

.impersonation-indicator a:hover,
#impersonation-indicator a:hover,
#impersonation-alert a:hover {
    color: #f8f9fa !important;
}

.impersonation-indicator .btn,
#impersonation-indicator .btn,
#impersonation-alert .btn {
    margin-left: 10px !important;
    background-color: transparent !important;
    color: white !important;
    border: 1px solid white !important;
    font-weight: normal !important;
    font-size: 0.875rem !important;
}

.impersonation-indicator .btn:hover,
#impersonation-indicator .btn:hover,
#impersonation-alert .btn:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Add padding to body when impersonation indicator is present */
body.is-impersonating {
    padding-top: 40px !important; /* Height of the impersonation banner */
}

/* Adjust navbar position when impersonation is active */
body.is-impersonating .navbar.sticky-top {
    top: 40px !important; /* Position navbar below the impersonation banner */
}

/* Floating impersonation button */
.impersonation-float-button,
#impersonation-float-button {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    background-color: #dc3545 !important;
    color: white !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3) !important;
    z-index: 9998 !important;
    cursor: pointer !important;
    font-size: 24px !important;
}

.impersonation-float-button:hover,
#impersonation-float-button:hover {
    background-color: #c82333 !important;
}

/* Impersonation dropdown menu */
.impersonation-dropdown,
#impersonation-dropdown {
    position: fixed !important;
    bottom: 85px !important;
    right: 20px !important;
    background-color: white !important;
    border-radius: 5px !important;
    box-shadow: 0 4px 10px rgba(0,0,0,0.3) !important;
    z-index: 9997 !important;
    padding: 10px !important;
    min-width: 200px !important;
}

.impersonation-dropdown.show,
#impersonation-dropdown.show {
    display: block !important;
}

.impersonation-dropdown a,
#impersonation-dropdown a {
    display: block !important;
    padding: 8px 10px !important;
    color: #333 !important;
    text-decoration: none !important;
    border-radius: 3px !important;
}

.impersonation-dropdown a:hover,
#impersonation-dropdown a:hover {
    background-color: #f8f9fa !important;
}

.impersonation-dropdown .dropdown-divider,
#impersonation-dropdown .dropdown-divider {
    height: 1px !important;
    background-color: #e9ecef !important;
    margin: 5px 0 !important;
}
