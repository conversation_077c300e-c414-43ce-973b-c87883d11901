from django.core.management.base import BaseCommand
from django.utils import timezone
from accounts.models import Company
from assistants.models import Assistant
from django.db import transaction

class Command(BaseCommand):
    help = 'Checks for expired tier and featured statuses for Companies and Assistants and reverts them.'

    def handle(self, *args, **options):
        now = timezone.now()
        self.stdout.write(f"[{now.strftime('%Y-%m-%d %H:%M:%S')}] Running check_expirations command...")

        reverted_company_tiers = 0
        reverted_company_featured = 0
        reverted_assistant_tiers = 0
        reverted_assistant_featured = 0

        try:
            with transaction.atomic():
                # Check expired Company tiers
                expired_company_tiers = Company.objects.filter(
                    tier_expiry_date__isnull=False,
                    tier_expiry_date__lte=now
                ).exclude(tier=Company.TIER_STANDARD) # Don't revert if already standard

                for company in expired_company_tiers:
                    self.stdout.write(self.style.WARNING(f"Reverting tier for Company '{company.name}' (ID: {company.id}) from {company.tier} to Standard due to expiry ({company.tier_expiry_date})."))
                    company.tier = Company.TIER_STANDARD
                    company.tier_expiry_date = None
                    company.tier_change_pending = False # Ensure pending is cleared
                    company.requested_tier = None
                    company.requested_tier_duration = None
                    company.save(update_fields=['tier', 'tier_expiry_date', 'tier_change_pending', 'requested_tier', 'requested_tier_duration'])
                    reverted_company_tiers += 1

                # Check expired Company featured status
                expired_company_featured = Company.objects.filter(
                    featured_expiry_date__isnull=False,
                    featured_expiry_date__lte=now,
                    is_featured=True # Only revert if currently featured
                )

                for company in expired_company_featured:
                    self.stdout.write(self.style.WARNING(f"Reverting featured status for Company '{company.name}' (ID: {company.id}) due to expiry ({company.featured_expiry_date})."))
                    company.is_featured = False
                    company.featured_expiry_date = None
                    company.featured_request_pending = False # Ensure pending is cleared
                    company.requested_featured_duration = None
                    company.save(update_fields=['is_featured', 'featured_expiry_date', 'featured_request_pending', 'requested_featured_duration'])
                    reverted_company_featured += 1

                # Check expired Assistant tiers
                expired_assistant_tiers = Assistant.objects.filter(
                    tier_expiry_date__isnull=False,
                    tier_expiry_date__lte=now
                ).exclude(tier=Assistant.TIER_STANDARD)

                for assistant in expired_assistant_tiers:
                    self.stdout.write(self.style.WARNING(f"Reverting tier for Assistant '{assistant.name}' (ID: {assistant.id}) from {assistant.tier} to Standard due to expiry ({assistant.tier_expiry_date})."))
                    assistant.tier = Assistant.TIER_STANDARD
                    assistant.tier_expiry_date = None
                    assistant.tier_change_pending = False
                    assistant.requested_tier = None
                    assistant.requested_tier_duration = None
                    assistant.save(update_fields=['tier', 'tier_expiry_date', 'tier_change_pending', 'requested_tier', 'requested_tier_duration'])
                    reverted_assistant_tiers += 1

                # Check expired Assistant featured status
                expired_assistant_featured = Assistant.objects.filter(
                    featured_expiry_date__isnull=False,
                    featured_expiry_date__lte=now,
                    is_featured=True
                )

                for assistant in expired_assistant_featured:
                    self.stdout.write(self.style.WARNING(f"Reverting featured status for Assistant '{assistant.name}' (ID: {assistant.id}) due to expiry ({assistant.featured_expiry_date})."))
                    assistant.is_featured = False
                    assistant.featured_expiry_date = None
                    assistant.featured_request_pending = False
                    assistant.requested_featured_duration = None
                    assistant.save(update_fields=['is_featured', 'featured_expiry_date', 'featured_request_pending', 'requested_featured_duration'])
                    reverted_assistant_featured += 1

            self.stdout.write(self.style.SUCCESS(
                f"Expiration check complete. Reverted: "
                f"{reverted_company_tiers} company tiers, "
                f"{reverted_company_featured} company featured, "
                f"{reverted_assistant_tiers} assistant tiers, "
                f"{reverted_assistant_featured} assistant featured."
            ))

        except Exception as e:
            self.stderr.write(self.style.ERROR(f"An error occurred during expiration check: {e}"))
