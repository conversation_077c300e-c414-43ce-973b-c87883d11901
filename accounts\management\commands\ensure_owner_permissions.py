"""
Management command to ensure all company owners have the proper permissions.
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import Permission, Group
from django.contrib.contenttypes.models import ContentType
from guardian.shortcuts import assign_perm, get_perms
from accounts.models import Company
from accounts.permissions import OWNER_PERMS_COMPANY

class Command(BaseCommand):
    help = 'Ensures all company owners have the proper permissions.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--company',
            type=int,
            help='Specify a company ID to update permissions for just that company',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed information about permissions being assigned',
        )

    def handle(self, *args, **options):
        company_id = options.get('company')
        verbose = options.get('verbose', False)
        
        # Get companies to process
        if company_id:
            companies = Company.objects.filter(id=company_id)
            if not companies.exists():
                self.stdout.write(self.style.ERROR(f"Company with ID {company_id} not found."))
                return
        else:
            companies = Company.objects.all()
            
        self.stdout.write(f"Processing {companies.count()} companies...")
        
        # Process each company
        for company in companies:
            owner = company.owner
            if not owner:
                self.stdout.write(self.style.WARNING(f"Company '{company.name}' (ID: {company.id}) has no owner. Skipping."))
                continue
                
            self.stdout.write(f"Processing company '{company.name}' (ID: {company.id}) with owner '{owner.username}'")
            
            # Get current permissions
            current_perms = get_perms(owner, company)
            if verbose:
                self.stdout.write(f"  Current permissions: {current_perms}")
            
            # Assign all owner permissions
            for perm in OWNER_PERMS_COMPANY:
                try:
                    if perm not in current_perms:
                        assign_perm(perm, owner, company)
                        if verbose:
                            self.stdout.write(self.style.SUCCESS(f"  Assigned permission '{perm}' to owner"))
                except Permission.DoesNotExist:
                    self.stdout.write(self.style.ERROR(f"  Permission '{perm}' does not exist"))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"  Error assigning permission '{perm}': {e}"))
            
            # Verify permissions after assignment
            final_perms = get_perms(owner, company)
            if verbose:
                self.stdout.write(f"  Final permissions: {final_perms}")
                
            # Check if any permissions are missing
            missing_perms = set(OWNER_PERMS_COMPANY) - set(final_perms)
            if missing_perms:
                self.stdout.write(self.style.WARNING(f"  Missing permissions: {missing_perms}"))
            else:
                self.stdout.write(self.style.SUCCESS(f"  All permissions assigned successfully"))
                
        self.stdout.write(self.style.SUCCESS("Done!"))
