#!/bin/bash
# Script to run the Django application with Gunicorn
# This replaces the Passenger deployment method

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# Install or update dependencies
pip install -r requirements.txt

# Collect static files
python manage.py collectstatic --noinput

# Apply migrations
python manage.py migrate

# Start Gunicorn with the configuration file
gunicorn prompt_game.wsgi:application -c gunicorn_config.py
