import qrcode
from io import BytesIO
from django.core.files.base import ContentFile
from django.utils.text import slugify
from PIL import Image, ImageDraw, ImageFont
from django.core.files.base import ContentFile
from django.urls import reverse
from django.contrib.sites.models import Site

def generate_qr_code(instance, url_path, field_name='qr_code'):
    """
    Generates a QR code for the given instance and URL path,
    and saves it to the specified ImageField.
    Adds a capital 'A' in the middle of the QR code to identify it as an assistant QR code.
    """
    try:
        # Construct the full URL
        current_site = Site.objects.get_current()
        # Ensure the path starts with a slash
        if not url_path.startswith('/'):
            url_path = '/' + url_path
        full_url = f"http://{current_site.domain}{url_path}" # Use http for local dev, adjust for https in prod

        # Generate QR code with higher error correction to ensure it remains scannable with the 'A' overlay
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_H,  # Use high error correction
            box_size=10,
            border=4,
        )
        qr.add_data(full_url)
        qr.make(fit=True)

        # Create QR code image
        qr_img = qr.make_image(fill_color="black", back_color="white")

        # Convert to RGB mode if it's not already
        if qr_img.mode != 'RGB':
            qr_img = qr_img.convert('RGB')

        # Create a drawing context
        draw = ImageDraw.Draw(qr_img)

        # Calculate the center of the QR code
        width, height = qr_img.size
        center_x, center_y = width // 2, height // 2

        # Calculate font size based on QR code size (approximately 1/5 of the QR code width)
        # Smaller font size to take up less space
        font_size = width // 5

        try:
            # Try to use a system font - Arial Bold if available
            try:
                # Try for Arial Bold first
                font = ImageFont.truetype("arialbd.ttf", font_size)
            except IOError:
                # Fall back to regular Arial
                font = ImageFont.truetype("arial.ttf", font_size)
        except IOError:
            # Fallback to default font if system font not available
            font = ImageFont.load_default()

        # Get the size of the letter 'A' with the chosen font
        text = 'A'
        try:
            # For newer Pillow versions
            text_width, text_height = draw.textbbox((0, 0), text, font=font)[2:]
        except AttributeError:
            # For older Pillow versions
            text_width, text_height = draw.textsize(text, font=font)

        # Calculate position to center the text
        text_x = center_x - (text_width // 2)
        text_y = center_y - (text_height // 2)

        # Draw white background for the letter to ensure visibility
        # Reduced padding to make the white space smaller
        padding = font_size // 6
        draw.rectangle(
            [
                text_x - padding,
                text_y - padding,
                text_x + text_width + padding,
                text_y + text_height + padding
            ],
            fill="white"
        )

        # Draw the letter 'A' in black with a bolder appearance
        # Draw the text multiple times with slight offsets to create a bold effect
        draw.text((text_x, text_y), text, fill="black", font=font)
        draw.text((text_x+1, text_y), text, fill="black", font=font)
        draw.text((text_x, text_y+1), text, fill="black", font=font)

        # Save QR code image to a BytesIO buffer
        buffer = BytesIO()
        qr_img.save(buffer, format='PNG')
        buffer.seek(0)

        # Create a unique filename
        model_name = instance.__class__.__name__.lower()
        instance_slug = getattr(instance, 'slug', instance.pk) # Use slug if available, else pk
        filename = f'{model_name}_qr_{instance_slug}.png'

        # Get the field and save the image
        image_field = getattr(instance, field_name)
        image_field.save(filename, ContentFile(buffer.read()), save=False) # save=False to avoid recursion if called in save()

        buffer.close()
        return True
    except Exception as e:
        print(f"Error generating QR code for {instance}: {e}")
        return False

def generate_company_qr(company):
    """
    Generate a QR code for a company with custom styling.
    Adds a capital 'A' in the middle of the QR code to identify it as an assistant QR code.
    """
    # Create QR code instance
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_H,  # Use high error correction
        box_size=10,
        border=4,
    )

    # Add company data
    data = {
        'id': str(company.id),
        'name': company.name,
        'type': 'company'
    }
    qr.add_data(str(data))
    qr.make(fit=True)

    # Create image with custom colors
    qr_image = qr.make_image(
        fill_color="#007bff",  # Bootstrap primary color
        back_color="white"
    ).convert('RGB')

    # Create a drawing context
    draw = ImageDraw.Draw(qr_image)

    # Calculate the center of the QR code
    width, height = qr_image.size
    center_x, center_y = width // 2, height // 2

    # Calculate font size based on QR code size (approximately 1/5 of the QR code width)
    # Smaller font size to take up less space
    font_size = width // 5

    try:
        # Try to use a system font - Arial Bold if available
        try:
            # Try for Arial Bold first
            font = ImageFont.truetype("arialbd.ttf", font_size)
        except IOError:
            # Fall back to regular Arial
            font = ImageFont.truetype("arial.ttf", font_size)
    except IOError:
        # Fallback to default font if system font not available
        font = ImageFont.load_default()

    # Get the size of the letter 'A' with the chosen font
    text = 'A'
    try:
        # For newer Pillow versions
        text_width, text_height = draw.textbbox((0, 0), text, font=font)[2:]
    except AttributeError:
        # For older Pillow versions
        text_width, text_height = draw.textsize(text, font=font)

    # Calculate position to center the text
    text_x = center_x - (text_width // 2)
    text_y = center_y - (text_height // 2)

    # Draw white background for the letter to ensure visibility
    # Reduced padding to make the white space smaller
    padding = font_size // 6
    draw.rectangle(
        [
            text_x - padding,
            text_y - padding,
            text_x + text_width + padding,
            text_y + text_height + padding
        ],
        fill="white"
    )

    # Draw the letter 'A' in black with a bolder appearance
    # Draw the text multiple times with slight offsets to create a bold effect
    draw.text((text_x, text_y), text, fill="black", font=font)
    draw.text((text_x+1, text_y), text, fill="black", font=font)
    draw.text((text_x, text_y+1), text, fill="black", font=font)

    # Create a BytesIO object to temporarily hold the image
    temp_buffer = BytesIO()

    # Save the image to the buffer
    qr_image.save(temp_buffer, format='PNG')

    # Create a unique filename
    filename = f'qr_codes/company_qr_{slugify(company.name)}.png'

    # Return ContentFile for saving to ImageField
    return ContentFile(temp_buffer.getvalue(), name=filename)

def format_phone_number(phone_number):
    """Format phone number to consistent format."""
    # Remove all non-digit characters
    digits = ''.join(filter(str.isdigit, phone_number))

    # Handle different formats based on length and country code
    if len(digits) == 10:  # Standard US/local number
        return f"+1 ({digits[:3]}) {digits[3:6]}-{digits[6:]}"
    elif len(digits) > 10:  # International number
        return f"+{digits[:-10]} ({digits[-10:-7]}) {digits[-7:-4]}-{digits[-4:]}"
    return phone_number  # Return original if can't format

def get_gravatar_url(email, size=40, default='mp'):
    """Generate Gravatar URL for an email address."""
    import hashlib
    email_hash = hashlib.md5(email.lower().encode('utf-8')).hexdigest()
    return f"https://www.gravatar.com/avatar/{email_hash}?s={size}&d={default}"

def generate_invite_token():
    """Generate a secure token for team invitations."""
    from django.utils.crypto import get_random_string
    return get_random_string(64)

def get_company_size_display(employee_count):
    """Convert employee count to display format."""
    if employee_count < 10:
        return "1-9 employees"
    elif employee_count < 50:
        return "10-49 employees"
    elif employee_count < 200:
        return "50-199 employees"
    elif employee_count < 1000:
        return "200-999 employees"
    return "1000+ employees"

def sanitize_company_name(name):
    """Clean and validate company name."""
    from django.utils.text import slugify
    # Remove any special characters except spaces and hyphens
    clean_name = ''.join(c for c in name if c.isalnum() or c in ' -')
    # Convert to title case and strip whitespace
    return clean_name.title().strip()

def validate_domain(domain):
    """Validate a custom domain."""
    import re
    pattern = r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$'
    return bool(re.match(pattern, domain))

def calculate_company_metrics(company):
    """Calculate various company metrics."""
    from django.utils import timezone
    from datetime import timedelta

    now = timezone.now()
    last_month = now - timedelta(days=30)

    # Get memberships instead of members (which doesn't exist anymore)
    metrics = {
        'total_members': company.memberships.count(),
        'active_members': 0,  # This would need to be calculated differently
        'total_content': 0,   # This field might not exist
        'total_assistants': 0,  # Assistants app doesn't exist
        'monthly_activity': company.activity_logs.filter(created_at__gte=last_month).count()
    }

    # Try to calculate active members if possible
    try:
        from django.contrib.auth import get_user_model
        User = get_user_model()
        member_ids = company.memberships.values_list('user_id', flat=True)
        active_members = User.objects.filter(id__in=member_ids, last_login__gte=last_month).count()
        metrics['active_members'] = active_members
    except Exception as e:
        print(f"Error calculating active members: {e}")

    return metrics


# --- New Permission Checking Utilities (Using django-guardian) ---

# Note: The old get_membership and has_company_permission functions are removed.
# Direct checks using user.has_perm(perm, obj) should be used instead.

from django.contrib.auth.models import Group # Import Group model

# Try to import assistant models, but handle the case where they don't exist
try:
    from assistants.models import Assistant, AssistantFolder
except ImportError:
    # Create dummy classes for Assistant and AssistantFolder
    class Assistant:
        pass

    class AssistantFolder:
        pass

def _user_in_company_group(user, company, group_names):
    """Helper function to check if a user is in specified groups for a company."""
    if not user or not user.is_authenticated or not company:
        return False
    try:
        # Assuming group names might be company-specific (e.g., "admin_1", "member_1")
        # or generic ("company administrators", "company members").
        # We'll try both specific and generic names.
        company_specific_group_names = [f"{name}_{company.id}" for name in group_names]
        all_possible_names = group_names + company_specific_group_names

        # Check if the user belongs to any of these groups
        return user.groups.filter(name__in=all_possible_names).exists()
    except Group.DoesNotExist:
        return False # Group doesn't exist
    except Exception as e:
        # Log error for debugging?
        print(f"Error checking group membership for user {user.id} in company {company.id}: {e}")
        return False

def can_access_assistant(user, assistant):
    """
    Checks if a user can access (view/use) a specific assistant using django-guardian.

    Args:
        user: The user object.
        assistant: The assistant object.

    Returns:
        bool: True if the user can access the assistant, False otherwise.
    """
    # Check if assistants app is available
    try:
        from assistants.models import Assistant
    except ImportError:
        # Assistants app not available, return False
        return False

    if not user or not user.is_authenticated or not assistant or not hasattr(assistant, 'company') or not assistant.company:
        return False

    # Owner always has access
    if user.id == assistant.company.owner_id:
        return True

    # Public assistants are accessible to any authenticated user
    if hasattr(assistant, 'is_public') and assistant.is_public:
        return True

    # For private assistants, check object-level view permission
    # This covers owner, admin (via access_all_private), and folder-specific access
    # granted during the migration.
    if user.has_perm('assistants.view_assistant', assistant):
        return True

    # NEW: Check if user is in company member or admin group for the assistant's company
    company_groups = ["company administrators", "company members"]
    if _user_in_company_group(user, assistant.company, company_groups):
        return True

    return False # Deny access if none of the above conditions are met


def can_edit_assistant(user, assistant):
    """Checks if user can edit a specific assistant using django-guardian."""
    # Check if assistants app is available
    try:
        import assistants
    except ImportError:
        # Assistants app not available, return False
        return False

    if not user or not user.is_authenticated or not assistant:
        return False

    # Check object-level change permission.
    # This covers owner, admin (via access_all_private), folder-specific access,
    # and potentially creator (if granted change_assistant perm on creation).
    return user.has_perm('assistants.change_assistant', assistant)


def can_delete_assistant(user, assistant):
    """Checks if user can delete a specific assistant using django-guardian."""
    # Check if assistants app is available
    try:
        import assistants
    except ImportError:
        # Assistants app not available, return False
        return False

    if not user or not user.is_authenticated or not assistant:
        return False
    return user.has_perm('assistants.delete_assistant', assistant)


def can_view_assistant_usage(user, assistant):
    """Checks if user can view usage for a specific assistant using django-guardian."""
    # Check if assistants app is available
    try:
        import assistants
    except ImportError:
        # Assistants app not available, return False
        return False

    if not user or not user.is_authenticated or not assistant:
        return False
    # Check custom object-level permission
    return user.has_perm('assistants.view_assistant_usage', assistant)


def can_view_assistant_analytics(user, assistant):
    """Checks if user can view analytics for a specific assistant using django-guardian."""
    # Check if assistants app is available
    try:
        import assistants
    except ImportError:
        # Assistants app not available, return False
        return False

    if not user or not user.is_authenticated or not assistant:
        return False
    # Check custom object-level permission
    return user.has_perm('assistants.view_assistant_analytics', assistant)


def can_create_assistant_token(user, assistant):
    """Checks if user can create tokens for a specific assistant using django-guardian."""
    # Check if assistants app is available
    try:
        import assistants
    except ImportError:
        # Assistants app not available, return False
        return False

    if not user or not user.is_authenticated or not assistant:
        return False
    # Check custom object-level permission
    return user.has_perm('assistants.create_assistant_token', assistant)

# Note: Permissions related to managing folders (add/change/delete AssistantFolder)
# will typically be checked against the *Company* object, as folders belong to a company.
# e.g., user.has_perm('assistants.add_assistantfolder', company)

# Note: Permissions related to managing company members/settings/billing
# will be checked against the *Company* object.
# e.g., user.has_perm('accounts.manage_members', company)

# --- Django-Impersonate Utility Functions ---

def get_impersonation_users(request):
    """
    Custom queryset for django-impersonate to determine which users can be impersonated.
    This function is referenced in settings via IMPERSONATE_CUSTOM_USER_QUERYSET.

    Returns all users that are company owners or have specific roles.
    """
    from django.contrib.auth import get_user_model
    from accounts.models import Company

    User = get_user_model()

    # Get all company owners
    company_owners = Company.objects.values_list('owner_id', flat=True).distinct()

    # Return queryset of users who are company owners
    return User.objects.filter(id__in=company_owners)

def allow_impersonation(request, impersonator, impersonatee):
    """
    Custom permission check for django-impersonate.
    This function is referenced in settings via IMPERSONATE_CUSTOM_ALLOW.

    Args:
        request: The current request
        impersonator: The user doing the impersonation
        impersonatee: The user being impersonated

    Returns:
        bool: True if impersonation is allowed, False otherwise
    """
    # Only superusers can impersonate
    if not impersonator.is_superuser:
        return False

    # Check if impersonatee is a company owner
    from accounts.models import Company
    is_company_owner = Company.objects.filter(owner=impersonatee).exists()

    # Allow impersonation if the user is a company owner
    return is_company_owner
