{% load static %}

<style>
    /* Additional page-specific styles for user progress page */
    
    /* Make table headers brighter */
    .table thead th {
        color: #ffffff !important;
        font-weight: bold !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5) !important;
    }
    
    /* Make user names brighter */
    .table tbody tr td .user-name,
    .table tbody tr td .d-flex .user-name,
    .table tbody tr td h6.user-name {
        color: #ffffff !important;
        font-weight: bold !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5) !important;
    }
    
    /* Make usernames brighter */
    .table tbody tr td small.user-handle,
    .table tbody tr td .user-handle {
        color: #ffffff !important;
    }
    
    /* Make all user data in table cells brighter */
    .table tbody tr td {
        color: #ffffff !important;
    }
    
    /* Make "Not specified" text brighter */
    .table tbody tr td span.text-muted,
    .table tbody tr td span[style*="color: #333333; font-style: italic;"] {
        color: #ffffff !important;
        font-style: italic;
    }
    
    /* Make "No certificates" text brighter */
    .table tbody tr td span.text-muted {
        color: #ffffff !important;
    }
    
    /* Ensure user avatars have good contrast */
    .avatar-placeholder {
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
    }
    
    /* Make badge text brighter while keeping the background color */
    .badge.bg-danger,
    .badge.bg-secondary,
    .badge.bg-success,
    .badge.bg-primary {
        color: #ffffff !important;
        font-weight: bold !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5) !important;
        border: 1px solid rgba(255, 255, 255, 0.3) !important;
    }
</style>
