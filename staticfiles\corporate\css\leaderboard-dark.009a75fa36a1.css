/* Dark Mode Styles for Leaderboard */

/* Main background and text colors */
.dark-mode .card {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Card headers */
.dark-mode .card-header {
    background-color: #2d2d2d !important;
    border-bottom: 1px solid #444444 !important;
    color: #ffffff !important;
}

.dark-mode .card-header.bg-primary {
    background-color: #1a4b9c !important;
    color: #ffffff !important;
    border-bottom: 1px solid #0a3b8c !important;
}

/* Card body */
.dark-mode .card-body {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* Form elements */
.dark-mode .form-select,
.dark-mode .form-control {
    background-color: #2a2a2a !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

.dark-mode .form-select option {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

.dark-mode .form-text {
    color: #aaaaaa !important;
}

.dark-mode .form-label {
    color: #ffffff !important;
}

/* Buttons */
.dark-mode .btn-outline-primary {
    color: #7ca8ff !important;
    border-color: #7ca8ff !important;
    background-color: transparent !important;
}

.dark-mode .btn-outline-primary:hover,
.dark-mode .btn-outline-primary.active {
    background-color: #1a4b9c !important;
    color: #ffffff !important;
    border-color: #1a4b9c !important;
}

.dark-mode .btn-primary {
    background-color: #1a4b9c !important;
    border-color: #1a4b9c !important;
    color: #ffffff !important;
}

.dark-mode .btn-primary:hover {
    background-color: #0d3b8c !important;
    border-color: #0d3b8c !important;
}

/* Nav tabs */
.dark-mode .nav-tabs {
    border-bottom-color: #444444 !important;
}

.dark-mode .nav-tabs .nav-link {
    color: #7ca8ff !important;
    background-color: #2a2a2a !important;
    border-color: #444444 !important;
}

.dark-mode .nav-tabs .nav-link.active {
    color: #ffffff !important;
    background-color: #1a4b9c !important;
    border-color: #1a4b9c !important;
}

/* Tables */
.dark-mode .table {
    color: #ffffff !important;
    background-color: transparent !important;
}

.dark-mode .table thead th {
    background-color: #2d2d2d !important;
    color: #ffffff !important;
    border-bottom-color: #444444 !important;
    border-top: none !important;
}

.dark-mode .table tbody tr {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

.dark-mode .table tbody tr:nth-of-type(odd) {
    background-color: #252525 !important;
}

.dark-mode .table tbody td {
    color: #ffffff !important;
    border-color: #444444 !important;
}

.dark-mode .table-hover tbody tr:hover {
    background-color: #333333 !important;
}

/* Override for table-primary */
.dark-mode .table-primary,
.dark-mode .table-primary > td,
.dark-mode .table-primary > th {
    background-color: #1a4b9c !important;
    color: #ffffff !important;
}

/* Badges */
.dark-mode .badge.bg-light {
    background-color: #444444 !important;
    color: #ffffff !important;
}

.dark-mode .badge.bg-primary {
    background-color: #1a4b9c !important;
    color: #ffffff !important;
}

.dark-mode .badge.bg-secondary {
    background-color: #555555 !important;
    color: #ffffff !important;
}

.dark-mode .badge.bg-success {
    background-color: #198754 !important;
    color: #ffffff !important;
}

.dark-mode .badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

/* Alerts */
.dark-mode .alert-info {
    background-color: #1a3c5c !important;
    color: #ffffff !important;
    border-color: #2a5c8c !important;
}

/* Footer */
.dark-mode .footer {
    background-color: #1a1a1a !important;
    color: #cccccc !important;
    border-top: 1px solid #444444 !important;
}

.dark-mode .footer .text-muted {
    color: #cccccc !important;
}

/* Fix for text in tables */
.dark-mode .table tbody * {
    color: #ffffff !important;
}

/* Fix for text in badges */
.dark-mode .badge.bg-warning.text-dark {
    color: #212529 !important;
}
