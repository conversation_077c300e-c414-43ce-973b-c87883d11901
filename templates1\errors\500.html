{% extends 'base/layout.html' %}
{% load static %}

{% block title %}Server Error - 24seven{% endblock %}

{% block body_class %}error-page{% endblock %}

{% block navbar %}{% endblock %}

{% block content %}
<div class="min-vh-100 d-flex align-items-center">
    <div class="container text-center py-5">
        <div class="display-1 text-warning mb-3">
            <i class="bi bi-exclamation-triangle"></i>
        </div>

        <h1 class="display-4 fw-bold mb-3">Server Error</h1>
        <h2 class="h4 mb-4">Sorry, something went wrong on our end</h2>

        <p class="lead text-muted mb-4">
            We've been notified and are working to fix the issue.<br>
            Please try again in a few moments.
        </p>

        <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
            <a href="{% url 'home' %}" class="btn btn-primary">
                <i class="bi bi-house-door me-2"></i>
                Back to Home
            </a>
            <button onclick="location.reload()" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-clockwise me-2"></i>
                Try Again
            </button>
            {% if request.user.is_authenticated %}
                <a href="{% url 'accounts:dashboard' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-speedometer2 me-2"></i>
                    Dashboard
                </a>
            {% endif %}
        </div>

        {% if user.is_staff %}
            <div class="mt-4 text-muted">
                <small>
                    <strong>Debug Info:</strong><br>
                    Request Path: {{ request.path }}<br>
                    User Agent: {{ request.META.HTTP_USER_AGENT }}<br>
                    Timestamp: {% now "Y-m-d H:i:s" %}<br>
                    {% if request.META.HTTP_X_FORWARDED_FOR %}
                        IP: {{ request.META.HTTP_X_FORWARDED_FOR }}
                    {% else %}
                        IP: {{ request.META.REMOTE_ADDR }}
                    {% endif %}
                </small>
            </div>

            <div class="mt-3">
                <a href="{% url 'admin:index' %}" class="btn btn-sm btn-outline-secondary">
                    <i class="bi bi-gear me-2"></i>
                    Admin Panel
                </a>
            </div>
        {% endif %}

        <div class="mt-5">
            <p class="small text-muted">
                Need help? <a href="{% url 'contact' %}">Contact Support</a>
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block footer %}{% endblock %}

{% block extra_css %}
<style>
.bi-exclamation-triangle {
    animation: shake 1s ease-in-out infinite;
}

@keyframes shake {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

.error-page .btn {
    min-width: 140px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Automatically refresh after 60 seconds if still on error page
    setTimeout(function() {
        if (document.querySelector('.error-page')) {
            location.reload();
        }
    }, 60000);
});
</script>
{% endblock %}
